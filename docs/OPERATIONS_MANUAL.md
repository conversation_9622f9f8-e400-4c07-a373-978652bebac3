# SeatMaster 运维手册

## 📋 概述

本手册为SeatMaster系统的日常运维提供详细指导，包括监控、维护、故障处理和性能优化等内容。

## 🔍 日常监控

### 系统健康检查清单

#### 每日检查 (自动化)
```bash
# 执行每日健康检查
/usr/local/bin/seatmaster-performance.sh

# 检查项目:
# ✓ 服务状态
# ✓ 系统资源使用
# ✓ 磁盘空间
# ✓ 内存使用
# ✓ 数据库连接
# ✓ 应用响应时间
```

#### 每周检查 (手动)
```bash
# 1. 检查备份完整性
ls -la /var/backups/seatmaster/
./scripts/backup.sh database  # 测试备份

# 2. 检查日志错误
./scripts/log-manager.sh analyze all

# 3. 检查安全状态
/usr/local/bin/security-check.sh

# 4. 检查性能指标
/usr/local/bin/jvm-monitor.sh
/usr/local/bin/mysql-monitor.sh
```

#### 每月检查 (手动)
```bash
# 1. 系统更新
sudo apt update && sudo apt list --upgradable

# 2. 证书有效期检查
sudo openssl x509 -in /etc/nginx/ssl/fullchain.pem -noout -dates

# 3. 磁盘空间趋势分析
df -h && du -sh /var/log/* /var/backups/*

# 4. 数据库性能分析
mysql -e "SHOW PROCESSLIST; SHOW ENGINE INNODB STATUS\G"
```

### 关键指标监控

#### 应用指标
```bash
# 后端服务状态
curl -s http://localhost:8081/actuator/health | jq .

# 响应时间监控
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8081/api/health

# JVM内存使用
jstat -gc $(pgrep -f seat-reservation-backend)

# 线程状态
jstack $(pgrep -f seat-reservation-backend) | grep "java.lang.Thread.State" | sort | uniq -c
```

#### 系统指标
```bash
# CPU使用率
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1

# 内存使用率
free | grep Mem | awk '{printf "%.2f%%\n", $3/$2 * 100.0}'

# 磁盘I/O
iostat -x 1 3

# 网络连接
ss -tuln | grep -E "(8081|3306|443)"
```

#### 数据库指标
```bash
# 连接数
mysql -e "SHOW STATUS LIKE 'Threads_connected';"

# 慢查询
mysql -e "SHOW STATUS LIKE 'Slow_queries';"

# InnoDB缓冲池使用率
mysql -e "SELECT ROUND((1 - (FREE_BUFFERS / POOL_SIZE)) * 100, 2) AS buffer_pool_usage_percent FROM information_schema.INNODB_BUFFER_POOL_STATS;"

# 锁等待
mysql -e "SELECT * FROM information_schema.INNODB_LOCKS;"
```

## 🚨 告警配置

### 告警阈值设置

#### 系统资源告警
```bash
# CPU使用率 > 80%
# 内存使用率 > 85%
# 磁盘使用率 > 85%
# 磁盘I/O等待 > 20%
# 网络连接数 > 1000
```

#### 应用告警
```bash
# 应用响应时间 > 2秒
# 错误率 > 5%
# JVM堆内存使用 > 85%
# GC时间占比 > 10%
# 线程死锁检测
```

#### 数据库告警
```bash
# 数据库连接数 > 80%最大值
# 慢查询数量 > 10/分钟
# 锁等待时间 > 30秒
# 复制延迟 > 60秒
```

### 告警通知配置
```bash
# 邮件通知配置
echo "ALERT_EMAIL=<EMAIL>" >> /etc/seatmaster/monitoring.conf

# Webhook通知配置
echo "ALERT_WEBHOOK=https://hooks.slack.com/services/..." >> /etc/seatmaster/monitoring.conf

# 短信通知配置 (可选)
echo "SMS_API_KEY=your_sms_api_key" >> /etc/seatmaster/monitoring.conf
```

## 🔧 日常维护任务

### 自动化维护任务

#### 每日任务 (cron配置)
```bash
# /etc/cron.d/seatmaster-maintenance

# 每日2点执行完整备份
0 2 * * * root /root/seatMaster/scripts/backup.sh full

# 每日3点清理临时文件
0 3 * * * root find /tmp -name "seatmaster*" -mtime +1 -delete

# 每日4点轮转日志
0 4 * * * root /root/seatMaster/scripts/log-manager.sh rotate

# 每日6点健康检查
0 6 * * * root /usr/local/bin/seatmaster-performance.sh >> /var/log/seatmaster/daily-check.log
```

#### 每周任务
```bash
# 每周日1点数据库优化
0 1 * * 0 root mysql -e "OPTIMIZE TABLE seat_reservation.reservations, seat_reservation.users;"

# 每周日2点清理旧备份
0 2 * * 0 root find /var/backups/seatmaster -type d -mtime +30 -exec rm -rf {} \;

# 每周日3点安全扫描
0 3 * * 0 root /usr/local/bin/security-check.sh >> /var/log/seatmaster/security-scan.log
```

### 手动维护任务

#### 数据库维护
```bash
# 1. 数据库表优化
mysql -u root -p << EOF
USE seat_reservation;
OPTIMIZE TABLE users;
OPTIMIZE TABLE reservations;
OPTIMIZE TABLE rooms;
OPTIMIZE TABLE schools;
ANALYZE TABLE users;
ANALYZE TABLE reservations;
EOF

# 2. 清理过期数据
mysql -u root -p << EOF
USE seat_reservation;
DELETE FROM reservations WHERE status = 'cancelled' AND created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
DELETE FROM task_execution_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
EOF

# 3. 重建索引 (如需要)
mysql -u root -p << EOF
USE seat_reservation;
ALTER TABLE reservations DROP INDEX idx_user_date, ADD INDEX idx_user_date (user_id, reservation_date);
EOF
```

#### 应用维护
```bash
# 1. 清理应用缓存
rm -rf /opt/seatmaster/temp/*
rm -rf /tmp/seatmaster*

# 2. 重启应用服务 (滚动重启)
systemctl restart seatmaster-backend
sleep 30
systemctl restart seatmaster-frontend

# 3. 清理JVM堆转储文件
find /opt/seatmaster/logs -name "*.hprof" -mtime +7 -delete
```

#### 系统维护
```bash
# 1. 系统包更新
apt update
apt list --upgradable
apt upgrade -y

# 2. 内核参数调优验证
sysctl -p /etc/sysctl.d/99-seatmaster-performance.conf

# 3. 文件系统检查
fsck -n /dev/sda1  # 只读检查

# 4. 清理系统日志
journalctl --vacuum-time=30d
```

## 🚑 故障处理流程

### 故障分类和处理

#### P0 - 紧急故障 (系统完全不可用)
```bash
# 处理时间: 15分钟内响应，1小时内恢复

# 1. 立即检查
systemctl status seatmaster-backend seatmaster-frontend nginx mysql

# 2. 快速恢复步骤
# 如果是应用问题
systemctl restart seatmaster-backend
systemctl restart seatmaster-frontend

# 如果是数据库问题
systemctl restart mysql
# 检查数据库日志
tail -f /var/log/mysql/error.log

# 如果是系统问题
# 检查磁盘空间
df -h
# 检查内存
free -h
# 检查进程
ps aux | head -20

# 3. 如果无法快速恢复，启动灾难恢复
./scripts/restore.sh /var/backups/seatmaster/latest full
```

#### P1 - 高优先级故障 (部分功能不可用)
```bash
# 处理时间: 30分钟内响应，4小时内恢复

# 1. 问题定位
./scripts/log-manager.sh analyze all
curl -s http://localhost:8081/actuator/health

# 2. 常见问题处理
# 数据库连接池耗尽
mysql -e "SHOW PROCESSLIST;" | grep Sleep | wc -l
# 重启应用释放连接
systemctl restart seatmaster-backend

# 内存不足
free -h
# 清理缓存
echo 3 > /proc/sys/vm/drop_caches

# 磁盘空间不足
du -sh /var/log/* | sort -hr
./scripts/log-manager.sh clean --days 7
```

#### P2 - 中等优先级故障 (性能问题)
```bash
# 处理时间: 2小时内响应，24小时内恢复

# 1. 性能分析
/usr/local/bin/jvm-monitor.sh
/usr/local/bin/mysql-monitor.sh
top -p $(pgrep -f seat-reservation-backend)

# 2. 性能优化
# JVM调优
source /opt/seatmaster/jvm.conf
systemctl restart seatmaster-backend

# 数据库调优
mysql -e "SHOW VARIABLES LIKE 'innodb_buffer_pool_size';"
# 如需调整，修改配置后重启MySQL

# 3. 监控改善情况
watch -n 5 'curl -w "%{time_total}" -o /dev/null -s http://localhost:8081/api/health'
```

### 故障处理记录模板

```bash
# 创建故障记录
cat > /var/log/seatmaster/incident-$(date +%Y%m%d-%H%M).md << EOF
# 故障处理记录

## 基本信息
- 故障时间: $(date)
- 故障级别: P0/P1/P2
- 影响范围: 
- 处理人员: 

## 故障现象
- 用户反馈: 
- 监控告警: 
- 系统表现: 

## 问题分析
- 根本原因: 
- 触发条件: 
- 影响评估: 

## 处理过程
- 应急措施: 
- 恢复步骤: 
- 验证结果: 

## 预防措施
- 监控改进: 
- 流程优化: 
- 技术改进: 

## 经验总结
- 经验教训: 
- 改进建议: 
EOF
```

## 📊 性能优化

### 性能基准测试

#### 应用性能测试
```bash
# 1. API响应时间测试
ab -n 1000 -c 10 http://localhost:8081/api/health

# 2. 数据库查询性能测试
mysql -e "SELECT SQL_NO_CACHE COUNT(*) FROM reservations WHERE reservation_date = CURDATE();" --verbose

# 3. 并发用户测试
# 使用JMeter或类似工具进行压力测试

# 4. 内存泄漏检测
jmap -histo $(pgrep -f seat-reservation-backend) | head -20
```

#### 系统性能基准
```bash
# 1. CPU性能测试
sysbench cpu --cpu-max-prime=20000 run

# 2. 内存性能测试
sysbench memory --memory-total-size=10G run

# 3. 磁盘I/O测试
sysbench fileio --file-total-size=10G prepare
sysbench fileio --file-total-size=10G --file-test-mode=rndrw run
sysbench fileio --file-total-size=10G cleanup

# 4. 网络性能测试
iperf3 -s  # 在服务器端
iperf3 -c server_ip  # 在客户端
```

### 性能调优策略

#### JVM调优
```bash
# 1. 堆内存调优
# 根据应用实际使用情况调整
export JAVA_OPTS="-Xms2g -Xmx4g"

# 2. GC调优
# 使用G1GC并调整暂停时间目标
export JAVA_OPTS="$JAVA_OPTS -XX:+UseG1GC -XX:MaxGCPauseMillis=100"

# 3. JIT编译优化
export JAVA_OPTS="$JAVA_OPTS -XX:+TieredCompilation -XX:TieredStopAtLevel=4"

# 4. 监控JVM性能
jstat -gc $(pgrep -f seat-reservation-backend) 5s
```

#### 数据库调优
```bash
# 1. 连接池优化
mysql -e "SET GLOBAL max_connections = 500;"
mysql -e "SET GLOBAL thread_cache_size = 50;"

# 2. 缓冲池优化
# 设置为可用内存的70-80%
mysql -e "SET GLOBAL innodb_buffer_pool_size = 2147483648;"  # 2GB

# 3. 查询优化
# 分析慢查询
mysql -e "SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;"

# 4. 索引优化
mysql -e "SHOW INDEX FROM reservations;"
# 根据查询模式添加复合索引
```

#### 系统调优
```bash
# 1. 网络参数优化
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 8192' >> /etc/sysctl.conf
sysctl -p

# 2. 文件描述符限制
echo '* soft nofile 65536' >> /etc/security/limits.conf
echo '* hard nofile 65536' >> /etc/security/limits.conf

# 3. 磁盘调度器优化
echo mq-deadline > /sys/block/sda/queue/scheduler

# 4. 内存管理优化
echo 10 > /proc/sys/vm/swappiness
echo 1 > /proc/sys/vm/overcommit_memory
```

## 📋 运维检查清单

### 每日检查清单
- [ ] 检查所有服务状态
- [ ] 检查系统资源使用情况
- [ ] 检查应用错误日志
- [ ] 检查备份执行状态
- [ ] 检查磁盘空间使用
- [ ] 检查数据库连接状态
- [ ] 检查网站可访问性

### 每周检查清单
- [ ] 分析性能趋势
- [ ] 检查安全日志
- [ ] 验证备份完整性
- [ ] 检查SSL证书有效期
- [ ] 清理临时文件和日志
- [ ] 检查系统更新
- [ ] 性能基准测试

### 每月检查清单
- [ ] 系统安全扫描
- [ ] 数据库性能分析
- [ ] 容量规划评估
- [ ] 灾难恢复演练
- [ ] 监控告警规则审查
- [ ] 文档更新
- [ ] 团队培训和知识分享

## 📞 应急联系方式

### 技术团队
- **系统管理员**: <EMAIL> / 138-0000-0000
- **数据库管理员**: <EMAIL> / 138-0000-0001
- **开发团队负责人**: <EMAIL> / 138-0000-0002

### 外部支持
- **云服务商支持**: <EMAIL>
- **网络服务商**: <EMAIL>
- **硬件供应商**: <EMAIL>

### 升级流程
1. **L1支持** (运维工程师) - 15分钟响应
2. **L2支持** (高级工程师) - 30分钟响应
3. **L3支持** (架构师/专家) - 1小时响应

---

*最后更新: 2024年11月22日*
