# 部署脚本密码配置说明

## 概述
为了避免在部署过程中重复输入MySQL密码，部署脚本现在支持多种密码配置方式。

## 配置方式

### 方案1：MySQL配置文件（推荐）

1. **复制配置文件模板**：
   ```bash
   cp .mysql_config.example .mysql_config
   ```

2. **编辑配置文件**：
   ```bash
   nano .mysql_config
   ```
   
   将 `your_mysql_password_here` 替换为实际的MySQL root密码：
   ```ini
   [client]
   user=root
   password=your_actual_password
   host=localhost
   ```

3. **设置安全权限**：
   ```bash
   chmod 600 .mysql_config
   ```

4. **运行部署脚本**：
   ```bash
   sudo ./scripts/deploy.sh prod traditional
   ```

### 方案2：环境变量

1. **设置环境变量**：
   ```bash
   export DB_PASSWORD="your_mysql_password"
   ```

2. **运行部署脚本**：
   ```bash
   sudo -E ./scripts/deploy.sh prod traditional
   ```
   
   注意：使用 `-E` 参数保留环境变量

### 方案3：一次性设置

1. **在命令行中设置密码并运行**：
   ```bash
   DB_PASSWORD="your_mysql_password" sudo -E ./scripts/deploy.sh prod traditional
   ```

### 方案4：交互式配置

如果没有预先配置密码，脚本会提示您选择配置方式：

```
请选择以下方式之一来配置数据库密码：
1. 创建MySQL配置文件（推荐）
2. 设置环境变量
3. 手动输入密码

请选择 (1-3):
```

## 安全注意事项

### ✅ 安全做法
- 使用MySQL配置文件（方案1）
- 设置正确的文件权限（600）
- 不要将包含密码的文件提交到版本控制系统
- 定期更换数据库密码

### ❌ 避免的做法
- 不要在命令行历史中暴露密码
- 不要在脚本中硬编码密码
- 不要使用过于简单的密码
- 不要在日志文件中记录密码

## 文件说明

### `.mysql_config`
- MySQL客户端配置文件
- 包含数据库连接信息和密码
- 权限应设置为600（仅所有者可读写）
- 已添加到.gitignore中

### `.mysql_config.example`
- 配置文件模板
- 不包含真实密码
- 可以安全地提交到版本控制系统

## 故障排除

### 问题1：权限错误
```
ERROR 1045 (28000): Access denied for user 'root'@'localhost'
```

**解决方案**：
- 检查密码是否正确
- 确认MySQL服务正在运行
- 验证用户权限

### 问题2：配置文件不生效
```
仍然提示输入密码
```

**解决方案**：
- 检查配置文件路径是否正确
- 确认文件权限设置为600
- 验证配置文件格式

### 问题3：环境变量不生效
```
环境变量未传递给sudo
```

**解决方案**：
- 使用 `sudo -E` 保留环境变量
- 或者在sudo命令中直接设置变量

## 使用示例

### 首次部署
```bash
# 1. 复制配置文件
cp .mysql_config.example .mysql_config

# 2. 编辑密码
nano .mysql_config

# 3. 设置权限
chmod 600 .mysql_config

# 4. 运行部署
sudo ./scripts/deploy.sh prod traditional
```

### 后续部署
```bash
# 直接运行，无需再次输入密码
sudo ./scripts/deploy.sh prod traditional
```

### 临时使用不同密码
```bash
# 使用环境变量覆盖配置文件
DB_PASSWORD="temp_password" sudo -E ./scripts/deploy.sh prod traditional
```

## 配置验证

部署脚本会自动检测配置方式并显示相应信息：

```
[STEP] 设置MySQL认证...
[INFO] 使用MySQL配置文件: /path/to/.mysql_config
```

或

```
[STEP] 设置MySQL认证...
[INFO] 使用环境变量中的数据库密码
```

## 相关文件

- `scripts/deploy.sh` - 主部署脚本
- `.mysql_config.example` - 配置文件模板
- `.mysql_config` - 实际配置文件（需要创建）
- `.gitignore` - 已添加MySQL配置文件排除规则

## 更新日志

- 2025-07-23: 添加MySQL密码自动化配置支持
- 支持配置文件、环境变量、交互式三种配置方式
- 自动执行等待时间范围更新脚本
