# wait_time约束问题解决指南

## 问题描述
在更新学校的wait_time字段时遇到CHECK约束违规错误：
```
Check constraint 'chk_wait_time' is violated
Error Code: 3819
```

## 🔍 快速诊断

### 步骤1：检查当前约束状态
```bash
mysql -u root -p < database/diagnose_wait_time_constraint.sql
```

### 步骤2：查看关键信息
重点检查以下输出：

1. **CHECK约束内容**：
   ```sql
   -- 应该显示：(wait_time >= 0.10 AND wait_time <= 10.00)
   -- 如果显示：(wait_time >= 0.10 AND wait_time <= 1.00) 则需要修复
   ```

2. **字段类型**：
   ```sql
   -- 应该是：DECIMAL(4,2)
   -- 如果是：DECIMAL(3,2) 则无法存储10.00
   ```

## 🛠️ 修复方案

### 方案1：自动修复（推荐）
```bash
# 执行修复脚本
mysql -u root -p < database/fix_wait_time_constraint.sql
```

### 方案2：手动修复
```sql
-- 1. 删除旧约束
ALTER TABLE schools DROP CONSTRAINT chk_wait_time;

-- 2. 修改字段类型
ALTER TABLE schools MODIFY COLUMN wait_time DECIMAL(4, 2) NOT NULL DEFAULT 0.50;

-- 3. 添加新约束
ALTER TABLE schools ADD CONSTRAINT chk_wait_time CHECK (wait_time >= 0.10 AND wait_time <= 10.00);
```

## 🧪 验证修复

### 测试约束范围
```sql
-- 这些操作应该成功
UPDATE schools SET wait_time = 0.10 WHERE id = 1;  -- 最小值
UPDATE schools SET wait_time = 5.50 WHERE id = 1;  -- 中间值
UPDATE schools SET wait_time = 10.00 WHERE id = 1; -- 最大值

-- 这些操作应该失败
UPDATE schools SET wait_time = 0.09 WHERE id = 1;  -- 小于最小值
UPDATE schools SET wait_time = 10.01 WHERE id = 1; -- 大于最大值
```

## 🚨 常见问题

### 问题1：约束删除失败
```
ERROR 1091 (42000): Can't DROP 'chk_wait_time'; check that column/key exists
```
**解决方案**：约束可能有不同的名称，查看所有约束：
```sql
SELECT CONSTRAINT_NAME, CHECK_CLAUSE FROM INFORMATION_SCHEMA.CHECK_CONSTRAINTS 
WHERE TABLE_SCHEMA = 'seat_reservation' AND TABLE_NAME = 'schools';
```

### 问题2：字段类型修改失败
```
ERROR 1264 (22003): Out of range value for column 'wait_time'
```
**解决方案**：检查现有数据是否有超出新范围的值：
```sql
SELECT id, name, wait_time FROM schools WHERE wait_time < 0.10 OR wait_time > 10.00;
```

### 问题3：约束仍然不工作
**可能原因**：
1. MySQL版本不支持CHECK约束（8.0.16之前的版本）
2. 约束被禁用
3. 缓存问题

**解决方案**：
```sql
-- 检查MySQL版本
SELECT VERSION();

-- 重启MySQL连接或刷新表缓存
FLUSH TABLES;
```

## 📋 完整检查清单

执行修复后，确认以下项目：

- [ ] CHECK约束显示正确范围：`(wait_time >= 0.10 AND wait_time <= 10.00)`
- [ ] 字段类型为：`DECIMAL(4,2)`
- [ ] 可以更新为0.10秒（最小值）
- [ ] 可以更新为10.00秒（最大值）
- [ ] 无法更新为0.09秒（应该失败）
- [ ] 无法更新为10.01秒（应该失败）
- [ ] 前端界面显示新的范围提示
- [ ] 后端验证接受新范围

## 🔄 应用程序重启

修复数据库约束后，建议重启应用程序：

```bash
# 如果使用systemd
sudo systemctl restart seatmaster

# 如果使用Docker
docker-compose restart backend

# 如果使用开发模式
# 重启Spring Boot应用
```

## 📞 紧急回滚

如果修复后出现问题，可以快速回滚到旧约束：

```sql
-- 紧急回滚到旧范围
ALTER TABLE schools DROP CONSTRAINT chk_wait_time;
ALTER TABLE schools MODIFY COLUMN wait_time DECIMAL(3, 2) NOT NULL DEFAULT 0.50;
ALTER TABLE schools ADD CONSTRAINT chk_wait_time CHECK (wait_time >= 0.10 AND wait_time <= 1.00);
```

## 📝 预防措施

为避免类似问题：

1. **测试环境验证**：所有数据库变更先在测试环境验证
2. **分步部署**：先更新数据库，再更新应用代码
3. **约束检查**：部署后立即验证约束是否正确
4. **监控日志**：关注应用启动后的数据库操作日志

## 🔗 相关文件

- 诊断脚本：`database/diagnose_wait_time_constraint.sql`
- 修复脚本：`database/fix_wait_time_constraint.sql`
- 原始更新脚本：`database/update_wait_time_range.sql`
- 修改记录：`issues/学校等待时间范围扩展记录.md`
