# SeatMaster 高精度模式快速参考

## 🚀 快速启动

### 1. 环境检查
```bash
# 检查Java版本
java -version

# 检查MySQL连接
mysql -u root -p -e "SELECT 1"

# 检查端口占用
netstat -ano | findstr :8085
```

### 2. 数据库准备
```sql
-- 添加必要字段
ALTER TABLE reservations ADD COLUMN execution_status VARCHAR(20) DEFAULT 'PENDING';

-- 创建索引
CREATE INDEX idx_status_time ON reservations(execution_status, reservation_open_time);
```

### 3. 服务启动
```bash
# 启动Worker服务器
cd worker-server-simple
mvn spring-boot:run

# 启动主服务器（可选）
cd backend
mvn spring-boot:run
```

## ⚙️ 核心配置

### 高精度调度配置
```java
// JobScheduler.java
@Scheduled(cron = "*/5 * * * * ?") // 5秒检测
public void scheduleAutoAssignTasks() {
    // 高精度任务调度逻辑
}
```

### 容错窗口配置
```java
// SimpleTaskScheduler.java
if (pastTime <= 5000) { // 5秒容错
    logger.warn("任务已过期{}ms，但在容错窗口内，立即执行", pastTime);
    return 0; // 立即执行
}
```

### 数据库连接池
```yaml
# application.yml
spring:
  datasource:
    hikari:
      maximum-pool-size: 30
      minimum-idle: 10
```

## 📊 关键指标

| 指标 | 目标值 | 监控方法 |
|------|--------|----------|
| 检测间隔 | 5秒 | 查看调度日志 |
| 容错窗口 | 5秒 | 查看执行日志 |
| 成功率 | >95% | 数据库统计 |
| 响应时间 | <10秒 | 性能监控 |

## 🔧 常用命令

### 查看系统状态
```bash
# 查看Worker进程
jps | grep worker

# 查看端口监听
netstat -tlnp | grep 8085

# 查看系统资源
top -p $(pgrep -f worker)
```

### 数据库查询
```sql
-- 查看待执行任务
SELECT * FROM reservations WHERE execution_status = 'PENDING' 
ORDER BY reservation_open_time LIMIT 10;

-- 查看执行统计
SELECT execution_status, COUNT(*) FROM reservations 
GROUP BY execution_status;

-- 查看最近执行的任务
SELECT * FROM reservations WHERE started_time IS NOT NULL 
ORDER BY started_time DESC LIMIT 10;
```

### 日志查看
```bash
# 实时查看Worker日志
tail -f worker-server-simple/logs/application.log

# 搜索特定任务
grep "Task 128" worker-server-simple/logs/application.log

# 查看错误日志
grep "ERROR" worker-server-simple/logs/application.log
```

## 🚨 故障快速诊断

### 问题1: 任务不执行
```bash
# 检查步骤
1. 确认Worker服务运行: jps | grep worker
2. 检查数据库连接: 查看连接池日志
3. 验证任务数据: SELECT * FROM reservations WHERE id = 任务ID
4. 查看调度日志: grep "定时任务启动" logs/application.log
```

### 问题2: 验证码处理失败
```bash
# 检查步骤
1. 确认OpenCV库: java -Djava.library.path=... -cp ... TestOpenCV
2. 检查图片文件: ls -la captcha/
3. 查看处理日志: grep "验证码处理" logs/application.log
```

### 问题3: 数据库错误
```sql
-- 检查表结构
DESCRIBE reservations;

-- 检查字段是否存在
SHOW COLUMNS FROM reservations LIKE 'execution_status';

-- 修复缺失字段
ALTER TABLE reservations ADD COLUMN execution_status VARCHAR(20) DEFAULT 'PENDING';
```

## 📈 性能优化

### 调整检测频率
```java
// 根据系统负载调整
@Scheduled(cron = "*/3 * * * * ?")  // 3秒 - 更高精度
@Scheduled(cron = "*/10 * * * * ?") // 10秒 - 降低负载
```

### 数据库优化
```sql
-- 清理历史数据
DELETE FROM reservations WHERE created_time < DATE_SUB(NOW(), INTERVAL 7 DAY);

-- 优化查询
EXPLAIN SELECT * FROM reservations WHERE execution_status = 'PENDING';
```

### JVM调优
```bash
# 生产环境推荐参数
-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
```

## 🔍 测试验证

### 功能测试
```bash
# 创建测试任务
INSERT INTO reservations (reservation_open_time, execution_status) 
VALUES (NOW(), 'PENDING');

# 观察执行日志
tail -f logs/application.log | grep "任务检测"
```

### 性能测试
```sql
-- 批量创建测试数据
INSERT INTO reservations (reservation_open_time, execution_status) 
SELECT DATE_ADD(NOW(), INTERVAL FLOOR(RAND() * 60) SECOND), 'PENDING' 
FROM information_schema.tables LIMIT 100;
```

## 📞 紧急联系

- **系统异常**: 立即检查Worker服务状态
- **数据库问题**: 检查连接池和Schema
- **性能问题**: 调整检测频率和连接池大小

---
**快速参考版本**: v1.0  
**适用系统**: SeatMaster 高精度高并发模式  
**更新日期**: 2025-07-07
