# SeatMaster 生产部署指南

## 📋 概述

SeatMaster 是一个智能座位预约管理系统，本指南将帮助您在生产环境中部署和配置该系统。

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Nginx (反向代理 + SSL)                    │
│                        端口: 80/443                         │
└─────────────────┬───────────────────────────────────────────┘
                  │
    ┌─────────────┼─────────────┐
    │             │             │
┌───▼────┐   ┌───▼────┐   ┌───▼────┐
│ 静态文件 │   │ API路由 │   │ WebSocket│
│ /static │   │  /api  │   │   /ws   │
└────────┘   └───┬────┘   └────────┘
                  │
         ┌────────▼────────┐
         │  Spring Boot    │
         │  (systemd管理)   │
         │   端口: 8081     │
         └────────┬────────┘
                  │
         ┌────────▼────────┐
         │     MySQL       │
         │   端口: 3306     │
         └─────────────────┘
```

## 🔧 系统要求

### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 50GB SSD
- **操作系统**: Ubuntu 20.04+ / CentOS 8+

### 推荐配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 100GB SSD
- **操作系统**: Ubuntu 22.04 LTS

### 软件依赖
- Java 8+
- Node.js 16+
- MySQL 8.0+
- Nginx 1.18+
- Redis 6.0+ (可选)

## 🚀 快速部署

### 1. 一键部署脚本

```bash
# 克隆项目
git clone https://github.com/yourusername/seatmaster.git
cd seatmaster

# 执行一键部署
sudo ./scripts/deploy.sh prod traditional

# 或使用Docker部署
sudo ./scripts/deploy.sh prod docker
```

### 2. 手动部署步骤

#### 步骤1: 环境准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础依赖
sudo apt install -y curl wget git unzip

# 安装Java
sudo apt install -y openjdk-8-jdk

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt install -y nodejs

# 安装MySQL
sudo apt install -y mysql-server

# 安装Nginx
sudo apt install -y nginx
```

#### 步骤2: 数据库配置
```bash
# 配置MySQL
sudo mysql_secure_installation

# 创建数据库
mysql -u root -p < database/seat_reservation.sql

# 应用生产环境优化
mysql -u root -p < database/production-setup.sql
```

#### 步骤3: 应用构建
```bash
# 构建后端
cd backend
mvn clean package -DskipTests -Pprod

# 构建前端
cd ../frontend
npm ci
npm run build
```

#### 步骤4: 部署配置
```bash
# 安装系统服务
sudo ./scripts/install-services.sh

# 配置SSL证书
sudo ./scripts/setup-ssl.sh yourdomain.com <EMAIL> letsencrypt

# 安全加固
sudo ./scripts/security-hardening.sh

# 性能优化
sudo ./scripts/performance-tuning.sh all
```

## 📁 目录结构

```
/opt/seatmaster/                 # 应用目录
├── seat-reservation-backend-1.0.0.jar
├── .env.production             # 环境配置
├── logs/                       # 应用日志
├── uploads/                    # 上传文件
└── temp/                       # 临时文件

/var/www/seatmaster/            # 前端文件
├── index.html
├── static/
└── ...

/etc/nginx/                     # Nginx配置
├── nginx.conf
├── conf.d/
│   ├── seatmaster.conf
│   └── ssl-params.conf
└── ssl/                        # SSL证书

/var/log/seatmaster/            # 日志目录
├── seatmaster-backend.log
├── access.log
├── error.log
└── backup.log

/var/backups/seatmaster/        # 备份目录
└── YYYYMMDD_HHMMSS/
```

## ⚙️ 配置文件

### 环境变量配置 (.env.production)
```bash
# 应用配置
SPRING_PROFILES_ACTIVE=prod
SERVER_PORT=8081

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=seat_reservation
DB_USERNAME=root
DB_PASSWORD=your_secure_password

# 安全配置
JWT_SECRET=your_jwt_secret_key
CORS_ALLOWED_ORIGINS=https://yourdomain.com

# 性能配置
JVM_OPTS="-Xms1g -Xmx2g -XX:+UseG1GC"
```

### Nginx配置示例
```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /etc/nginx/ssl/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/privkey.pem;
    
    location /api/ {
        proxy_pass http://localhost:8081/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location / {
        root /var/www/seatmaster;
        try_files $uri $uri/ /index.html;
    }
}
```

## 🔐 安全配置

### SSL/TLS配置
```bash
# 自动配置Let's Encrypt证书
sudo ./scripts/setup-ssl.sh yourdomain.com <EMAIL> letsencrypt

# 或生成自签名证书（测试用）
sudo ./scripts/setup-ssl.<NAME_EMAIL> selfsigned
```

### 防火墙配置
```bash
# 配置UFW防火墙
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable
```

### 系统安全加固
```bash
# 执行安全加固脚本
sudo ./scripts/security-hardening.sh

# 包含以下安全措施:
# - SSH安全配置
# - Fail2Ban入侵防护
# - 系统内核加固
# - 审计日志配置
# - 安全工具安装
```

## 📊 监控和日志

### 健康检查
```bash
# 系统健康检查
sudo /usr/local/bin/seatmaster-performance.sh

# 应用健康检查
curl http://localhost:8081/actuator/health

# 服务状态检查
sudo systemctl status seatmaster-backend
sudo systemctl status seatmaster-frontend
sudo systemctl status nginx
```

### 日志管理
```bash
# 查看应用日志
sudo ./scripts/log-manager.sh tail backend

# 分析日志
sudo ./scripts/log-manager.sh analyze backend

# 日志轮转
sudo ./scripts/log-manager.sh rotate
```

### 性能监控
```bash
# JVM监控
sudo /usr/local/bin/jvm-monitor.sh

# 数据库监控
sudo /usr/local/bin/mysql-monitor.sh

# 系统监控
sudo /usr/local/bin/seatmaster-performance.sh
```

## 💾 备份和恢复

### 自动备份配置
```bash
# 配置每日自动备份
sudo ./scripts/setup-backup.sh --schedule daily --time 02:00 --retention 30

# 配置邮件通知
sudo ./scripts/setup-backup.sh --email <EMAIL>
```

### 手动备份
```bash
# 完整备份
sudo ./scripts/backup.sh full

# 仅备份数据库
sudo ./scripts/backup.sh database

# 仅备份应用文件
sudo ./scripts/backup.sh files
```

### 数据恢复
```bash
# 完整恢复
sudo ./scripts/restore.sh /var/backups/seatmaster/20241122_143000 full

# 仅恢复数据库
sudo ./scripts/restore.sh /var/backups/seatmaster/20241122_143000 database
```

## 🔄 服务管理

### 服务控制命令
```bash
# 启动所有服务
sudo seatmaster start

# 停止所有服务
sudo seatmaster stop

# 重启所有服务
sudo seatmaster restart

# 查看服务状态
sudo seatmaster status

# 查看服务日志
sudo seatmaster logs backend
sudo seatmaster logs frontend
sudo seatmaster logs nginx
```

### 单独服务管理
```bash
# 后端服务
sudo systemctl start seatmaster-backend
sudo systemctl stop seatmaster-backend
sudo systemctl restart seatmaster-backend
sudo systemctl status seatmaster-backend

# 前端服务
sudo systemctl start seatmaster-frontend
sudo systemctl stop seatmaster-frontend

# Nginx服务
sudo systemctl start nginx
sudo systemctl reload nginx
```

## 🔧 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查日志
sudo journalctl -u seatmaster-backend -f

# 检查端口占用
sudo netstat -tlnp | grep :8081

# 检查Java进程
sudo ps aux | grep java
```

#### 2. 数据库连接失败
```bash
# 检查MySQL状态
sudo systemctl status mysql

# 测试数据库连接
mysql -h localhost -u root -p -e "SELECT 1"

# 检查数据库配置
sudo cat /etc/mysql/conf.d/performance.cnf
```

#### 3. Nginx配置错误
```bash
# 测试Nginx配置
sudo nginx -t

# 重新加载配置
sudo nginx -s reload

# 检查错误日志
sudo tail -f /var/log/nginx/error.log
```

#### 4. SSL证书问题
```bash
# 检查证书有效期
sudo openssl x509 -in /etc/nginx/ssl/fullchain.pem -text -noout

# 续期Let's Encrypt证书
sudo certbot renew

# 测试HTTPS连接
curl -I https://yourdomain.com
```

### 性能问题诊断
```bash
# 系统资源使用
top
htop
iotop

# 内存使用分析
free -h
sudo cat /proc/meminfo

# 磁盘使用分析
df -h
sudo du -sh /opt/seatmaster/*

# 网络连接分析
sudo netstat -tuln
sudo ss -tuln
```

## 📈 性能优化

### JVM调优
```bash
# 应用JVM优化配置
source /opt/seatmaster/jvm.conf
export JAVA_OPTS

# 监控GC性能
sudo /usr/local/bin/jvm-monitor.sh
```

### 数据库优化
```bash
# 应用数据库性能配置
sudo cp config/database-performance.cnf /etc/mysql/conf.d/
sudo systemctl restart mysql

# 监控数据库性能
sudo /usr/local/bin/mysql-monitor.sh
```

### 系统级优化
```bash
# 应用系统性能优化
sudo ./scripts/performance-tuning.sh system

# 包含以下优化:
# - 内核参数调优
# - 文件描述符限制
# - 网络参数优化
# - 磁盘调度器优化
```

## 🔄 更新和维护

### 应用更新
```bash
# 滚动更新
sudo ./scripts/update.sh rolling

# 蓝绿部署
sudo ./scripts/update.sh blue-green

# 立即更新
sudo ./scripts/update.sh immediate
```

### 系统维护
```bash
# 系统更新
sudo apt update && sudo apt upgrade -y

# 清理日志
sudo ./scripts/log-manager.sh clean --days 30

# 清理备份
find /var/backups/seatmaster -type d -mtime +30 -exec rm -rf {} \;

# 数据库维护
mysql -u root -p -e "OPTIMIZE TABLE seat_reservation.*"
```

## 📞 技术支持

### 联系方式
- **技术支持**: <EMAIL>
- **文档**: https://docs.seatmaster.com
- **GitHub**: https://github.com/yourusername/seatmaster

### 日志收集
```bash
# 收集诊断信息
sudo ./scripts/log-manager.sh collect

# 生成系统报告
sudo /usr/local/bin/seatmaster-performance.sh > system-report.txt
```

---

## 📝 附录

### A. 端口列表
- **80**: HTTP (重定向到HTTPS)
- **443**: HTTPS
- **3000**: 前端开发服务器
- **8081**: 后端API服务
- **3306**: MySQL数据库

### B. 重要文件路径
- 应用配置: `/opt/seatmaster/.env.production`
- Nginx配置: `/etc/nginx/conf.d/seatmaster.conf`
- 系统服务: `/etc/systemd/system/seatmaster-*.service`
- 日志文件: `/var/log/seatmaster/`
- 备份目录: `/var/backups/seatmaster/`

### C. 默认账户
- **管理员**: admin / admin123 (首次登录后请修改)
- **数据库**: root / root5869087

---

*最后更新: 2024年11月22日*
