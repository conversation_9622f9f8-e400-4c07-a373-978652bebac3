# 生产环境数据库安全部署指南

## 概述
本指南提供了安全的生产环境数据库配置方法，避免了原始脚本中的潜在风险。

## 🚨 原始脚本的风险分析

### 高风险部分（已移除）

#### 1. 自动数据清理存储过程
```sql
CREATE PROCEDURE CleanupOldLogs(IN days_to_keep INT)
-- 风险：自动删除数据，可能导致数据丢失
```

#### 2. 定时任务
```sql
CREATE EVENT daily_cleanup ON SCHEDULE EVERY 1 DAY
-- 风险：每天自动删除90天前的数据
```

#### 3. 全局设置更改
```sql
SET GLOBAL event_scheduler = ON;
-- 风险：影响整个MySQL实例
```

## ✅ 安全部署步骤

### 步骤1：备份现有数据
```bash
# 创建完整备份
mysqldump -u root -p seat_reservation > backup_$(date +%Y%m%d_%H%M%S).sql

# 验证备份文件
ls -la backup_*.sql
```

### 步骤2：检查当前数据库状态
```sql
-- 连接到数据库
mysql -u root -p

-- 检查表结构
USE seat_reservation;
SHOW TABLES;
DESCRIBE users;
DESCRIBE reservations;
DESCRIBE rooms;
DESCRIBE schools;
```

### 步骤3：执行安全配置脚本
```bash
# 使用安全版本的脚本
mysql -u root -p < database/production-setup-safe.sql
```

### 步骤4：验证配置结果
```sql
-- 检查索引是否创建成功
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME
FROM information_schema.statistics 
WHERE TABLE_SCHEMA = 'seat_reservation'
AND INDEX_NAME != 'PRIMARY'
ORDER BY TABLE_NAME, INDEX_NAME;

-- 检查表大小和行数
SELECT 
    table_name,
    table_rows,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
FROM information_schema.tables 
WHERE table_schema = 'seat_reservation';
```

## 📋 安全脚本包含的内容

### ✅ 包含（安全）
1. **索引优化**
   - 用户表：username, created_time, role, remaining_days
   - 预约表：user_id, room_id, start_time, end_time, worker_id
   - 房间表：school_id, name, roomNum, created_time
   - 学校表：name, created_time, wait_time
   - Worker服务器表：status, last_heartbeat, enabled

2. **数据库配置**
   - 字符集设置为 utf8mb4
   - 可选的用户创建（注释状态）

3. **安全检查**
   - 使用 `IF NOT EXISTS` 避免重复创建
   - 检查表存在性再创建索引
   - 立即清理临时存储过程

### ❌ 不包含（风险项）
1. **自动数据清理**
2. **定时任务**
3. **全局MySQL设置更改**
4. **数据删除操作**

## 🔧 可选配置

### 创建专用数据库用户（推荐）
如果您想创建专用的数据库用户而不是使用root：

```sql
-- 1. 创建应用用户
CREATE USER 'seatmaster_app'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON seat_reservation.* TO 'seatmaster_app'@'localhost';

-- 2. 创建只读用户（用于监控）
CREATE USER 'seatmaster_readonly'@'localhost' IDENTIFIED BY 'readonly_password';
GRANT SELECT ON seat_reservation.* TO 'seatmaster_readonly'@'localhost';

-- 3. 刷新权限
FLUSH PRIVILEGES;
```

### 手动性能优化（可选）
```sql
-- 更新表统计信息（手动执行）
ANALYZE TABLE users, reservations, rooms, schools, worker_servers;

-- 优化表（在低峰期执行）
OPTIMIZE TABLE users, reservations, rooms, schools, worker_servers;
```

## 📊 性能监控

### 查询性能检查
```sql
-- 检查慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- 查看索引使用情况
SHOW INDEX FROM reservations;
EXPLAIN SELECT * FROM reservations WHERE user_id = 1;
```

### 表大小监控
```sql
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'seat_reservation'
ORDER BY (data_length + index_length) DESC;
```

## 🚨 故障排除

### 常见问题

#### 1. 索引创建失败
```
ERROR 1061 (42000): Duplicate key name 'idx_username'
```
**解决方案**：索引已存在，这是正常的。脚本使用了 `IF NOT EXISTS`。

#### 2. 权限不足
```
ERROR 1142 (42000): INDEX command denied
```
**解决方案**：确保使用具有足够权限的用户（如root）。

#### 3. 表不存在
```
ERROR 1146 (42S02): Table 'seat_reservation.xxx' doesn't exist
```
**解决方案**：某些可选表不存在是正常的，脚本会跳过。

## 📝 维护建议

### 定期维护（手动执行）
```sql
-- 每月执行一次（在低峰期）
ANALYZE TABLE users, reservations, rooms, schools, worker_servers;

-- 每季度执行一次（在维护窗口）
OPTIMIZE TABLE users, reservations, rooms, schools, worker_servers;
```

### 数据清理（手动控制）
如果需要清理旧数据，建议手动执行并仔细检查：

```sql
-- 示例：清理90天前的测试数据（请根据实际需求调整）
-- 注意：执行前请备份！
SELECT COUNT(*) FROM reservations WHERE created_time < DATE_SUB(NOW(), INTERVAL 90 DAY);
-- 确认数量后再执行删除
-- DELETE FROM reservations WHERE created_time < DATE_SUB(NOW(), INTERVAL 90 DAY) LIMIT 1000;
```

## 🔒 安全最佳实践

1. **定期备份**：每日自动备份数据库
2. **权限最小化**：应用使用专用用户，不使用root
3. **监控日志**：启用慢查询日志监控性能
4. **手动维护**：避免自动化数据删除，手动控制维护操作
5. **测试环境**：所有数据库变更先在测试环境验证

## 总结

使用 `database/production-setup-safe.sql` 脚本可以安全地优化您的生产数据库，而不会引入自动化数据删除或定时任务的风险。所有优化都是只读的或者是安全的索引创建操作。
