# MySQL 兼容性指南

## 概述

本文档说明 seatMaster 项目在不同 MySQL 版本下的兼容性要求和已知问题的解决方案。

## 支持的 MySQL 版本

- **推荐版本**: MySQL 8.0.x
- **最低版本**: MySQL 5.7.x
- **测试版本**: MySQL 8.0.42

## 已修复的兼容性问题

### 1. 索引创建语法错误 (已修复)

**问题描述**:
在 MySQL 8.0 中，`ALTER TABLE ... ADD INDEX IF NOT EXISTS` 和 `CREATE INDEX IF NOT EXISTS` 语法不被支持，导致部署时出现语法错误：

```
ERROR 1064 (42000): You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'IF NOT EXISTS idx_username (username)'
```

**解决方案**:
使用存储过程 `SafeCreateIndex` 来安全地创建索引，避免重复创建错误：

```sql
DELIMITER $$
CREATE PROCEDURE SafeCreateIndex(
    IN table_name VARCHAR(64),
    IN index_name VARCHAR(64), 
    IN column_list VARCHAR(255)
)
BEGIN
    DECLARE index_exists INT DEFAULT 0;
    
    -- 检查索引是否已存在
    SELECT COUNT(*) INTO index_exists
    FROM information_schema.statistics 
    WHERE table_schema = DATABASE() 
    AND table_name = table_name 
    AND index_name = index_name;
    
    -- 如果索引不存在则创建
    IF index_exists = 0 THEN
        SET @sql = CONCAT('CREATE INDEX ', index_name, ' ON ', table_name, ' (', column_list, ')');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END$$
DELIMITER ;
```

**修复的文件**:
- `database/production-setup.sql`
- `database/production-setup-safe.sql`
- `database/add_distributed_fields.sql` (2025-07-25 修复)
- `database/add_reservation_time_fields.sql` (2025-07-25 修复)
- `database/update_schema_simple.sql` (2025-07-25 修复)

### 2. 最新修复 (2025-07-25)

**问题**: 多个SQL文件中使用了不兼容的 `CREATE INDEX IF NOT EXISTS` 语法

**影响文件**:
- `database/add_distributed_fields.sql`
- `database/add_reservation_time_fields.sql`
- `database/update_schema_simple.sql`

**修复方法**: 统一使用 `SafeCreateIndex` 存储过程方式创建索引

## 部署注意事项

### 1. 生产环境部署

使用以下命令进行生产环境部署：

```bash
sudo ./scripts/deploy.sh prod traditional
```

### 2. MySQL 配置检查

部署前请确认 MySQL 配置：

```sql
-- 检查 MySQL 版本
SELECT VERSION();

-- 检查字符集支持
SHOW VARIABLES LIKE 'character_set%';

-- 检查时区设置
SELECT @@time_zone;
```

### 3. 权限要求

确保 MySQL 用户具有以下权限：

```sql
GRANT CREATE, ALTER, INDEX, SELECT, INSERT, UPDATE, DELETE ON seat_reservation.* TO 'your_user'@'localhost';
GRANT CREATE ROUTINE, ALTER ROUTINE, EXECUTE ON seat_reservation.* TO 'your_user'@'localhost';
```

## 故障排除

### 1. 索引创建失败

**症状**: 部署时出现索引相关的 SQL 语法错误

**解决方法**:
1. 确认使用的是修复后的 SQL 脚本
2. 检查 MySQL 版本是否支持
3. 手动执行存储过程测试

### 2. 存储过程权限错误

**症状**: `ERROR 1419 (HY000): You do not have the SUPER privilege`

**解决方法**:
```sql
-- 临时设置（仅当前会话）
SET GLOBAL log_bin_trust_function_creators = 1;

-- 或在 my.cnf 中永久设置
[mysqld]
log_bin_trust_function_creators = 1
```

### 3. 字符集问题

**症状**: 中文字符显示异常

**解决方法**:
```sql
-- 检查数据库字符集
SHOW CREATE DATABASE seat_reservation;

-- 如需修改
ALTER DATABASE seat_reservation CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

## 性能优化建议

### 1. 索引策略

修复后的索引创建策略包括：

- **用户表**: username, created_time, role, remaining_days
- **预约表**: user_id, room_id, start_time, end_time, 复合索引
- **房间表**: school_id, name, roomNum
- **学校表**: name, wait_time
- **Worker服务器表**: status, last_heartbeat, enabled

### 2. 查询优化

```sql
-- 使用 EXPLAIN 分析查询性能
EXPLAIN SELECT * FROM reservations WHERE user_id = 1 AND start_time > NOW();

-- 检查索引使用情况
SHOW INDEX FROM reservations;
```

## 版本升级指南

### 从 MySQL 5.7 升级到 8.0

1. **备份数据**:
```bash
mysqldump -u root -p seat_reservation > backup_before_upgrade.sql
```

2. **升级 MySQL**:
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade mysql-server

# CentOS/RHEL
sudo yum update mysql-server
```

3. **运行升级脚本**:
```bash
mysql_upgrade -u root -p
```

4. **验证兼容性**:
```bash
# 重新部署以验证所有功能正常
sudo ./scripts/deploy.sh prod traditional
```

## 联系支持

如果遇到其他 MySQL 兼容性问题，请：

1. 收集错误日志和 MySQL 版本信息
2. 检查本文档的故障排除部分
3. 联系技术支持团队

## 更新历史

- **2025-07-25**: 修复 MySQL 8.0 索引创建语法兼容性问题
- **2025-07-25**: 添加存储过程安全索引创建方案
- **2025-07-25**: 更新部署脚本和文档
