# SeatMaster 快速参考指南

## 🚀 快速命令

### 服务管理
```bash
# 一键服务控制
sudo seatmaster start|stop|restart|status

# 单独服务控制
sudo systemctl start|stop|restart seatmaster-backend
sudo systemctl start|stop|restart seatmaster-frontend
sudo systemctl start|stop|restart nginx
sudo systemctl start|stop|restart mysql

# 查看服务日志
sudo seatmaster logs backend|frontend|nginx
sudo journalctl -u seatmaster-backend -f
```

### 健康检查
```bash
# 系统健康检查
sudo /usr/local/bin/seatmaster-performance.sh

# 应用健康检查
curl http://localhost:8081/actuator/health
curl http://localhost:3000

# 数据库健康检查
mysql -u root -p -e "SELECT 1"
sudo /usr/local/bin/mysql-monitor.sh
```

### 备份和恢复
```bash
# 手动备份
sudo ./scripts/backup.sh full|database|files|config

# 恢复数据
sudo ./scripts/restore.sh /backup/path full|database|files|config

# 查看备份状态
sudo /usr/local/bin/seatmaster-backup-monitor.sh
```

### 日志管理
```bash
# 查看实时日志
sudo ./scripts/log-manager.sh tail backend|frontend|nginx

# 分析日志
sudo ./scripts/log-manager.sh analyze backend

# 搜索日志
sudo ./scripts/log-manager.sh search --pattern "ERROR"

# 清理日志
sudo ./scripts/log-manager.sh clean --days 30
```

## 🔧 配置文件位置

### 应用配置
```bash
/opt/seatmaster/.env.production          # 环境变量
/opt/seatmaster/jvm.conf                 # JVM配置
/etc/seatmaster/backup.conf              # 备份配置
```

### 系统配置
```bash
/etc/nginx/conf.d/seatmaster.conf        # Nginx主配置
/etc/nginx/ssl/                          # SSL证书
/etc/mysql/conf.d/performance.cnf        # MySQL性能配置
/etc/systemd/system/seatmaster-*.service # 系统服务
```

### 日志文件
```bash
/var/log/seatmaster/                     # 应用日志目录
/var/log/nginx/                          # Nginx日志
/var/log/mysql/                          # MySQL日志
```

## 📊 监控端点

### 应用监控
```bash
# 健康检查
GET http://localhost:8081/actuator/health

# 应用信息
GET http://localhost:8081/actuator/info

# 性能指标
GET http://localhost:8081/actuator/metrics

# JVM信息
GET http://localhost:8081/actuator/metrics/jvm.memory.used
```

### 系统监控
```bash
# CPU使用率
top -bn1 | grep "Cpu(s)"

# 内存使用
free -h

# 磁盘使用
df -h

# 网络连接
ss -tuln | grep -E "(8081|3000|3306)"
```

## 🚨 故障排除

### 常见问题快速解决

#### 服务启动失败
```bash
# 检查端口占用
sudo netstat -tlnp | grep :8081

# 检查Java进程
sudo ps aux | grep java

# 查看启动日志
sudo journalctl -u seatmaster-backend --since "5 minutes ago"

# 重启服务
sudo systemctl restart seatmaster-backend
```

#### 数据库连接失败
```bash
# 检查MySQL状态
sudo systemctl status mysql

# 测试连接
mysql -h localhost -u root -p -e "SELECT 1"

# 检查连接数
mysql -e "SHOW STATUS LIKE 'Threads_connected'"

# 重启MySQL
sudo systemctl restart mysql
```

#### 网站无法访问
```bash
# 检查Nginx状态
sudo systemctl status nginx

# 测试Nginx配置
sudo nginx -t

# 检查端口监听
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :443

# 重新加载Nginx
sudo nginx -s reload
```

#### 性能问题
```bash
# 检查系统负载
uptime

# 检查内存使用
free -h

# 检查磁盘I/O
iostat -x 1 3

# 检查JVM状态
sudo /usr/local/bin/jvm-monitor.sh
```

## 🔐 安全检查

### 快速安全检查
```bash
# 检查防火墙状态
sudo ufw status

# 检查SSH配置
sudo sshd -T | grep -E "(PermitRootLogin|PasswordAuthentication)"

# 检查Fail2Ban状态
sudo fail2ban-client status

# 检查SSL证书
sudo openssl x509 -in /etc/nginx/ssl/fullchain.pem -noout -dates

# 运行安全扫描
sudo /usr/local/bin/security-check.sh
```

### 安全事件响应
```bash
# 查看登录失败记录
sudo grep "Failed password" /var/log/auth.log | tail -10

# 查看Fail2Ban日志
sudo tail -f /var/log/fail2ban.log

# 检查异常网络连接
sudo netstat -tuln | grep ESTABLISHED

# 查看系统用户
cat /etc/passwd | grep -v nologin
```

## 📈 性能调优

### 快速性能优化
```bash
# 应用JVM优化
source /opt/seatmaster/jvm.conf
sudo systemctl restart seatmaster-backend

# 应用数据库优化
sudo systemctl restart mysql

# 应用系统优化
sudo sysctl -p /etc/sysctl.d/99-seatmaster-performance.conf

# 清理系统缓存
sudo echo 3 > /proc/sys/vm/drop_caches
```

### 性能监控
```bash
# JVM性能
sudo /usr/local/bin/jvm-monitor.sh

# 数据库性能
sudo /usr/local/bin/mysql-monitor.sh

# 系统性能
sudo /usr/local/bin/seatmaster-performance.sh

# 实时监控
watch -n 5 'curl -w "%{time_total}" -o /dev/null -s http://localhost:8081/api/health'
```

## 🔄 部署和更新

### 快速部署
```bash
# 一键部署
sudo ./scripts/deploy.sh prod traditional

# Docker部署
sudo ./scripts/deploy.sh prod docker

# 构建应用
sudo ./scripts/build.sh prod
```

### 应用更新
```bash
# 滚动更新
sudo ./scripts/update.sh rolling

# 蓝绿部署
sudo ./scripts/update.sh blue-green

# 立即更新
sudo ./scripts/update.sh immediate
```

## 📋 维护任务

### 日常维护
```bash
# 系统更新检查
sudo apt list --upgradable

# 清理临时文件
sudo find /tmp -name "seatmaster*" -mtime +1 -delete

# 数据库优化
mysql -e "OPTIMIZE TABLE seat_reservation.reservations"

# 日志轮转
sudo ./scripts/log-manager.sh rotate
```

### 定期维护
```bash
# 备份验证
sudo ./scripts/backup.sh database
sudo ./scripts/restore.sh /var/backups/seatmaster/latest database

# 性能基准测试
ab -n 1000 -c 10 http://localhost:8081/api/health

# 安全扫描
sudo rkhunter --check
sudo lynis audit system
```

## 📞 紧急联系

### 故障等级
- **P0 (紧急)**: 系统完全不可用 - 15分钟响应
- **P1 (高)**: 部分功能不可用 - 30分钟响应  
- **P2 (中)**: 性能问题 - 2小时响应
- **P3 (低)**: 一般问题 - 24小时响应

### 联系方式
```bash
# 技术支持
echo "技术支持: <EMAIL>"
echo "紧急热线: 400-xxx-xxxx"

# 发送故障报告
mail -s "SeatMaster故障报告" <EMAIL> < /var/log/seatmaster/error.log
```

## 🔍 常用查询

### 系统信息查询
```bash
# 系统版本
cat /etc/os-release

# 硬件信息
lscpu
free -h
df -h

# 网络信息
ip addr show
ss -tuln
```

### 应用信息查询
```bash
# Java版本
java -version

# 应用版本
cat /opt/seatmaster/version.txt

# 进程信息
ps aux | grep -E "(java|nginx|mysql)"

# 端口使用
sudo netstat -tlnp | grep -E "(8081|3000|3306|80|443)"
```

### 数据库查询
```bash
# 数据库状态
mysql -e "SHOW STATUS"

# 表信息
mysql -e "USE seat_reservation; SHOW TABLES;"

# 用户统计
mysql -e "USE seat_reservation; SELECT COUNT(*) FROM users;"

# 预约统计
mysql -e "USE seat_reservation; SELECT status, COUNT(*) FROM reservations GROUP BY status;"
```

## 📝 快速备忘

### 重要端口
- **80**: HTTP
- **443**: HTTPS  
- **3000**: 前端开发服务器
- **8081**: 后端API
- **3306**: MySQL

### 重要路径
- **应用**: `/opt/seatmaster/`
- **前端**: `/var/www/seatmaster/`
- **日志**: `/var/log/seatmaster/`
- **备份**: `/var/backups/seatmaster/`
- **配置**: `/etc/nginx/conf.d/`

### 默认账户
- **系统用户**: `seatmaster`
- **数据库**: `root / root5869087`
- **管理员**: `admin / admin123`

---

*快速参考 - 最后更新: 2024年11月22日*
