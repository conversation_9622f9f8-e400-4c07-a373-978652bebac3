# 学校视图功能测试计划

## 测试概述

本文档描述了 seatMaster 项目中新增的学校视图功能的完整测试计划，包括功能测试、性能测试和用户体验测试。

## 测试环境

- **前端**: Vue 3 + Element Plus
- **后端**: Spring Boot + MySQL
- **测试浏览器**: Chrome, Firefox, Safari, Edge
- **测试设备**: 桌面端、平板、手机

## 1. 后端API测试

### 1.1 学校概览API测试
**接口**: `GET /api/admin/execution-logs/schools-overview`

**测试用例**:
- [ ] 基本查询功能
  - 验证返回数据格式正确
  - 验证分页功能正常
  - 验证排序功能正常
- [ ] 筛选功能测试
  - 学校名称搜索
  - 日期范围筛选
  - 排序字段和方向
- [ ] 边界条件测试
  - 空数据情况
  - 大数据量情况
  - 无效参数处理
- [ ] 性能测试
  - 响应时间 < 500ms
  - 并发请求处理
  - 数据库查询优化验证

### 1.2 学校日期统计API测试
**接口**: `GET /api/admin/execution-logs/school/{schoolId}/daily-stats`

**测试用例**:
- [ ] 基本查询功能
  - 验证特定学校的日期统计
  - 验证默认日期范围（30天）
  - 验证自定义日期范围
- [ ] 数据准确性验证
  - 每日执行次数统计
  - 每日成功率计算
  - 每日平均耗时计算
- [ ] 错误处理测试
  - 无效学校ID
  - 无效日期范围
  - 学校无数据情况

## 2. 前端功能测试

### 2.1 视图切换测试
- [ ] 任务视图 ↔ 学校视图切换
- [ ] 用户视图 ↔ 学校视图切换
- [ ] URL参数正确更新
- [ ] 视图状态保持

### 2.2 学校列表显示测试
- [ ] 学校数据正确显示
  - 学校名称
  - 总执行次数
  - 总成功率
  - 平均耗时
  - 今日执行次数
  - 今日成功率
- [ ] 数据格式化正确
  - 数字千分位分隔符
  - 百分比显示
  - 时间格式化
- [ ] 成功率颜色编码
  - 高成功率（≥90%）显示绿色
  - 中等成功率（70-89%）显示橙色
  - 低成功率（<70%）显示红色

### 2.3 筛选和排序测试
- [ ] 学校名称搜索功能
- [ ] 排序功能测试
  - 总执行次数排序
  - 总成功率排序
  - 平均耗时排序
  - 今日执行次数排序
  - 今日成功率排序
- [ ] 升序/降序切换
- [ ] 日期范围筛选

### 2.4 分页功能测试
- [ ] 页面导航正常
- [ ] 页面大小调整
- [ ] 总数显示正确
- [ ] 分页状态保持

### 2.5 展开详情测试
- [ ] 学校行点击展开/折叠
- [ ] 日期统计数据加载
- [ ] 统计表格显示正确
- [ ] 刷新统计功能
- [ ] 加载状态显示

## 3. 用户体验测试

### 3.1 响应式设计测试
- [ ] 桌面端显示（>1200px）
- [ ] 平板端显示（768px-1200px）
- [ ] 手机端显示（<768px）
- [ ] 统计表格移动端适配

### 3.2 交互体验测试
- [ ] 点击反馈及时
- [ ] 加载状态清晰
- [ ] 错误提示友好
- [ ] 空状态处理合理
- [ ] Tooltip提示有用

### 3.3 性能体验测试
- [ ] 页面加载速度
- [ ] 数据切换流畅
- [ ] 滚动性能良好
- [ ] 内存使用合理

## 4. 数据准确性验证

### 4.1 统计数据验证
- [ ] 总执行次数与数据库一致
- [ ] 成功率计算正确
- [ ] 平均耗时计算准确
- [ ] 今日统计实时更新

### 4.2 日期统计验证
- [ ] 按日期聚合正确
- [ ] 日期范围筛选准确
- [ ] 跨月份统计正确
- [ ] 时区处理正确

## 5. 兼容性测试

### 5.1 浏览器兼容性
- [ ] Chrome (最新版本)
- [ ] Firefox (最新版本)
- [ ] Safari (最新版本)
- [ ] Edge (最新版本)

### 5.2 设备兼容性
- [ ] Windows PC
- [ ] Mac
- [ ] iPad
- [ ] iPhone
- [ ] Android 手机

## 6. 安全性测试

### 6.1 权限验证
- [ ] 管理员权限检查
- [ ] 非管理员访问拒绝
- [ ] 登录状态验证

### 6.2 数据安全
- [ ] SQL注入防护
- [ ] XSS攻击防护
- [ ] 参数验证完整

## 7. 性能基准测试

### 7.1 响应时间要求
- [ ] 学校概览查询 < 500ms
- [ ] 学校日期统计 < 300ms
- [ ] 页面切换 < 200ms
- [ ] 数据筛选 < 300ms

### 7.2 并发性能
- [ ] 10个并发用户正常
- [ ] 50个并发用户可接受
- [ ] 100个并发用户不崩溃

## 8. 回归测试

### 8.1 现有功能验证
- [ ] 任务视图功能正常
- [ ] 用户视图功能正常
- [ ] 导出功能正常
- [ ] 筛选功能正常

### 8.2 集成测试
- [ ] 与现有系统集成正常
- [ ] 数据库连接稳定
- [ ] 缓存机制正常

## 测试执行记录

| 测试项目 | 执行日期 | 测试结果 | 问题描述 | 修复状态 |
|---------|---------|---------|---------|---------|
| 后端API基本功能 | | | | |
| 前端视图切换 | | | | |
| 数据准确性 | | | | |
| 响应式设计 | | | | |
| 性能测试 | | | | |

## 测试结论

待测试完成后填写：
- 功能完整性评估
- 性能表现评估
- 用户体验评估
- 发现问题总结
- 改进建议
