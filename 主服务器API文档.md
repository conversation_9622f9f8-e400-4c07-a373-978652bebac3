# SeatMaster 主服务器 API 文档

## 概述

SeatMaster 主服务器是基于 Spring Boot 的核心服务器，负责管理副服务器、分布式任务调度、用户管理等功能。服务器提供完整的 RESTful API 接口。

**服务器信息：**
- 默认端口：`8081`
- 基础URL：`http://localhost:8081`
- API 基础路径：`/api`
- 服务名称：`SeatMaster Backend Server`

---

## 认证说明

**当前状态：** 认证临时禁用，所有 API 可直接访问
**生产环境：** 需要 Bearer Token 认证

```http
Authorization: Bearer <token>
```

---

## 副服务器管理 API

### 1. 服务器列表管理

#### 1.1 获取所有副服务器列表
```http
GET /api/admin/worker-management/servers
```

**描述：** 获取系统中所有副服务器的详细信息，包括状态、负载等

**响应示例：**
```json
{
  "code": 200,
  "message": "获取副服务器列表成功",
  "data": [
    {
      "id": "worker-001",
      "workerId": "worker-001",
      "name": "主要副服务器",
      "host": "localhost",
      "port": 8082,
      "maxConcurrentTasks": 10,
      "status": "ONLINE",
      "currentLoad": 3,
      "totalTasksCompleted": 150,
      "totalTasksFailed": 5,
      "lastHeartbeat": "2025-06-28T09:30:00",
      "createdTime": "2025-06-28T08:00:00",
      "updatedTime": "2025-06-28T09:30:00"
    },
    {
      "id": "worker-002",
      "workerId": "worker-002",
      "name": "备用副服务器",
      "host": "localhost",
      "port": 8083,
      "maxConcurrentTasks": 8,
      "status": "OFFLINE",
      "currentLoad": 0,
      "totalTasksCompleted": 80,
      "totalTasksFailed": 2,
      "lastHeartbeat": "2025-06-28T09:25:00",
      "createdTime": "2025-06-28T08:00:00",
      "updatedTime": "2025-06-28T09:25:00"
    }
  ]
}
```

**响应字段说明：**
- `id`: 服务器主键ID
- `workerId`: 副服务器唯一标识
- `name`: 服务器名称
- `host`: 服务器主机地址
- `port`: 服务器端口
- `maxConcurrentTasks`: 最大并发任务数
- `status`: 服务器状态 (ONLINE/OFFLINE/BUSY/ERROR)
- `currentLoad`: 当前负载（正在处理的任务数）
- `totalTasksCompleted`: 总完成任务数
- `totalTasksFailed`: 总失败任务数
- `lastHeartbeat`: 最后心跳时间

#### 1.2 根据ID获取副服务器详情
```http
GET /api/admin/worker-management/servers/{id}
```

**路径参数：**
- `id`: 服务器ID

**响应格式：** 与获取列表相同，但只返回单个服务器对象

#### 1.3 创建新的副服务器
```http
POST /api/admin/worker-management/servers
```

**请求体：**
```json
{
  "workerId": "worker-003",
  "name": "新副服务器",
  "host": "localhost",
  "port": 8084,
  "maxConcurrentTasks": 10
}
```

**请求字段验证：**
- `workerId`: 必填，3-50字符，只能包含字母、数字、下划线和横线
- `name`: 必填，2-100字符
- `host`: 必填，有效的主机地址格式
- `port`: 必填，1024-65535
- `maxConcurrentTasks`: 必填，1-1000

#### 1.4 更新副服务器配置
```http
PUT /api/admin/worker-management/servers/{id}
```

**请求体：** 与创建相同

#### 1.5 删除副服务器
```http
DELETE /api/admin/worker-management/servers/{id}
```

### 2. 服务器状态管理

#### 2.1 获取在线副服务器列表
```http
GET /api/admin/worker-management/servers/online
```

**描述：** 获取当前在线的副服务器列表

**响应格式：** 与获取所有服务器相同，但只包含状态为 ONLINE 的服务器

#### 2.2 获取可用副服务器列表
```http
GET /api/admin/worker-management/servers/available
```

**描述：** 获取当前可用的副服务器列表（在线且负载未满）

#### 2.3 健康检查
```http
POST /api/admin/worker-management/servers/{id}/health-check
```

**描述：** 对指定副服务器执行健康检查

**响应示例：**
```json
{
  "code": 200,
  "message": "健康检查完成",
  "data": {
    "serverId": "worker-001",
    "healthy": true,
    "responseTime": 150,
    "status": "ONLINE",
    "lastCheck": "2025-06-28T09:30:00"
  }
}
```

#### 2.4 更新心跳超时的服务器状态
```http
POST /api/admin/worker-management/servers/update-timeout-status?timeoutMinutes=5
```

**查询参数：**
- `timeoutMinutes`: 超时分钟数，默认5分钟

**响应示例：**
```json
{
  "code": 200,
  "message": "更新超时服务器状态成功",
  "data": 2
}
```

### 3. 批量操作

#### 3.1 批量操作副服务器
```http
POST /api/admin/worker-management/servers/batch-operation
```

**请求体：**
```json
{
  "ids": ["worker-001", "worker-002"],
  "operation": "restart"
}
```

**支持的操作：**
- `start`: 启动服务器
- `stop`: 停止服务器
- `restart`: 重启服务器
- `delete`: 删除服务器

#### 3.2 批量健康检查
```http
POST /api/admin/worker-management/servers/batch-health-check
```

**请求体：**
```json
["worker-001", "worker-002", "worker-003"]
```

### 4. 统计和监控

#### 4.1 获取副服务器统计信息
```http
GET /api/admin/worker-management/statistics
```

**响应示例：**
```json
{
  "code": 200,
  "message": "获取统计信息成功",
  "data": {
    "totalServers": 5,
    "onlineServers": 3,
    "offlineServers": 2,
    "totalTasks": 1250,
    "completedTasks": 1200,
    "failedTasks": 50,
    "averageLoadRate": 35.5,
    "totalCapacity": 50,
    "usedCapacity": 18
  }
}
```

**统计字段说明：**
- `totalServers`: 总服务器数
- `onlineServers`: 在线服务器数
- `offlineServers`: 离线服务器数
- `totalTasks`: 总任务数
- `completedTasks`: 已完成任务数
- `failedTasks`: 失败任务数
- `averageLoadRate`: 平均负载率
- `totalCapacity`: 总容量
- `usedCapacity`: 已使用容量

---

## 分布式任务管理 API

### 1. 任务统计

#### 1.1 获取任务统计数据
```http
GET /api/admin/distributed-tasks/statistics
```

#### 1.2 获取副服务器状态概览
```http
GET /api/admin/distributed-tasks/worker-status
```

**响应示例：**
```json
{
  "code": 200,
  "message": "获取副服务器状态概览成功",
  "data": [
    {
      "workerId": "worker-001",
      "name": "主要副服务器",
      "status": "ONLINE",
      "currentLoad": 3,
      "maxConcurrentTasks": 10,
      "loadRate": 30,
      "totalTasksCompleted": 150,
      "totalTasksFailed": 5,
      "lastHeartbeat": "2025-06-28T09:30:00"
    }
  ]
}
```

### 2. 任务列表管理

#### 2.1 获取预约任务列表（分页）
```http
GET /api/admin/distributed-tasks/tasks?page=1&size=20&status=PENDING&keyword=user123
```

**查询参数：**
- `page`: 页码，默认1
- `size`: 每页大小，默认10
- `status`: 执行状态筛选 (PENDING/RUNNING/COMPLETED/FAILED) - 可选
- `keyword`: 搜索关键词 - 可选
- `workerId`: 副服务器ID筛选 - 可选

**接口说明：**
这个接口用于获取系统中所有的座位预约任务，每个预约记录都被视为一个任务。主要用于：
- 管理员查看所有预约任务
- 监控任务执行状态
- 手动分配任务给副服务器
- 搜索特定用户的预约

**响应示例：**
```json
{
  "code": 200,
  "message": "获取预约任务列表成功",
  "data": {
    "total": 5,
    "size": 10,
    "current": 1,
    "pages": 1,
    "records": [
      {
        "reservationId": 83,
        "userId": 20,
        "username": "test_user",
        "userName": "测试用户",
        "schoolId": 1,
        "schoolName": "测试学校",
        "roomId": 64,
        "roomName": "测试房间",
        "seatId": "2",
        "startTime": "09:00:00",
        "endTime": "17:00:00",
        "reservationOpenTime": "08:30:00",
        "reservationType": "SAME_DAY",
        "createdTime": "2025-06-28T08:00:00",
        "workerId": "worker-001",
        "workerName": "主要副服务器",
        "executionResult": "执行成功",
        "lastExecutionTime": "2025-06-28T08:30:00",
        "retryCount": 0
      }
    ]
  }
}
```

**故障排查：**
如果接口无响应或返回空数据，可能的原因：
1. **数据库连接问题**：检查数据库是否正常连接
2. **数据表为空**：检查 `reservations` 表是否有数据
3. **关联查询失败**：复杂的多表关联可能导致查询失败
4. **权限问题**：确保有管理员权限访问

**测试方法：**
```bash
# 基础测试
curl -X GET http://localhost:8081/api/admin/distributed-tasks/tasks

# 带参数测试
curl -X GET "http://localhost:8081/api/admin/distributed-tasks/tasks?page=1&size=5"

# 检查数据库数据
mysql -u root -proot seat_reservation -e "SELECT COUNT(*) FROM reservations;"
```

#### 2.2 获取任务详情
```http
GET /api/admin/distributed-tasks/tasks/{taskId}
```

### 3. 任务操作

#### 3.1 手动分配任务到指定副服务器
```http
POST /api/admin/distributed-tasks/tasks/{taskId}/assign?workerId=worker-001
```

#### 3.2 重新执行失败的任务
```http
POST /api/admin/distributed-tasks/tasks/{taskId}/retry
```

#### 3.3 取消任务执行
```http
POST /api/admin/distributed-tasks/tasks/{taskId}/cancel
```

#### 3.4 自动分配待执行任务
```http
POST /api/admin/distributed-tasks/auto-assign
```

---

## 负载率计算说明

### 负载率公式
```
负载率 = (currentLoad / maxConcurrentTasks) * 100
```

### 示例计算
- 服务器最大并发任务数：10
- 当前活跃任务数：3
- 负载率：(3 / 10) * 100 = 30%

### 负载率状态
- **绿色 (0-49%)**：负载正常
- **黄色 (50-79%)**：负载较高
- **红色 (80-100%)**：负载过高

---

## 错误响应格式

所有API在发生错误时都会返回统一的错误响应格式：

```json
{
  "code": 500,
  "message": "详细错误信息",
  "data": null
}
```

### 常见错误码
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

---

## 使用示例

### PowerShell 示例
```powershell
# 获取所有副服务器
Invoke-WebRequest -Uri "http://localhost:8081/api/admin/worker-management/servers" -Method GET

# 获取统计信息
Invoke-WebRequest -Uri "http://localhost:8081/api/admin/worker-management/statistics" -Method GET

# 创建新服务器
$body = @{
    workerId = "worker-003"
    name = "新副服务器"
    host = "localhost"
    port = 8084
    maxConcurrentTasks = 10
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:8081/api/admin/worker-management/servers" -Method POST -Body $body -ContentType "application/json"
```

### cURL 示例
```bash
# 获取所有副服务器
curl -X GET http://localhost:8081/api/admin/worker-management/servers

# 健康检查
curl -X POST http://localhost:8081/api/admin/worker-management/servers/worker-001/health-check

# 获取在线服务器
curl -X GET http://localhost:8081/api/admin/worker-management/servers/online
```

### JavaScript 示例
```javascript
// 获取副服务器列表
fetch('http://localhost:8081/api/admin/worker-management/servers')
  .then(response => response.json())
  .then(data => {
    console.log('服务器列表:', data.data);
    
    // 计算负载率
    data.data.forEach(server => {
      const loadRate = Math.round((server.currentLoad / server.maxConcurrentTasks) * 100);
      console.log(`${server.name}: ${loadRate}% 负载率`);
    });
  });
```

---

## 配置信息

### 服务器配置
- **端口**: 8081
- **数据库**: MySQL (seat_reservation)
- **认证**: JWT (当前禁用)

### 副服务器配置
- **默认端口范围**: 8082-8089
- **默认最大并发任务数**: 10
- **心跳间隔**: 30秒
- **心跳超时**: 5分钟

---

## 注意事项

1. **端口配置**: 主服务器运行在8081端口，副服务器运行在8082+端口
2. **CORS支持**: 已配置跨域访问支持
3. **认证状态**: 当前临时禁用认证，生产环境需启用
4. **负载率实时性**: 负载率数据通过副服务器心跳实时更新
5. **数据一致性**: 所有统计数据基于数据库实时查询
6. **错误处理**: 所有API都有完善的错误处理和日志记录

---

## 版本信息

- **API版本**: v1.0
- **文档版本**: 2025-06-28
- **兼容性**: Spring Boot 2.7.14
- **最后更新**: 2025-06-28
