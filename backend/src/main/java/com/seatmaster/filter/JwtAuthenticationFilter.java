package com.seatmaster.filter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.seatmaster.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                    FilterChain filterChain) throws ServletException, IOException {
        
        String authHeader = request.getHeader("Authorization");
        String token = null;
        String username = null;
        String errorMessage = null;
        
        // 检查Authorization头是否存在且以Bearer开头
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            token = authHeader.substring(7);
            try {
                username = jwtUtil.extractUsername(token);
            } catch (io.jsonwebtoken.ExpiredJwtException e) {
                errorMessage = "JWT令牌已过期，请重新登录";
                logger.error("JWT令牌已过期", e);
            } catch (io.jsonwebtoken.SignatureException e) {
                errorMessage = "JWT令牌签名无效";
                logger.error("JWT令牌签名无效", e);
            } catch (io.jsonwebtoken.MalformedJwtException e) {
                errorMessage = "JWT令牌格式错误";
                logger.error("JWT令牌格式错误", e);
            } catch (Exception e) {
                errorMessage = "JWT令牌解析失败";
                logger.error("JWT令牌解析失败", e);
            }
        }
        
        // 如果JWT解析失败且不是公开接口，返回错误信息
        if (errorMessage != null && !isPublicPath(request.getRequestURI())) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 401);
            errorResponse.put("message", errorMessage);
            errorResponse.put("data", null);
            
            ObjectMapper objectMapper = new ObjectMapper();
            response.getWriter().write(objectMapper.writeValueAsString(errorResponse));
            return;
        }
        
        // 如果令牌有效且用户未认证，则设置认证信息
        if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
            if (jwtUtil.validateToken(token, username)) {
                UsernamePasswordAuthenticationToken authToken = 
                    new UsernamePasswordAuthenticationToken(username, null, new ArrayList<>());
                authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authToken);
            }
        }
        
        filterChain.doFilter(request, response);
    }
    
    /**
     * 检查是否为公开路径，无需JWT验证
     */
    private boolean isPublicPath(String path) {
        return path.startsWith("/api/auth/") ||
               path.startsWith("/api/schools/") ||
               path.startsWith("/api/debug/") ||
               path.startsWith("/api/worker/");
    }
} 