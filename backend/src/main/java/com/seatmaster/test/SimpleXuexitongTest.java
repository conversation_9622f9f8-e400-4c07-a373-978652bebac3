package com.seatmaster.test;

import com.seatmaster.service.ReservationData;

import java.time.LocalTime;

/**
 * 简单的学习通预约功能测试
 * 不依赖Spring和日志框架
 */
public class SimpleXuexitongTest {
    
    public static void main(String[] args) {
        System.out.println("=== 学习通预约功能测试开始 ===");
        
        SimpleXuexitongTest test = new SimpleXuexitongTest();
        
        // 测试1: 数据模型测试
        test.testReservationDataModel();
        
        // 测试2: 数据验证测试
        test.testDataValidation();
        
        // 测试3: 真实数据测试
        test.testRealData();
        
        System.out.println("=== 学习通预约功能测试完成 ===");
    }
    
    /**
     * 测试预约数据模型
     */
    public void testReservationDataModel() {
        System.out.println("\n--- 测试1: 预约数据模型测试 ---");
        
        try {
            // 创建测试数据
            ReservationData testData = ReservationData.Builder.fromDatabase(
                "test_user",             // 用户名
                "test_password",         // 密码
                1L,                      // 用户ID
                1L,                      // 预约ID
                "TEST_ROOM",             // 房间号
                "A001",                  // 座位号
                LocalTime.of(9, 0),      // 开始时间
                LocalTime.of(17, 0),     // 结束时间
                "08:00",                 // 预约开放时间
                "SAME_DAY"               // 预约类型
            );
            
            System.out.println("✅ 测试数据创建成功:");
            System.out.println("   " + testData.getSummary());
            System.out.println("   预约日期: " + testData.getReservationDate());
            System.out.println("   时间段: " + testData.getTimeSlot());
            System.out.println("   预约类型: " + testData.getReservationTypeText());
            
        } catch (Exception e) {
            System.out.println("❌ 数据模型测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试数据验证
     */
    public void testDataValidation() {
        System.out.println("\n--- 测试2: 数据验证测试 ---");
        
        try {
            // 测试有效数据
            ReservationData validData = createTestData();
            System.out.println("有效数据测试: " + (validData.isValid() ? "✅ 通过" : "❌ 失败"));
            
            // 测试无效数据 - 缺少用户名
            ReservationData invalidData1 = createTestData();
            invalidData1.setUsername(null);
            System.out.println("无效数据测试1 (缺少用户名): " + (invalidData1.isValid() ? "❌ 应该失败" : "✅ 正确失败"));
            
            // 测试无效数据 - 缺少房间号
            ReservationData invalidData2 = createTestData();
            invalidData2.setRoomNum("");
            System.out.println("无效数据测试2 (缺少房间号): " + (invalidData2.isValid() ? "❌ 应该失败" : "✅ 正确失败"));
            
            // 测试无效数据 - 时间为空
            ReservationData invalidData3 = createTestData();
            invalidData3.setStartTime(null);
            System.out.println("无效数据测试3 (缺少开始时间): " + (invalidData3.isValid() ? "❌ 应该失败" : "✅ 正确失败"));
            
        } catch (Exception e) {
            System.out.println("❌ 数据验证测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试真实数据
     */
    public void testRealData() {
        System.out.println("\n--- 测试3: 真实数据测试 ---");
        
        try {
            // 使用数据库第一条记录的真实数据
            ReservationData realData = createRealTestData();
            
            System.out.println("真实数据创建成功:");
            System.out.println("   " + realData.getSummary());
            
            // 验证数据
            boolean isValid = realData.isValid();
            System.out.println("数据有效性: " + (isValid ? "✅ 有效" : "❌ 无效"));
            
            if (isValid) {
                System.out.println("详细信息:");
                System.out.println("   用户名: " + realData.getUsername());
                System.out.println("   房间号: " + realData.getRoomNum());
                System.out.println("   座位号: " + realData.getSeatId());
                System.out.println("   预约日期: " + realData.getReservationDate());
                System.out.println("   时间段: " + realData.getTimeSlot());
                System.out.println("   预约类型: " + realData.getReservationTypeText());
                System.out.println("   开放时间: " + realData.getFormattedOpenTime());
                
                // 模拟API调用测试
                testApiCall(realData);
            }
            
        } catch (Exception e) {
            System.out.println("❌ 真实数据测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 模拟API调用测试
     */
    private void testApiCall(ReservationData data) {
        System.out.println("\n--- 模拟API调用测试 ---");
        
        try {
            System.out.println("🔄 模拟学习通登录...");
            System.out.println("   用户名: " + data.getUsername());
            System.out.println("   密码: " + maskPassword(data.getPassword()));
            
            // 模拟登录成功
            System.out.println("✅ 模拟登录成功");
            
            System.out.println("🔄 模拟查询房间...");
            System.out.println("   房间号: " + data.getRoomNum());
            
            // 模拟房间查询成功
            System.out.println("✅ 模拟房间查询成功");
            
            System.out.println("🔄 模拟检查座位可用性...");
            System.out.println("   座位: " + data.getSeatId());
            System.out.println("   日期: " + data.getReservationDate());
            
            // 模拟座位可用
            System.out.println("✅ 模拟座位可用");
            
            System.out.println("🔄 模拟提交预约...");
            System.out.println("   时间段: " + data.getTimeSlot());
            System.out.println("   预约类型: " + data.getReservationTypeText());
            
            // 模拟预约成功
            System.out.println("🎉 模拟预约成功!");
            
            System.out.println("\n📊 预约结果摘要:");
            System.out.println("   预约ID: " + data.getReservationId());
            System.out.println("   用户: " + data.getUsername());
            System.out.println("   房间: " + data.getRoomNum());
            System.out.println("   座位: " + data.getSeatId());
            System.out.println("   日期: " + data.getReservationDate());
            System.out.println("   时间: " + data.getTimeSlot());
            System.out.println("   类型: " + data.getReservationTypeText());
            
        } catch (Exception e) {
            System.out.println("❌ API调用测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 创建测试数据
     */
    private ReservationData createTestData() {
        return ReservationData.Builder.fromDatabase(
            "test_user",             // 用户名
            "test_password",         // 密码
            1L,                      // 用户ID
            1L,                      // 预约ID
            "TEST_ROOM",             // 房间号
            "A001",                  // 座位号
            LocalTime.of(9, 0),      // 开始时间
            LocalTime.of(17, 0),     // 结束时间
            "08:00",                 // 预约开放时间
            "SAME_DAY"               // 预约类型
        );
    }
    
    /**
     * 创建真实测试数据（基于数据库第一条记录）
     */
    private ReservationData createRealTestData() {
        return ReservationData.Builder.fromDatabase(
            "18755869972",           // 真实用户名
            "tcc123698741",          // 真实密码
            20L,                     // 用户ID
            83L,                     // 预约ID
            "4991",                  // 房间号
            "2",                     // 座位号
            LocalTime.of(15, 0),     // 开始时间 15:00
            LocalTime.of(19, 0),     // 结束时间 19:00
            "13:53:00",              // 预约开放时间
            "SAME_DAY"               // 预约类型
        );
    }
    
    /**
     * 掩码密码显示
     */
    private String maskPassword(String password) {
        if (password == null || password.length() <= 2) {
            return "***";
        }
        return password.substring(0, 2) + "***" + password.substring(password.length() - 2);
    }
}
