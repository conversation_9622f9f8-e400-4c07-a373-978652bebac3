package com.seatmaster.test;

import java.time.LocalTime;

/**
 * Simple Xuexitong reservation test in English
 */
public class EnglishXuexitongTest {
    
    public static void main(String[] args) {
        System.out.println("=== Xuexitong Reservation Test Started ===");
        
        EnglishXuexitongTest test = new EnglishXuexitongTest();
        
        // Test 1: Data model test
        test.testReservationDataModel();
        
        // Test 2: Data validation test
        test.testDataValidation();
        
        // Test 3: Real data test
        test.testRealData();
        
        System.out.println("=== Xuexitong Reservation Test Completed ===");
    }
    
    /**
     * Test reservation data model
     */
    public void testReservationDataModel() {
        System.out.println("\n--- Test 1: Reservation Data Model Test ---");
        
        try {
            // Create test data
            SimpleReservationData testData = SimpleReservationData.fromDatabase(
                "test_user",             // username
                "test_password",         // password
                1L,                      // user ID
                1L,                      // reservation ID
                "TEST_ROOM",             // room number
                "A001",                  // seat ID
                LocalTime.of(9, 0),      // start time
                LocalTime.of(17, 0),     // end time
                "08:00",                 // reservation open time
                "SAME_DAY"               // reservation type
            );
            
            System.out.println("✅ Test data created successfully:");
            System.out.println("   " + testData.getSummary());
            System.out.println("   Reservation date: " + testData.getReservationDate());
            System.out.println("   Time slot: " + testData.getTimeSlot());
            System.out.println("   Reservation type: " + testData.getReservationTypeText());
            
        } catch (Exception e) {
            System.out.println("❌ Data model test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Test data validation
     */
    public void testDataValidation() {
        System.out.println("\n--- Test 2: Data Validation Test ---");
        
        try {
            // Test valid data
            SimpleReservationData validData = createTestData();
            System.out.println("Valid data test: " + (validData.isValid() ? "✅ Pass" : "❌ Fail"));

            // Test invalid data - missing username
            SimpleReservationData invalidData1 = createTestData();
            invalidData1.setUsername(null);
            System.out.println("Invalid data test 1 (missing username): " + (invalidData1.isValid() ? "❌ Should fail" : "✅ Correctly failed"));

            // Test invalid data - missing room number
            SimpleReservationData invalidData2 = createTestData();
            invalidData2.setRoomNum("");
            System.out.println("Invalid data test 2 (missing room number): " + (invalidData2.isValid() ? "❌ Should fail" : "✅ Correctly failed"));

            // Test invalid data - missing time
            SimpleReservationData invalidData3 = createTestData();
            invalidData3.setStartTime(null);
            System.out.println("Invalid data test 3 (missing start time): " + (invalidData3.isValid() ? "❌ Should fail" : "✅ Correctly failed"));
            
        } catch (Exception e) {
            System.out.println("❌ Data validation test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Test real data
     */
    public void testRealData() {
        System.out.println("\n--- Test 3: Real Data Test ---");
        
        try {
            // Use real data from database first record
            SimpleReservationData realData = createRealTestData();
            
            System.out.println("Real data created successfully:");
            System.out.println("   " + realData.getSummary());
            
            // Validate data
            boolean isValid = realData.isValid();
            System.out.println("Data validity: " + (isValid ? "✅ Valid" : "❌ Invalid"));
            
            if (isValid) {
                System.out.println("Detailed information:");
                System.out.println("   Username: " + realData.getUsername());
                System.out.println("   Room number: " + realData.getRoomNum());
                System.out.println("   Seat ID: " + realData.getSeatId());
                System.out.println("   Reservation date: " + realData.getReservationDate());
                System.out.println("   Time slot: " + realData.getTimeSlot());
                System.out.println("   Reservation type: " + realData.getReservationTypeText());
                System.out.println("   Open time: " + realData.getFormattedOpenTime());
                
                // Simulate API call test
                testApiCall(realData);
            }
            
        } catch (Exception e) {
            System.out.println("❌ Real data test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Simulate API call test
     */
    private void testApiCall(SimpleReservationData data) {
        System.out.println("\n--- Simulate API Call Test ---");
        
        try {
            System.out.println("🔄 Simulating Xuexitong login...");
            System.out.println("   Username: " + data.getUsername());
            System.out.println("   Password: " + maskPassword(data.getPassword()));
            
            // Simulate successful login
            System.out.println("✅ Simulated login successful");
            
            System.out.println("🔄 Simulating room query...");
            System.out.println("   Room number: " + data.getRoomNum());
            
            // Simulate successful room query
            System.out.println("✅ Simulated room query successful");
            
            System.out.println("🔄 Simulating seat availability check...");
            System.out.println("   Seat: " + data.getSeatId());
            System.out.println("   Date: " + data.getReservationDate());
            
            // Simulate seat available
            System.out.println("✅ Simulated seat available");
            
            System.out.println("🔄 Simulating reservation submission...");
            System.out.println("   Time slot: " + data.getTimeSlot());
            System.out.println("   Reservation type: " + data.getReservationTypeText());
            
            // Simulate successful reservation
            System.out.println("🎉 Simulated reservation successful!");
            
            System.out.println("\n📊 Reservation result summary:");
            System.out.println("   Reservation ID: " + data.getReservationId());
            System.out.println("   User: " + data.getUsername());
            System.out.println("   Room: " + data.getRoomNum());
            System.out.println("   Seat: " + data.getSeatId());
            System.out.println("   Date: " + data.getReservationDate());
            System.out.println("   Time: " + data.getTimeSlot());
            System.out.println("   Type: " + data.getReservationTypeText());
            
        } catch (Exception e) {
            System.out.println("❌ API call test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Create test data
     */
    private SimpleReservationData createTestData() {
        return SimpleReservationData.fromDatabase(
            "test_user",             // username
            "test_password",         // password
            1L,                      // user ID
            1L,                      // reservation ID
            "TEST_ROOM",             // room number
            "A001",                  // seat ID
            LocalTime.of(9, 0),      // start time
            LocalTime.of(17, 0),     // end time
            "08:00",                 // reservation open time
            "SAME_DAY"               // reservation type
        );
    }
    
    /**
     * Create real test data (based on database first record)
     */
    private SimpleReservationData createRealTestData() {
        return SimpleReservationData.fromDatabase(
            "18755869972",           // real username
            "tcc123698741",          // real password
            20L,                     // user ID
            83L,                     // reservation ID
            "4991",                  // room number
            "2",                     // seat ID
            LocalTime.of(15, 0),     // start time 15:00
            LocalTime.of(19, 0),     // end time 19:00
            "13:53:00",              // reservation open time
            "SAME_DAY"               // reservation type
        );
    }
    
    /**
     * Mask password for display
     */
    private String maskPassword(String password) {
        if (password == null || password.length() <= 2) {
            return "***";
        }
        return password.substring(0, 2) + "***" + password.substring(password.length() - 2);
    }
}
