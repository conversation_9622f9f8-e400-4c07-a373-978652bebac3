package com.seatmaster.test;

import com.seatmaster.service.ReservationData;
import com.seatmaster.service.XuexitongApiService;
import com.seatmaster.service.XuexitongResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalTime;

/**
 * 学习通预约功能测试运行器
 * 独立运行，不依赖Spring容器
 */
public class XuexitongTestRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(XuexitongTestRunner.class);
    
    public static void main(String[] args) {
        logger.info("=== 学习通预约功能测试开始 ===");
        
        XuexitongTestRunner runner = new XuexitongTestRunner();
        
        // 测试1: 基本功能测试
        runner.testBasicFunctionality();
        
        // 测试2: 数据验证测试
        runner.testDataValidation();
        
        // 测试3: 实际预约测试（使用第一条记录数据）
        runner.testActualReservation();
        
        logger.info("=== 学习通预约功能测试完成 ===");
    }
    
    /**
     * 测试基本功能
     */
    public void testBasicFunctionality() {
        logger.info("\n--- 测试1: 基本功能测试 ---");
        
        try {
            // 创建测试数据
            ReservationData testData = createTestData();
            
            logger.info("创建测试数据成功:");
            logger.info("  {}", testData.getSummary());
            
            // 验证数据有效性
            boolean isValid = testData.isValid();
            logger.info("数据有效性检查: {}", isValid ? "✅ 通过" : "❌ 失败");
            
            if (isValid) {
                logger.info("预约日期计算: {}", testData.getReservationDate());
                logger.info("时间段格式化: {}", testData.getTimeSlot());
                logger.info("预约类型文本: {}", testData.getReservationTypeText());
            }
            
        } catch (Exception e) {
            logger.error("基本功能测试异常", e);
        }
    }
    
    /**
     * 测试数据验证
     */
    public void testDataValidation() {
        logger.info("\n--- 测试2: 数据验证测试 ---");
        
        try {
            // 测试有效数据
            ReservationData validData = createTestData();
            logger.info("有效数据测试: {}", validData.isValid() ? "✅ 通过" : "❌ 失败");
            
            // 测试无效数据 - 缺少用户名
            ReservationData invalidData1 = createTestData();
            invalidData1.setUsername(null);
            logger.info("无效数据测试1 (缺少用户名): {}", invalidData1.isValid() ? "❌ 应该失败" : "✅ 正确失败");
            
            // 测试无效数据 - 缺少房间号
            ReservationData invalidData2 = createTestData();
            invalidData2.setRoomNum("");
            logger.info("无效数据测试2 (缺少房间号): {}", invalidData2.isValid() ? "❌ 应该失败" : "✅ 正确失败");
            
            // 测试无效数据 - 时间为空
            ReservationData invalidData3 = createTestData();
            invalidData3.setStartTime(null);
            logger.info("无效数据测试3 (缺少开始时间): {}", invalidData3.isValid() ? "❌ 应该失败" : "✅ 正确失败");
            
        } catch (Exception e) {
            logger.error("数据验证测试异常", e);
        }
    }
    
    /**
     * 测试实际预约功能
     */
    public void testActualReservation() {
        logger.info("\n--- 测试3: 实际预约测试 ---");
        
        try {
            // 使用数据库第一条记录的真实数据
            ReservationData realData = createRealTestData();
            
            logger.info("使用真实数据进行预约测试:");
            logger.info("  {}", realData.getSummary());
            
            // 创建学习通API服务
            XuexitongApiService apiService = new XuexitongApiService();
            
            logger.info("开始执行学习通预约...");
            long startTime = System.currentTimeMillis();
            
            // 执行预约
            XuexitongResponse response = apiService.executeReservation(realData);
            
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 分析结果
            logger.info("预约执行完成，耗时: {}ms", executionTime);
            logger.info("预约结果摘要: {}", response.getSummary());
            
            if (response.isSuccess()) {
                logger.info("🎉 预约成功!");
                logger.info("成功消息: {}", response.getMessage());
                
                if (response.getData() != null && !response.getData().isEmpty()) {
                    logger.info("返回数据:");
                    response.getData().forEach((key, value) -> 
                        logger.info("  {}: {}", key, value));
                }
            } else {
                logger.warn("⚠️ 预约失败");
                logger.warn("失败原因: {}", response.getMessage());
                
                if (response.getErrorCode() != null) {
                    logger.warn("错误代码: {}", response.getErrorCode());
                }
                
                // 错误类型分析
                analyzeError(response);
            }
            
        } catch (Exception e) {
            logger.error("实际预约测试异常", e);
        }
    }
    
    /**
     * 创建测试数据
     */
    private ReservationData createTestData() {
        return ReservationData.Builder.fromDatabase(
            "test_user",             // 用户名
            "test_password",         // 密码
            1L,                      // 用户ID
            1L,                      // 预约ID
            "TEST_ROOM",             // 房间号
            "A001",                  // 座位号
            LocalTime.of(9, 0),      // 开始时间
            LocalTime.of(17, 0),     // 结束时间
            "08:00",                 // 预约开放时间
            "SAME_DAY"               // 预约类型
        );
    }
    
    /**
     * 创建真实测试数据（基于数据库第一条记录）
     */
    private ReservationData createRealTestData() {
        return ReservationData.Builder.fromDatabase(
            "18755869972",           // 真实用户名
            "tcc123698741",          // 真实密码
            20L,                     // 用户ID
            83L,                     // 预约ID
            "4991",                  // 房间号
            "2",                     // 座位号
            LocalTime.of(15, 0),     // 开始时间 15:00
            LocalTime.of(19, 0),     // 结束时间 19:00
            "13:53:00",              // 预约开放时间
            "SAME_DAY"               // 预约类型
        );
    }
    
    /**
     * 分析错误类型
     */
    private void analyzeError(XuexitongResponse response) {
        if (response.isNetworkError()) {
            logger.warn("错误类型: 🌐 网络错误");
            logger.warn("建议: 检查网络连接和API端点配置");
        } else if (response.isAuthError()) {
            logger.warn("错误类型: 🔐 认证错误");
            logger.warn("建议: 检查用户名密码是否正确");
        } else if (response.isSeatUnavailableError()) {
            logger.warn("错误类型: 💺 座位不可用");
            logger.warn("建议: 选择其他座位或时间段");
        } else {
            logger.warn("错误类型: ❓ 其他错误");
            logger.warn("建议: 查看详细错误信息进行排查");
        }
    }
}
