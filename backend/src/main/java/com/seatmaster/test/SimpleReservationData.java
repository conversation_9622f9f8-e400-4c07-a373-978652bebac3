package com.seatmaster.test;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * Simple reservation data model for testing
 * No external dependencies
 */
public class SimpleReservationData {
    
    // User information
    private String username;
    private String password;
    private Long userId;
    
    // Reservation information
    private Long reservationId;
    private String roomNum;
    private String seatId;
    private LocalTime startTime;
    private LocalTime endTime;
    
    // Reservation configuration
    private String reservationOpenTime;
    private String reservationType;
    private LocalDate reservationDate;
    
    // Execution configuration
    private boolean isRetry;
    private int retryCount;
    
    // Constructors
    public SimpleReservationData() {}
    
    public SimpleReservationData(String username, String password, Long userId, Long reservationId,
                                String roomNum, String seatId, LocalTime startTime, LocalTime endTime,
                                String reservationOpenTime, String reservationType) {
        this.username = username;
        this.password = password;
        this.userId = userId;
        this.reservationId = reservationId;
        this.roomNum = roomNum;
        this.seatId = seatId;
        this.startTime = startTime;
        this.endTime = endTime;
        this.reservationOpenTime = reservationOpenTime;
        this.reservationType = reservationType;
        this.isRetry = false;
        this.retryCount = 0;
    }
    
    // Getters and Setters
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
    
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }
    
    public Long getReservationId() { return reservationId; }
    public void setReservationId(Long reservationId) { this.reservationId = reservationId; }
    
    public String getRoomNum() { return roomNum; }
    public void setRoomNum(String roomNum) { this.roomNum = roomNum; }
    
    public String getSeatId() { return seatId; }
    public void setSeatId(String seatId) { this.seatId = seatId; }
    
    public LocalTime getStartTime() { return startTime; }
    public void setStartTime(LocalTime startTime) { this.startTime = startTime; }
    
    public LocalTime getEndTime() { return endTime; }
    public void setEndTime(LocalTime endTime) { this.endTime = endTime; }
    
    public String getReservationOpenTime() { return reservationOpenTime; }
    public void setReservationOpenTime(String reservationOpenTime) { this.reservationOpenTime = reservationOpenTime; }
    
    public String getReservationType() { return reservationType; }
    public void setReservationType(String reservationType) { this.reservationType = reservationType; }
    
    public LocalDate getReservationDate() {
        if (reservationDate != null) {
            return reservationDate;
        }
        
        LocalDate today = LocalDate.now();
        if ("ADVANCE_ONE_DAY".equals(reservationType)) {
            reservationDate = today.plusDays(1);
        } else {
            reservationDate = today;
        }
        return reservationDate;
    }
    
    public void setReservationDate(LocalDate reservationDate) { this.reservationDate = reservationDate; }
    
    public boolean isRetry() { return isRetry; }
    public void setRetry(boolean retry) { isRetry = retry; }
    
    public int getRetryCount() { return retryCount; }
    public void setRetryCount(int retryCount) { this.retryCount = retryCount; }
    
    // Business methods
    public boolean isValid() {
        return username != null && !username.trim().isEmpty() &&
               password != null && !password.trim().isEmpty() &&
               roomNum != null && !roomNum.trim().isEmpty() &&
               seatId != null && !seatId.trim().isEmpty() &&
               startTime != null &&
               endTime != null &&
               reservationType != null;
    }
    
    public String getTimeSlot() {
        if (startTime != null && endTime != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
            return startTime.format(formatter) + "-" + endTime.format(formatter);
        }
        return "Not set";
    }
    
    public String getReservationTypeText() {
        if ("ADVANCE_ONE_DAY".equals(reservationType)) {
            return "Advance one day reservation";
        } else {
            return "Same day reservation";
        }
    }
    
    public String getFormattedOpenTime() {
        if (reservationOpenTime != null && !reservationOpenTime.trim().isEmpty()) {
            return reservationOpenTime;
        }
        return "08:00"; // Default open time
    }
    
    public String getSummary() {
        return String.format("User=%s, Room=%s, Seat=%s, Time=%s, Date=%s, Type=%s",
                           username, roomNum, seatId, getTimeSlot(), 
                           getReservationDate(), getReservationTypeText());
    }
    
    @Override
    public String toString() {
        return getSummary();
    }
    
    // Static factory method
    public static SimpleReservationData fromDatabase(String username, String password, Long userId,
                                                    Long reservationId, String roomNum, String seatId,
                                                    LocalTime startTime, LocalTime endTime,
                                                    String reservationOpenTime, String reservationType) {
        return new SimpleReservationData(username, password, userId, reservationId,
                                        roomNum, seatId, startTime, endTime,
                                        reservationOpenTime, reservationType);
    }
}
