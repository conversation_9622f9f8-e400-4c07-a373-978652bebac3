package com.seatmaster.controller;

import com.seatmaster.common.Result;
import com.seatmaster.dto.WorkerServerDTO;
import com.seatmaster.service.AsyncTaskReassignmentService;
import com.seatmaster.service.WorkerServerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 管理员副服务器管理控制器
 * 仅管理员可访问
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/worker-management")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
// @PreAuthorize("hasRole('ADMIN')") // 临时禁用认证以便测试
@Validated
public class AdminWorkerController {

    private final WorkerServerService workerServerService;
    private final AsyncTaskReassignmentService asyncTaskReassignmentService;

    // ==================== 服务器列表管理 ====================

    /**
     * 获取所有副服务器列表
     */
    @GetMapping("/servers")
    public Result<List<WorkerServerDTO>> getAllServers() {
        try {
            List<WorkerServerDTO> servers = workerServerService.getAllServers();
            return Result.success("获取副服务器列表成功", servers);
        } catch (Exception e) {
            log.error("获取副服务器列表失败", e);
            return Result.error("获取副服务器列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取副服务器详情
     */
    @GetMapping("/servers/{id}")
    public Result<WorkerServerDTO> getServerById(@PathVariable @NotNull String id) {
        try {
            WorkerServerDTO server = workerServerService.getServerById(id);
            if (server == null) {
                return Result.error("副服务器不存在");
            }
            return Result.success("获取副服务器详情成功", server);
        } catch (Exception e) {
            log.error("获取副服务器详情失败: id={}", id, e);
            return Result.error("获取副服务器详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建新的副服务器
     */
    @PostMapping("/servers")
    public Result<WorkerServerDTO> createServer(@Valid @RequestBody WorkerServerDTO.CreateRequest request) {
        try {
            WorkerServerDTO server = workerServerService.createServer(request);
            log.info("管理员创建副服务器成功: workerId={}, name={}", server.getWorkerId(), server.getName());
            return Result.success("创建副服务器成功", server);
        } catch (Exception e) {
            log.error("创建副服务器失败: request={}", request, e);
            return Result.error("创建副服务器失败: " + e.getMessage());
        }
    }

    /**
     * 更新副服务器配置
     */
    @PutMapping("/servers/{id}")
    public Result<WorkerServerDTO> updateServer(@PathVariable @NotNull String id,
                                               @Valid @RequestBody WorkerServerDTO.UpdateRequest request) {
        try {
            // 验证路径参数ID不能为空
            if (id == null || id.trim().isEmpty()) {
                return Result.error("服务器ID不能为空");
            }

            // 将URL路径中的ID设置到请求对象中
            request.setId(id);
            WorkerServerDTO server = workerServerService.updateServer(request);
            log.info("管理员更新副服务器成功: id={}, name={}", id, server.getName());
            return Result.success("更新副服务器成功", server);
        } catch (Exception e) {
            log.error("更新副服务器失败: id={}, request={}", id, request, e);
            return Result.error("更新副服务器失败: " + e.getMessage());
        }
    }

    /**
     * 删除副服务器
     */
    @DeleteMapping("/servers/{id}")
    public Result<Void> deleteServer(@PathVariable @NotNull String id) {
        try {
            boolean success = workerServerService.deleteServer(id);
            if (success) {
                log.info("管理员删除副服务器成功: id={}", id);
                return Result.success("删除副服务器成功", null);
            } else {
                return Result.error("删除副服务器失败");
            }
        } catch (Exception e) {
            log.error("删除副服务器失败: id={}", id, e);
            return Result.error("删除副服务器失败: " + e.getMessage());
        }
    }

    // ==================== 服务器操作控制 ====================

    /**
     * Worker 心跳接口
     */
    @PostMapping("/servers/{id}/heartbeat")
    public Result<String> heartbeat(@PathVariable @NotNull String id, @RequestBody Map<String, Object> heartbeatData) {
        try {
            workerServerService.updateHeartbeat(id, heartbeatData);
            log.debug("收到Worker心跳: id={}, status={}", id, heartbeatData.get("status"));
            return Result.success("心跳更新成功");
        } catch (Exception e) {
            log.error("更新Worker心跳失败: id={}", id, e);
            return Result.error("心跳更新失败: " + e.getMessage());
        }
    }

    /**
     * Worker 注销接口
     */
    @PostMapping("/servers/{id}/unregister")
    public Result<String> unregister(@PathVariable @NotNull String id) {
        try {
            workerServerService.unregisterWorker(id);
            log.info("Worker注销成功: id={}", id);
            return Result.success("注销成功");
        } catch (Exception e) {
            log.error("Worker注销失败: id={}", id, e);
            return Result.error("注销失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查 - 已禁用主动健康检查功能
     * 注释原因：根据需求，移除主服务器主动发送心跳的功能
     * 为保持API兼容性，返回明确的错误信息
     */
    @PostMapping("/servers/{id}/health-check")
    public Result<String> healthCheck(@PathVariable @NotNull String id) {
        log.warn("健康检查功能已禁用: 管理员尝试检查服务器 id={}", id);
        return Result.error("主动健康检查功能已禁用。系统现在只接收副服务器的心跳信号，不再主动发送检查请求。");
    }

    // ==================== 批量操作 ====================

    /**
     * 批量操作副服务器
     */
    @PostMapping("/servers/batch-operation")
    public Result<Map<String, Object>> batchOperation(@Valid @RequestBody WorkerServerDTO.BatchOperationRequest request) {
        try {
            Map<String, Object> result = workerServerService.batchOperation(request);
            log.info("管理员执行批量操作: operation={}, ids={}, successCount={}", 
                    request.getOperation(), request.getIds(), result.get("successCount"));
            return Result.success("批量操作完成", result);
        } catch (Exception e) {
            log.error("批量操作失败: request={}", request, e);
            return Result.error("批量操作失败: " + e.getMessage());
        }
    }

    /**
     * 批量健康检查 - 已禁用主动健康检查功能
     * 注释原因：根据需求，移除主服务器主动发送心跳的功能
     * 为保持API兼容性，返回明确的错误信息
     */
    @PostMapping("/servers/batch-health-check")
    public Result<String> batchHealthCheck(@RequestBody @NotEmpty List<String> ids) {
        log.warn("批量健康检查功能已禁用: 管理员尝试检查服务器 ids={}", ids);
        return Result.error("主动健康检查功能已禁用。系统现在只接收副服务器的心跳信号，不再主动发送检查请求。");
    }

    // ==================== 统计和监控 ====================

    /**
     * 获取副服务器统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getStatistics() {
        try {
            Map<String, Object> statistics = workerServerService.getServerStatistics();
            return Result.success("获取统计信息成功", statistics);
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取在线副服务器列表
     */
    @GetMapping("/servers/online")
    public Result<List<WorkerServerDTO>> getOnlineServers() {
        try {
            List<WorkerServerDTO> servers = workerServerService.getOnlineServers();
            return Result.success("获取在线副服务器列表成功", servers);
        } catch (Exception e) {
            log.error("获取在线副服务器列表失败", e);
            return Result.error("获取在线副服务器列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取可用副服务器列表
     */
    @GetMapping("/servers/available")
    public Result<List<WorkerServerDTO>> getAvailableServers() {
        try {
            List<WorkerServerDTO> servers = workerServerService.getAvailableServers();
            return Result.success("获取可用副服务器列表成功", servers);
        } catch (Exception e) {
            log.error("获取可用副服务器列表失败", e);
            return Result.error("获取可用副服务器列表失败: " + e.getMessage());
        }
    }

    /**
     * 更新心跳超时的服务器状态
     */
    @PostMapping("/servers/update-timeout-status")
    public Result<Integer> updateTimeoutServers(@RequestParam(defaultValue = "5") int timeoutMinutes) {
        try {
            int updatedCount = workerServerService.updateTimeoutServers(timeoutMinutes);
            log.info("管理员更新心跳超时服务器状态: timeoutMinutes={}, updatedCount={}", timeoutMinutes, updatedCount);
            return Result.success("更新超时服务器状态成功", updatedCount);
        } catch (Exception e) {
            log.error("更新超时服务器状态失败: timeoutMinutes={}", timeoutMinutes, e);
            return Result.error("更新超时服务器状态失败: " + e.getMessage());
        }
    }

    // ==================== 异步任务重新分配状态查询 ====================

    /**
     * 查询任务重新分配状态
     */
    @GetMapping("/task-reassignment/status/{taskId}")
    public Result<AsyncTaskReassignmentService.ReassignmentStatus> getReassignmentStatus(
            @PathVariable @NotNull String taskId) {
        try {
            AsyncTaskReassignmentService.ReassignmentStatus status =
                    asyncTaskReassignmentService.getReassignmentStatus(taskId);

            if (status == null) {
                return Result.error("任务重新分配状态不存在: " + taskId);
            }

            return Result.success("获取任务重新分配状态成功", status);
        } catch (Exception e) {
            log.error("获取任务重新分配状态失败: taskId={}", taskId, e);
            return Result.error("获取任务重新分配状态失败: " + e.getMessage());
        }
    }

    /**
     * 清理已完成的任务重新分配状态记录
     */
    @PostMapping("/task-reassignment/cleanup")
    public Result<String> cleanupReassignmentStatus() {
        try {
            asyncTaskReassignmentService.cleanupCompletedStatus();
            log.info("管理员清理任务重新分配状态记录");
            return Result.success("清理任务重新分配状态记录成功");
        } catch (Exception e) {
            log.error("清理任务重新分配状态记录失败", e);
            return Result.error("清理任务重新分配状态记录失败: " + e.getMessage());
        }
    }
}
