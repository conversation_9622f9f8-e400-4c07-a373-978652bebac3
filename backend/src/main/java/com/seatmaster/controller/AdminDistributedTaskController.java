package com.seatmaster.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seatmaster.common.Result;
import com.seatmaster.dto.ReservationTaskDTO;
import com.seatmaster.dto.TaskStatisticsDTO;
import com.seatmaster.dto.WorkerServerDTO;
import com.seatmaster.entity.Reservation;
import com.seatmaster.mapper.ReservationMapper;
import com.seatmaster.service.DistributedTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.*;

/**
 * 管理员分布式任务管理控制器
 * 仅管理员可访问
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/distributed-tasks")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
// @PreAuthorize("hasRole('ADMIN')") // 临时注释掉用于测试
@Validated
public class AdminDistributedTaskController {

    private final DistributedTaskService distributedTaskService;
    private final ReservationMapper reservationMapper;

    // ==================== 统计数据接口 ====================

    /**
     * 获取任务统计数据
     */
    @GetMapping("/statistics")
    public Result<TaskStatisticsDTO> getTaskStatistics() {
        try {
            TaskStatisticsDTO statistics = distributedTaskService.getTaskStatistics();
            return Result.success("获取任务统计数据成功", statistics);
        } catch (Exception e) {
            log.error("获取任务统计数据失败", e);
            return Result.error("获取任务统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取副服务器状态概览
     */
    @GetMapping("/worker-status")
    public Result<List<Map<String, Object>>> getWorkerStatusOverview() {
        try {
            List<Map<String, Object>> workerStatus = distributedTaskService.getWorkerStatusOverview();
            return Result.success("获取副服务器状态概览成功", workerStatus);
        } catch (Exception e) {
            log.error("获取副服务器状态概览失败", e);
            return Result.error("获取副服务器状态概览失败: " + e.getMessage());
        }
    }

    // ==================== 任务列表管理 ====================

    /**
     * 获取预约任务列表（分页）
     */
    @GetMapping("/tasks")
    public Result<Page<ReservationTaskDTO>> getReservationTasks(
            @RequestParam(defaultValue = "1") @Positive int page,
            @RequestParam(defaultValue = "10") @Positive int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String workerId) {
        try {
            Page<ReservationTaskDTO> taskPage = distributedTaskService.getReservationTasks(page, size, status, keyword, workerId);
            return Result.success("获取预约任务列表成功", taskPage);
        } catch (Exception e) {
            log.error("获取预约任务列表失败: page={}, size={}, status={}, keyword={}, workerId={}", page, size, status, keyword, workerId, e);
            return Result.error("获取预约任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取任务详情
     */
    @GetMapping("/tasks/{id}")
    public Result<ReservationTaskDTO> getTaskDetail(@PathVariable @NotNull Long id) {
        try {
            ReservationTaskDTO taskDetail = distributedTaskService.getTaskDetail(id);
            if (taskDetail == null) {
                return Result.error("任务不存在");
            }
            return Result.success("获取任务详情成功", taskDetail);
        } catch (Exception e) {
            log.error("获取任务详情失败: id={}", id, e);
            return Result.error("获取任务详情失败: " + e.getMessage());
        }
    }

    // ==================== 任务操作接口 ====================

    /**
     * 手动分配任务到指定副服务器
     */
    @PostMapping("/tasks/{id}/assign")
    public Result<String> assignTaskToWorker(
            @PathVariable @NotNull Long id,
            @RequestParam @NotNull String workerId) {
        try {
            boolean success = distributedTaskService.assignTaskToWorker(id, workerId);
            if (success) {
                log.info("管理员手动分配任务成功: taskId={}, workerId={}", id, workerId);
                return Result.success("任务分配成功", null);
            } else {
                return Result.error("任务分配失败，请检查任务状态和副服务器状态");
            }
        } catch (Exception e) {
            log.error("手动分配任务失败: taskId={}, workerId={}", id, workerId, e);
            return Result.error("任务分配失败: " + e.getMessage());
        }
    }

    /**
     * 立即执行任务（支持重试失败任务和立即执行正常任务）
     */
    @PostMapping("/tasks/{id}/retry")
    public Result<String> retryFailedTask(@PathVariable @NotNull Long id) {
        try {
            log.info("🚀 收到立即执行任务请求: taskId={}", id);
            boolean success = distributedTaskService.retryFailedTask(id);
            if (success) {
                log.info("✅ 管理员立即执行任务成功: taskId={}", id);
                return Result.success("任务立即执行设置成功", null);
            } else {
                log.warn("❌ 管理员立即执行任务失败: taskId={}", id);
                return Result.error("任务立即执行设置失败，请检查任务是否已分配给副服务器");
            }
        } catch (Exception e) {
            log.error("💥 立即执行任务异常: taskId={}", id, e);
            return Result.error("任务立即执行失败: " + e.getMessage());
        }
    }

    /**
     * 调试副服务器调用
     */
    @PostMapping("/tasks/{id}/debug-worker-call")
    public Result<Map<String, Object>> debugWorkerCall(@PathVariable @NotNull Long id) {
        try {
            log.info("🔧 开始调试副服务器调用: taskId={}", id);
            Map<String, Object> debugInfo = distributedTaskService.debugWorkerCall(id);
            log.info("🔧 调试完成: taskId={}, result={}", id, debugInfo);
            return Result.success("调试完成", debugInfo);
        } catch (Exception e) {
            log.error("🔧 调试副服务器调用失败: taskId={}", id, e);
            return Result.error("调试失败: " + e.getMessage());
        }
    }

    /**
     * 取消任务执行
     */
    @PostMapping("/tasks/{id}/cancel")
    public Result<String> cancelTask(@PathVariable @NotNull Long id) {
        try {
            boolean success = distributedTaskService.cancelTask(id);
            if (success) {
                log.info("管理员取消任务成功: taskId={}", id);
                return Result.success("任务取消成功", null);
            } else {
                return Result.error("任务取消失败，请检查任务状态");
            }
        } catch (Exception e) {
            log.error("取消任务失败: taskId={}", id, e);
            return Result.error("任务取消失败: " + e.getMessage());
        }
    }

    /**
     * 批量分配任务
     */
    @PostMapping("/tasks/batch-assign")
    public Result<Map<String, Object>> batchAssignTasks(
            @RequestParam List<Long> taskIds,
            @RequestParam @NotNull String workerId) {
        try {
            Map<String, Object> result = distributedTaskService.batchAssignTasks(taskIds, workerId);
            log.info("管理员批量分配任务完成: taskIds={}, workerId={}, result={}", taskIds, workerId, result);
            return Result.success("批量分配任务完成", result);
        } catch (Exception e) {
            log.error("批量分配任务失败: taskIds={}, workerId={}", taskIds, workerId, e);
            return Result.error("批量分配任务失败: " + e.getMessage());
        }
    }

    // ==================== 自动化操作接口 ====================

    /**
     * 自动分配待执行任务
     */
    @PostMapping("/auto-assign")
    public Result<Map<String, Object>> autoAssignPendingTasks() {
        try {
            Map<String, Object> result = distributedTaskService.autoAssignPendingTasks();
            log.info("管理员触发自动分配任务完成: result={}", result);
            return Result.success("自动分配任务完成", result);
        } catch (Exception e) {
            log.error("自动分配任务失败", e);
            return Result.error("自动分配任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取可用副服务器列表
     */
    @GetMapping("/available-workers")
    public Result<List<WorkerServerDTO>> getAvailableWorkers() {
        try {
            List<WorkerServerDTO> availableWorkers = distributedTaskService.getAvailableWorkers();
            return Result.success("获取可用副服务器列表成功", availableWorkers);
        } catch (Exception e) {
            log.error("获取可用副服务器列表失败", e);
            return Result.error("获取可用副服务器列表失败: " + e.getMessage());
        }
    }

    // ==================== 日志和维护接口 ====================

    /**
     * 获取任务执行日志
     */
    @GetMapping("/tasks/{id}/logs")
    public Result<List<String>> getTaskExecutionLogs(@PathVariable @NotNull Long id) {
        try {
            List<String> logs = distributedTaskService.getTaskExecutionLogs(id);
            return Result.success("获取任务执行日志成功", logs);
        } catch (Exception e) {
            log.error("获取任务执行日志失败: taskId={}", id, e);
            return Result.error("获取任务执行日志失败: " + e.getMessage());
        }
    }

    /**
     * 清理过期任务记录
     */
    @PostMapping("/cleanup")
    public Result<Integer> cleanupExpiredTasks(@RequestParam(defaultValue = "30") int daysToKeep) {
        try {
            int cleanedCount = distributedTaskService.cleanupExpiredTasks(daysToKeep);
            log.info("管理员清理过期任务记录完成: daysToKeep={}, cleanedCount={}", daysToKeep, cleanedCount);
            return Result.success("清理过期任务记录完成", cleanedCount);
        } catch (Exception e) {
            log.error("清理过期任务记录失败: daysToKeep={}", daysToKeep, e);
            return Result.error("清理过期任务记录失败: " + e.getMessage());
        }
    }

    /**
     * 调试接口：直接查询数据库
     */
    @GetMapping("/debug/database")
    public Result<Map<String, Object>> debugDatabase() {
        try {
            Map<String, Object> result = new HashMap<>();

            // 1. 测试基础查询
            QueryWrapper<Reservation> wrapper = new QueryWrapper<>();
            List<Reservation> allReservations = reservationMapper.selectList(wrapper);
            result.put("totalReservations", allReservations.size());
            log.info("=== 调试信息 ===");
            log.info("MyBatis Plus查询结果: {}条记录", allReservations.size());

            // 2. 测试原生SQL查询
            List<Map<String, Object>> rawResults = reservationMapper.selectMaps(
                new QueryWrapper<Reservation>().last("LIMIT 5"));
            result.put("rawQueryResults", rawResults.size());
            log.info("原生查询结果: {}条记录", rawResults.size());

            // 3. 显示原生查询的详细结果
            if (!rawResults.isEmpty()) {
                result.put("sampleRawData", rawResults);
                log.info("原生查询示例数据: {}", rawResults.get(0));
            }

            // 4. 测试分页查询
            Page<Reservation> page = new Page<>(1, 10);
            Page<Reservation> reservationPage = reservationMapper.selectPage(page, wrapper);
            result.put("pageTotal", reservationPage.getTotal());
            result.put("pageRecords", reservationPage.getRecords().size());
            log.info("分页查询结果: total={}, records={}", reservationPage.getTotal(), reservationPage.getRecords().size());

            // 5. 显示MyBatis Plus查询的详细结果
            if (!allReservations.isEmpty()) {
                List<Map<String, Object>> sampleReservations = new ArrayList<>();
                for (int i = 0; i < Math.min(3, allReservations.size()); i++) {
                    Reservation r = allReservations.get(i);
                    Map<String, Object> sample = new HashMap<>();
                    sample.put("id", r.getId());
                    sample.put("userId", r.getUserId());
                    sample.put("roomId", r.getRoomId());
                    sample.put("seatId", r.getSeatId());
                    sample.put("workerId", r.getWorkerId());
                    sample.put("createdTime", r.getCreatedTime());
                    sampleReservations.add(sample);
                    log.info("MyBatis Plus记录{}: id={}, userId={}, roomId={}, seatId={}",
                            i+1, r.getId(), r.getUserId(), r.getRoomId(), r.getSeatId());
                }
                result.put("sampleReservations", sampleReservations);
            }

            return Result.success("数据库调试信息获取成功", result);

        } catch (Exception e) {
            log.error("数据库调试失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", e.getMessage());
            errorResult.put("stackTrace", Arrays.toString(e.getStackTrace()));
            return Result.error("数据库调试失败: " + e.getMessage());
        }
    }

    /**
     * 调试接口：测试任务查询
     */
    @GetMapping("/debug-reservations")
    public Result<Map<String, Object>> debugReservations() {
        try {
            Map<String, Object> result = new HashMap<>();

            // 1. 检查基础数据
            QueryWrapper<Reservation> wrapper = new QueryWrapper<>();
            List<Reservation> allReservations = reservationMapper.selectList(wrapper);
            result.put("totalReservations", allReservations.size());

            // 简化任务查询方法已删除（包含虚假数据）

            // 3. 测试分布式任务服务
            try {
                Page<ReservationTaskDTO> serviceResult = distributedTaskService.getReservationTasks(1, 5, null, null, null);
                result.put("serviceTasksTotal", serviceResult.getTotal());
                result.put("serviceTasksRecords", serviceResult.getRecords().size());
                if (!serviceResult.getRecords().isEmpty()) {
                    result.put("serviceTasksSample", serviceResult.getRecords());
                }
                log.info("服务层查询结果: total={}, records={}", serviceResult.getTotal(), serviceResult.getRecords().size());
            } catch (Exception e) {
                result.put("serviceTasksError", e.getMessage());
                log.error("服务层查询失败", e);
            }

            return Result.success("任务查询调试信息获取成功", result);

        } catch (Exception e) {
            log.error("任务查询调试接口失败", e);
            return Result.error("任务查询调试接口失败: " + e.getMessage());
        }
    }
}
