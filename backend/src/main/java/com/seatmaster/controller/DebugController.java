package com.seatmaster.controller;

import com.seatmaster.common.Result;
import com.seatmaster.entity.User;
import com.seatmaster.mapper.ReservationMapper;
import com.seatmaster.mapper.UserMapper;
import com.seatmaster.dto.UserProfileResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/debug")
@CrossOrigin(origins = "*")
public class DebugController {
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private ReservationMapper reservationMapper;
    
    /**
     * 调试用户预约信息
     */
    @GetMapping("/user/{userId}/reservations")
    public Result<?> debugUserReservations(@PathVariable Long userId) {
        try {
            log.info("开始调试用户ID {}的预约信息", userId);
            
            // 1. 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                return Result.error("用户不存在: " + userId);
            }
            
            log.info("找到用户: {} (ID: {})", user.getUsername(), user.getId());
            
            // 2. 统计预约总数
            int totalCount = reservationMapper.countReservationsByUserId(userId);
            log.info("用户{}的预约总数: {}", userId, totalCount);
            
            // 3. 获取所有预约记录
            java.util.List<UserProfileResponse.CurrentReservation> allReservations = 
                reservationMapper.getAllReservationsByUserId(userId);
            log.info("查询到{}条预约记录", allReservations.size());
            
            // 4. 尝试获取当前预约（有时间限制）
            UserProfileResponse.CurrentReservation currentReservation = null;
            try {
                currentReservation = reservationMapper.getCurrentReservationByUserId(userId);
                log.info("当前预约查询结果: {}", currentReservation != null ? "找到" : "未找到");
            } catch (Exception e) {
                log.error("查询当前预约时出错", e);
            }
            
            // 5. 尝试获取最新预约（无时间限制）
            UserProfileResponse.CurrentReservation latestReservation = null;
            try {
                latestReservation = reservationMapper.getLatestActiveReservationByUserId(userId);
                log.info("最新预约查询结果: {}", latestReservation != null ? "找到" : "未找到");
            } catch (Exception e) {
                log.error("查询最新预约时出错", e);
            }
            
            // 6. 构建调试响应
            java.util.Map<String, Object> debugData = new java.util.HashMap<>();
            debugData.put("user", user);
            debugData.put("totalReservationCount", totalCount);
            debugData.put("allReservations", allReservations);
            debugData.put("currentReservation", currentReservation);
            debugData.put("latestReservation", latestReservation);
            
            // 7. 详细记录每个预约
            for (int i = 0; i < allReservations.size(); i++) {
                UserProfileResponse.CurrentReservation res = allReservations.get(i);
                log.info("预约{}详情: ID={}, 学校={}, 房间={}, 座位={}, 状态={}, 开始={}, 结束={}", 
                    i+1, res.getReservationId(), res.getSchoolName(), res.getRoomName(), 
                    res.getSeatId(), res.getStatus(), res.getStartTime(), res.getEndTime());
            }
            
            return Result.success("调试数据获取成功", debugData);
            
        } catch (Exception e) {
            log.error("调试过程中发生错误", e);
            return Result.error("调试失败: " + e.getMessage());
        }
    }
} 