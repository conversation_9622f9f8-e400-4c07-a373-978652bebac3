package com.seatmaster.controller;

import com.seatmaster.service.ExecutionLogQueryService;
import com.seatmaster.dto.ExecutionLogDTO;
import com.seatmaster.dto.ExecutionLogQueryDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 执行日志控制器
 * 提供学习通API执行历史的查询接口
 */
@RestController
@RequestMapping("/api/execution-logs")
@CrossOrigin(origins = "*")
public class ExecutionLogController {

    @Autowired
    private ExecutionLogQueryService executionLogQueryService;

    /**
     * 获取用户的执行历史
     * GET /api/execution-logs/user/{userIdOrUsername}
     */
    @GetMapping("/user/{userIdOrUsername}")
    public ResponseEntity<Map<String, Object>> getUserExecutionLogs(
            @PathVariable String userIdOrUsername,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        try {
            System.out.println("=== ExecutionLogController.getUserExecutionLogs ===");
            System.out.println("userIdOrUsername: " + userIdOrUsername);
            System.out.println("page: " + page + ", size: " + size);

            // 检查是否为纯数字且长度合理（用户ID通常较短，手机号等长数字当作用户名）
            Long userId = null;
            String username = null;

            // 如果是纯数字且长度小于等于6位，认为是用户ID；否则认为是用户名（包括手机号）
            if (userIdOrUsername.matches("\\d+") && userIdOrUsername.length() <= 6) {
                try {
                    userId = Long.parseLong(userIdOrUsername);
                    System.out.println("解析为用户ID: " + userId);
                } catch (NumberFormatException e) {
                    username = userIdOrUsername;
                    System.out.println("解析为用户名（数字解析失败）: " + username);
                }
            } else {
                username = userIdOrUsername;
                System.out.println("解析为用户名: " + username);
            }

            ExecutionLogQueryDTO queryDTO = new ExecutionLogQueryDTO();
            queryDTO.setUserId(userId);
            queryDTO.setUsername(username);
            queryDTO.setPage(page);
            queryDTO.setSize(size);
            queryDTO.setStatus(status);
            queryDTO.setStartDate(startDate);
            queryDTO.setEndDate(endDate);

            Map<String, Object> result = executionLogQueryService.getUserExecutionLogs(queryDTO);
            return ResponseEntity.ok(result);

        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(Map.of("error", "查询执行历史失败: " + e.getMessage()));
        }
    }





    /**
     * 导出用户执行日志
     * GET /api/execution-logs/export/{userId}
     */
    @GetMapping("/export/{userId}")
    public ResponseEntity<Map<String, Object>> exportUserLogs(
            @PathVariable Long userId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        try {
            String filePath = executionLogQueryService.exportUserLogs(userId, startDate, endDate);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "日志导出成功",
                "filePath", filePath
            ));

        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                .body(Map.of("error", "导出日志失败: " + e.getMessage()));
        }
    }

    /**
     * 获取最近的执行日志（用于实时监控）
     * GET /api/execution-logs/recent/{userId}
     */
    @GetMapping("/recent/{userId}")
    public ResponseEntity<List<ExecutionLogDTO>> getRecentExecutionLogs(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "10") int limit) {

        try {
            List<ExecutionLogDTO> logs = executionLogQueryService.getRecentExecutionLogs(userId, limit);
            return ResponseEntity.ok(logs);
            
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }
}
