package com.seatmaster.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.seatmaster.common.Result;
import com.seatmaster.dto.GenerateCodeRequest;
import com.seatmaster.dto.GenerateCodeResponse;
import com.seatmaster.entity.RedemptionCode;
import com.seatmaster.entity.User;
import com.seatmaster.service.RedemptionService;
import com.seatmaster.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 管理员端兑换码控制器
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/redemption")
@RequiredArgsConstructor
@CrossOrigin(origins = "http://localhost:3000")
public class AdminRedemptionController {
    
    private final RedemptionService redemptionService;
    private final UserService userService;
    
    /**
     * 生成兑换码
     */
    @PostMapping("/generate")
    public Result<GenerateCodeResponse> generateCodes(
            @Valid @RequestBody GenerateCodeRequest request,
            Authentication authentication) {
        try {
            String username = authentication.getName();
            User admin = userService.findByUsername(username);
            
            if (admin == null) {
                return Result.error("用户不存在");
            }
            
            // 检查管理员权限
            if (!User.Role.ADMIN.equals(admin.getRole())) {
                return Result.error("权限不足，只有管理员可以生成兑换码");
            }
            
            GenerateCodeResponse response = redemptionService.generateCodes(request, admin.getId());
            
            return Result.success("成功生成" + response.getTotalGenerated() + "个兑换码", response);
            
        } catch (Exception e) {
            log.error("生成兑换码失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取兑换码列表
     */
    @GetMapping("/codes")
    public Result<IPage<RedemptionCode>> getCodesList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) Boolean isUsed,
            @RequestParam(required = false) String batchId,
            Authentication authentication) {
        try {
            String username = authentication.getName();
            User admin = userService.findByUsername(username);
            
            if (admin == null) {
                return Result.error("用户不存在");
            }
            
            // 检查管理员权限
            if (!User.Role.ADMIN.equals(admin.getRole())) {
                return Result.error("权限不足");
            }
            
            IPage<RedemptionCode> codes = redemptionService.getCodesList(page, size, isUsed, batchId);
            
            return Result.success("获取成功", codes);
            
        } catch (Exception e) {
            log.error("获取兑换码列表失败", e);
            return Result.error("获取兑换码列表失败");
        }
    }
    
    /**
     * 删除兑换码
     */
    @DeleteMapping("/codes/{codeId}")
    public Result<String> deleteCode(
            @PathVariable Long codeId,
            Authentication authentication) {
        try {
            String username = authentication.getName();
            User admin = userService.findByUsername(username);
            
            if (admin == null) {
                return Result.error("用户不存在");
            }
            
            // 检查管理员权限
            if (!User.Role.ADMIN.equals(admin.getRole())) {
                return Result.error("权限不足");
            }
            
            boolean success = redemptionService.deleteCode(codeId);
            
            if (success) {
                return Result.success("兑换码删除成功", null);
            } else {
                return Result.error("兑换码删除失败");
            }
            
        } catch (Exception e) {
            log.error("删除兑换码失败", e);
            return Result.error(e.getMessage());
        }
    }
}
