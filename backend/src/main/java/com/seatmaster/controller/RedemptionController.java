package com.seatmaster.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.seatmaster.common.Result;
import com.seatmaster.dto.RedeemCodeRequest;
import com.seatmaster.dto.RedeemCodeResponse;
import com.seatmaster.dto.RedemptionHistoryResponse;
import com.seatmaster.entity.User;
import com.seatmaster.service.RedemptionService;
import com.seatmaster.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 用户端兑换码控制器
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@RestController
@RequestMapping("/api/user/redemption")
@RequiredArgsConstructor
@CrossOrigin(origins = "http://localhost:3000")
public class RedemptionController {
    
    private final RedemptionService redemptionService;
    private final UserService userService;
    
    /**
     * 兑换码兑换
     */
    @PostMapping("/redeem")
    public Result<RedeemCodeResponse> redeemCode(
            @Valid @RequestBody RedeemCodeRequest request,
            Authentication authentication,
            HttpServletRequest httpRequest) {
        try {
            String username = authentication.getName();
            User user = userService.findByUsername(username);
            
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            // 获取用户IP和User-Agent
            String userIp = getClientIpAddress(httpRequest);
            String userAgent = httpRequest.getHeader("User-Agent");
            
            RedeemCodeResponse response = redemptionService.redeemCode(
                    request.getCode(), user.getId(), userIp, userAgent);
            
            return Result.success("兑换成功，已为您增加" + response.getDaysAdded() + "天使用时间", response);
            
        } catch (Exception e) {
            log.error("兑换码兑换失败", e);
            return Result.error(e.getMessage());
        }
    }
    
    /**
     * 获取兑换历史
     */
    @GetMapping("/history")
    public Result<IPage<RedemptionHistoryResponse>> getRedemptionHistory(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            Authentication authentication) {
        try {
            String username = authentication.getName();
            User user = userService.findByUsername(username);
            
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            IPage<RedemptionHistoryResponse> history = redemptionService.getUserRedemptionHistory(
                    user.getId(), page, size);
            
            return Result.success("获取成功", history);
            
        } catch (Exception e) {
            log.error("获取兑换历史失败", e);
            return Result.error("获取兑换历史失败");
        }
    }
    
    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
