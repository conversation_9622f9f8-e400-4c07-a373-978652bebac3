package com.seatmaster.controller;

import com.seatmaster.common.Result;
import com.seatmaster.entity.Room;
import com.seatmaster.entity.School;
import com.seatmaster.service.RoomService;
import com.seatmaster.service.SchoolService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/admin/room-management")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
@PreAuthorize("hasRole('ADMIN')")
public class AdminRoomController {
    
    private final SchoolService schoolService;
    private final RoomService roomService;
    
    // ==================== 学校管理 ====================
    
    /**
     * 获取所有学校列表
     */
    @GetMapping("/schools")
    public Result<List<School>> getAllSchools() {
        try {
            List<School> schools = schoolService.getAllSchools();
            return Result.success("获取学校列表成功", schools);
        } catch (Exception e) {
            log.error("获取学校列表失败", e);
            return Result.error("获取学校列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建学校
     */
    @PostMapping("/schools")
    public Result<School> createSchool(@Valid @RequestBody School school) {
        try {
            School createdSchool = schoolService.createSchool(school);
            log.info("创建学校成功: {}", createdSchool.getName());
            return Result.success("创建学校成功", createdSchool);
        } catch (Exception e) {
            log.error("创建学校失败", e);
            return Result.error("创建学校失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新学校
     */
    @PutMapping("/schools/{id}")
    public Result<School> updateSchool(@PathVariable Long id, @Valid @RequestBody School school) {
        try {
            school.setId(id);
            School updatedSchool = schoolService.updateSchool(school);
            log.info("更新学校成功: {}", updatedSchool.getName());
            return Result.success("更新学校成功", updatedSchool);
        } catch (Exception e) {
            log.error("更新学校失败", e);
            return Result.error("更新学校失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除学校
     */
    @DeleteMapping("/schools/{id}")
    public Result<?> deleteSchool(@PathVariable Long id) {
        try {
            boolean deleted = schoolService.deleteSchool(id);
            if (deleted) {
                log.info("删除学校成功: ID={}", id);
                return Result.success("删除学校成功", null);
            } else {
                return Result.error("删除学校失败");
            }
        } catch (Exception e) {
            log.error("删除学校失败", e);
            return Result.error("删除学校失败: " + e.getMessage());
        }
    }
    
    // ==================== 房间管理 ====================

    /**
     * 测试接口
     */
    @GetMapping("/test")
    public Result<String> test() {
        return Result.success("房间管理控制器工作正常", "test");
    }

    /**
     * 获取所有房间列表
     */
    @GetMapping("/rooms")
    public Result<List<Room>> getAllRooms() {
        try {
            log.info("开始获取所有房间列表");
            List<Room> rooms = roomService.getAllRooms();
            log.info("成功获取{}个房间", rooms.size());
            return Result.success("获取房间列表成功", rooms);
        } catch (Exception e) {
            log.error("获取房间列表失败", e);
            return Result.error("获取房间列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据学校ID获取房间列表
     */
    @GetMapping("/schools/{schoolId}/rooms")
    public Result<List<Room>> getRoomsBySchoolId(@PathVariable Long schoolId) {
        try {
            List<Room> rooms = roomService.getRoomsBySchoolId(schoolId);
            return Result.success("获取房间列表成功", rooms);
        } catch (Exception e) {
            log.error("获取房间列表失败", e);
            return Result.error("获取房间列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取房间详情
     */
    @GetMapping("/rooms/{id}")
    public Result<Room> getRoomById(@PathVariable Long id) {
        try {
            Room room = roomService.getRoomById(id);
            if (room != null) {
                return Result.success("获取房间详情成功", room);
            } else {
                return Result.error("房间不存在");
            }
        } catch (Exception e) {
            log.error("获取房间详情失败", e);
            return Result.error("获取房间详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建房间
     */
    @PostMapping("/rooms")
    public Result<Room> createRoom(@Valid @RequestBody Room room) {
        try {
            Room createdRoom = roomService.createRoom(room);
            log.info("创建房间成功: {}", createdRoom.getName());
            return Result.success("创建房间成功", createdRoom);
        } catch (Exception e) {
            log.error("创建房间失败", e);
            return Result.error("创建房间失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新房间
     */
    @PutMapping("/rooms/{id}")
    public Result<Room> updateRoom(@PathVariable Long id, @Valid @RequestBody Room room) {
        try {
            room.setId(id);
            Room updatedRoom = roomService.updateRoom(room);
            log.info("更新房间成功: {}", updatedRoom.getName());
            return Result.success("更新房间成功", updatedRoom);
        } catch (Exception e) {
            log.error("更新房间失败", e);
            return Result.error("更新房间失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除房间
     */
    @DeleteMapping("/rooms/{id}")
    public Result<?> deleteRoom(@PathVariable Long id) {
        try {
            boolean deleted = roomService.deleteRoom(id);
            if (deleted) {
                log.info("删除房间成功: ID={}", id);
                return Result.success("删除房间成功", null);
            } else {
                return Result.error("删除房间失败");
            }
        } catch (Exception e) {
            log.error("删除房间失败", e);
            return Result.error("删除房间失败: " + e.getMessage());
        }
    }
}
