package com.seatmaster.controller;

import com.seatmaster.common.Result;
import com.seatmaster.dto.AnnouncementDTO;
import com.seatmaster.entity.User;
import com.seatmaster.service.AnnouncementService;
import com.seatmaster.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户公告控制器
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@RestController
@RequestMapping("/api/user/announcements")
@RequiredArgsConstructor
@CrossOrigin(origins = "http://localhost:3000")
public class UserAnnouncementController {
    
    private final AnnouncementService announcementService;
    private final UserService userService;
    
    /**
     * 获取活跃的公告列表
     */
    @GetMapping
    public Result<List<AnnouncementDTO>> getActiveAnnouncements(Authentication authentication) {
        try {
            String username = authentication.getName();
            User user = userService.findByUsername(username);
            
            List<AnnouncementDTO> announcements = announcementService.getActiveAnnouncements(user.getRole().name());
            return Result.success("获取公告列表成功", announcements);
        } catch (Exception e) {
            log.error("获取公告列表失败", e);
            return Result.error("获取公告列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取未读的弹窗公告
     */
    @GetMapping("/unread-popup")
    public Result<List<AnnouncementDTO>> getUnreadPopupAnnouncements(Authentication authentication) {
        try {
            String username = authentication.getName();
            User user = userService.findByUsername(username);
            
            List<AnnouncementDTO> announcements = announcementService.getUnreadPopupAnnouncements(
                user.getId(), user.getRole().name());
            return Result.success("获取未读公告成功", announcements);
        } catch (Exception e) {
            log.error("获取未读公告失败", e);
            return Result.error("获取未读公告失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取公告详情
     */
    @GetMapping("/{id}")
    public Result<AnnouncementDTO> getAnnouncement(
            @PathVariable Long id,
            Authentication authentication) {
        try {
            String username = authentication.getName();
            User user = userService.findByUsername(username);
            
            AnnouncementDTO announcement = announcementService.getAnnouncementById(id);
            
            // 自动标记为已读
            announcementService.markAnnouncementAsRead(user.getId(), id);
            
            return Result.success("获取公告详情成功", announcement);
        } catch (Exception e) {
            log.error("获取公告详情失败: id={}", id, e);
            return Result.error("获取公告详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 标记公告为已读
     */
    @PostMapping("/{id}/mark-read")
    public Result<String> markAnnouncementAsRead(
            @PathVariable Long id,
            Authentication authentication) {
        try {
            String username = authentication.getName();
            User user = userService.findByUsername(username);
            
            boolean success = announcementService.markAnnouncementAsRead(user.getId(), id);
            if (success) {
                return Result.success("标记已读成功");
            } else {
                return Result.error("标记已读失败");
            }
        } catch (Exception e) {
            log.error("标记公告已读失败: id={}", id, e);
            return Result.error("标记已读失败: " + e.getMessage());
        }
    }
    
    /**
     * 关闭公告弹窗
     */
    @PostMapping("/{id}/dismiss")
    public Result<String> dismissAnnouncementPopup(
            @PathVariable Long id,
            Authentication authentication) {
        try {
            String username = authentication.getName();
            User user = userService.findByUsername(username);
            
            boolean success = announcementService.dismissAnnouncementPopup(user.getId(), id);
            if (success) {
                return Result.success("关闭弹窗成功");
            } else {
                return Result.error("关闭弹窗失败");
            }
        } catch (Exception e) {
            log.error("关闭公告弹窗失败: id={}", id, e);
            return Result.error("关闭弹窗失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量标记公告为已读
     */
    @PostMapping("/batch-mark-read")
    public Result<String> batchMarkAnnouncementsAsRead(
            @RequestBody Map<String, List<Long>> request,
            Authentication authentication) {
        try {
            String username = authentication.getName();
            User user = userService.findByUsername(username);
            
            List<Long> announcementIds = request.get("announcementIds");
            if (announcementIds == null || announcementIds.isEmpty()) {
                return Result.error("公告ID列表不能为空");
            }
            
            int successCount = 0;
            for (Long announcementId : announcementIds) {
                try {
                    if (announcementService.markAnnouncementAsRead(user.getId(), announcementId)) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("标记公告已读失败: announcementId={}", announcementId, e);
                }
            }
            
            return Result.success("批量标记已读成功，共处理 " + successCount + " 条公告");
        } catch (Exception e) {
            log.error("批量标记公告已读失败", e);
            return Result.error("批量标记已读失败: " + e.getMessage());
        }
    }
}
