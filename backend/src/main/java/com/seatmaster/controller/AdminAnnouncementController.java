package com.seatmaster.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.seatmaster.common.Result;
import com.seatmaster.dto.AnnouncementDTO;
import com.seatmaster.service.AnnouncementService;
import com.seatmaster.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 管理员公告管理控制器
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/announcements")
@RequiredArgsConstructor
@CrossOrigin(origins = "http://localhost:3000")
public class AdminAnnouncementController {
    
    private final AnnouncementService announcementService;
    private final UserService userService;
    
    /**
     * 创建公告
     */
    @PostMapping
    public Result<AnnouncementDTO> createAnnouncement(
            @Valid @RequestBody AnnouncementDTO.CreateRequest request,
            Authentication authentication) {
        try {
            String username = authentication.getName();
            Long userId = userService.findByUsername(username).getId();
            
            AnnouncementDTO announcement = announcementService.createAnnouncement(request, userId);
            return Result.success("公告创建成功", announcement);
        } catch (Exception e) {
            log.error("创建公告失败", e);
            return Result.error("创建公告失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新公告
     */
    @PutMapping("/{id}")
    public Result<AnnouncementDTO> updateAnnouncement(
            @PathVariable Long id,
            @Valid @RequestBody AnnouncementDTO.UpdateRequest request) {
        try {
            AnnouncementDTO announcement = announcementService.updateAnnouncement(id, request);
            return Result.success("公告更新成功", announcement);
        } catch (Exception e) {
            log.error("更新公告失败: id={}", id, e);
            return Result.error("更新公告失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除公告
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteAnnouncement(@PathVariable Long id) {
        try {
            boolean success = announcementService.deleteAnnouncement(id);
            if (success) {
                return Result.success("公告删除成功");
            } else {
                return Result.error("公告删除失败");
            }
        } catch (Exception e) {
            log.error("删除公告失败: id={}", id, e);
            return Result.error("删除公告失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取公告详情
     */
    @GetMapping("/{id}")
    public Result<AnnouncementDTO> getAnnouncement(@PathVariable Long id) {
        try {
            AnnouncementDTO announcement = announcementService.getAnnouncementById(id);
            return Result.success("获取公告详情成功", announcement);
        } catch (Exception e) {
            log.error("获取公告详情失败: id={}", id, e);
            return Result.error("获取公告详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 分页查询公告
     */
    @GetMapping
    public Result<IPage<AnnouncementDTO>> getAnnouncementPage(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) Boolean enabled,
            @RequestParam(required = false) String targetUsers,
            @RequestParam(defaultValue = "priority") String sortBy,
            @RequestParam(defaultValue = "desc") String sortOrder) {
        try {
            AnnouncementDTO.QueryRequest query = new AnnouncementDTO.QueryRequest();
            query.setPage(page);
            query.setSize(size);
            query.setTitle(title);
            query.setEnabled(enabled);
            query.setSortBy(sortBy);
            query.setSortOrder(sortOrder);
            
            if (targetUsers != null && !targetUsers.isEmpty()) {
                try {
                    query.setTargetUsers(com.seatmaster.entity.Announcement.TargetUsers.valueOf(targetUsers));
                } catch (IllegalArgumentException e) {
                    return Result.error("无效的目标用户类型: " + targetUsers);
                }
            }
            
            IPage<AnnouncementDTO> result = announcementService.getAnnouncementPage(query);
            return Result.success("获取公告列表成功", result);
        } catch (Exception e) {
            log.error("获取公告列表失败", e);
            return Result.error("获取公告列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 切换公告启用状态
     */
    @PutMapping("/{id}/toggle-status")
    public Result<String> toggleAnnouncementStatus(
            @PathVariable Long id,
            @RequestBody Map<String, Boolean> request) {
        try {
            Boolean enabled = request.get("enabled");
            if (enabled == null) {
                return Result.error("缺少enabled参数");
            }
            
            boolean success = announcementService.toggleAnnouncementStatus(id, enabled);
            if (success) {
                return Result.success("公告状态更新成功");
            } else {
                return Result.error("公告状态更新失败");
            }
        } catch (Exception e) {
            log.error("切换公告状态失败: id={}", id, e);
            return Result.error("切换公告状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量删除公告
     */
    @DeleteMapping("/batch")
    public Result<String> batchDeleteAnnouncements(@RequestBody List<Long> ids) {
        try {
            int deletedCount = announcementService.batchDeleteAnnouncements(ids);
            return Result.success("批量删除成功，共删除 " + deletedCount + " 条公告");
        } catch (Exception e) {
            log.error("批量删除公告失败: ids={}", ids, e);
            return Result.error("批量删除公告失败: " + e.getMessage());
        }
    }
}
