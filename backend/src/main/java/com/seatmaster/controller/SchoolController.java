package com.seatmaster.controller;

import com.seatmaster.common.Result;
import com.seatmaster.entity.School;
import com.seatmaster.entity.Room;
import com.seatmaster.service.SchoolService;
import com.seatmaster.mapper.RoomMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/schools")
@RequiredArgsConstructor
@CrossOrigin(origins = "http://localhost:3000")
public class SchoolController {
    
    private final SchoolService schoolService;
    private final RoomMapper roomMapper;
    
    /**
     * 获取所有学校列表
     */
    @GetMapping
    public Result<List<School>> getAllSchools() {
        try {
            List<School> schools = schoolService.getAllSchools();
            return Result.success("获取学校列表成功", schools);
        } catch (Exception e) {
            return Result.error("获取学校列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取学校
     */
    @GetMapping("/{id}")
    public Result<School> getSchoolById(@PathVariable Long id) {
        try {
            School school = schoolService.getSchoolById(id);
            if (school != null) {
                return Result.success("获取学校信息成功", school);
            } else {
                return Result.error("学校不存在");
            }
        } catch (Exception e) {
            return Result.error("获取学校信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据学校ID获取房间列表
     */
    @GetMapping("/{schoolId}/rooms")
    public Result<List<Room>> getRoomsBySchoolId(@PathVariable Long schoolId) {
        try {
            List<Room> rooms = roomMapper.findBySchoolId(schoolId);
            return Result.success("获取房间列表成功", rooms);
        } catch (Exception e) {
            return Result.error("获取房间列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建学校
     */
    @PostMapping
    public Result<School> createSchool(@RequestBody School school) {
        try {
            School createdSchool = schoolService.createSchool(school);
            return Result.success("创建学校成功", createdSchool);
        } catch (Exception e) {
            return Result.error("创建学校失败: " + e.getMessage());
        }
    }

    /**
     * 更新学校
     */
    @PutMapping("/{id}")
    public Result<School> updateSchool(@PathVariable Long id, @RequestBody School school) {
        try {
            school.setId(id);
            School updatedSchool = schoolService.updateSchool(school);
            return Result.success("更新学校成功", updatedSchool);
        } catch (Exception e) {
            return Result.error("更新学校失败: " + e.getMessage());
        }
    }

    /**
     * 删除学校
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteSchool(@PathVariable Long id) {
        try {
            if (schoolService.deleteSchool(id)) {
                return Result.success("删除学校成功", null);
            } else {
                return Result.error("删除学校失败");
            }
        } catch (Exception e) {
            return Result.error("删除学校失败: " + e.getMessage());
        }
    }
}