package com.seatmaster.controller;

import com.seatmaster.service.DistributedTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 调度器测试控制器
 * 用于手动触发定时任务和验证系统状态
 */
@Slf4j
@RestController
@RequestMapping("/api/scheduler-test")
@RequiredArgsConstructor
public class SchedulerTestController {

    private final DistributedTaskService distributedTaskService;
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 手动触发自动任务分配
     */
    @PostMapping("/trigger-auto-assign")
    public Map<String, Object> triggerAutoAssign() {
        String currentTime = LocalDateTime.now().format(TIME_FORMATTER);
        log.info("🔧【手动触发】{} - 开始手动执行自动任务分配", currentTime);
        
        Map<String, Object> response = new HashMap<>();
        long startTime = System.currentTimeMillis();
        
        try {
            Map<String, Object> result = distributedTaskService.autoAssignPendingTasks();
            long duration = System.currentTimeMillis() - startTime;
            
            response.put("success", true);
            response.put("triggerTime", currentTime);
            response.put("duration", duration + "ms");
            response.put("result", result);
            
            log.info("✅【手动触发完成】执行耗时: {}ms, 结果: {}", duration, result);
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            response.put("success", false);
            response.put("triggerTime", currentTime);
            response.put("duration", duration + "ms");
            response.put("error", e.getMessage());
            
            log.error("❌【手动触发失败】执行耗时: {}ms", duration, e);
        }
        
        return response;
    }

    /**
     * 获取系统状态
     */
    @GetMapping("/system-status")
    public Map<String, Object> getSystemStatus() {
        Map<String, Object> status = new HashMap<>();
        String currentTime = LocalDateTime.now().format(TIME_FORMATTER);
        
        try {
            // 获取当前时间信息
            status.put("currentTime", currentTime);
            status.put("currentTimeHHmm", LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm")));
            
            // 获取可用Worker信息
            status.put("availableWorkers", distributedTaskService.getAvailableWorkers());
            
            // 获取Worker状态概览
            status.put("workerStatusOverview", distributedTaskService.getWorkerStatusOverview());
            
            // 获取任务统计
            status.put("taskStatistics", distributedTaskService.getTaskStatistics());
            
            status.put("success", true);
            
        } catch (Exception e) {
            status.put("success", false);
            status.put("error", e.getMessage());
            log.error("获取系统状态失败", e);
        }
        
        return status;
    }

    /**
     * 测试特定预约记录的状态
     */
    @GetMapping("/test-reservation/{id}")
    public Map<String, Object> testReservation(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        String currentTime = LocalDateTime.now().format(TIME_FORMATTER);
        
        try {
            result.put("checkTime", currentTime);
            result.put("reservationId", id);
            
            // 获取任务详情
            result.put("taskDetail", distributedTaskService.getTaskDetail(id));
            
            result.put("success", true);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            log.error("测试预约记录失败: id={}", id, e);
        }
        
        return result;
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", LocalDateTime.now().format(TIME_FORMATTER));
        health.put("scheduler", "ENABLED");
        health.put("message", "调度器测试控制器运行正常");
        return health;
    }
}