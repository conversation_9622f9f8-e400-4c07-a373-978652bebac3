package com.seatmaster.controller;

import com.seatmaster.dto.ExecutionLogDTO;
import com.seatmaster.dto.ExecutionLogQueryDTO;
import com.seatmaster.entity.School;
import com.seatmaster.service.AdminExecutionLogService;
import com.seatmaster.service.SchoolService;
import com.seatmaster.common.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


import java.util.List;
import java.util.Map;

/**
 * 管理员执行日志管理控制器
 * 仅管理员可访问，提供完整的技术调试信息
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/execution-logs")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
@PreAuthorize("hasRole('ADMIN')")
public class AdminExecutionLogController {

    private final AdminExecutionLogService adminExecutionLogService;
    private final SchoolService schoolService;

    /**
     * 获取所有用户的执行日志（管理员视图）
     * GET /api/admin/execution-logs
     */
    @GetMapping
    public Result<Map<String, Object>> getAllExecutionLogs(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String roomid,
            @RequestParam(required = false) String seatid,
            @RequestParam(required = false) Long schoolId) {
        
        try {
            ExecutionLogQueryDTO queryDTO = new ExecutionLogQueryDTO();
            queryDTO.setPage(page);
            queryDTO.setSize(size);
            queryDTO.setStatus(status);
            queryDTO.setUsername(username);
            queryDTO.setStartDate(startDate);
            queryDTO.setEndDate(endDate);
            queryDTO.setRoomid(roomid);
            queryDTO.setSeatid(seatid);
            queryDTO.setSchoolId(schoolId);

            Map<String, Object> result = adminExecutionLogService.getAllExecutionLogs(queryDTO);
            return Result.success("获取执行日志成功", result);
            
        } catch (Exception e) {
            log.error("获取执行日志失败", e);
            return Result.error("获取执行日志失败: " + e.getMessage());
        }
    }

    /**
     * 获取执行统计信息（管理员视图）
     * GET /api/admin/execution-logs/stats
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getExecutionStats(
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        try {
            Map<String, Object> stats = adminExecutionLogService.getExecutionStats(username, startDate, endDate);
            return Result.success("获取统计信息成功", stats);
            
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户执行日志详情（管理员视图）
     * GET /api/admin/execution-logs/user/{username}
     */
    @GetMapping("/user/{username}")
    public Result<Map<String, Object>> getUserExecutionLogs(
            @PathVariable String username,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        try {
            ExecutionLogQueryDTO queryDTO = new ExecutionLogQueryDTO();
            queryDTO.setUsername(username);
            queryDTO.setPage(page);
            queryDTO.setSize(size);
            queryDTO.setStatus(status);
            queryDTO.setStartDate(startDate);
            queryDTO.setEndDate(endDate);

            Map<String, Object> result = adminExecutionLogService.getUserExecutionLogs(queryDTO);
            return Result.success("获取用户执行日志成功", result);
            
        } catch (Exception e) {
            log.error("获取用户执行日志失败", e);
            return Result.error("获取用户执行日志失败: " + e.getMessage());
        }
    }

    /**
     * 获取执行日志详情
     * GET /api/admin/execution-logs/{logId}
     */
    @GetMapping("/{logId}")
    public Result<ExecutionLogDTO> getExecutionLogDetail(@PathVariable Long logId) {
        try {
            ExecutionLogDTO logDetail = adminExecutionLogService.getExecutionLogDetail(logId);
            if (logDetail != null) {
                return Result.success("获取日志详情成功", logDetail);
            } else {
                return Result.error("日志不存在");
            }
            
        } catch (Exception e) {
            log.error("获取日志详情失败", e);
            return Result.error("获取日志详情失败: " + e.getMessage());
        }
    }

    /**
     * 导出执行日志
     * GET /api/admin/execution-logs/export
     */
    @GetMapping("/export")
    public Result<String> exportExecutionLogs(
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "csv") String format) {
        
        try {
            String filePath = adminExecutionLogService.exportExecutionLogs(username, status, startDate, endDate, format);
            return Result.success("导出成功", filePath);
            
        } catch (Exception e) {
            log.error("导出执行日志失败", e);
            return Result.error("导出失败: " + e.getMessage());
        }
    }

    /**
     * 获取执行日志概览统计
     * GET /api/admin/execution-logs/overview
     */
    @GetMapping("/overview")
    public Result<Map<String, Object>> getExecutionOverview() {
        try {
            Map<String, Object> overview = adminExecutionLogService.getExecutionOverview();
            return Result.success("获取概览统计成功", overview);
            
        } catch (Exception e) {
            log.error("获取概览统计失败", e);
            return Result.error("获取概览统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取最近的执行趋势
     * GET /api/admin/execution-logs/trends
     */
    @GetMapping("/trends")
    public Result<List<Map<String, Object>>> getExecutionTrends(
            @RequestParam(defaultValue = "7") int days) {

        try {
            List<Map<String, Object>> trends = adminExecutionLogService.getExecutionTrends(days);
            return Result.success("获取执行趋势成功", trends);

        } catch (Exception e) {
            log.error("获取执行趋势失败", e);
            return Result.error("获取执行趋势失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户概览列表（用户视图模式）
     * GET /api/admin/execution-logs/users-overview
     */
    @GetMapping("/users-overview")
    public Result<Map<String, Object>> getUsersOverview(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String searchUsername,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "totalExecutions") String sortBy,
            @RequestParam(defaultValue = "desc") String sortOrder,
            @RequestParam(required = false) Long schoolId) {

        try {
            Map<String, Object> result = adminExecutionLogService.getUsersOverview(
                page, size, searchUsername, startDate, endDate, sortBy, sortOrder, schoolId);
            return Result.success("获取用户概览成功", result);

        } catch (Exception e) {
            log.error("获取用户概览失败", e);
            return Result.error("获取用户概览失败: " + e.getMessage());
        }
    }

    /**
     * 获取特定用户的最近执行记录（用户视图展开详情）
     * GET /api/admin/execution-logs/user/{username}/recent
     */
    @GetMapping("/user/{username}/recent")
    public Result<List<ExecutionLogDTO>> getUserRecentLogs(
            @PathVariable String username,
            @RequestParam(defaultValue = "15") int limit,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        try {
            List<ExecutionLogDTO> logs = adminExecutionLogService.getUserRecentLogs(
                username, limit, startDate, endDate);
            return Result.success("获取用户最近记录成功", logs);

        } catch (Exception e) {
            log.error("获取用户最近记录失败", e);
            return Result.error("获取用户最近记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取学校列表（用于筛选下拉选择）
     * GET /api/admin/execution-logs/schools
     */
    @GetMapping("/schools")
    public Result<List<School>> getSchools() {
        try {
            List<School> schools = schoolService.getAllSchools();
            return Result.success("获取学校列表成功", schools);
        } catch (Exception e) {
            log.error("获取学校列表失败", e);
            return Result.error("获取学校列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取学校概览列表（学校视图模式）
     * GET /api/admin/execution-logs/schools-overview
     *
     * @param page 页码，默认1
     * @param size 每页大小，默认20
     * @param searchSchoolName 学校名称搜索关键词（可选）
     * @param startDate 开始日期，格式yyyy-MM-dd（可选）
     * @param endDate 结束日期，格式yyyy-MM-dd（可选）
     * @param sortBy 排序字段，默认totalExecutions（可选值：totalExecutions, successRate, avgDurationMs, todayExecutions, todaySuccessRate）
     * @param sortOrder 排序方向，默认desc（可选值：asc, desc）
     * @return 学校概览统计数据，包含学校名称、总成功率、总执行次数、平均耗时、今日执行次数、今日成功率等
     */
    @GetMapping("/schools-overview")
    public Result<Map<String, Object>> getSchoolsOverview(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String searchSchoolName,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "totalExecutions") String sortBy,
            @RequestParam(defaultValue = "desc") String sortOrder) {

        try {
            // 参数验证
            if (page < 1) {
                return Result.error("页码必须大于0");
            }
            if (size < 1 || size > 100) {
                return Result.error("每页大小必须在1-100之间");
            }

            Map<String, Object> result = adminExecutionLogService.getSchoolsOverview(
                page, size, searchSchoolName, startDate, endDate, sortBy, sortOrder);
            return Result.success("获取学校概览成功", result);

        } catch (Exception e) {
            log.error("获取学校概览失败", e);
            return Result.error("获取学校概览失败: " + e.getMessage());
        }
    }

    /**
     * 获取特定学校的日期维度详细统计（学校视图展开详情）
     * GET /api/admin/execution-logs/school/{schoolId}/daily-stats
     *
     * @param schoolId 学校ID
     * @param startDate 开始日期，格式yyyy-MM-dd（可选，默认最近30天）
     * @param endDate 结束日期，格式yyyy-MM-dd（可选，默认今天）
     * @param page 页码，默认1
     * @param size 每页大小，默认30
     * @return 该学校按日期维度的详细统计数据，包含每日执行次数、成功率、平均耗时等
     */
    @GetMapping("/school/{schoolId}/daily-stats")
    public Result<Map<String, Object>> getSchoolDailyStats(
            @PathVariable Long schoolId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "30") int size) {

        try {
            // 参数验证
            if (schoolId == null || schoolId <= 0) {
                return Result.error("学校ID无效");
            }
            if (page < 1) {
                return Result.error("页码必须大于0");
            }
            if (size < 1 || size > 100) {
                return Result.error("每页大小必须在1-100之间");
            }

            Map<String, Object> result = adminExecutionLogService.getSchoolDailyStats(
                schoolId, startDate, endDate, page, size);
            return Result.success("获取学校日期统计成功", result);

        } catch (Exception e) {
            log.error("获取学校日期统计失败: schoolId={}", schoolId, e);
            return Result.error("获取学校日期统计失败: " + e.getMessage());
        }
    }
}
