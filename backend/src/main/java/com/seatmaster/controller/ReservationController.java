package com.seatmaster.controller;

import com.seatmaster.common.Result;
import com.seatmaster.entity.Reservation;
import com.seatmaster.entity.Room;
import com.seatmaster.entity.User;
import com.seatmaster.service.ReservationService;
import com.seatmaster.service.UserService;
import com.seatmaster.mapper.RoomMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@RestController
@RequestMapping("/api/reservations")
@CrossOrigin
public class ReservationController {
    
    @Autowired
    private ReservationService reservationService;
    
    @Autowired
    private RoomMapper roomMapper;
    
    @Autowired
    private UserService userService;
    
    /**
     * 智能解析时间字符串，支持多种格式
     * @param timeStr 时间字符串，可能是 HH:MM:SS, HH:MM 或 ISO 8601 格式
     * @return LocalTime对象
     */
    private LocalTime parseTimeString(String timeStr) {
        try {
            // 尝试直接解析为时间格式
            if (timeStr.matches("^\\d{2}:\\d{2}(:\\d{2})?$")) {
                return LocalTime.parse(timeStr);
            }
            
            // 尝试解析为ISO 8601日期时间格式，提取时间部分
            if (timeStr.contains("T")) {
                LocalDateTime dateTime = LocalDateTime.parse(timeStr);
                return dateTime.toLocalTime();
            }
            
            // 如果包含日期，尝试其他格式
            if (timeStr.contains("-") || timeStr.contains("/")) {
                // 可能的格式：yyyy-MM-dd HH:mm:ss 或类似
                try {
                    LocalDateTime dateTime = LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    return dateTime.toLocalTime();
                } catch (Exception e) {
                    // 继续尝试其他格式
                }
            }
            
            // 最后尝试直接解析
            return LocalTime.parse(timeStr);
            
        } catch (Exception e) {
            throw new RuntimeException("无法解析时间格式: " + timeStr + "。支持的格式: HH:MM, HH:MM:SS, 或 ISO 8601 (yyyy-MM-ddTHH:mm:ss)");
        }
    }
    
    /**
     * 创建或更新预约
     */
    @PostMapping("/create")
    public Result<?> createReservation(@RequestParam Long roomId,
                                     @RequestParam String seatId,
                                     @RequestParam String startTime,
                                     @RequestParam String endTime,
                                     @RequestParam(required = false, defaultValue = "") String reservationOpenTime,
                                     @RequestParam(required = false, defaultValue = "SAME_DAY") String reservationType,
                                     Authentication authentication) {
        try {
            // 获取当前用户
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);
            
            if (currentUser == null) {
                return Result.error("用户不存在");
            }
            
            // 智能解析时间，支持多种格式
            LocalTime start = parseTimeString(startTime);
            LocalTime end = parseTimeString(endTime);
            
            // 创建或更新预约
            Reservation reservation = reservationService.createOrUpdateReservation(
                currentUser.getId(), roomId, seatId, start, end, reservationOpenTime, reservationType);
                
            return Result.success("预约成功", reservation);
            
        } catch (Exception e) {
            return Result.error("预约失败: " + e.getMessage());
        }
    }

    /**
     * 更新预约
     */
    @PostMapping("/update")
    public Result<?> updateReservation(@RequestParam Long roomId,
                                     @RequestParam String seatId,
                                     @RequestParam String startTime,
                                     @RequestParam String endTime,
                                     @RequestParam(required = false, defaultValue = "") String reservationOpenTime,
                                     @RequestParam(required = false, defaultValue = "SAME_DAY") String reservationType,
                                     Authentication authentication) {
        try {
            // 获取当前用户
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);

            if (currentUser == null) {
                return Result.error("用户不存在");
            }

            // 智能解析时间，支持多种格式
            LocalTime start = parseTimeString(startTime);
            LocalTime end = parseTimeString(endTime);

            // 更新预约
            Reservation reservation = reservationService.updateReservation(
                currentUser.getId(), roomId, seatId, start, end, reservationOpenTime, reservationType);

            return Result.success("预约更新成功", reservation);

        } catch (Exception e) {
            return Result.error("预约更新失败: " + e.getMessage());
        }
    }

    /**
     * 取消预约
     */
    @PostMapping("/cancel/{reservationId}")
    public Result<?> cancelReservation(@PathVariable Long reservationId, Authentication authentication) {
        try {
            String username = authentication.getName();
            User currentUser = userService.findByUsername(username);
            
            if (currentUser == null) {
                return Result.error("用户不存在");
            }
            
            boolean success = reservationService.cancelReservation(reservationId, currentUser.getId());
            
            if (success) {
                return Result.success("取消预约成功");
            } else {
                return Result.error("取消预约失败");
            }
            
        } catch (Exception e) {
            return Result.error("取消预约失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取房间列表及可用座位信息
     */
    @GetMapping("/rooms")
    public Result<?> getRooms() {
        try {
            List<Room> rooms = roomMapper.findAllWithAvailableSeats();
            return Result.success("获取房间列表成功", rooms);
        } catch (Exception e) {
            return Result.error("获取房间列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取指定房间的可用座位数
     */
    @GetMapping("/rooms/{roomId}/available-seats")
    public Result<?> getAvailableSeats(@PathVariable Long roomId,
                                     @RequestParam String startTime,
                                     @RequestParam String endTime) {
        try {
            // 智能解析时间，支持多种格式
            LocalTime start = parseTimeString(startTime);
            LocalTime end = parseTimeString(endTime);
            
            int availableSeats = reservationService.getAvailableSeatsCount(roomId, start, end);
            
            return Result.success("获取可用座位数成功", availableSeats);
        } catch (Exception e) {
            return Result.error("获取可用座位数失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取房间活跃预约列表
     */
    @GetMapping("/rooms/{roomId}/active")
    public Result<?> getActiveReservations(@PathVariable Long roomId) {
        try {
            List<Reservation> reservations = reservationService.getActiveReservations(roomId);
            return Result.success("获取活跃预约列表成功", reservations);
        } catch (Exception e) {
            return Result.error("获取活跃预约列表失败: " + e.getMessage());
        }
    }
} 