package com.seatmaster.controller;

import com.seatmaster.common.Result;
import com.seatmaster.service.WorkerServerService;
import com.seatmaster.util.IpAddressUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * Worker 心跳控制器
 * 不需要认证，供 Worker 服务器注册和发送心跳使用
 */
@Slf4j
@RestController
@RequestMapping("/api/worker")
@RequiredArgsConstructor
@CrossOrigin(origins = "*")
@Validated
public class WorkerHeartbeatController {

    private final WorkerServerService workerServerService;
    private final IpAddressUtil ipAddressUtil;

    /**
     * Worker 心跳接口（无需认证）
     */
    @PostMapping("/heartbeat/{id}")
    public Result<String> heartbeat(@PathVariable @NotNull String id,
                                   @RequestBody Map<String, Object> heartbeatData,
                                   HttpServletRequest request) {
        try {
            // 获取真实客户端IP地址
            String clientIp = ipAddressUtil.getRealClientIp(request);
            log.debug("收到Worker心跳: id={}, status={}, clientIp={}",
                     id, heartbeatData.get("status"), clientIp);

            // 将IP信息传递给Service层
            workerServerService.updateHeartbeat(id, heartbeatData, clientIp);

            log.info("Worker心跳更新成功: id={}, 来源IP={}", id, clientIp);
            return Result.success("心跳更新成功");
        } catch (Exception e) {
            log.error("更新Worker心跳失败: id={}", id, e);
            return Result.error("心跳更新失败: " + e.getMessage());
        }
    }

    /**
     * Worker 注册接口（无需认证）
     */
    @PostMapping("/register")
    public Result<String> register(@RequestBody Map<String, Object> registerData,
                                  HttpServletRequest request) {
        try {
            String workerId = (String) registerData.get("workerId");
            if (workerId == null) {
                return Result.error("workerId 不能为空");
            }

            // 获取真实客户端IP地址
            String clientIp = ipAddressUtil.getRealClientIp(request);
            log.info("收到Worker注册请求: workerId={}, clientIp={}", workerId, clientIp);

            // 将真实IP传递给Service层进行注册
            workerServerService.autoRegisterWorker(workerId, registerData, clientIp);

            log.info("Worker注册成功: workerId={}, 注册IP={}", workerId, clientIp);
            return Result.success("注册成功");
        } catch (Exception e) {
            log.error("Worker注册失败: data={}", registerData, e);
            return Result.error("注册失败: " + e.getMessage());
        }
    }

    /**
     * Worker 注销接口（无需认证）
     */
    @PostMapping("/unregister/{id}")
    public Result<String> unregister(@PathVariable @NotNull String id) {
        try {
            workerServerService.unregisterWorker(id);
            log.info("Worker注销成功: id={}", id);
            return Result.success("注销成功");
        } catch (Exception e) {
            log.error("Worker注销失败: id={}", id, e);
            return Result.error("注销失败: " + e.getMessage());
        }
    }

    /**
     * Worker 健康检查接口（无需认证）
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        try {
            Map<String, Object> healthInfo = Map.of(
                "status", "UP",
                "timestamp", System.currentTimeMillis(),
                "service", "worker-heartbeat-controller"
            );
            return Result.success("健康检查通过", healthInfo);
        } catch (Exception e) {
            log.error("健康检查失败", e);
            return Result.error("健康检查失败: " + e.getMessage());
        }
    }
}
