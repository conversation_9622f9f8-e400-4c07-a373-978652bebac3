package com.seatmaster.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;

/**
 * IP检测配置属性类
 * 用于读取application.yml中的IP检测相关配置
 */
@Data
@Slf4j
@Component
@ConfigurationProperties(prefix = "seatmaster.ip-detection")
public class IpDetectionProperties {
    
    /**
     * 是否启用自动IP检测功能
     * 默认值：true
     */
    private boolean enabled = true;
    
    /**
     * 是否信任代理头部信息
     * 在代理环境下是否信任X-Forwarded-For等头部
     * 默认值：true
     */
    private boolean trustProxyHeaders = true;
    
    /**
     * 无法获取真实IP时是否使用Worker报告的host
     * 作为最后的fallback机制
     * 默认值：true
     */
    private boolean fallbackToReportedHost = true;
    
    /**
     * IP检测日志级别
     * 可选值：DEBUG, INFO, WARN, ERROR
     * 默认值：INFO
     */
    private String logLevel = "INFO";
    
    /**
     * 有效的日志级别列表
     */
    private static final List<String> VALID_LOG_LEVELS = Arrays.asList(
        "DEBUG", "INFO", "WARN", "ERROR"
    );
    
    /**
     * 配置初始化后的验证
     */
    @PostConstruct
    public void validateConfiguration() {
        // 验证日志级别的有效性
        if (logLevel != null && !VALID_LOG_LEVELS.contains(logLevel.toUpperCase())) {
            log.warn("无效的IP检测日志级别: {}，将使用默认值INFO。有效值: {}", 
                    logLevel, VALID_LOG_LEVELS);
            logLevel = "INFO";
        }
        
        // 标准化日志级别为大写
        if (logLevel != null) {
            logLevel = logLevel.toUpperCase();
        }
        
        // 记录配置加载情况
        log.info("IP检测配置加载完成: enabled={}, trustProxyHeaders={}, fallbackToReportedHost={}, logLevel={}", 
                enabled, trustProxyHeaders, fallbackToReportedHost, logLevel);
        
        // 配置合理性检查
        if (!enabled) {
            log.warn("IP自动检测功能已禁用，将使用Worker报告的host信息");
        }
        
        if (!trustProxyHeaders) {
            log.info("代理头部信任已禁用，将直接使用request.getRemoteAddr()");
        }
        
        if (!fallbackToReportedHost) {
            log.warn("Worker报告host的fallback机制已禁用，可能导致某些情况下无法获取有效IP");
        }
    }
    
    /**
     * 检查是否应该记录指定级别的日志
     * 
     * @param level 要检查的日志级别
     * @return true表示应该记录该级别的日志
     */
    public boolean shouldLog(String level) {
        if (level == null || logLevel == null) {
            return false;
        }
        
        List<String> levels = Arrays.asList("DEBUG", "INFO", "WARN", "ERROR");
        int currentLevelIndex = levels.indexOf(logLevel.toUpperCase());
        int checkLevelIndex = levels.indexOf(level.toUpperCase());
        
        return checkLevelIndex >= currentLevelIndex;
    }
    
    /**
     * 获取配置摘要信息（用于调试）
     * 
     * @return 配置摘要字符串
     */
    public String getConfigSummary() {
        return String.format(
            "IpDetectionConfig[enabled=%s, trustProxy=%s, fallback=%s, logLevel=%s]",
            enabled, trustProxyHeaders, fallbackToReportedHost, logLevel
        );
    }
}
