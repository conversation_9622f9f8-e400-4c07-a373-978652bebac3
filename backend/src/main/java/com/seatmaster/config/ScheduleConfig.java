package com.seatmaster.config;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.seatmaster.entity.User;
import com.seatmaster.mapper.UserMapper;
import com.seatmaster.service.impl.ReservationServiceImpl;
import com.seatmaster.dto.WorkerServerDTO;
import com.seatmaster.service.DistributedTaskService;
import com.seatmaster.service.WorkerServerService;
import com.seatmaster.util.ApplicationContextHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.List;
import java.util.Map;

/**
 * 定时任务配置类
 * 用于自动处理剩余天数为0的用户预约和每日剩余天数减少
 */
@Configuration
@EnableScheduling
public class ScheduleConfig {

    private static final Logger log = LoggerFactory.getLogger(ScheduleConfig.class);

    private final ReservationServiceImpl reservationService;
    private final UserMapper userMapper;
    private final DistributedTaskService distributedTaskService;
    private final WorkerServerService workerServerService;

    @Value("${seatmaster.auto-assignment.enabled:true}")
    private boolean autoAssignmentEnabled;

    @Value("${seatmaster.auto-assignment.log-level:INFO}")
    private String logLevel;

    public ScheduleConfig(ReservationServiceImpl reservationService,
                         UserMapper userMapper,
                         WorkerServerService workerServerService) {
        this.reservationService = reservationService;
        this.userMapper = userMapper;
        this.distributedTaskService = null; // 暂时设为null，避免循环依赖
        this.workerServerService = workerServerService;
    }
    
    /**
     * 每小时检查一次剩余天数为0的用户，自动删除其预约
     * cron表达式：0 0 * * * * 表示每小时的第0分0秒执行
     */
    @Scheduled(cron = "0 0 * * * *")
    public void manageUserReservationStatus() {
        try {
            // 删除剩余天数为0的用户的预约
            reservationService.deleteReservationsForUsersWithZeroDays();
            System.out.println("定时任务：已删除剩余天数为0的用户的预约记录");
        } catch (Exception e) {
            System.err.println("定时任务执行失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 每天凌晨1点执行：减少所有用户的剩余天数
     * cron表达式：0 0 1 * * * 表示每天凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 * * *")
    public void decreaseUserRemainingDays() {
        try {
            // 获取所有普通用户（排除管理员）
            QueryWrapper<User> userWrapper = new QueryWrapper<>();
            userWrapper.ne("role", "ADMIN").gt("remaining_days", 0);
            List<User> users = userMapper.selectList(userWrapper);
            
            int updatedCount = 0;
            for (User user : users) {
                // 减少剩余天数
                user.setRemainingDays(user.getRemainingDays() - 1);
                userMapper.updateById(user);
                updatedCount++;
            }
            
            System.out.println("每日剩余天数减少任务完成：更新了" + updatedCount + "个用户的剩余天数");

            // 处理剩余天数为0的用户预约状态
            reservationService.deleteReservationsForUsersWithZeroDays();
            System.out.println("已删除剩余天数为0的用户的预约记录");
            
        } catch (Exception e) {
            System.err.println("每日剩余天数减少任务失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 每天凌晨2点执行一次全面检查
     * 注意：现在改为不自动取消预约，只记录状态
     * cron表达式：0 0 2 * * * 表示每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * *")
    public void dailyMaintenanceTask() {
        try {
            // 不再自动取消预约，只记录日志
            System.out.println("每日维护任务执行完成：剩余天数为0的用户状态为暂停，但保留预约");
        } catch (Exception e) {
            System.err.println("每日维护任务执行失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试用的定时任务：每30秒减少一次剩余天数（仅用于测试，生产环境应禁用）
     * 启用此任务用于快速测试剩余天数减少功能
     * 注意：已禁用此测试任务，避免干扰正常功能
     */
    // @Scheduled(fixedRate = 30000) // 每30秒执行一次 - 已禁用
    private void testDecreaseRemainingDays() {
        try {
            QueryWrapper<User> userWrapper = new QueryWrapper<>();
            userWrapper.ne("role", "ADMIN").gt("remaining_days", 0);
            List<User> users = userMapper.selectList(userWrapper);
            
            int updatedCount = 0;
            for (User user : users) {
                user.setRemainingDays(user.getRemainingDays() - 1);
                userMapper.updateById(user);
                updatedCount++;
            }
            
            if (updatedCount > 0) {
                System.out.println("测试模式：减少了" + updatedCount + "个用户的剩余天数");
                // 不再自动取消预约，剩余天数为0的用户状态设为暂停但保留预约
            }
            
        } catch (Exception e) {
            System.err.println("测试任务失败：" + e.getMessage());
        }
    }

    /**
     * 自动分配待执行任务的定时任务
     * 每5分钟检查一次未分配的预约任务，自动分配给可用的副服务器
     * cron表达式：0 星号/5 * * * * 表示每5分钟执行一次
     */
    @Scheduled(cron = "0 */5 * * * *")
    public void autoAssignPendingTasks() {
        if (!autoAssignmentEnabled) {
            return;
        }

        try {
            log.info("开始执行自动分配任务定时检查");

            // 通过ApplicationContext获取DistributedTaskService，避免循环依赖
            DistributedTaskService taskService = ApplicationContextHolder.getBean(DistributedTaskService.class);
            if (taskService == null) {
                log.warn("DistributedTaskService未找到，跳过自动分配");
                return;
            }

            Map<String, Object> result = taskService.autoAssignPendingTasks();

            Integer assignedCount = (Integer) result.get("assignedCount");
            String message = (String) result.get("message");

            if (assignedCount != null && assignedCount > 0) {
                log.info("自动分配任务完成: 成功分配 {} 个任务", assignedCount);
            } else if ("INFO".equals(logLevel)) {
                log.info("自动分配任务检查完成: {}", message != null ? message : "无待分配任务");
            }

        } catch (Exception e) {
            log.error("自动分配任务定时检查失败", e);
        }
    }

    /**
     * 定时检查副服务器心跳超时状态
     * 每5分钟执行一次，检查心跳超时的服务器并更新其状态为离线
     * 注意：主动健康检查功能已禁用，主服务器不再主动发送心跳检测请求
     * cron表达式：0 星号/5 * * * * 表示每5分钟执行一次
     */
    @Scheduled(cron = "0 */5 * * * *")
    public void checkWorkerServerHeartbeat() {
        try {
            log.info("开始执行副服务器心跳超时检查定时任务");

            // 1. 检查心跳超时的服务器，将其状态设为离线
            int timeoutMinutes = 10; // 10分钟无心跳视为超时
            int timeoutUpdatedCount = workerServerService.updateTimeoutServers(timeoutMinutes);

            if (timeoutUpdatedCount > 0) {
                log.warn("发现心跳超时的服务器: 更新数量={}", timeoutUpdatedCount);
            }

            // 2. 主动健康检查功能已禁用 - 主服务器不再主动发送心跳检测请求
            // 注释原因：根据需求，移除主服务器主动发送心跳的功能，只保留接收心跳的功能
            /*
            List<WorkerServerDTO> allServers = workerServerService.getAllServers();
            int totalServers = allServers.size();
            int onlineCount = 0;
            int offlineCount = 0;
            int errorCount = 0;

            log.info("开始对 {} 个副服务器执行健康检查", totalServers);

            for (WorkerServerDTO server : allServers) {
                try {
                    WorkerServerDTO.HealthCheckResponse response = workerServerService.healthCheck(server.getId());

                    if (response.isSuccess()) {
                        if ("ONLINE".equals(response.getStatus())) {
                            onlineCount++;
                        } else {
                            errorCount++;
                        }

                        log.debug("服务器健康检查成功: {} - {}, 响应时间: {}ms",
                                server.getName(), response.getStatus(), response.getResponseTime());
                    } else {
                        offlineCount++;
                        log.warn("服务器健康检查失败: {} - {}, 错误: {}",
                                server.getName(), response.getStatus(), response.getMessage());
                    }

                } catch (Exception e) {
                    offlineCount++;
                    log.error("服务器健康检查异常: {} - {}", server.getName(), e.getMessage());
                }

                // 避免过于频繁的请求，每次检查间隔500ms
                Thread.sleep(500);
            }

            // 3. 记录检查结果统计
            log.info("副服务器心跳检查完成: 总数={}, 在线={}, 离线={}, 错误={}",
                    totalServers, onlineCount, offlineCount, errorCount);

            // 4. 如果有服务器离线，记录警告
            if (offlineCount > 0) {
                log.warn("检测到 {} 个副服务器离线，请检查服务器状态", offlineCount);
            }

            // 5. 如果所有服务器都离线，记录严重警告
            if (onlineCount == 0 && totalServers > 0) {
                log.error("警告：所有副服务器都处于离线状态！请立即检查服务器连接");
            }
            */

            log.info("副服务器心跳超时检查完成: 仅执行超时检查，主动健康检查已禁用");

        } catch (Exception e) {
            log.error("副服务器心跳超时检查定时任务执行失败", e);
        }
    }
}