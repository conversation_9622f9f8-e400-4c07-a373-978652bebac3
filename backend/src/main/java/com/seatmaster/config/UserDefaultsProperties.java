package com.seatmaster.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 用户默认配置属性类
 * 用于读取application.yml中的用户默认设置
 */
@Data
@Component
@ConfigurationProperties(prefix = "seatmaster.user-defaults")
public class UserDefaultsProperties {
    
    /**
     * 新注册用户默认试用天数
     * 默认值：7天
     */
    private Integer trialDays = 3;
}
