package com.seatmaster.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate配置类
 * 🚀 简化配置，专注直连Worker方案
 */
@Configuration
public class RestTemplateConfig {

    /**
     * 🚀 优化RestTemplate配置
     * 减少超时时间，提升调用速度
     * @return RestTemplate实例
     */
    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(5000);  // 连接超时5秒
        factory.setReadTimeout(15000);    // 读取超时15秒

        return new RestTemplate(factory);
    }

    /**
     * 🚀 快速RestTemplate - 专门用于Worker调用
     * 更激进的超时配置
     * @return 快速RestTemplate实例
     */
    @Bean("fastRestTemplate")
    public RestTemplate fastRestTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(2000);  // 连接超时2秒
        factory.setReadTimeout(10000);    // 读取超时10秒

        return new RestTemplate(factory);
    }
}
