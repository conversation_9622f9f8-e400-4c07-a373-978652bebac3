package com.seatmaster.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 生成兑换码请求DTO
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
public class GenerateCodeRequest {
    
    /**
     * 增加的天数
     */
    @NotNull(message = "天数不能为空")
    @Min(value = 1, message = "天数不能少于1天")
    @Max(value = 365, message = "天数不能超过365天")
    private Integer daysToAdd;
    
    /**
     * 生成数量
     */
    @NotNull(message = "生成数量不能为空")
    @Min(value = 1, message = "生成数量不能少于1个")
    @Max(value = 1000, message = "单次生成数量不能超过1000个")
    private Integer quantity;
    
    /**
     * 过期时间（可选，NULL表示永不过期）
     */
    private LocalDateTime expireTime;
    
    /**
     * 兑换码描述（可选）
     */
    private String description;
}
