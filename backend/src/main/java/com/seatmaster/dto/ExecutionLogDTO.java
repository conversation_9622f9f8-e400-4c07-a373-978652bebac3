package com.seatmaster.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * 执行日志数据传输对象
 * 重构后适配 reservation_logs 表结构
 */
@Data
public class ExecutionLogDTO {

    private Long id;
    private Long reservationId;  // 原 taskId，现在对应 reservation_logs.reservation_id
    private String username;     // 原 userInfo，现在直接使用 username

    // 预约基础信息
    private String roomid;       // 原 roomInfo，现在使用 roomid
    private String seatid;       // 原 seatInfo，现在使用 seatid

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate reserveDate;  // 预约日期

    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime startTime;    // 预约开始时间

    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime endTime;      // 预约结束时间

    // 执行结果信息
    private String status;          // 原 executionStatus，现在使用 status (success/failed/error)
    private String errorMessage;    // 原 errorDetails，现在使用 error_message
    private Object apiResponse;     // 原 apiResponseJson，现在使用 JSON 类型

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime apiResponseTime;  // API响应时间（毫秒精度）

    private Integer attemptCount;   // 尝试次数
    private BigDecimal executionTime;  // 执行时间（秒，原来是毫秒）

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;   // 创建时间

    // 关联信息（通过JOIN获取）
    private Long userId;           // 用户ID（从users表获取）
    private String userDisplayName; // 用户显示名称
    private String roomName;       // 房间名称
    private String schoolName;     // 学校名称
    

    // 常量定义
    private static final String UNKNOWN_TEXT = "未知";
    private static final String UNKNOWN_ICON = "❓";
    private static final String SEPARATOR = " | ";
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 便捷方法
    public String getStatusIcon() {
        return getStatusIcon(status);
    }

    public String getStatusText() {
        return getStatusText(status);
    }

    public String getDurationText() {
        return formatDuration(executionTime);
    }

    // 私有工具方法 - 提取重复逻辑

    /**
     * 安全获取状态图标
     */
    private static String getStatusIcon(String status) {
        if (isNullOrEmpty(status)) return UNKNOWN_ICON;
        switch (status.toLowerCase().trim()) {
            case "success": return "✅";
            case "failed": return "❌";
            case "error": return "⚠️";
            default: return UNKNOWN_ICON;
        }
    }

    /**
     * 安全获取状态文本
     */
    private static String getStatusText(String status) {
        if (isNullOrEmpty(status)) return UNKNOWN_TEXT;
        switch (status.toLowerCase().trim()) {
            case "success": return "执行成功";
            case "failed": return "执行失败";
            case "error": return "执行异常";
            default: return "未知状态";
        }
    }

    /**
     * 统一的时长格式化方法
     */
    private static String formatDuration(BigDecimal executionTime) {
        if (executionTime == null) return UNKNOWN_TEXT;

        double seconds = executionTime.doubleValue();
        if (seconds < 0) return UNKNOWN_TEXT;

        if (seconds < 1.0) {
            return String.format("%.0fms", seconds * 1000);
        } else {
            return String.format("%.1f秒", seconds);
        }
    }

    /**
     * 安全的字符串空值检查
     */
    private static boolean isNullOrEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 安全的字符串格式化
     */
    private static String safeFormat(String template, Object... args) {
        try {
            return String.format(template, args);
        } catch (Exception e) {
            return UNKNOWN_TEXT;
        }
    }

    /**
     * 获取预约时间段文本
     */
    public String getTimeRange() {
        if (startTime == null || endTime == null) return UNKNOWN_TEXT;
        return safeFormat("%s - %s",
            formatTime(startTime),
            formatTime(endTime));
    }

    /**
     * 获取用户信息显示文本
     */
    public String getUserInfo() {
        if (!isNullOrEmpty(userDisplayName)) {
            return safeFormat("%s (%s)", userDisplayName.trim(),
                isNullOrEmpty(username) ? UNKNOWN_TEXT : username.trim());
        }
        return isNullOrEmpty(username) ? "未知用户" : username.trim();
    }

    /**
     * 获取座位信息显示文本
     */
    public String getSeatInfo() {
        return isNullOrEmpty(seatid) ? "未知座位" : "座位" + seatid.trim();
    }

    /**
     * 获取房间信息显示文本
     */
    public String getRoomInfo() {
        if (!isNullOrEmpty(roomName)) {
            return roomName.trim();
        }
        return isNullOrEmpty(roomid) ? "未知房间" : "房间" + roomid.trim();
    }

    /**
     * 统一的时间格式化方法
     */
    private static String formatTime(LocalTime time) {
        if (time == null) return UNKNOWN_TEXT;
        try {
            return time.format(TIME_FORMATTER);
        } catch (Exception e) {
            return time.toString();
        }
    }

    /**
     * 统一的日期时间格式化方法
     */
    private static String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) return UNKNOWN_TEXT;
        try {
            return dateTime.format(DATETIME_FORMATTER);
        } catch (Exception e) {
            return dateTime.toString();
        }
    }

    /**
     * 获取执行详情文本（替代原来的stepDurationText）
     */
    public String getExecutionDetails() {
        StringBuilder details = new StringBuilder();

        // 重试次数信息
        if (attemptCount != null && attemptCount > 1) {
            details.append("重试").append(attemptCount).append("次");
        }

        // 执行时间信息
        if (executionTime != null) {
            appendWithSeparator(details, "耗时" + formatDuration(executionTime));
        }

        // API响应时间信息
        if (apiResponseTime != null) {
            appendWithSeparator(details, "响应时间" + formatDateTime(apiResponseTime));
        }

        return details.length() > 0 ? details.toString() : "无详细信息";
    }

    /**
     * 安全地向StringBuilder添加内容，自动处理分隔符
     */
    private static void appendWithSeparator(StringBuilder sb, String content) {
        if (isNullOrEmpty(content)) return;

        if (sb.length() > 0) {
            sb.append(SEPARATOR);
        }
        sb.append(content);
    }

}
