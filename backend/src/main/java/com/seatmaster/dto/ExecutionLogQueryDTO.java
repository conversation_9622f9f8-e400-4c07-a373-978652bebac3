package com.seatmaster.dto;

import lombok.Data;

/**
 * 执行日志查询参数DTO
 */
@Data
public class ExecutionLogQueryDTO {

    private Long userId;
    private String username;        // 支持通过用户名查询
    private String status;          // SUCCESS, FAILED, ERROR
    private String startDate;       // yyyy-MM-dd
    private String endDate;         // yyyy-MM-dd

    // 管理员专用筛选条件
    private String roomid;          // 房间ID
    private String seatid;          // 座位ID
    private String roomName;        // 房间名称
    private String schoolName;      // 学校名称
    private Long schoolId;          // 学校ID（用于高效筛选）

    
    // 分页参数
    private int page = 1;
    private int size = 20;
    
    // 排序参数
    private String sortBy = "execution_end_time";
    private String sortOrder = "DESC";
    
    // 便捷方法
    public int getOffset() {
        return (page - 1) * size;
    }
    
    public boolean hasDateFilter() {
        return startDate != null || endDate != null;
    }
    
    public boolean hasStatusFilter() {
        return status != null && !status.trim().isEmpty();
    }

    public boolean hasSchoolFilter() {
        return schoolId != null && schoolId > 0;
    }

    public boolean isValidSchoolId() {
        return schoolId == null || schoolId > 0;
    }
}
