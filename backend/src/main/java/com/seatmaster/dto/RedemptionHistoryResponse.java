package com.seatmaster.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 兑换历史响应DTO
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
public class RedemptionHistoryResponse {
    
    /**
     * 记录ID
     */
    private Long id;
    
    /**
     * 兑换码（脱敏显示）
     */
    private String code;
    
    /**
     * 增加的天数
     */
    private Integer daysAdded;
    
    /**
     * 兑换时间
     */
    private LocalDateTime redemptionTime;
    
    /**
     * 兑换前剩余天数
     */
    private Integer daysBefore;
    
    /**
     * 兑换后剩余天数
     */
    private Integer daysAfter;
    
    /**
     * 脱敏处理兑换码
     * 例如：ABC123DEF456 -> ABC***F456
     */
    public static String maskCode(String originalCode) {
        if (originalCode == null || originalCode.length() < 6) {
            return originalCode;
        }
        
        int length = originalCode.length();
        String prefix = originalCode.substring(0, 3);
        String suffix = originalCode.substring(length - 3);
        
        return prefix + "***" + suffix;
    }
}
