package com.seatmaster.dto;

import lombok.Data;

import javax.validation.constraints.*;
import java.time.LocalDateTime;

/**
 * 副服务器数据传输对象
 */
@Data
public class WorkerServerDTO {

    /**
     * 主键ID（更新时需要）
     */
    private String id;

    /**
     * 副服务器唯一标识
     */
    @NotBlank(message = "服务器ID不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "服务器ID只能包含字母、数字、下划线和横线")
    @Size(min = 3, max = 50, message = "服务器ID长度必须在3-50个字符之间")
    private String workerId;

    /**
     * 副服务器名称
     */
    @NotBlank(message = "服务器名称不能为空")
    @Size(min = 2, max = 100, message = "服务器名称长度必须在2-100个字符之间")
    private String name;

    /**
     * 服务器主机地址
     */
    @NotBlank(message = "主机地址不能为空")
    @Pattern(regexp = "^(localhost|((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)|([a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.)*)$", 
             message = "主机地址格式不正确")
    private String host;

    /**
     * 服务器端口
     */
    @NotNull(message = "端口不能为空")
    @Min(value = 1024, message = "端口号必须大于等于1024")
    @Max(value = 65535, message = "端口号必须小于等于65535")
    private Integer port;

    /**
     * 最大并发任务数
     */
    @NotNull(message = "最大并发任务数不能为空")
    @Min(value = 1, message = "最大并发任务数必须大于0")
    @Max(value = 1000, message = "最大并发任务数不能超过1000")
    private Integer maxConcurrentTasks;

    /**
     * 服务器状态（只读，由系统维护）
     */
    private String status;

    /**
     * 当前负载（只读，由系统维护）
     */
    private Integer currentLoad;

    /**
     * 总完成任务数（只读，由系统维护）
     */
    private Long totalTasksCompleted;

    /**
     * 总失败任务数（只读，由系统维护）
     */
    private Long totalTasksFailed;

    /**
     * 最后心跳时间（只读，由系统维护）
     */
    private LocalDateTime lastHeartbeat;

    /**
     * 创建时间（只读）
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间（只读）
     */
    private LocalDateTime updatedTime;

    // 计算属性

    /**
     * 获取服务器URL
     */
    public String getServerUrl() {
        if (host == null || port == null) {
            return null;
        }
        return "http://" + host + ":" + port;
    }

    /**
     * 获取成功率
     */
    public Double getSuccessRate() {
        if (totalTasksCompleted == null || totalTasksFailed == null) {
            return null;
        }
        
        long totalTasks = totalTasksCompleted + totalTasksFailed;
        if (totalTasks == 0) {
            return null;
        }
        
        return (double) totalTasksCompleted / totalTasks * 100;
    }

    /**
     * 获取负载率
     */
    public Double getLoadRate() {
        if (currentLoad == null || maxConcurrentTasks == null || maxConcurrentTasks == 0) {
            return 0.0;
        }
        
        return (double) currentLoad / maxConcurrentTasks * 100;
    }

    /**
     * 判断是否在线
     */
    public boolean isOnline() {
        return "ONLINE".equals(status);
    }

    /**
     * 判断是否可用
     */
    public boolean isAvailable() {
        return isOnline() && (currentLoad == null || maxConcurrentTasks == null || 
                             currentLoad < maxConcurrentTasks);
    }

    /**
     * 获取显示状态
     */
    public String getDisplayStatus() {
        if (status == null) {
            return "未知";
        }
        
        switch (status) {
            case "ONLINE":
                if (currentLoad != null && maxConcurrentTasks != null) {
                    return String.format("在线 (%d/%d)", currentLoad, maxConcurrentTasks);
                }
                return "在线";
            case "OFFLINE":
                return "离线";
            case "BUSY":
                return "繁忙";
            case "ERROR":
                return "错误";
            default:
                return status;
        }
    }

    /**
     * 创建请求DTO（用于新增）
     */
    @Data
    public static class CreateRequest {
        @NotBlank(message = "服务器ID不能为空")
        @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "服务器ID只能包含字母、数字、下划线和横线")
        @Size(min = 3, max = 50, message = "服务器ID长度必须在3-50个字符之间")
        private String workerId;

        @NotBlank(message = "服务器名称不能为空")
        @Size(min = 2, max = 100, message = "服务器名称长度必须在2-100个字符之间")
        private String name;

        @NotBlank(message = "主机地址不能为空")
        private String host;

        @NotNull(message = "端口不能为空")
        @Min(value = 1024, message = "端口号必须大于等于1024")
        @Max(value = 65535, message = "端口号必须小于等于65535")
        private Integer port;

        @NotNull(message = "最大并发任务数不能为空")
        @Min(value = 1, message = "最大并发任务数必须大于0")
        @Max(value = 1000, message = "最大并发任务数不能超过1000")
        private Integer maxConcurrentTasks;
    }

    /**
     * 更新请求DTO（用于修改）
     */
    @Data
    public static class UpdateRequest {
        // ID字段从URL路径参数获取，不需要客户端在请求体中提供
        // 移除@NotNull验证，避免验证时机问题
        private String id;

        @NotBlank(message = "服务器名称不能为空")
        @Size(min = 2, max = 100, message = "服务器名称长度必须在2-100个字符之间")
        private String name;

        @NotBlank(message = "主机地址不能为空")
        @Pattern(regexp = "^(localhost|((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)|([a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.)*)$",
                 message = "主机地址格式不正确")
        private String host;

        @NotNull(message = "端口不能为空")
        @Min(value = 1024, message = "端口号必须大于等于1024")
        @Max(value = 65535, message = "端口号必须小于等于65535")
        private Integer port;

        @NotNull(message = "最大并发任务数不能为空")
        @Min(value = 1, message = "最大并发任务数必须大于0")
        @Max(value = 1000, message = "最大并发任务数不能超过1000")
        private Integer maxConcurrentTasks;
    }

    /**
     * 批量操作请求DTO
     */
    @Data
    public static class BatchOperationRequest {
        @NotEmpty(message = "服务器ID列表不能为空")
        private java.util.List<String> ids;

        @NotBlank(message = "操作类型不能为空")
        @Pattern(regexp = "^(start|stop|restart|delete)$", message = "操作类型必须是: start, stop, restart, delete")
        private String operation;
    }

    /**
     * 健康检查响应DTO
     */
    @Data
    public static class HealthCheckResponse {
        private boolean success;
        private String message;
        private String status;
        private LocalDateTime timestamp;
        private Long responseTime; // 响应时间（毫秒）
        private Object data;
    }
}
