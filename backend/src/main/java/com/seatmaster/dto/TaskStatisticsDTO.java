package com.seatmaster.dto;

import lombok.Data;

/**
 * 任务统计数据传输对象
 * 用于分布式任务管理页面的系统状态概览
 */
@Data
public class TaskStatisticsDTO {
    
    /**
     * 已完成任务数
     */
    private Long completedTasks;
    
    /**
     * 待执行任务数
     */
    private Long pendingTasks;
    
    /**
     * 执行中任务数
     */
    private Long runningTasks;
    
    /**
     * 失败任务数
     */
    private Long failedTasks;
    
    /**
     * 总任务数
     */
    private Long totalTasks;
    
    /**
     * 成功率（百分比）
     */
    private Double successRate;
    
    /**
     * 今日新增任务数
     */
    private Long todayNewTasks;
    
    /**
     * 今日完成任务数
     */
    private Long todayCompletedTasks;
    
    /**
     * 平均执行时间（分钟）
     */
    private Double averageExecutionTime;
    
    /**
     * 在线副服务器数量
     */
    private Integer onlineWorkers;
    
    /**
     * 总副服务器数量
     */
    private Integer totalWorkers;
    
    /**
     * 系统负载率（百分比）
     */
    private Double systemLoadRate;
    
    /**
     * 构造函数
     */
    public TaskStatisticsDTO() {
        this.completedTasks = 0L;
        this.pendingTasks = 0L;
        this.runningTasks = 0L;
        this.failedTasks = 0L;
        this.totalTasks = 0L;
        this.successRate = 0.0;
        this.todayNewTasks = 0L;
        this.todayCompletedTasks = 0L;
        this.averageExecutionTime = 0.0;
        this.onlineWorkers = 0;
        this.totalWorkers = 0;
        this.systemLoadRate = 0.0;
    }
    
    /**
     * 计算成功率
     */
    public void calculateSuccessRate() {
        if (totalTasks > 0) {
            this.successRate = (completedTasks.doubleValue() / totalTasks.doubleValue()) * 100;
        } else {
            this.successRate = 0.0;
        }
    }
    
    /**
     * 计算系统负载率
     */
    public void calculateSystemLoadRate() {
        if (totalWorkers > 0) {
            this.systemLoadRate = (runningTasks.doubleValue() / (totalWorkers * 10.0)) * 100; // 假设每个服务器最大10个任务
        } else {
            this.systemLoadRate = 0.0;
        }
    }
}
