package com.seatmaster.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 兑换码兑换响应DTO
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
public class RedeemCodeResponse {
    
    /**
     * 增加的天数
     */
    private Integer daysAdded;
    
    /**
     * 兑换前剩余天数
     */
    private Integer daysBefore;
    
    /**
     * 兑换后剩余天数
     */
    private Integer daysAfter;
    
    /**
     * 兑换时间
     */
    private LocalDateTime redemptionTime;
    
    /**
     * 兑换码描述
     */
    private String description;
    
    public RedeemCodeResponse() {}
    
    public RedeemCodeResponse(Integer daysAdded, Integer daysBefore, Integer daysAfter, 
                             LocalDateTime redemptionTime, String description) {
        this.daysAdded = daysAdded;
        this.daysBefore = daysBefore;
        this.daysAfter = daysAfter;
        this.redemptionTime = redemptionTime;
        this.description = description;
    }
}
