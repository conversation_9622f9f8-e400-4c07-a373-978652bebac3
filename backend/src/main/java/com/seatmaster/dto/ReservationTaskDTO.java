package com.seatmaster.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 预约任务数据传输对象
 * 用于分布式任务管理页面的任务列表显示
 */
@Data
public class ReservationTaskDTO {
    
    /**
     * 预约ID（任务ID）
     */
    private Long reservationId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户姓名
     */
    private String userName;
    
    /**
     * 学校ID
     */
    private Long schoolId;
    
    /**
     * 学校名称
     */
    private String schoolName;
    
    /**
     * 房间ID
     */
    private Long roomId;
    
    /**
     * 房间名称
     */
    private String roomName;
    
    /**
     * 座位号
     */
    private String seatId;
    
    /**
     * 预约开始时间
     */
    private LocalTime startTime;
    
    /**
     * 预约结束时间
     */
    private LocalTime endTime;
    
    /**
     * 预约开放时间
     */
    private String reservationOpenTime;
    
    /**
     * 预约类型
     */
    private String reservationType;
    

    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    // ==================== 分布式任务字段 ====================
    
    /**
     * 分配的副服务器ID
     */
    private String workerId;
    
    /**
     * 副服务器名称
     */
    private String workerName;

    /**
     * 副服务器URL
     */
    private String workerUrl;
    

    
    /**
     * 执行结果
     */
    private String executionResult;
    
    /**
     * 最后执行时间
     */
    private LocalDateTime lastExecutionTime;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 执行耗时（毫秒）
     */
    private Long executionDuration;
    
    /**
     * 是否可以重试
     */
    private Boolean canRetry;
    
    /**
     * 是否可以手动分配
     */
    private Boolean canAssign;
    
    /**
     * 优先级
     */
    private Integer priority;
    

    
    /**
     * 获取预约类型的中文描述
     */
    public String getReservationTypeText() {
        if (reservationType == null) {
            return "未知";
        }
        switch (reservationType) {
            case "SAME_DAY":
                return "当天预约";
            case "ADVANCE_ONE_DAY":
                return "提前一天预约";
            default:
                return "未知";
        }
    }
    
    /**
     * 判断是否可以重试
     */
    public Boolean getCanRetry() {
        return retryCount == null || retryCount < 3;
    }

    /**
     * 判断是否可以手动分配
     */
    public Boolean getCanAssign() {
        return true; // 简化逻辑，所有任务都可以分配
    }
}
