package com.seatmaster.dto;

import lombok.Data;

import java.util.List;

/**
 * 生成兑换码响应DTO
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
public class GenerateCodeResponse {
    
    /**
     * 批次ID
     */
    private String batchId;
    
    /**
     * 生成的兑换码列表
     */
    private List<String> codes;
    
    /**
     * 总生成数量
     */
    private Integer totalGenerated;
    
    /**
     * 每个兑换码的天数
     */
    private Integer daysToAdd;
    
    /**
     * 描述
     */
    private String description;
    
    public GenerateCodeResponse() {}
    
    public GenerateCodeResponse(String batchId, List<String> codes, Integer totalGenerated, 
                               Integer daysToAdd, String description) {
        this.batchId = batchId;
        this.codes = codes;
        this.totalGenerated = totalGenerated;
        this.daysToAdd = daysToAdd;
        this.description = description;
    }
}
