package com.seatmaster.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
public class UserProfileResponse {
    private Long id;
    private String username;
    private String name;
    private String role;
    private LocalDateTime createdTime;
    private Integer remainingDays; // 剩余可预约天数
    private String userStatus; // 用户状态：ACTIVE(正常) 或 SUSPENDED(暂停)

    // 当前预约信息
    private CurrentReservation currentReservation;
    
    @Data
    public static class CurrentReservation {
        private Long reservationId;
        private String schoolName;
        private String roomName;
        private Long roomId;
        private String seatId;
        private LocalTime startTime; // 预约开始时间（仅时间，格式HH:MM:SS）
        private LocalTime endTime; // 预约结束时间（仅时间，格式HH:MM:SS）
        private String status;
        private String reservationOpenTime;  // 预约开放时间
        private String reservationType;      // 预约类型
        // 注意：剩余天数信息现在在用户主信息中的 remainingDays 字段
    }
} 