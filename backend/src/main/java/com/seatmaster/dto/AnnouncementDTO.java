package com.seatmaster.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.seatmaster.entity.Announcement;
import lombok.Data;

import javax.validation.constraints.*;
import java.time.LocalDateTime;

/**
 * 公告DTO类
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
public class AnnouncementDTO {
    
    private Long id;
    
    @NotBlank(message = "公告标题不能为空")
    @Size(max = 200, message = "公告标题长度不能超过200个字符")
    private String title;
    
    @NotBlank(message = "公告内容不能为空")
    private String content;
    
    @Size(max = 500, message = "公告摘要长度不能超过500个字符")
    private String summary;
    
    @Min(value = 0, message = "优先级不能小于0")
    @Max(value = 999, message = "优先级不能大于999")
    private Integer priority = 0;
    
    private Boolean enabled = true;
    
    private Boolean popup = true;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;
    
    @NotNull(message = "目标用户类型不能为空")
    private Announcement.TargetUsers targetUsers = Announcement.TargetUsers.ALL;
    
    private Integer viewCount = 0;
    
    private Long createdBy;
    
    private String createdByName;
    
    private LocalDateTime createdTime;
    
    private LocalDateTime updatedTime;
    
    // 用户相关字段（用于前端显示）
    private Boolean hasRead;
    
    private Boolean isDismissed;
    
    /**
     * 创建公告请求DTO
     */
    @Data
    public static class CreateRequest {
        
        @NotBlank(message = "公告标题不能为空")
        @Size(max = 200, message = "公告标题长度不能超过200个字符")
        private String title;
        
        @NotBlank(message = "公告内容不能为空")
        private String content;
        
        @Size(max = 500, message = "公告摘要长度不能超过500个字符")
        private String summary;
        
        @Min(value = 0, message = "优先级不能小于0")
        @Max(value = 999, message = "优先级不能大于999")
        private Integer priority = 0;
        
        private Boolean enabled = true;
        
        private Boolean popup = true;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime startTime;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime endTime;
        
        @NotNull(message = "目标用户类型不能为空")
        private Announcement.TargetUsers targetUsers = Announcement.TargetUsers.ALL;
    }
    
    /**
     * 更新公告请求DTO
     */
    @Data
    public static class UpdateRequest {
        
        @NotBlank(message = "公告标题不能为空")
        @Size(max = 200, message = "公告标题长度不能超过200个字符")
        private String title;
        
        @NotBlank(message = "公告内容不能为空")
        private String content;
        
        @Size(max = 500, message = "公告摘要长度不能超过500个字符")
        private String summary;
        
        @Min(value = 0, message = "优先级不能小于0")
        @Max(value = 999, message = "优先级不能大于999")
        private Integer priority;
        
        private Boolean enabled;
        
        private Boolean popup;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime startTime;

        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private LocalDateTime endTime;
        
        private Announcement.TargetUsers targetUsers;
    }
    
    /**
     * 公告查询请求DTO
     */
    @Data
    public static class QueryRequest {
        
        private String title;
        
        private Boolean enabled;
        
        private Announcement.TargetUsers targetUsers;
        
        private LocalDateTime startDate;
        
        private LocalDateTime endDate;
        
        @Min(value = 1, message = "页码必须大于0")
        private Integer page = 1;
        
        @Min(value = 1, message = "每页大小必须大于0")
        @Max(value = 100, message = "每页大小不能超过100")
        private Integer size = 10;
        
        private String sortBy = "priority";
        
        private String sortOrder = "desc";
    }
}
