package com.seatmaster.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seatmaster.entity.RedemptionLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 兑换记录Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Mapper
public interface RedemptionLogMapper extends BaseMapper<RedemptionLog> {
    
    /**
     * 分页查询用户兑换历史
     */
    @Select("SELECT * FROM redemption_logs WHERE user_id = #{userId} ORDER BY redemption_time DESC")
    IPage<RedemptionLog> selectPageByUserId(Page<RedemptionLog> page, @Param("userId") Long userId);
    
    /**
     * 统计用户总兑换次数
     */
    @Select("SELECT COUNT(*) FROM redemption_logs WHERE user_id = #{userId}")
    Long countByUserId(@Param("userId") Long userId);
    
    /**
     * 统计用户总获得天数
     */
    @Select("SELECT COALESCE(SUM(days_added), 0) FROM redemption_logs WHERE user_id = #{userId}")
    Integer sumDaysByUserId(@Param("userId") Long userId);
    
    /**
     * 统计指定时间范围内的兑换次数
     */
    @Select("SELECT COUNT(*) FROM redemption_logs WHERE redemption_time BETWEEN #{startTime} AND #{endTime}")
    Long countByTimeRange(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
