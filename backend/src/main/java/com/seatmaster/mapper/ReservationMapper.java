package com.seatmaster.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seatmaster.entity.Reservation;
import com.seatmaster.dto.UserProfileResponse;
import com.seatmaster.dto.ReservationTaskDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ReservationMapper extends BaseMapper<Reservation> {
    
    // 查询最新的活动或暂停预约（修复版本，正确获取座位号）
    @Select("SELECT " +
            "r.id as reservationId, " +
            "COALESCE(s.name, '未知学校') as schoolName, " +
            "COALESCE(rm.name, '未知房间') as roomName, " +
            "r.room_id as roomId, " +
            "COALESCE(r.seat_num, 'N/A') as seatId, " +
            "r.start_time as startTime, " +
            "r.end_time as endTime, " +
            "'ACTIVE' as status, " +
            "COALESCE(r.reservation_open_time, '') as reservationOpenTime, " +
            "COALESCE(r.reservation_type, '') as reservationType " +
            "FROM reservations r " +
            "LEFT JOIN rooms rm ON r.room_id = rm.id " +
            "LEFT JOIN schools s ON rm.school_id = s.id " +
            "WHERE r.user_id = #{userId} " +
            "ORDER BY r.created_time DESC " +
            "LIMIT 1")
    UserProfileResponse.CurrentReservation getCurrentReservationByUserId(Long userId);
    
    // 新增：查询最新的活动预约（不限时间）- 修复版本
    @Select("SELECT " +
            "r.id as reservationId, " +
            "COALESCE(s.name, '未知学校') as schoolName, " +
            "COALESCE(rm.name, '未知房间') as roomName, " +
            "r.room_id as roomId, " +
            "COALESCE(r.seat_num, 'N/A') as seatId, " +
            "r.start_time as startTime, " +
            "r.end_time as endTime, " +
            "'ACTIVE' as status, " +
            "COALESCE(r.reservation_open_time, '') as reservationOpenTime, " +
            "COALESCE(r.reservation_type, '') as reservationType " +
            "FROM reservations r " +
            "LEFT JOIN rooms rm ON r.room_id = rm.id " +
            "LEFT JOIN schools s ON rm.school_id = s.id " +
            "WHERE r.user_id = #{userId} " +
            "ORDER BY r.created_time DESC " +
            "LIMIT 1")
    UserProfileResponse.CurrentReservation getLatestActiveReservationByUserId(Long userId);
    
    // 查询用户所有预约（用于调试）- 修复版本
    @Select("SELECT " +
            "r.id as reservationId, " +
            "COALESCE(s.name, '未知学校') as schoolName, " +
            "COALESCE(rm.name, '未知房间') as roomName, " +
            "r.room_id as roomId, " +
            "COALESCE(r.seat_num, 'N/A') as seatId, " +
            "r.start_time as startTime, " +
            "r.end_time as endTime, " +
            "'ACTIVE' as status, " +
            "COALESCE(r.reservation_open_time, '') as reservationOpenTime, " +
            "COALESCE(r.reservation_type, '') as reservationType " +
            "FROM reservations r " +
            "LEFT JOIN rooms rm ON r.room_id = rm.id " +
            "LEFT JOIN schools s ON rm.school_id = s.id " +
            "WHERE r.user_id = #{userId} " +
            "ORDER BY r.created_time DESC")
    java.util.List<UserProfileResponse.CurrentReservation> getAllReservationsByUserId(Long userId);
    
    // 添加一个简化的测试查询
    @Select("SELECT COUNT(*) FROM reservations WHERE user_id = #{userId}")
    int countReservationsByUserId(Long userId);
    
    // 添加一个不带时间限制的查询 - 修复版本
    @Select("SELECT " +
            "r.id as reservationId, " +
            "COALESCE(s.name, '未知学校') as schoolName, " +
            "COALESCE(rm.name, '未知房间') as roomName, " +
            "r.room_id as roomId, " +
            "COALESCE(r.seat_num, 'N/A') as seatId, " +
            "r.start_time as startTime, " +
            "r.end_time as endTime, " +
            "'ACTIVE' as status, " +
            "COALESCE(r.reservation_open_time, '') as reservationOpenTime, " +
            "COALESCE(r.reservation_type, '') as reservationType " +
            "FROM reservations r " +
            "LEFT JOIN rooms rm ON r.room_id = rm.id " +
            "LEFT JOIN schools s ON rm.school_id = s.id " +
            "WHERE r.user_id = #{userId} " +
            "ORDER BY r.start_time ASC " +
            "LIMIT 1")
    UserProfileResponse.CurrentReservation getReservationByUserIdNoTimeLimit(Long userId);
    
    // 获取下一个可用座位号
    @Select("SELECT LPAD((SELECT COUNT(*) FROM reservations WHERE room_id = #{roomId} AND end_time > NOW()) + 1, 3, '0')")
    String getNextAvailableSeatNumber(Long roomId);

    // ==================== 分布式任务管理查询方法 ====================

    /**
     * 分页查询预约任务列表
     */
    @Select("<script>" +
            "SELECT " +
            "r.id as reservationId, " +
            "r.user_id as userId, " +
            "u.username as username, " +
            "u.name as userName, " +
            "rm.school_id as schoolId, " +
            "COALESCE(s.name, '未知学校') as schoolName, " +
            "r.room_id as roomId, " +
            "COALESCE(rm.name, '未知房间') as roomName, " +
            "COALESCE(r.seat_num, 'N/A') as seatId, " +
            "r.start_time as startTime, " +
            "r.end_time as endTime, " +
            "r.reservation_open_time as reservationOpenTime, " +
            "r.reservation_type as reservationType, " +
            "r.created_time as createdTime, " +
            "COALESCE(r.worker_id, '') as workerId, " +
            "COALESCE(ws.name, '') as workerName, " +
            "COALESCE(ws.server_url, '') as workerUrl, " +
            "COALESCE(r.execution_result, r.error_message) as executionResult, " +
            "COALESCE(r.last_execution_time, r.actual_execution_time) as lastExecutionTime, " +
            "COALESCE(r.retry_count, 0) as retryCount " +
            "FROM reservations r " +
            "LEFT JOIN users u ON r.user_id = u.id " +
            "LEFT JOIN rooms rm ON r.room_id = rm.id " +
            "LEFT JOIN schools s ON rm.school_id = s.id " +
            "LEFT JOIN worker_servers ws ON r.worker_id = ws.id " +

            "WHERE 1=1 " +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND (u.username LIKE CONCAT('%', #{keyword}, '%') " +
            "OR u.name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR r.seat_num LIKE CONCAT('%', #{keyword}, '%')) " +
            "</if>" +
            "<if test='workerId != null and workerId != \"\"'>" +
            "AND r.worker_id = #{workerId} " +
            "</if>" +
            "ORDER BY r.created_time DESC" +
            "</script>")
    Page<ReservationTaskDTO> selectReservationTasksPage(Page<ReservationTaskDTO> page,
                                                        @Param("status") String status,
                                                        @Param("keyword") String keyword,
                                                        @Param("workerId") String workerId);

    // 已删除包含虚假数据的selectSimpleTasksPage方法

    /**
     * 根据ID查询任务详情
     */
    @Select("SELECT " +
            "r.id as reservationId, " +
            "r.user_id as userId, " +
            "u.username as username, " +
            "u.name as userName, " +
            "rm.school_id as schoolId, " +
            "COALESCE(s.name, '未知学校') as schoolName, " +
            "r.room_id as roomId, " +
            "COALESCE(rm.name, '未知房间') as roomName, " +
            "COALESCE(r.seat_num, 'N/A') as seatId, " +
            "r.start_time as startTime, " +
            "r.end_time as endTime, " +
            "r.reservation_open_time as reservationOpenTime, " +
            "r.reservation_type as reservationType, " +
            "r.created_time as createdTime, " +
            "COALESCE(r.worker_id, '') as workerId, " +
            "COALESCE(ws.name, '') as workerName, " +
            "COALESCE(ws.server_url, '') as workerUrl, " +
            "COALESCE(r.execution_result, r.error_message) as executionResult, " +
            "COALESCE(r.last_execution_time, r.actual_execution_time) as lastExecutionTime, " +
            "COALESCE(r.retry_count, 0) as retryCount " +
            "FROM reservations r " +
            "LEFT JOIN users u ON r.user_id = u.id " +
            "LEFT JOIN rooms rm ON r.room_id = rm.id " +
            "LEFT JOIN schools s ON rm.school_id = s.id " +
            "LEFT JOIN worker_servers ws ON r.worker_id = ws.id " +

            "WHERE r.id = #{reservationId}")
    ReservationTaskDTO selectTaskDetailById(@Param("reservationId") Long reservationId);
}