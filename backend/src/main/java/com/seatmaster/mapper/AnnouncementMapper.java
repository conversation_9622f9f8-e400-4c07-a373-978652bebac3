package com.seatmaster.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seatmaster.dto.AnnouncementDTO;
import com.seatmaster.entity.Announcement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 公告Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Mapper
public interface AnnouncementMapper extends BaseMapper<Announcement> {
    
    /**
     * 分页查询公告（带创建者信息）
     */
    @Select({
        "SELECT a.*, u.name as created_by_name ",
        "FROM announcements a ",
        "LEFT JOIN users u ON a.created_by = u.id ",
        "ORDER BY a.priority DESC, a.created_time DESC"
    })
    IPage<AnnouncementDTO> selectAnnouncementPage(Page<AnnouncementDTO> page);
    
    /**
     * 获取活跃的公告列表（用于用户查看）
     */
    @Select({
        "SELECT a.*, u.name as created_by_name ",
        "FROM announcements a ",
        "LEFT JOIN users u ON a.created_by = u.id ",
        "WHERE a.is_enabled = TRUE ",
        "  AND (a.start_time IS NULL OR a.start_time <= NOW()) ",
        "  AND (a.end_time IS NULL OR a.end_time >= NOW()) ",
        "  AND (a.target_users = 'ALL' OR a.target_users = #{userRole}) ",
        "ORDER BY a.priority DESC, a.created_time DESC"
    })
    List<AnnouncementDTO> selectActiveAnnouncements(@Param("userRole") String userRole);
    
    /**
     * 获取用户未读的弹窗公告
     */
    @Select({
        "SELECT a.*, u.name as created_by_name ",
        "FROM announcements a ",
        "LEFT JOIN users u ON a.created_by = u.id ",
        "LEFT JOIN user_announcement_reads uar ON a.id = uar.announcement_id AND uar.user_id = #{userId} ",
        "WHERE a.is_enabled = TRUE ",
        "  AND a.is_popup = TRUE ",
        "  AND (a.start_time IS NULL OR a.start_time <= NOW()) ",
        "  AND (a.end_time IS NULL OR a.end_time >= NOW()) ",
        "  AND (a.target_users = 'ALL' OR a.target_users = #{userRole}) ",
        "  AND uar.id IS NULL ",
        "ORDER BY a.priority DESC, a.created_time DESC"
    })
    List<AnnouncementDTO> selectUnreadPopupAnnouncements(@Param("userId") Long userId, @Param("userRole") String userRole);
    
    /**
     * 增加公告查看次数
     */
    @Update("UPDATE announcements SET view_count = view_count + 1 WHERE id = #{id}")
    int incrementViewCount(@Param("id") Long id);
    
    /**
     * 根据ID获取公告详情（带创建者信息）
     */
    @Select({
        "SELECT a.*, u.name as created_by_name ",
        "FROM announcements a ",
        "LEFT JOIN users u ON a.created_by = u.id ",
        "WHERE a.id = #{id}"
    })
    AnnouncementDTO selectAnnouncementById(@Param("id") Long id);
}
