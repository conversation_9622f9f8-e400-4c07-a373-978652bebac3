package com.seatmaster.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.seatmaster.entity.UserAnnouncementRead;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户公告阅读记录Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Mapper
public interface UserAnnouncementReadMapper extends BaseMapper<UserAnnouncementRead> {
    
    /**
     * 查询用户对指定公告的阅读记录
     */
    @Select("SELECT * FROM user_announcement_reads WHERE user_id = #{userId} AND announcement_id = #{announcementId}")
    UserAnnouncementRead selectByUserAndAnnouncement(@Param("userId") Long userId, @Param("announcementId") Long announcementId);
    
    /**
     * 查询用户已读的公告ID列表
     */
    @Select("SELECT announcement_id FROM user_announcement_reads WHERE user_id = #{userId}")
    List<Long> selectReadAnnouncementIds(@Param("userId") Long userId);
    
    /**
     * 查询用户已关闭弹窗的公告ID列表
     */
    @Select("SELECT announcement_id FROM user_announcement_reads WHERE user_id = #{userId} AND is_dismissed = TRUE")
    List<Long> selectDismissedAnnouncementIds(@Param("userId") Long userId);
}
