package com.seatmaster.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.seatmaster.entity.WorkerServer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 副服务器数据访问层
 */
@Mapper
public interface WorkerServerMapper extends BaseMapper<WorkerServer> {

    /**
     * 根据workerId查询服务器
     */
    @Select("SELECT * FROM worker_servers WHERE id = #{workerId}")
    WorkerServer selectByWorkerId(@Param("workerId") String workerId);

    /**
     * 获取在线服务器列表
     */
    @Select("SELECT * FROM worker_servers WHERE status = 'ONLINE' AND enabled = 1 ORDER BY current_load ASC")
    List<WorkerServer> selectOnlineServers();

    /**
     * 获取可用服务器列表（在线且负载未满）
     */
    @Select("SELECT * FROM worker_servers WHERE status = 'ONLINE' AND enabled = 1 AND current_load < max_concurrent_tasks ORDER BY current_load ASC")
    List<WorkerServer> selectAvailableServers();

    /**
     * 更新服务器状态
     */
    @Update("UPDATE worker_servers SET status = #{status}, last_heartbeat = #{heartbeat}, updated_time = NOW() WHERE id = #{workerId}")
    int updateStatus(@Param("workerId") String workerId,
                    @Param("status") String status,
                    @Param("heartbeat") LocalDateTime heartbeat);

    /**
     * 更新服务器负载
     */
    @Update("UPDATE worker_servers SET current_load = #{currentLoad}, updated_time = NOW() WHERE id = #{workerId}")
    int updateLoad(@Param("workerId") String workerId, @Param("currentLoad") Integer currentLoad);

    /**
     * 更新任务统计
     */
    @Update("UPDATE worker_servers SET total_tasks_completed = #{completed}, total_tasks_failed = #{failed}, updated_time = NOW() WHERE worker_id = #{workerId}")
    int updateTaskStats(@Param("workerId") String workerId, 
                       @Param("completed") Long completed, 
                       @Param("failed") Long failed);

    /**
     * 增加完成任务数
     */
    @Update("UPDATE worker_servers SET total_tasks_completed = total_tasks_completed + 1, updated_time = NOW() WHERE worker_id = #{workerId}")
    int incrementCompletedTasks(@Param("workerId") String workerId);

    /**
     * 增加失败任务数
     */
    @Update("UPDATE worker_servers SET total_tasks_failed = total_tasks_failed + 1, updated_time = NOW() WHERE worker_id = #{workerId}")
    int incrementFailedTasks(@Param("workerId") String workerId);

    /**
     * 批量更新心跳超时的服务器状态为离线
     */
    @Update("UPDATE worker_servers SET status = 'OFFLINE', updated_time = NOW() WHERE last_heartbeat < #{timeoutThreshold} AND status != 'OFFLINE'")
    int updateTimeoutServersToOffline(@Param("timeoutThreshold") LocalDateTime timeoutThreshold);

    /**
     * 获取服务器统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as totalServers, " +
            "SUM(CASE WHEN status = 'ONLINE' THEN 1 ELSE 0 END) as onlineServers, " +
            "SUM(CASE WHEN status = 'OFFLINE' THEN 1 ELSE 0 END) as offlineServers, " +
            "SUM(CASE WHEN status = 'BUSY' THEN 1 ELSE 0 END) as busyServers, " +
            "SUM(CASE WHEN status = 'ERROR' THEN 1 ELSE 0 END) as errorServers, " +
            "SUM(current_load) as totalCurrentLoad, " +
            "SUM(max_concurrent_tasks) as totalMaxTasks, " +
            "SUM(total_tasks_completed) as totalCompleted, " +
            "SUM(total_tasks_failed) as totalFailed " +
            "FROM worker_servers")
    Map<String, Object> getServerStatistics();

    /**
     * 获取服务器性能数据（最近24小时）
     */
    @Select("SELECT " +
            "worker_id, " +
            "name, " +
            "status, " +
            "current_load, " +
            "max_concurrent_tasks, " +
            "total_tasks_completed, " +
            "total_tasks_failed, " +
            "last_heartbeat, " +
            "CASE " +
            "  WHEN total_tasks_completed + total_tasks_failed > 0 " +
            "  THEN ROUND(total_tasks_completed * 100.0 / (total_tasks_completed + total_tasks_failed), 2) " +
            "  ELSE NULL " +
            "END as success_rate, " +
            "CASE " +
            "  WHEN max_concurrent_tasks > 0 " +
            "  THEN ROUND(current_load * 100.0 / max_concurrent_tasks, 2) " +
            "  ELSE 0 " +
            "END as load_rate " +
            "FROM worker_servers " +
            "ORDER BY status DESC, current_load ASC")
    List<Map<String, Object>> getServerPerformanceData();

    /**
     * 检查workerId是否已存在
     */
    @Select("SELECT COUNT(*) FROM worker_servers WHERE id = #{workerId} AND id != #{excludeId}")
    int countByWorkerIdExcludeId(@Param("workerId") String workerId, @Param("excludeId") String excludeId);

    /**
     * 检查端口是否已被使用（从server_url解析）
     */
    @Select("SELECT COUNT(*) FROM worker_servers WHERE server_url LIKE CONCAT('%:', #{port}) AND id != #{excludeId}")
    int countByHostPortExcludeId(@Param("host") String host, @Param("port") Integer port, @Param("excludeId") String excludeId);

    /**
     * 获取负载最低的可用服务器
     */
    @Select("SELECT * FROM worker_servers WHERE status = 'ONLINE' AND current_load < max_concurrent_tasks ORDER BY current_load ASC LIMIT 1")
    WorkerServer selectLeastLoadedServer();

    /**
     * 根据状态获取服务器列表
     */
    @Select("SELECT * FROM worker_servers WHERE status = #{status} ORDER BY updated_time DESC")
    List<WorkerServer> selectByStatus(@Param("status") String status);

    /**
     * 获取最近活跃的服务器（按心跳时间排序）
     */
    @Select("SELECT * FROM worker_servers WHERE last_heartbeat IS NOT NULL ORDER BY last_heartbeat DESC LIMIT #{limit}")
    List<WorkerServer> selectRecentActiveServers(@Param("limit") Integer limit);

    /**
     * 批量删除指定ID的服务器
     */
    @Update("<script>" +
            "DELETE FROM worker_servers WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int deleteBatchByIds(@Param("ids") List<String> ids);
}
