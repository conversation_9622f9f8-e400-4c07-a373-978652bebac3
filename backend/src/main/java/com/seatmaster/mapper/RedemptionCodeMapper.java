package com.seatmaster.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seatmaster.entity.RedemptionCode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 兑换码Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Mapper
public interface RedemptionCodeMapper extends BaseMapper<RedemptionCode> {
    
    /**
     * 根据兑换码查询
     */
    @Select("SELECT * FROM redemption_codes WHERE code = #{code}")
    RedemptionCode findByCode(@Param("code") String code);
    
    /**
     * 分页查询兑换码（带状态筛选）
     */
    @Select("<script>" +
            "SELECT rc.*, u.username as created_by_username " +
            "FROM redemption_codes rc " +
            "LEFT JOIN users u ON rc.created_by_admin_id = u.id " +
            "WHERE 1=1 " +
            "<if test='isUsed != null'>" +
            "AND rc.is_used = #{isUsed} " +
            "</if>" +
            "<if test='batchId != null and batchId != \"\"'>" +
            "AND rc.batch_id = #{batchId} " +
            "</if>" +
            "ORDER BY rc.created_time DESC" +
            "</script>")
    IPage<RedemptionCode> selectPageWithStatus(
            Page<RedemptionCode> page,
            @Param("isUsed") Boolean isUsed,
            @Param("batchId") String batchId
    );
    
    /**
     * 统计兑换码数量
     */
    @Select("SELECT COUNT(*) FROM redemption_codes WHERE is_used = #{isUsed}")
    Long countByUsedStatus(@Param("isUsed") Boolean isUsed);
    
    /**
     * 统计过期的兑换码数量
     */
    @Select("SELECT COUNT(*) FROM redemption_codes WHERE expire_time < NOW() AND is_used = false")
    Long countExpired();
}
