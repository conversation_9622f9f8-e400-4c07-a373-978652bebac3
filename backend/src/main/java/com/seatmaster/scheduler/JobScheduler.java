package com.seatmaster.scheduler;

import com.seatmaster.service.DistributedTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * Job scheduler for periodic tasks
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JobScheduler {

    private final DistributedTaskService distributedTaskService;
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * Auto assign pending tasks every 5 seconds
     */
    // @Scheduled(cron = "*/5 * * * * ?")
    public void scheduleAutoAssignTasks() {
        String currentTime = LocalDateTime.now().format(TIME_FORMATTER);
        log.info("Task scheduler started at {}", currentTime);
        
        long startTime = System.currentTimeMillis();
        
        try {
            Map<String, Object> result = distributedTaskService.autoAssignPendingTasks();
            
            long duration = System.currentTimeMillis() - startTime;
            
            Object assignedCount = result.get("assignedCount");
            Object totalPendingTasks = result.get("totalPendingTasks");
            Object readyTasks = result.get("readyTasks");
            Object message = result.get("message");
            
            log.info("Task scheduler completed - time: {}ms", duration);
            log.info("Results - Assigned: {}, Ready: {}, Total: {}, Message: {}",
                assignedCount, readyTasks, totalPendingTasks, message);
            
            if (assignedCount != null && (Integer) assignedCount > 0) {
                log.info("Successfully assigned {} tasks", assignedCount);
            }
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Task scheduler failed - time: {}ms, Error: {}",
                duration, e.getMessage(), e);
        }
    }
    
    /**
     * Log scheduler health every 5 minutes
     */
    @Scheduled(cron = "0 */5 * * * ?")
    public void logSchedulerHealth() {
        String currentTime = LocalDateTime.now().format(TIME_FORMATTER);
        log.info("Scheduler heartbeat at {}", currentTime);
    }
}
