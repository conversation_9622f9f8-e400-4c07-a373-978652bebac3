package com.seatmaster.service;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 学习通API响应模型
 * 统一封装学习通API的响应结果
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class XuexitongResponse {
    
    /**
     * 响应状态
     */
    private boolean success;        // 是否成功
    private String message;         // 响应消息
    private String errorCode;       // 错误代码
    
    /**
     * 响应数据
     */
    private Map<String, Object> data;  // 响应数据
    
    /**
     * 元数据
     */
    private LocalDateTime timestamp;   // 响应时间
    private long executionTime;       // 执行耗时（毫秒）
    
    /**
     * 创建成功响应
     * @param message 成功消息
     * @return 响应对象
     */
    public static XuexitongResponse success(String message) {
        return success(message, null);
    }
    
    /**
     * 创建成功响应
     * @param message 成功消息
     * @param data 响应数据
     * @return 响应对象
     */
    public static XuexitongResponse success(String message, Map<String, Object> data) {
        XuexitongResponse response = new XuexitongResponse();
        response.setSuccess(true);
        response.setMessage(message);
        response.setData(data);
        response.setTimestamp(LocalDateTime.now());
        return response;
    }
    
    /**
     * 创建失败响应
     * @param message 错误消息
     * @return 响应对象
     */
    public static XuexitongResponse failure(String message) {
        return failure(message, null);
    }
    
    /**
     * 创建失败响应
     * @param message 错误消息
     * @param errorCode 错误代码
     * @return 响应对象
     */
    public static XuexitongResponse failure(String message, String errorCode) {
        XuexitongResponse response = new XuexitongResponse();
        response.setSuccess(false);
        response.setMessage(message);
        response.setErrorCode(errorCode);
        response.setTimestamp(LocalDateTime.now());
        return response;
    }
    
    /**
     * 设置执行耗时
     * @param startTime 开始时间
     */
    public void setExecutionTime(long startTime) {
        this.executionTime = System.currentTimeMillis() - startTime;
    }
    
    /**
     * 获取数据字段
     * @param key 字段名
     * @return 字段值
     */
    public Object getData(String key) {
        if (data == null) {
            return null;
        }
        return data.get(key);
    }
    
    /**
     * 获取字符串数据字段
     * @param key 字段名
     * @return 字符串值
     */
    public String getStringData(String key) {
        Object value = getData(key);
        return value != null ? value.toString() : null;
    }
    
    /**
     * 检查是否包含指定数据字段
     * @param key 字段名
     * @return 是否包含
     */
    public boolean hasData(String key) {
        return data != null && data.containsKey(key);
    }
    
    /**
     * 获取响应摘要
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("状态=").append(success ? "成功" : "失败");
        summary.append(", 消息=").append(message);
        
        if (errorCode != null) {
            summary.append(", 错误代码=").append(errorCode);
        }
        
        if (executionTime > 0) {
            summary.append(", 耗时=").append(executionTime).append("ms");
        }
        
        if (data != null && !data.isEmpty()) {
            summary.append(", 数据字段=").append(data.keySet());
        }
        
        return summary.toString();
    }
    
    /**
     * 检查是否为特定错误
     * @param errorCode 错误代码
     * @return 是否匹配
     */
    public boolean isError(String errorCode) {
        return !success && errorCode != null && errorCode.equals(this.errorCode);
    }
    
    /**
     * 检查是否为网络错误
     */
    public boolean isNetworkError() {
        return !success && (
            isError("NETWORK_ERROR") ||
            isError("TIMEOUT") ||
            isError("CONNECTION_FAILED") ||
            (message != null && (
                message.contains("网络") ||
                message.contains("超时") ||
                message.contains("连接")
            ))
        );
    }
    
    /**
     * 检查是否为认证错误
     */
    public boolean isAuthError() {
        return !success && (
            isError("AUTH_FAILED") ||
            isError("LOGIN_FAILED") ||
            isError("TOKEN_EXPIRED") ||
            (message != null && (
                message.contains("登录") ||
                message.contains("认证") ||
                message.contains("权限")
            ))
        );
    }
    
    /**
     * 检查是否为座位不可用错误
     */
    public boolean isSeatUnavailableError() {
        return !success && (
            isError("SEAT_UNAVAILABLE") ||
            isError("SEAT_OCCUPIED") ||
            (message != null && (
                message.contains("座位") ||
                message.contains("不可用") ||
                message.contains("已被占用")
            ))
        );
    }
    
    @Override
    public String toString() {
        return getSummary();
    }
}
