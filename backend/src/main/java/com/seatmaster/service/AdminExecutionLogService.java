package com.seatmaster.service;

import com.seatmaster.dto.ExecutionLogDTO;
import com.seatmaster.dto.ExecutionLogQueryDTO;

import java.util.List;
import java.util.Map;

/**
 * 管理员执行日志服务接口
 * 提供完整的技术调试信息和统计功能
 */
public interface AdminExecutionLogService {
    
    /**
     * 获取所有用户的执行日志（管理员视图）
     * 包含完整的技术调试信息
     */
    Map<String, Object> getAllExecutionLogs(ExecutionLogQueryDTO queryDTO);
    
    /**
     * 获取用户执行日志
     */
    Map<String, Object> getUserExecutionLogs(ExecutionLogQueryDTO queryDTO);
    
    /**
     * 获取执行统计信息
     * 包括总执行次数、成功率、平均耗时等
     */
    Map<String, Object> getExecutionStats(String username, String startDate, String endDate);
    
    /**
     * 获取执行日志详情
     */
    ExecutionLogDTO getExecutionLogDetail(Long logId);
    
    /**
     * 导出执行日志
     * 支持CSV和Excel格式
     */
    String exportExecutionLogs(String username, String status, String startDate, String endDate, String format);
    
    /**
     * 获取执行概览统计
     * 包括今日、本周、本月的执行情况
     */
    Map<String, Object> getExecutionOverview();
    
    /**
     * 获取执行趋势数据
     * 用于图表展示
     */
    List<Map<String, Object>> getExecutionTrends(int days);
    
    /**
     * 获取用户执行排行榜
     */
    List<Map<String, Object>> getUserExecutionRanking(int limit);
    
    /**
     * 获取房间使用统计
     */
    List<Map<String, Object>> getRoomUsageStats();
    
    /**
     * 获取错误分析统计
     */
    Map<String, Object> getErrorAnalysis();

    /**
     * 获取用户概览列表（用户视图模式）
     * 包含用户统计信息和分页
     */
    Map<String, Object> getUsersOverview(int page, int size, String searchUsername,
                                        String startDate, String endDate, String sortBy, String sortOrder, Long schoolId);

    /**
     * 获取特定用户的最近执行记录
     * 用于用户视图的展开详情
     */
    List<ExecutionLogDTO> getUserRecentLogs(String username, int limit, String startDate, String endDate);

    /**
     * 获取学校概览列表（学校视图模式）
     * 包含学校统计信息和分页
     *
     * @param page 页码
     * @param size 每页大小
     * @param searchSchoolName 学校名称搜索关键词
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param sortBy 排序字段
     * @param sortOrder 排序方向
     * @return 学校概览统计数据
     */
    Map<String, Object> getSchoolsOverview(int page, int size, String searchSchoolName,
                                          String startDate, String endDate, String sortBy, String sortOrder);

    /**
     * 获取特定学校的日期维度详细统计
     * 用于学校视图的展开详情
     *
     * @param schoolId 学校ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param page 页码
     * @param size 每页大小
     * @return 该学校按日期维度的详细统计数据
     */
    Map<String, Object> getSchoolDailyStats(Long schoolId, String startDate, String endDate, int page, int size);
}
