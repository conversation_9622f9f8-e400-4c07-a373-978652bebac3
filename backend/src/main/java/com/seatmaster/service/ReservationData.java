package com.seatmaster.service;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * 预约数据模型
 * 封装学习通预约所需的所有信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReservationData {
    
    /**
     * 用户信息
     */
    private String username;        // 学习通用户名
    private String password;        // 学习通密码
    private Long userId;           // 系统用户ID
    
    /**
     * 预约信息
     */
    private Long reservationId;    // 预约记录ID
    private String roomNum;        // 房间号（来自rooms表）
    private String seatId;         // 座位号
    private LocalTime startTime;   // 开始时间
    private LocalTime endTime;     // 结束时间
    
    /**
     * 预约配置
     */
    private String reservationOpenTime;  // 预约开放时间（如：08:00）
    private String reservationType;      // 预约类型：SAME_DAY/ADVANCE_ONE_DAY
    
    /**
     * 执行配置
     */
    private LocalDate reservationDate;   // 预约日期（根据类型计算）
    private boolean isRetry;            // 是否为重试
    private int retryCount;             // 重试次数
    
    /**
     * 根据预约类型计算预约日期
     * @return 预约日期
     */
    public LocalDate getReservationDate() {
        if (reservationDate != null) {
            return reservationDate;
        }
        
        LocalDate today = LocalDate.now();
        
        if ("ADVANCE_ONE_DAY".equals(reservationType)) {
            // 提前一天预约，预约明天的座位
            reservationDate = today.plusDays(1);
        } else {
            // 当天预约，预约今天的座位
            reservationDate = today;
        }
        
        return reservationDate;
    }
    
    /**
     * 获取预约类型的中文描述
     */
    public String getReservationTypeText() {
        if ("ADVANCE_ONE_DAY".equals(reservationType)) {
            return "提前一天预约";
        } else {
            return "当天预约";
        }
    }
    
    /**
     * 获取格式化的时间段
     */
    public String getTimeSlot() {
        if (startTime != null && endTime != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
            return startTime.format(formatter) + "-" + endTime.format(formatter);
        }
        return "未设置";
    }
    
    /**
     * 获取格式化的预约开放时间
     */
    public String getFormattedOpenTime() {
        if (reservationOpenTime != null && !reservationOpenTime.trim().isEmpty()) {
            return reservationOpenTime;
        }
        return "08:00"; // 默认开放时间
    }
    
    /**
     * 检查数据完整性
     */
    public boolean isValid() {
        return username != null && !username.trim().isEmpty() &&
               password != null && !password.trim().isEmpty() &&
               roomNum != null && !roomNum.trim().isEmpty() &&
               seatId != null && !seatId.trim().isEmpty() &&
               startTime != null &&
               endTime != null &&
               reservationType != null;
    }
    
    /**
     * 获取数据摘要（用于日志）
     */
    public String getSummary() {
        return String.format("用户=%s, 房间=%s, 座位=%s, 时间=%s, 日期=%s, 类型=%s",
                           username, roomNum, seatId, getTimeSlot(), 
                           getReservationDate(), getReservationTypeText());
    }
    
    /**
     * 创建重试数据
     */
    public ReservationData createRetryData() {
        return ReservationData.builder()
                .username(this.username)
                .password(this.password)
                .userId(this.userId)
                .reservationId(this.reservationId)
                .roomNum(this.roomNum)
                .seatId(this.seatId)
                .startTime(this.startTime)
                .endTime(this.endTime)
                .reservationOpenTime(this.reservationOpenTime)
                .reservationType(this.reservationType)
                .reservationDate(this.reservationDate)
                .isRetry(true)
                .retryCount(this.retryCount + 1)
                .build();
    }
    
    /**
     * 从数据库记录创建预约数据
     */
    public static class Builder {
        
        /**
         * 从数据库查询结果创建预约数据
         * @param username 用户名
         * @param password 密码
         * @param userId 用户ID
         * @param reservationId 预约ID
         * @param roomNum 房间号
         * @param seatId 座位号
         * @param startTime 开始时间
         * @param endTime 结束时间
         * @param reservationOpenTime 预约开放时间
         * @param reservationType 预约类型
         * @return 预约数据
         */
        public static ReservationData fromDatabase(String username, String password, Long userId,
                                                 Long reservationId, String roomNum, String seatId,
                                                 LocalTime startTime, LocalTime endTime,
                                                 String reservationOpenTime, String reservationType) {
            return ReservationData.builder()
                    .username(username)
                    .password(password)
                    .userId(userId)
                    .reservationId(reservationId)
                    .roomNum(roomNum)
                    .seatId(seatId)
                    .startTime(startTime)
                    .endTime(endTime)
                    .reservationOpenTime(reservationOpenTime)
                    .reservationType(reservationType)
                    .isRetry(false)
                    .retryCount(0)
                    .build();
        }
    }
}
