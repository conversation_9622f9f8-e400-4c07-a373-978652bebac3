package com.seatmaster.service.impl;

import com.seatmaster.dto.ExecutionLogDTO;
import com.seatmaster.dto.ExecutionLogQueryDTO;
import com.seatmaster.service.ExecutionLogQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.*;
import java.util.*;

/**
 * 执行日志服务实现
 */
@Service
public class ExecutionLogQueryServiceImpl implements ExecutionLogQueryService {

    private static final Logger log = LoggerFactory.getLogger(ExecutionLogQueryServiceImpl.class);

    // SQL查询常量
    private static final String BASE_SELECT_SQL =
        "SELECT rl.*, u.id as user_id, u.name as user_display_name, " +
        "       rm.name as room_name, s.name as school_name " +
        "FROM reservation_logs rl " +
        "LEFT JOIN reservations r ON rl.reservation_id = r.id " +
        "LEFT JOIN users u ON u.username = rl.username " +
        "LEFT JOIN rooms rm ON rm.roomNum = rl.roomid " +
        "LEFT JOIN schools s ON rm.school_id = s.id ";

    private static final String COUNT_SELECT_SQL =
        "SELECT COUNT(*) FROM reservation_logs rl " +
        "LEFT JOIN reservations r ON rl.reservation_id = r.id " +
        "LEFT JOIN users u ON u.username = rl.username " +
        "LEFT JOIN rooms rm ON rm.roomNum = rl.roomid " +
        "LEFT JOIN schools s ON rm.school_id = s.id ";

    private static final String ORDER_BY_CLAUSE =
        "ORDER BY COALESCE(rl.api_response_time, rl.created_at) DESC ";



    @Autowired
    private DataSource dataSource;

    @Override
    public Map<String, Object> getUserExecutionLogs(ExecutionLogQueryDTO queryDTO) {
        long startTime = System.currentTimeMillis();
        log.debug("开始查询用户执行日志: userId={}, username={}, page={}, size={}",
            queryDTO.getUserId(), queryDTO.getUsername(), queryDTO.getPage(), queryDTO.getSize());

        List<ExecutionLogDTO> logs = new ArrayList<>();
        int totalCount = 0;

        try (Connection connection = dataSource.getConnection()) {

            // 构建查询SQL
            StringBuilder sql = new StringBuilder(BASE_SELECT_SQL);
            List<Object> params = new ArrayList<>();

            // 添加WHERE条件
            appendWhereConditions(sql, params, queryDTO);

            // 添加排序和分页
            sql.append(ORDER_BY_CLAUSE);
            sql.append("LIMIT ? OFFSET ?");
            params.add(queryDTO.getSize());
            params.add(queryDTO.getOffset());

            // 执行查询
            logs = executeQuery(connection, sql.toString(), params);

            // 查询总数
            totalCount = getTotalCount(connection, queryDTO);

            long queryTime = System.currentTimeMillis() - startTime;
            log.debug("查询完成: 返回{}条记录, 总计{}条, 耗时{}ms",
                logs.size(), totalCount, queryTime);

        } catch (SQLException e) {
            log.error("查询执行日志失败: {}", e.getMessage(), e);
            throw new RuntimeException("查询执行日志失败", e);
        }

        return buildResultMap(logs, totalCount, queryDTO);
    }

    /**
     * 添加WHERE条件
     */
    private void appendWhereConditions(StringBuilder sql, List<Object> params, ExecutionLogQueryDTO queryDTO) {
        // 支持通过userId或username查询
        if (queryDTO.getUserId() != null) {
            sql.append("WHERE u.id = ? ");
            params.add(queryDTO.getUserId());
        } else if (queryDTO.getUsername() != null) {
            sql.append("WHERE rl.username = ? ");
            params.add(queryDTO.getUsername());
        } else {
            throw new IllegalArgumentException("必须提供userId或username");
        }

        // 添加状态过滤
        if (queryDTO.hasStatusFilter()) {
            sql.append("AND rl.status = ? ");
            params.add(queryDTO.getStatus());
        }

        // 添加日期过滤
        if (queryDTO.getStartDate() != null) {
            sql.append("AND DATE(rl.reserve_date) >= ? ");
            params.add(queryDTO.getStartDate());
        }
        if (queryDTO.getEndDate() != null) {
            sql.append("AND DATE(rl.reserve_date) <= ? ");
            params.add(queryDTO.getEndDate());
        }
    }

    /**
     * 执行查询并返回结果列表
     */
    private List<ExecutionLogDTO> executeQuery(Connection connection, String sql, List<Object> params) throws SQLException {
        List<ExecutionLogDTO> logs = new ArrayList<>();

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            setParameters(stmt, params);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    ExecutionLogDTO log = mapResultSetToDTO(rs);
                    logs.add(log);
                }
            }
        }

        return logs;
    }

    /**
     * 统一的参数设置方法
     */
    private void setParameters(PreparedStatement stmt, List<Object> params) throws SQLException {
        for (int i = 0; i < params.size(); i++) {
            stmt.setObject(i + 1, params.get(i));
        }
    }

    /**
     * 构建返回结果Map
     */
    private Map<String, Object> buildResultMap(List<ExecutionLogDTO> logs, int totalCount, ExecutionLogQueryDTO queryDTO) {
        Map<String, Object> result = new HashMap<>();
        result.put("logs", logs);
        result.put("totalCount", totalCount);
        result.put("page", queryDTO.getPage());
        result.put("size", queryDTO.getSize());
        result.put("totalPages", (int) Math.ceil((double) totalCount / queryDTO.getSize()));
        return result;
    }





    @Override
    public String exportUserLogs(Long userId, String startDate, String endDate) {
        // TODO: 实现日志导出功能
        return "logs/exports/user_" + userId + "_" + System.currentTimeMillis() + ".log";
    }

    @Override
    public List<ExecutionLogDTO> getRecentExecutionLogs(Long userId, int limit) {
        log.debug("查询最近执行日志: userId={}, limit={}", userId, limit);

        try (Connection connection = dataSource.getConnection()) {
            String sql = BASE_SELECT_SQL + "WHERE u.id = ? " + ORDER_BY_CLAUSE + "LIMIT ?";
            List<Object> params = Arrays.asList(userId, limit);

            return executeQuery(connection, sql, params);

        } catch (SQLException e) {
            log.error("查询最近执行日志失败: {}", e.getMessage(), e);
            throw new RuntimeException("查询最近执行日志失败", e);
        }
    }

    // 辅助方法
    private ExecutionLogDTO mapResultSetToDTO(ResultSet rs) throws SQLException {
        ExecutionLogDTO dto = new ExecutionLogDTO();

        // 基础字段映射
        dto.setId(rs.getLong("id"));
        dto.setReservationId(rs.getLong("reservation_id"));
        dto.setUsername(rs.getString("username"));

        // 预约基础信息
        dto.setRoomid(rs.getString("roomid"));
        dto.setSeatid(rs.getString("seatid"));

        // 预约时间信息
        java.sql.Date reserveDate = rs.getDate("reserve_date");
        if (reserveDate != null) {
            dto.setReserveDate(reserveDate.toLocalDate());
        }

        Time startTime = rs.getTime("start_time");
        if (startTime != null) {
            dto.setStartTime(startTime.toLocalTime());
        }

        Time endTime = rs.getTime("end_time");
        if (endTime != null) {
            dto.setEndTime(endTime.toLocalTime());
        }

        // 执行结果信息
        dto.setStatus(rs.getString("status"));
        dto.setErrorMessage(rs.getString("error_message"));

        // API响应信息 - 处理JSON类型
        String apiResponseStr = rs.getString("api_response");
        if (apiResponseStr != null && !apiResponseStr.trim().isEmpty()) {
            dto.setApiResponse(apiResponseStr); // 可以考虑解析为JSON对象
        }

        // API响应时间
        Timestamp apiResponseTime = rs.getTimestamp("api_response_time");
        if (apiResponseTime != null) {
            dto.setApiResponseTime(apiResponseTime.toLocalDateTime());
        }

        // 尝试次数和执行时间
        dto.setAttemptCount(rs.getObject("attempt_count", Integer.class));

        BigDecimal executionTime = rs.getBigDecimal("execution_time");
        if (executionTime != null) {
            dto.setExecutionTime(executionTime);
        }

        // 创建时间
        Timestamp createdAt = rs.getTimestamp("created_at");
        if (createdAt != null) {
            dto.setCreatedAt(createdAt.toLocalDateTime());
        }

        // 关联信息
        dto.setUserId(rs.getObject("user_id", Long.class));
        dto.setUserDisplayName(rs.getString("user_display_name"));
        dto.setRoomName(rs.getString("room_name"));
        dto.setSchoolName(rs.getString("school_name"));

        return dto;
    }

    private int getTotalCount(Connection connection, ExecutionLogQueryDTO queryDTO) throws SQLException {
        StringBuilder sql = new StringBuilder(COUNT_SELECT_SQL);
        List<Object> params = new ArrayList<>();

        // 添加WHERE条件（复用逻辑）
        appendWhereConditions(sql, params, queryDTO);

        try (PreparedStatement stmt = connection.prepareStatement(sql.toString())) {
            setParameters(stmt, params);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }

        return 0;
    }






}
