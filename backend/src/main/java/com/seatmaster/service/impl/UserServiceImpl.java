package com.seatmaster.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.seatmaster.config.UserDefaultsProperties;
import com.seatmaster.dto.AuthResponse;
import com.seatmaster.dto.LoginRequest;
import com.seatmaster.dto.RegisterRequest;
import com.seatmaster.entity.User;
import com.seatmaster.mapper.UserMapper;
import com.seatmaster.service.UserService;
import com.seatmaster.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    private final UserDefaultsProperties userDefaultsProperties;
    
    @Override
    public AuthResponse register(RegisterRequest request) {
        // 检查用户名是否已存在
        if (existsByUsername(request.getUsername())) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 创建新用户 - 使用明文密码
        User user = new User();
        user.setUsername(request.getUsername());
        user.setPassword(request.getPassword()); // 直接存储明文密码
        user.setName(request.getName());
        user.setRole(User.Role.USER);
        user.setRemainingDays(userDefaultsProperties.getTrialDays()); // 使用配置的默认试用天数
        user.setCreatedTime(LocalDateTime.now());
        
        userMapper.insert(user);
        
        // 生成JWT token
        String token = jwtUtil.generateToken(user.getUsername());
        
        return new AuthResponse(token, user.getUsername(), user.getName(), user.getRole().name());
    }
    
    @Override
    public AuthResponse login(LoginRequest request) {
        User user = findByUsername(request.getUsername());
        if (user == null) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 改为明文密码比较
        if (!request.getPassword().equals(user.getPassword())) {
            throw new RuntimeException("用户名或密码错误");
        }
        
        // 生成JWT token
        String token = jwtUtil.generateToken(user.getUsername());
        
        return new AuthResponse(token, user.getUsername(), user.getName(), user.getRole().name());
    }
    
    @Override
    public User findByUsername(String username) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username);
        return userMapper.selectOne(queryWrapper);
    }
    
    @Override
    public User findById(Long userId) {
        return userMapper.selectById(userId);
    }
    
    @Override
    public boolean existsByUsername(String username) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username);
        return userMapper.selectCount(queryWrapper) > 0;
    }
    
    @Override
    public boolean updateById(User user) {
        try {
            return userMapper.updateById(user) > 0;
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public boolean checkPassword(String rawPassword, String encodedPassword) {
        // 改为明文密码比较
        return rawPassword.equals(encodedPassword);
    }
    
    @Override
    public boolean changePassword(Long userId, String newPassword) {
        try {
            User user = userMapper.selectById(userId);
            if (user == null) {
                return false;
            }
            
            // 直接存储明文密码
            user.setPassword(newPassword);
            return userMapper.updateById(user) > 0;
        } catch (Exception e) {
            return false;
        }
    }
} 