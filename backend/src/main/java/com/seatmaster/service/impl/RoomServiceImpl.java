package com.seatmaster.service.impl;

import com.seatmaster.entity.Room;
import com.seatmaster.mapper.RoomMapper;
import com.seatmaster.service.RoomService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class RoomServiceImpl implements RoomService {

    private final RoomMapper roomMapper;

    @Override
    public List<Room> getAllRooms() {
        return roomMapper.selectList(null);
    }

    @Override
    public List<Room> getRoomsBySchoolId(Long schoolId) {
        return roomMapper.findBySchoolId(schoolId);
    }

    @Override
    public Room getRoomById(Long id) {
        return roomMapper.selectById(id);
    }

    @Override
    public Room createRoom(Room room) {
        room.setCreatedTime(java.time.LocalDateTime.now());
        roomMapper.insert(room);
        return room;
    }

    @Override
    public Room updateRoom(Room room) {
        roomMapper.updateById(room);
        return room;
    }

    @Override
    public boolean deleteRoom(Long id) {
        return roomMapper.deleteById(id) > 0;
    }
}
