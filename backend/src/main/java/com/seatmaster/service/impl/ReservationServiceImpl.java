package com.seatmaster.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.seatmaster.entity.Reservation;
import com.seatmaster.entity.Room;
import com.seatmaster.entity.User;
import com.seatmaster.dto.UserProfileResponse;
import com.seatmaster.mapper.ReservationMapper;
import com.seatmaster.mapper.RoomMapper;
import com.seatmaster.mapper.UserMapper;
import com.seatmaster.service.DistributedTaskService;
import com.seatmaster.service.ReservationService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Service
public class ReservationServiceImpl implements ReservationService {
    
    private static final Logger logger = LoggerFactory.getLogger(ReservationServiceImpl.class);
    
    @Autowired
    private ReservationMapper reservationMapper;
    
    @Autowired
    private RoomMapper roomMapper;
    
    @Autowired
    private UserMapper userMapper;

    @Autowired
    @Lazy
    private DistributedTaskService distributedTaskService;

    /**
     * 自动删除剩余天数为0的用户的所有活跃预约
     */
    @Transactional
    public void deleteReservationsForUsersWithZeroDays() {
        // 找到剩余天数为0的用户
        QueryWrapper<User> userWrapper = new QueryWrapper<>();
        userWrapper.le("remaining_days", 0);
        List<User> usersWithZeroDays = userMapper.selectList(userWrapper);

        int deletedCount = 0;
        for (User user : usersWithZeroDays) {
            // 删除该用户的所有预约
            QueryWrapper<Reservation> reservationWrapper = new QueryWrapper<>();
            reservationWrapper.eq("user_id", user.getId());

            List<Reservation> activeReservations = reservationMapper.selectList(reservationWrapper);
            for (Reservation reservation : activeReservations) {
                reservationMapper.deleteById(reservation.getId());
                deletedCount++;
                logger.info("删除用户{}的预约记录，预约ID: {}, 座位: {}",
                           user.getUsername(), reservation.getId(), reservation.getSeatId());
            }
        }

        if (deletedCount > 0) {
            logger.info("共删除了{}个剩余天数为0的用户的预约记录", deletedCount);
        }
    }

    /**
     * 立即检查并删除指定用户的预约（如果剩余天数为0）
     * @param userId 用户ID
     */
    @Transactional
    public void checkAndDeleteUserReservationsIfZeroDays(Long userId) {
        try {
            // 查询用户信息
            User user = userMapper.selectById(userId);
            if (user == null) {
                return;
            }

            // 如果剩余天数为0，删除其所有预约
            if (user.getRemainingDays() <= 0) {
                QueryWrapper<Reservation> reservationWrapper = new QueryWrapper<>();
                reservationWrapper.eq("user_id", userId);

                List<Reservation> activeReservations = reservationMapper.selectList(reservationWrapper);
                for (Reservation reservation : activeReservations) {
                    reservationMapper.deleteById(reservation.getId());
                    logger.info("立即删除用户{}的预约记录，预约ID: {}, 座位: {}",
                               user.getUsername(), reservation.getId(), reservation.getSeatId());
                }

                if (!activeReservations.isEmpty()) {
                    logger.info("用户{}剩余天数为0，立即删除了{}个预约记录",
                               user.getUsername(), activeReservations.size());
                }
            }
        } catch (Exception e) {
            logger.error("检查并删除用户{}预约时发生错误: {}", userId, e.getMessage(), e);
        }
    }

    /**
     * 注意：由于现在采用删除策略而不是暂停策略，
     * 不再需要恢复预约的方法，因为删除的预约无法恢复。
     * 用户需要重新创建预约。
     */
    
    /**
     * 清理用户的重复预约，确保每个用户只有一个活跃预约
     * @param userId 用户ID
     */
    @Transactional
    private void cleanupDuplicateReservations(Long userId) {
        // 查找用户的所有预约
        QueryWrapper<Reservation> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId)
               .orderByDesc("created_time"); // 按创建时间降序，保留最新的
        
        List<Reservation> activeReservations = reservationMapper.selectList(wrapper);
        
        // 如果有多个活跃预约，取消除最新的以外的所有预约
        if (activeReservations.size() > 1) {
            for (int i = 1; i < activeReservations.size(); i++) {
                Reservation oldReservation = activeReservations.get(i);
                oldReservation.setErrorMessage("系统自动取消重复预约");
                reservationMapper.updateById(oldReservation);
            }
        }
    }
    
    @Override
    @Transactional
    public Reservation createOrUpdateReservation(Long userId, Long roomId, String seatId, 
                                               LocalTime startTime, LocalTime endTime, 
                                               String reservationOpenTime, String reservationType) {
        try {
            logger.info("开始处理预约请求: 用户ID={}, 房间ID={}, 座位号={}, 时间=[{}-{}]", 
                       userId, roomId, seatId, startTime, endTime);
            
            // 1. 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            // 检查用户剩余天数，剩余天数为0时不允许预约
            if (user.getRemainingDays() <= 0) {
                logger.warn("用户{}剩余天数为{}，不允许预约", userId, user.getRemainingDays());
                throw new RuntimeException("您的剩余天数不足，无法进行预约。请联系管理员充值。");
            } else {
                logger.info("用户{}剩余天数为{}，状态正常", userId, user.getRemainingDays());
            }
            
            // 2. 检查房间是否存在
            Room room = roomMapper.selectById(roomId);
            if (room == null) {
                throw new RuntimeException("房间不存在");
            }
            
            // 3. 检查时间间隔是否至少15分钟
            long diffMinutes = java.time.Duration.between(startTime, endTime).toMinutes();
            if (diffMinutes <= 0) {
                // 处理跨午夜的情况
                diffMinutes = java.time.Duration.between(startTime, endTime.plusHours(24)).toMinutes();
            }
            if (diffMinutes < 15) {
                throw new RuntimeException("预约时间间隔至少需要15分钟");
            }

            // 3.5. 检查房间最大预约时长限制（仅用于前端提示）
            if (room.getMaxReservationHours() != null) {
                long totalMinutes = java.time.Duration.between(startTime, endTime).toMinutes();
                if (totalMinutes <= 0) {
                    // 处理跨午夜的情况
                    totalMinutes = java.time.Duration.between(startTime, endTime.plusHours(24)).toMinutes();
                }
                double totalHours = totalMinutes / 60.0;
                double maxHours = room.getMaxReservationHours().doubleValue();

                if (totalHours > maxHours) {
                    logger.info("预约时长{:.1f}小时超过房间限制{:.1f}小时，但系统已移除拆分功能，将创建单一预约记录",
                        totalHours, maxHours);
                }
            }
            
            // 4. 查找用户的现有预约
            QueryWrapper<Reservation> userWrapper = new QueryWrapper<>();
            userWrapper.eq("user_id", userId)
                      .orderByDesc("created_time");

            List<Reservation> userReservations = reservationMapper.selectList(userWrapper);
            logger.info("用户{}当前有{}个活跃预约", userId, userReservations.size());

            // 5. 如果用户已有预约，提示使用更新接口
            if (!userReservations.isEmpty()) {
                Reservation existingReservation = userReservations.get(0);
                logger.warn("用户{}已有活跃预约，建议使用更新接口。现有预约: ID={}, 座位={}, 时间=[{}-{}]",
                           userId, existingReservation.getId(), existingReservation.getSeatId(),
                           existingReservation.getStartTime(), existingReservation.getEndTime());
                throw new RuntimeException("系统检测到您已有一个活跃的预约（座位 " + existingReservation.getSeatId() +
                                         "），如需修改预约信息，请直接在当前页面进行更新操作");
            }

            // 现在确认这是创建新预约的操作
            Reservation currentReservation = null;
            
            // 6. 检查目标座位是否被其他用户占用
            QueryWrapper<Reservation> seatConflictWrapper = new QueryWrapper<>();
            seatConflictWrapper.eq("room_id", roomId)
                              .eq("seat_num", seatId)
                              .ne("user_id", userId); // 排除当前用户

            // 添加更详细的日志，记录SQL查询条件
            logger.info("座位冲突查询条件: room_id={}, seat_num={}, user_id!={}",
                      roomId, seatId, userId);

            // 检查数据库中是否存在任何与该座位相关的记录（不限状态）
            QueryWrapper<Reservation> allSeatRecordsWrapper = new QueryWrapper<>();
            allSeatRecordsWrapper.eq("room_id", roomId).eq("seat_num", seatId);
            List<Reservation> allSeatRecords = reservationMapper.selectList(allSeatRecordsWrapper);
            logger.info("座位{}在房间{}共有{}条记录", seatId, roomId, allSeatRecords.size());
            for (Reservation record : allSeatRecords) {
                logger.info("座位记录: ID={}, 用户ID={}, 创建时间={}",
                          record.getId(), record.getUserId(), record.getCreatedTime());
            }

            List<Reservation> seatConflictReservations = reservationMapper.selectList(seatConflictWrapper);
            logger.info("座位{}冲突检查结果: 房间{}, 排除用户{}, 找到{}个其他用户的活跃预约",
                        seatId, roomId, userId, seatConflictReservations.size());

            // 打印详细的冲突预约信息
            for (Reservation conflict : seatConflictReservations) {
                logger.info("发现其他用户预约: ID={}, 用户ID={}, 座位={}, 时间=[{}-{}]",
                           conflict.getId(), conflict.getUserId(), conflict.getSeatId(),
                           conflict.getStartTime(), conflict.getEndTime());
            }

            // 检查时间冲突：只有时间重叠的预约才算冲突
            List<Reservation> timeConflictReservations = seatConflictReservations.stream()
                .filter(reservation -> isTimeConflict(startTime, endTime,
                        reservation.getStartTime(), reservation.getEndTime()))
                .collect(java.util.stream.Collectors.toList());

            logger.info("座位{}时间冲突检查结果: 请求时间[{}-{}], 发现{}个时间冲突的预约",
                        seatId, startTime, endTime, timeConflictReservations.size());

            // 打印时间冲突的预约详情
            for (Reservation conflict : timeConflictReservations) {
                logger.warn("时间冲突预约: ID={}, 用户ID={}, 时间=[{}-{}]",
                           conflict.getId(), conflict.getUserId(),
                           conflict.getStartTime(), conflict.getEndTime());
            }

            // 检查是否确实存在时间冲突
            if (!timeConflictReservations.isEmpty()) {
                logger.warn("座位{}在时间段[{}-{}]已被其他用户预约，拒绝本次预约请求",
                           seatId, startTime, endTime);
                throw new RuntimeException("座位 " + seatId + " 在请求的时间段已被其他用户预约，请选择其他时间或座位");
            } else {
                logger.info("座位{}在时间段[{}-{}]没有冲突，可以继续预约流程", seatId, startTime, endTime);
            }
            
            // 7. 执行创建操作
            Reservation reservation;
            boolean isNewReservation = true; // 现在我们确定这是新预约

            logger.info("创建新预约: 用户ID={}, 座位={}, 预约开放时间={}, 预约类型={}",
                       userId, seatId, reservationOpenTime, reservationType);
            reservation = new Reservation();
            reservation.setUserId(userId);
            reservation.setRoomId(roomId);
            reservation.setSeatId(seatId);
            reservation.setStartTime(startTime);
            reservation.setEndTime(endTime);
            reservation.setReservationOpenTime(reservationOpenTime);
            reservation.setReservationType(reservationType);
            reservation.setCreatedTime(LocalDateTime.now());

            try {
                int insertResult = reservationMapper.insert(reservation);
                if (insertResult == 0) {
                    throw new RuntimeException("创建预约失败");
                }
                logger.info("新预约创建成功: ID={}", reservation.getId());
            } catch (Exception e) {
                logger.error("创建预约时发生异常: {}", e.getMessage(), e);
                // 检查是否是唯一约束冲突
                if (e.getMessage() != null && (e.getMessage().contains("unique_user_reservation") ||
                    e.getMessage().contains("Duplicate entry"))) {

                    logger.warn("用户{}已有活跃预约，无法创建新预约。错误信息: {}", userId, e.getMessage());

                    // 查询用户当前的预约
                    QueryWrapper<Reservation> userCheckWrapper2 = new QueryWrapper<>();
                    userCheckWrapper2.eq("user_id", userId);
                    List<Reservation> existingUserReservations = reservationMapper.selectList(userCheckWrapper2);

                    if (!existingUserReservations.isEmpty()) {
                        Reservation existingReservation = existingUserReservations.get(0);
                        logger.info("用户当前预约: ID={}, 座位={}, 时间=[{}-{}]",
                                  existingReservation.getId(), existingReservation.getSeatId(),
                                  existingReservation.getStartTime(), existingReservation.getEndTime());
                        throw new RuntimeException("系统检测到您已有一个活跃的预约（座位 " + existingReservation.getSeatId() +
                                                 "），如需修改预约信息，请直接在当前页面进行更新操作");
                    } else {
                        logger.warn("数据库约束冲突，但查询未发现用户预约记录，可能存在数据不一致问题");
                        throw new RuntimeException("预约创建失败，请稍后重试");
                    }
                }
                throw e; // 重新抛出其他类型的异常
            }
            
            // 8. 如果是新预约，扣减用户剩余天数（只有剩余天数大于0时才扣减）
            if (isNewReservation) {
                if (user.getRemainingDays() > 0) {
                    user.setRemainingDays(user.getRemainingDays() - 1);
                    userMapper.updateById(user);
                    logger.info("扣减用户{}剩余天数，当前剩余: {}", userId, user.getRemainingDays());
                } else {
                    logger.info("用户{}剩余天数为{}，不扣减天数，状态为暂停", userId, user.getRemainingDays());
                }
            }
            
            // 9. 最终验证：确保用户只有一个预约
            QueryWrapper<Reservation> finalCheckWrapper = new QueryWrapper<>();
            finalCheckWrapper.eq("user_id", userId);
            long activeCount = reservationMapper.selectCount(finalCheckWrapper);
            if (activeCount > 1) {
                logger.error("最终验证失败：用户{}存在{}个预约", userId, activeCount);
                throw new RuntimeException("系统错误：用户存在多个预约");
            }
            
            logger.info("预约处理完成: 用户ID={}, 座位={}, 预约ID={}", userId, seatId, reservation.getId());

            // 10. 如果是新创建的预约，立即触发自动分配
            if (isNewReservation) {
                try {
                    if (distributedTaskService != null) {
                        logger.info("触发自动分配: 预约ID={}", reservation.getId());
                        java.util.Map<String, Object> assignResult = distributedTaskService.autoAssignPendingTasks();
                        Integer assignedCount = (Integer) assignResult.get("assignedCount");
                        if (assignedCount != null && assignedCount > 0) {
                            logger.info("自动分配成功: 分配了{}个任务", assignedCount);
                        } else {
                            logger.info("自动分配完成: 当前无可分配任务或无可用服务器");
                        }
                    } else {
                        logger.warn("DistributedTaskService 未初始化，跳过自动分配");
                    }
                } catch (Exception e) {
                    // 自动分配失败不影响预约创建
                    logger.warn("自动分配任务失败，但预约创建成功: {}", e.getMessage());
                }
            }

            return reservation;
        } catch (Exception e) {
            logger.error("预约处理异常: 用户ID={}, 座位={}, 异常信息: {}", userId, seatId, e.getMessage(), e);
            
            // 检查数据库约束异常
            if (e.getMessage() != null) {
                if (e.getMessage().contains("unique_user_reservation") ||
                    e.getMessage().contains("Duplicate entry")) {
                    logger.warn("数据库约束触发: 用户{}已有活跃预约", userId);

                    // 查询用户当前的预约
                    QueryWrapper<Reservation> userCheckWrapper3 = new QueryWrapper<>();
                    userCheckWrapper3.eq("user_id", userId);
                    List<Reservation> allUserReservations = reservationMapper.selectList(userCheckWrapper3);

                    if (!allUserReservations.isEmpty()) {
                        Reservation existingReservation = allUserReservations.get(0);
                        logger.info("用户当前预约: ID={}, 座位={}, 时间=[{}-{}]",
                                  existingReservation.getId(), existingReservation.getSeatId(),
                                  existingReservation.getStartTime(), existingReservation.getEndTime());
                        throw new RuntimeException("系统检测到您已有一个活跃的预约（座位 " + existingReservation.getSeatId() +
                                                 "），如需修改预约信息，请直接在当前页面进行更新操作");
                    } else {
                        logger.warn("数据库约束冲突，但查询未发现用户预约记录，可能存在数据不一致问题");
                        throw new RuntimeException("预约操作失败，请稍后重试");
                    }
                }
                if (e.getMessage().contains("座位已被其他用户预约")) {
                    logger.warn("触发器约束触发: 座位{}已被占用", seatId);
                    throw e; // 直接重新抛出这个异常
                }
            }
            
            // 重新抛出其他异常
            throw e;
        }
    }

    @Override
    @Transactional
    public Reservation updateReservation(Long userId, Long roomId, String seatId,
                                       LocalTime startTime, LocalTime endTime,
                                       String reservationOpenTime, String reservationType) {
        try {
            logger.info("开始处理预约更新请求: 用户ID={}, 房间ID={}, 座位号={}, 时间=[{}-{}]",
                       userId, roomId, seatId, startTime, endTime);

            // 1. 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            // 检查用户剩余天数，剩余天数为0时不允许预约
            if (user.getRemainingDays() <= 0) {
                logger.warn("用户{}剩余天数为{}，不允许预约", userId, user.getRemainingDays());
                throw new RuntimeException("您的剩余天数不足，无法进行预约。请联系管理员充值。");
            }

            // 2. 检查房间是否存在
            Room room = roomMapper.selectById(roomId);
            if (room == null) {
                throw new RuntimeException("房间不存在");
            }

            // 3. 检查时间间隔是否至少15分钟
            long diffMinutes = java.time.Duration.between(startTime, endTime).toMinutes();
            if (diffMinutes <= 0) {
                // 处理跨午夜的情况
                diffMinutes = java.time.Duration.between(startTime, endTime.plusHours(24)).toMinutes();
            }
            if (diffMinutes < 15) {
                throw new RuntimeException("预约时间间隔至少需要15分钟");
            }

            // 4. 查找用户的现有预约
            QueryWrapper<Reservation> userWrapper = new QueryWrapper<>();
            userWrapper.eq("user_id", userId)
                      .orderByDesc("created_time");

            List<Reservation> userReservations = reservationMapper.selectList(userWrapper);
            if (userReservations.isEmpty()) {
                throw new RuntimeException("未找到您的现有预约，请先创建预约");
            }

            Reservation currentReservation = userReservations.get(0);
            logger.info("找到用户现有预约: ID={}, 座位={}, 时间=[{}-{}]",
                       currentReservation.getId(), currentReservation.getSeatId(),
                       currentReservation.getStartTime(), currentReservation.getEndTime());

            // 5. 检查目标座位是否被其他用户占用（排除当前用户的预约）
            QueryWrapper<Reservation> seatConflictWrapper = new QueryWrapper<>();
            seatConflictWrapper.eq("room_id", roomId)
                              .eq("seat_num", seatId)
                              .ne("user_id", userId) // 排除当前用户
                              .ne("id", currentReservation.getId()); // 排除当前预约

            List<Reservation> seatConflictReservations = reservationMapper.selectList(seatConflictWrapper);
            logger.info("座位{}冲突检查结果: 房间{}, 排除用户{}, 找到{}个其他用户的活跃预约",
                        seatId, roomId, userId, seatConflictReservations.size());

            // 检查时间冲突：只有时间重叠的预约才算冲突
            List<Reservation> timeConflictReservations = seatConflictReservations.stream()
                .filter(reservation -> isTimeConflict(startTime, endTime,
                        reservation.getStartTime(), reservation.getEndTime()))
                .collect(java.util.stream.Collectors.toList());

            if (!timeConflictReservations.isEmpty()) {
                logger.warn("座位{}在时间段[{}-{}]已被其他用户预约，拒绝更新请求",
                           seatId, startTime, endTime);
                throw new RuntimeException("座位 " + seatId + " 在请求的时间段已被其他用户预约，请选择其他时间或座位");
            }

            // 6. 更新预约信息
            currentReservation.setRoomId(roomId);
            currentReservation.setSeatId(seatId);
            currentReservation.setStartTime(startTime);
            currentReservation.setEndTime(endTime);
            currentReservation.setReservationOpenTime(reservationOpenTime);
            currentReservation.setReservationType(reservationType);

            int updateResult = reservationMapper.updateById(currentReservation);
            if (updateResult == 0) {
                throw new RuntimeException("更新预约失败");
            }

            logger.info("预约更新成功: ID={}, 新座位={}, 新时间=[{}-{}]",
                       currentReservation.getId(), seatId, startTime, endTime);

            return currentReservation;

        } catch (Exception e) {
            logger.error("预约更新异常: 用户ID={}, 座位={}, 异常信息: {}", userId, seatId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 检查两个时间段是否冲突
     * @param start1 时间段1开始时间
     * @param end1 时间段1结束时间
     * @param start2 时间段2开始时间
     * @param end2 时间段2结束时间
     * @return 是否冲突
     */
    private boolean isTimeConflict(LocalTime start1, LocalTime end1, LocalTime start2, LocalTime end2) {
        // 修复：正确的时间冲突检查逻辑
        // 两个时间段冲突的条件：max(start1, start2) < min(end1, end2)
        LocalTime maxStart = start1.isAfter(start2) ? start1 : start2;
        LocalTime minEnd = end1.isBefore(end2) ? end1 : end2;
        
        boolean conflict = maxStart.isBefore(minEnd);
        
        // 添加调试日志
        logger.debug("时间冲突检查: [{}-{}] vs [{}-{}] = {}", 
                    start1, end1, start2, end2, conflict);
        
        return conflict;
    }
    
    @Override
    @Transactional
    public boolean cancelReservation(Long reservationId, Long userId) {
        // 1. 查找预约记录
        Reservation reservation = reservationMapper.selectById(reservationId);
        if (reservation == null || !reservation.getUserId().equals(userId)) {
            return false;
        }

        // 2. 真正删除预约记录
        logger.info("用户{}取消预约，删除预约记录ID: {}", userId, reservationId);
        return reservationMapper.deleteById(reservationId) > 0;
    }
    
    @Override
    public UserProfileResponse.CurrentReservation getCurrentReservation(Long userId) {
        return reservationMapper.getCurrentReservationByUserId(userId);
    }
    
    @Override
    public int getAvailableSeatsCount(Long roomId, LocalTime startTime, LocalTime endTime) {
        // 1. 检查房间是否存在
        Room room = roomMapper.selectById(roomId);
        if (room == null) {
            return 0;
        }

        // 2. 查询该时间段已预约的座位数
        QueryWrapper<Reservation> wrapper = new QueryWrapper<>();
        wrapper.eq("room_id", roomId);

        List<Reservation> activeReservations = reservationMapper.selectList(wrapper);

        // 检查时间冲突的预约数量
        long conflictCount = activeReservations.stream()
                .mapToLong(reservation -> isTimeConflict(startTime, endTime,
                        reservation.getStartTime(), reservation.getEndTime()) ? 1 : 0)
                .sum();

        // 3. 由于不再维护总座位数，返回一个基于冲突数的可用座位估算
        // 假设房间有足够的座位，返回一个合理的可用座位数
        int estimatedTotalSeats = 100; // 假设每个房间有100个座位
        return Math.max(0, estimatedTotalSeats - (int)conflictCount);
    }
    
    @Override
    public List<Reservation> getActiveReservations(Long roomId) {
        QueryWrapper<Reservation> wrapper = new QueryWrapper<>();
        wrapper.eq("room_id", roomId)
               .orderByAsc("start_time");

        return reservationMapper.selectList(wrapper);
    }



    /**
     * 删除用户现有的预约
     */
    private void deleteExistingUserReservations(Long userId) {
        QueryWrapper<Reservation> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("user_id", userId);
        List<Reservation> existingReservations = reservationMapper.selectList(deleteWrapper);

        if (!existingReservations.isEmpty()) {
            logger.info("删除用户{}的{}个现有预约", userId, existingReservations.size());
            reservationMapper.delete(deleteWrapper);
        }
    }

    /**
     * 检查时间段是否与其他用户的预约冲突
     */
    private boolean hasTimeConflictWithOtherUsers(Long userId, Long roomId, String seatId,
                                                 LocalTime startTime, LocalTime endTime) {
        QueryWrapper<Reservation> conflictWrapper = new QueryWrapper<>();
        conflictWrapper.eq("room_id", roomId)
                      .eq("seat_num", seatId)
                      .ne("user_id", userId); // 排除当前用户

        List<Reservation> otherUserReservations = reservationMapper.selectList(conflictWrapper);

        return otherUserReservations.stream()
                .anyMatch(reservation -> isTimeConflict(startTime, endTime,
                    reservation.getStartTime(), reservation.getEndTime()));
    }
}