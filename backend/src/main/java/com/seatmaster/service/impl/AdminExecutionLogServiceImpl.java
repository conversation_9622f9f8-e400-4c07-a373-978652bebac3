package com.seatmaster.service.impl;

import com.seatmaster.dto.ExecutionLogDTO;
import com.seatmaster.dto.ExecutionLogQueryDTO;
import com.seatmaster.service.AdminExecutionLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.sql.*;
import java.time.LocalDate;
import java.util.*;

/**
 * 管理员执行日志服务实现
 * 提供完整的技术调试信息和统计功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdminExecutionLogServiceImpl implements AdminExecutionLogService {

    private final DataSource dataSource;
    
    // SQL查询常量
    private static final String BASE_SELECT_SQL = 
        "SELECT rl.*, u.id as user_id, u.name as user_display_name, " +
        "       rm.name as room_name, s.name as school_name " +
        "FROM reservation_logs rl " +
        "LEFT JOIN users u ON u.username = rl.username " +
        "LEFT JOIN rooms rm ON rm.roomNum = rl.roomid " +
        "LEFT JOIN schools s ON rm.school_id = s.id ";
    
    private static final String COUNT_SELECT_SQL = 
        "SELECT COUNT(*) FROM reservation_logs rl " +
        "LEFT JOIN users u ON u.username = rl.username " +
        "LEFT JOIN rooms rm ON rm.roomNum = rl.roomid " +
        "LEFT JOIN schools s ON rm.school_id = s.id ";
    
    private static final String ORDER_BY_CLAUSE = 
        "ORDER BY COALESCE(rl.api_response_time, rl.created_at) DESC ";

    @Override
    public Map<String, Object> getAllExecutionLogs(ExecutionLogQueryDTO queryDTO) {
        long startTime = System.currentTimeMillis();
        log.debug("管理员查询所有执行日志: page={}, size={}, status={}, username={}", 
            queryDTO.getPage(), queryDTO.getSize(), queryDTO.getStatus(), queryDTO.getUsername());
        
        List<ExecutionLogDTO> logs = new ArrayList<>();
        int totalCount = 0;

        try (Connection connection = dataSource.getConnection()) {
            
            // 构建查询SQL
            StringBuilder sql = new StringBuilder(BASE_SELECT_SQL);
            List<Object> params = new ArrayList<>();

            // 添加WHERE条件
            appendWhereConditions(sql, params, queryDTO);
            
            // 添加排序和分页
            sql.append(ORDER_BY_CLAUSE);
            sql.append("LIMIT ? OFFSET ?");
            params.add(queryDTO.getSize());
            params.add(queryDTO.getOffset());
            
            // 执行查询
            logs = executeQuery(connection, sql.toString(), params);
            
            // 查询总数
            totalCount = getTotalCount(connection, queryDTO);
            
            long queryTime = System.currentTimeMillis() - startTime;
            log.debug("管理员查询完成: 返回{}条记录, 总计{}条, 耗时{}ms", 
                logs.size(), totalCount, queryTime);
            
        } catch (SQLException e) {
            log.error("管理员查询执行日志失败: {}", e.getMessage(), e);
            throw new RuntimeException("查询执行日志失败", e);
        }

        return buildResultMap(logs, totalCount, queryDTO);
    }

    @Override
    public Map<String, Object> getUserExecutionLogs(ExecutionLogQueryDTO queryDTO) {
        // 复用getAllExecutionLogs的逻辑，因为已经支持按用户名筛选
        return getAllExecutionLogs(queryDTO);
    }

    @Override
    public Map<String, Object> getExecutionStats(String username, String startDate, String endDate) {
        log.debug("获取执行统计信息: username={}, startDate={}, endDate={}", username, startDate, endDate);
        
        Map<String, Object> stats = new HashMap<>();
        
        try (Connection connection = dataSource.getConnection()) {
            
            // 构建WHERE条件
            StringBuilder whereCondition = new StringBuilder("WHERE 1=1 ");
            List<Object> params = new ArrayList<>();
            
            if (username != null && !username.trim().isEmpty()) {
                whereCondition.append("AND rl.username = ? ");
                params.add(username);
            }
            
            if (startDate != null && !startDate.trim().isEmpty()) {
                whereCondition.append("AND DATE(rl.reserve_date) >= ? ");
                params.add(startDate);
            }
            
            if (endDate != null && !endDate.trim().isEmpty()) {
                whereCondition.append("AND DATE(rl.reserve_date) <= ? ");
                params.add(endDate);
            }
            
            // 总执行次数
            String totalSql = "SELECT COUNT(*) FROM reservation_logs rl " + whereCondition;
            
            // 成功次数
            String successSql = "SELECT COUNT(*) FROM reservation_logs rl " + 
                               whereCondition + "AND rl.status = 'success'";
            
            // 平均耗时（从预约开放时间到响应时间的真实时间差，转换为毫秒）
            String avgTimeSql = "SELECT AVG(" +
                               "TIMESTAMPDIFF(MICROSECOND, " +
                               "CONCAT(rl.reserve_date, ' ', COALESCE(r.reservation_open_time, '08:00'), ':00'), " +
                               "rl.api_response_time) / 1000" +
                               ") FROM reservation_logs rl " +
                               "LEFT JOIN reservations r ON rl.reservation_id = r.id " +
                               whereCondition +
                               "AND rl.api_response_time IS NOT NULL " +
                               "AND r.reservation_open_time IS NOT NULL " +
                               "AND TIMESTAMPDIFF(SECOND, " +
                               "CONCAT(rl.reserve_date, ' ', COALESCE(r.reservation_open_time, '08:00'), ':00'), " +
                               "rl.api_response_time) BETWEEN 0 AND 300";
            
            // 最近7天的执行情况
            String recentSql = "SELECT DATE(rl.reserve_date) as exec_date, " +
                              "COUNT(*) as total, " +
                              "SUM(CASE WHEN rl.status = 'success' THEN 1 ELSE 0 END) as success " +
                              "FROM reservation_logs rl " +
                              whereCondition + "AND rl.reserve_date >= DATE_SUB(NOW(), INTERVAL 7 DAY) " +
                              "GROUP BY DATE(rl.reserve_date) " +
                              "ORDER BY exec_date DESC";
            
            // 执行查询
            stats.put("totalExecutions", executeCountQuery(connection, totalSql, params));
            stats.put("successExecutions", executeCountQuery(connection, successSql, params));
            stats.put("averageDurationMs", executeAvgQuery(connection, avgTimeSql, params));
            stats.put("recentStats", executeRecentStatsQuery(connection, recentSql, params));
            
            // 计算成功率
            calculateSuccessRate(stats);
            
        } catch (SQLException e) {
            log.error("查询统计信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("查询统计信息失败", e);
        }
        
        return stats;
    }

    @Override
    public ExecutionLogDTO getExecutionLogDetail(Long logId) {
        try (Connection connection = dataSource.getConnection()) {
            String sql = BASE_SELECT_SQL + "WHERE rl.id = ?";
            
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setLong(1, logId);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        return mapResultSetToDTO(rs);
                    }
                }
            }
        } catch (SQLException e) {
            log.error("获取日志详情失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取日志详情失败", e);
        }
        
        return null;
    }

    @Override
    public String exportExecutionLogs(String username, String status, String startDate, String endDate, String format) {
        log.debug("导出执行日志: username={}, status={}, startDate={}, endDate={}, format={}",
            username, status, startDate, endDate, format);

        try (Connection connection = dataSource.getConnection()) {
            // 构建查询SQL
            StringBuilder sql = new StringBuilder(BASE_SELECT_SQL);
            List<Object> params = new ArrayList<>();

            // 添加筛选条件
            boolean hasWhere = false;

            if (username != null && !username.trim().isEmpty()) {
                sql.append(hasWhere ? "AND " : "WHERE ");
                sql.append("rl.username = ? ");
                params.add(username);
                hasWhere = true;
            }

            if (status != null && !status.trim().isEmpty()) {
                sql.append(hasWhere ? "AND " : "WHERE ");
                sql.append("rl.status = ? ");
                params.add(status);
                hasWhere = true;
            }

            if (startDate != null && !startDate.trim().isEmpty()) {
                sql.append(hasWhere ? "AND " : "WHERE ");
                sql.append("DATE(rl.reserve_date) >= ? ");
                params.add(startDate);
                hasWhere = true;
            }

            if (endDate != null && !endDate.trim().isEmpty()) {
                sql.append(hasWhere ? "AND " : "WHERE ");
                sql.append("DATE(rl.reserve_date) <= ? ");
                params.add(endDate);
                hasWhere = true;
            }

            sql.append(ORDER_BY_CLAUSE);
            sql.append("LIMIT 10000"); // 限制导出数量

            // 执行查询
            List<ExecutionLogDTO> logs = executeQuery(connection, sql.toString(), params);

            // 生成文件
            String timestamp = String.valueOf(System.currentTimeMillis());
            String fileName = "execution_logs_" + timestamp + "." + format.toLowerCase();
            String filePath = "exports/" + fileName;

            if ("csv".equalsIgnoreCase(format)) {
                generateCSVFile(logs, filePath);
            } else {
                // 默认CSV格式
                generateCSVFile(logs, filePath);
            }

            log.info("导出执行日志成功: {} 条记录, 文件: {}", logs.size(), filePath);
            return filePath;

        } catch (SQLException e) {
            log.error("导出执行日志失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出执行日志失败", e);
        }
    }

    /**
     * 生成CSV文件
     */
    private void generateCSVFile(List<ExecutionLogDTO> logs, String filePath) {
        try {
            // 创建导出目录
            java.io.File exportDir = new java.io.File("exports");
            if (!exportDir.exists()) {
                exportDir.mkdirs();
            }

            try (java.io.FileWriter writer = new java.io.FileWriter(filePath, java.nio.charset.StandardCharsets.UTF_8)) {
                // 写入BOM以支持Excel正确显示中文
                writer.write('\ufeff');

                // 写入CSV头部
                writer.write("日志ID,预约ID,用户名,用户显示名,房间ID,房间名称,座位ID,状态,预约日期,开始时间,结束时间,执行时间(秒),尝试次数,API响应时间,创建时间,错误信息,学校名称\n");

                // 写入数据
                for (ExecutionLogDTO log : logs) {
                    writer.write(String.format("%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
                        csvEscape(String.valueOf(log.getId())),
                        csvEscape(String.valueOf(log.getReservationId())),
                        csvEscape(log.getUsername()),
                        csvEscape(log.getUserDisplayName()),
                        csvEscape(log.getRoomid()),
                        csvEscape(log.getRoomName()),
                        csvEscape(log.getSeatid()),
                        csvEscape(log.getStatus()),
                        csvEscape(log.getReserveDate() != null ? log.getReserveDate().toString() : ""),
                        csvEscape(log.getStartTime() != null ? log.getStartTime().toString() : ""),
                        csvEscape(log.getEndTime() != null ? log.getEndTime().toString() : ""),
                        csvEscape(log.getExecutionTime() != null ? log.getExecutionTime().toString() : ""),
                        csvEscape(String.valueOf(log.getAttemptCount())),
                        csvEscape(log.getApiResponseTime() != null ? log.getApiResponseTime().toString() : ""),
                        csvEscape(log.getCreatedAt() != null ? log.getCreatedAt().toString() : ""),
                        csvEscape(log.getErrorMessage()),
                        csvEscape(log.getSchoolName())
                    ));
                }
            }
        } catch (Exception e) {
            log.error("生成CSV文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成CSV文件失败", e);
        }
    }

    /**
     * CSV字段转义
     */
    private String csvEscape(String value) {
        if (value == null) return "";

        // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
        if (value.contains(",") || value.contains("\"") || value.contains("\n") || value.contains("\r")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }

        return value;
    }

    @Override
    public Map<String, Object> getExecutionOverview() {
        Map<String, Object> overview = new HashMap<>();
        
        try (Connection connection = dataSource.getConnection()) {
            LocalDate today = LocalDate.now();
            LocalDate weekStart = today.minusDays(today.getDayOfWeek().getValue() - 1);
            LocalDate monthStart = today.withDayOfMonth(1);
            
            // 今日统计
            overview.put("today", getDateRangeStats(connection, today.toString(), today.toString()));
            
            // 本周统计
            overview.put("thisWeek", getDateRangeStats(connection, weekStart.toString(), today.toString()));
            
            // 本月统计
            overview.put("thisMonth", getDateRangeStats(connection, monthStart.toString(), today.toString()));
            
        } catch (SQLException e) {
            log.error("获取概览统计失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取概览统计失败", e);
        }
        
        return overview;
    }

    @Override
    public List<Map<String, Object>> getExecutionTrends(int days) {
        List<Map<String, Object>> trends = new ArrayList<>();
        
        try (Connection connection = dataSource.getConnection()) {
            String sql = "SELECT DATE(rl.reserve_date) as exec_date, " +
                        "COUNT(*) as total, " +
                        "SUM(CASE WHEN rl.status = 'success' THEN 1 ELSE 0 END) as success, " +
                        "SUM(CASE WHEN rl.status = 'failed' THEN 1 ELSE 0 END) as failed, " +
                        "SUM(CASE WHEN rl.status = 'error' THEN 1 ELSE 0 END) as error " +
                        "FROM reservation_logs rl " +
                        "WHERE rl.reserve_date >= DATE_SUB(NOW(), INTERVAL ? DAY) " +
                        "GROUP BY DATE(rl.reserve_date) " +
                        "ORDER BY exec_date ASC";
            
            try (PreparedStatement stmt = connection.prepareStatement(sql)) {
                stmt.setInt(1, days);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        Map<String, Object> trend = new HashMap<>();
                        trend.put("date", rs.getDate("exec_date"));
                        trend.put("total", rs.getInt("total"));
                        trend.put("success", rs.getInt("success"));
                        trend.put("failed", rs.getInt("failed"));
                        trend.put("error", rs.getInt("error"));
                        trends.add(trend);
                    }
                }
            }
        } catch (SQLException e) {
            log.error("获取执行趋势失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取执行趋势失败", e);
        }
        
        return trends;
    }

    @Override
    public List<Map<String, Object>> getUserExecutionRanking(int limit) {
        // TODO: 实现用户执行排行榜
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getRoomUsageStats() {
        // TODO: 实现房间使用统计
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> getErrorAnalysis() {
        // TODO: 实现错误分析统计
        return new HashMap<>();
    }

    @Override
    public Map<String, Object> getUsersOverview(int page, int size, String searchUsername,
                                               String startDate, String endDate, String sortBy, String sortOrder, Long schoolId) {
        log.debug("获取用户概览: page={}, size={}, search={}, sortBy={}, schoolId={}", page, size, searchUsername, sortBy, schoolId);

        try (Connection connection = dataSource.getConnection()) {
            // 构建用户统计查询
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT u.username, u.name as user_display_name, ");
            sql.append("COUNT(rl.id) as total_executions, ");
            sql.append("SUM(CASE WHEN rl.status = 'success' THEN 1 ELSE 0 END) as success_executions, ");
            sql.append("MAX(COALESCE(rl.api_response_time, rl.created_at)) as last_execution_time, ");
            sql.append("AVG(CASE WHEN rl.api_response_time IS NOT NULL AND r.reservation_open_time IS NOT NULL ");
            sql.append("AND TIMESTAMPDIFF(SECOND, ");
            sql.append("CONCAT(rl.reserve_date, ' ', COALESCE(r.reservation_open_time, '08:00'), ':00'), ");
            sql.append("rl.api_response_time) BETWEEN 0 AND 300 ");
            sql.append("THEN TIMESTAMPDIFF(MICROSECOND, ");
            sql.append("CONCAT(rl.reserve_date, ' ', COALESCE(r.reservation_open_time, '08:00'), ':00'), ");
            sql.append("rl.api_response_time) / 1000 ELSE NULL END) as avg_duration_ms ");
            sql.append("FROM users u ");
            sql.append("LEFT JOIN reservation_logs rl ON u.username = rl.username ");
            sql.append("LEFT JOIN reservations r ON rl.reservation_id = r.id ");
            sql.append("LEFT JOIN rooms rm ON rl.roomid = rm.roomNum ");

            List<Object> params = new ArrayList<>();

            // 添加筛选条件
            StringBuilder whereClause = new StringBuilder("WHERE 1=1 ");

            if (searchUsername != null && !searchUsername.trim().isEmpty()) {
                whereClause.append("AND u.username LIKE ? ");
                params.add("%" + searchUsername + "%");
            }

            if (startDate != null && !startDate.trim().isEmpty()) {
                whereClause.append("AND DATE(rl.reserve_date) >= ? ");
                params.add(startDate);
            }

            if (endDate != null && !endDate.trim().isEmpty()) {
                whereClause.append("AND DATE(rl.reserve_date) <= ? ");
                params.add(endDate);
            }

            if (schoolId != null && schoolId > 0) {
                whereClause.append("AND rm.school_id = ? ");
                params.add(schoolId);
            }

            sql.append(whereClause);
            sql.append("GROUP BY u.username, u.name ");
            sql.append("HAVING COUNT(rl.id) > 0 ");

            // 添加排序
            String orderColumn = getSortColumn(sortBy);
            String orderDirection = "desc".equalsIgnoreCase(sortOrder) ? "DESC" : "ASC";
            sql.append("ORDER BY ").append(orderColumn).append(" ").append(orderDirection).append(" ");

            // 分页
            int offset = (page - 1) * size;
            sql.append("LIMIT ? OFFSET ?");
            params.add(size);
            params.add(offset);

            // 执行查询
            List<Map<String, Object>> users = new ArrayList<>();
            try (PreparedStatement stmt = connection.prepareStatement(sql.toString())) {
                setParameters(stmt, params);

                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        Map<String, Object> user = new HashMap<>();
                        user.put("username", rs.getString("username"));
                        user.put("userDisplayName", rs.getString("user_display_name"));
                        user.put("totalExecutions", rs.getInt("total_executions"));
                        user.put("successExecutions", rs.getInt("success_executions"));
                        user.put("lastExecutionTime", rs.getTimestamp("last_execution_time"));
                        user.put("avgDurationMs", rs.getDouble("avg_duration_ms"));

                        // 计算成功率
                        int total = rs.getInt("total_executions");
                        int success = rs.getInt("success_executions");
                        double successRate = total > 0 ? (double) success / total * 100 : 0;
                        user.put("successRate", Math.round(successRate * 100.0) / 100.0);

                        users.add(user);
                    }
                }
            }

            // 获取总数
            int totalCount = getTotalUsersCount(connection, searchUsername, startDate, endDate);

            Map<String, Object> result = new HashMap<>();
            result.put("users", users);
            result.put("totalCount", totalCount);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) totalCount / size));

            return result;

        } catch (SQLException e) {
            log.error("获取用户概览失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取用户概览失败", e);
        }
    }

    @Override
    public List<ExecutionLogDTO> getUserRecentLogs(String username, int limit, String startDate, String endDate) {
        log.debug("获取用户最近记录: username={}, limit={}", username, limit);

        try (Connection connection = dataSource.getConnection()) {
            StringBuilder sql = new StringBuilder(BASE_SELECT_SQL);
            List<Object> params = new ArrayList<>();

            sql.append("WHERE rl.username = ? ");
            params.add(username);

            if (startDate != null && !startDate.trim().isEmpty()) {
                sql.append("AND DATE(rl.reserve_date) >= ? ");
                params.add(startDate);
            }

            if (endDate != null && !endDate.trim().isEmpty()) {
                sql.append("AND DATE(rl.reserve_date) <= ? ");
                params.add(endDate);
            }

            sql.append(ORDER_BY_CLAUSE);
            sql.append("LIMIT ?");
            params.add(limit);

            return executeQuery(connection, sql.toString(), params);

        } catch (SQLException e) {
            log.error("获取用户最近记录失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取用户最近记录失败", e);
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 添加WHERE条件
     */
    private void appendWhereConditions(StringBuilder sql, List<Object> params, ExecutionLogQueryDTO queryDTO) {
        boolean hasWhere = false;

        // 用户名筛选
        if (queryDTO.getUsername() != null && !queryDTO.getUsername().trim().isEmpty()) {
            sql.append(hasWhere ? "AND " : "WHERE ");
            sql.append("rl.username = ? ");
            params.add(queryDTO.getUsername());
            hasWhere = true;
        }

        // 状态筛选
        if (queryDTO.hasStatusFilter()) {
            sql.append(hasWhere ? "AND " : "WHERE ");
            sql.append("rl.status = ? ");
            params.add(queryDTO.getStatus());
            hasWhere = true;
        }

        // 房间筛选
        if (queryDTO.getRoomid() != null && !queryDTO.getRoomid().trim().isEmpty()) {
            sql.append(hasWhere ? "AND " : "WHERE ");
            sql.append("rl.roomid = ? ");
            params.add(queryDTO.getRoomid());
            hasWhere = true;
        }

        // 座位筛选
        if (queryDTO.getSeatid() != null && !queryDTO.getSeatid().trim().isEmpty()) {
            sql.append(hasWhere ? "AND " : "WHERE ");
            sql.append("rl.seatid = ? ");
            params.add(queryDTO.getSeatid());
            hasWhere = true;
        }

        // 学校筛选
        if (queryDTO.hasSchoolFilter()) {
            sql.append(hasWhere ? "AND " : "WHERE ");
            sql.append("rm.school_id = ? ");
            params.add(queryDTO.getSchoolId());
            hasWhere = true;
        }

        // 日期筛选
        if (queryDTO.getStartDate() != null && !queryDTO.getStartDate().trim().isEmpty()) {
            sql.append(hasWhere ? "AND " : "WHERE ");
            sql.append("DATE(rl.reserve_date) >= ? ");
            params.add(queryDTO.getStartDate());
            hasWhere = true;
        }

        if (queryDTO.getEndDate() != null && !queryDTO.getEndDate().trim().isEmpty()) {
            sql.append(hasWhere ? "AND " : "WHERE ");
            sql.append("DATE(rl.reserve_date) <= ? ");
            params.add(queryDTO.getEndDate());
            hasWhere = true;
        }
    }

    /**
     * 执行查询并返回结果列表
     */
    private List<ExecutionLogDTO> executeQuery(Connection connection, String sql, List<Object> params) throws SQLException {
        List<ExecutionLogDTO> logs = new ArrayList<>();

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            setParameters(stmt, params);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    ExecutionLogDTO log = mapResultSetToDTO(rs);
                    logs.add(log);
                }
            }
        }

        return logs;
    }

    /**
     * 统一的参数设置方法
     */
    private void setParameters(PreparedStatement stmt, List<Object> params) throws SQLException {
        for (int i = 0; i < params.size(); i++) {
            stmt.setObject(i + 1, params.get(i));
        }
    }

    /**
     * 获取总记录数
     */
    private int getTotalCount(Connection connection, ExecutionLogQueryDTO queryDTO) throws SQLException {
        StringBuilder sql = new StringBuilder(COUNT_SELECT_SQL);
        List<Object> params = new ArrayList<>();

        // 添加WHERE条件（复用逻辑）
        appendWhereConditions(sql, params, queryDTO);

        try (PreparedStatement stmt = connection.prepareStatement(sql.toString())) {
            setParameters(stmt, params);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }

        return 0;
    }

    /**
     * 构建返回结果Map
     */
    private Map<String, Object> buildResultMap(List<ExecutionLogDTO> logs, int totalCount, ExecutionLogQueryDTO queryDTO) {
        Map<String, Object> result = new HashMap<>();
        result.put("logs", logs);
        result.put("totalCount", totalCount);
        result.put("page", queryDTO.getPage());
        result.put("size", queryDTO.getSize());
        result.put("totalPages", (int) Math.ceil((double) totalCount / queryDTO.getSize()));
        return result;
    }

    /**
     * 执行计数查询
     */
    private int executeCountQuery(Connection connection, String sql, List<Object> params) throws SQLException {
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            setParameters(stmt, params);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }
        return 0;
    }

    /**
     * 执行平均值查询
     */
    private Double executeAvgQuery(Connection connection, String sql, List<Object> params) throws SQLException {
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            setParameters(stmt, params);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getDouble(1);
                }
            }
        }
        return 0.0;
    }

    /**
     * 执行最近统计查询
     */
    private List<Map<String, Object>> executeRecentStatsQuery(Connection connection, String sql, List<Object> params) throws SQLException {
        List<Map<String, Object>> stats = new ArrayList<>();

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            setParameters(stmt, params);
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    Map<String, Object> stat = new HashMap<>();
                    stat.put("date", rs.getDate("exec_date"));
                    stat.put("total", rs.getInt("total"));
                    stat.put("success", rs.getInt("success"));
                    stats.add(stat);
                }
            }
        }

        return stats;
    }

    /**
     * 计算成功率
     */
    private void calculateSuccessRate(Map<String, Object> stats) {
        int total = (Integer) stats.get("totalExecutions");
        int success = (Integer) stats.get("successExecutions");
        double successRate = total > 0 ? (double) success / total * 100 : 0;
        stats.put("successRate", Math.round(successRate * 100.0) / 100.0);
    }

    /**
     * 获取日期范围统计
     */
    private Map<String, Object> getDateRangeStats(Connection connection, String startDate, String endDate) throws SQLException {
        Map<String, Object> stats = new HashMap<>();

        String sql = "SELECT COUNT(*) as total, " +
                    "SUM(CASE WHEN rl.status = 'success' THEN 1 ELSE 0 END) as success, " +
                    "SUM(CASE WHEN rl.status = 'failed' THEN 1 ELSE 0 END) as failed, " +
                    "SUM(CASE WHEN rl.status = 'error' THEN 1 ELSE 0 END) as error " +
                    "FROM reservation_logs rl " +
                    "WHERE DATE(rl.reserve_date) >= ? AND DATE(rl.reserve_date) <= ?";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, startDate);
            stmt.setString(2, endDate);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    stats.put("total", rs.getInt("total"));
                    stats.put("success", rs.getInt("success"));
                    stats.put("failed", rs.getInt("failed"));
                    stats.put("error", rs.getInt("error"));

                    int total = rs.getInt("total");
                    int success = rs.getInt("success");
                    double successRate = total > 0 ? (double) success / total * 100 : 0;
                    stats.put("successRate", Math.round(successRate * 100.0) / 100.0);
                }
            }
        }

        return stats;
    }

    /**
     * 将ResultSet映射为ExecutionLogDTO
     */
    private ExecutionLogDTO mapResultSetToDTO(ResultSet rs) throws SQLException {
        ExecutionLogDTO dto = new ExecutionLogDTO();

        // 基础字段
        dto.setId(rs.getLong("id"));
        dto.setReservationId(rs.getLong("reservation_id"));
        dto.setUsername(rs.getString("username"));
        dto.setRoomid(rs.getString("roomid"));
        dto.setSeatid(rs.getString("seatid"));
        dto.setStatus(rs.getString("status"));

        // 时间字段
        Timestamp reserveDate = rs.getTimestamp("reserve_date");
        if (reserveDate != null) {
            dto.setReserveDate(reserveDate.toLocalDateTime().toLocalDate());
        }

        Time startTime = rs.getTime("start_time");
        if (startTime != null) {
            dto.setStartTime(startTime.toLocalTime());
        }

        Time endTime = rs.getTime("end_time");
        if (endTime != null) {
            dto.setEndTime(endTime.toLocalTime());
        }

        Timestamp apiResponseTime = rs.getTimestamp("api_response_time");
        if (apiResponseTime != null) {
            dto.setApiResponseTime(apiResponseTime.toLocalDateTime());
        }

        Timestamp createdAt = rs.getTimestamp("created_at");
        if (createdAt != null) {
            dto.setCreatedAt(createdAt.toLocalDateTime());
        }

        // 执行相关字段
        BigDecimal executionTime = rs.getBigDecimal("execution_time");
        if (executionTime != null) {
            dto.setExecutionTime(executionTime);
        }

        dto.setAttemptCount(rs.getInt("attempt_count"));
        dto.setApiResponse(rs.getString("api_response"));
        dto.setErrorMessage(rs.getString("error_message"));

        // 关联字段
        dto.setUserId(rs.getLong("user_id"));
        dto.setUserDisplayName(rs.getString("user_display_name"));
        dto.setRoomName(rs.getString("room_name"));
        dto.setSchoolName(rs.getString("school_name"));

        return dto;
    }

    /**
     * 获取排序列名
     */
    private String getSortColumn(String sortBy) {
        switch (sortBy) {
            case "username":
                return "u.username";
            case "totalExecutions":
                return "total_executions";
            case "successRate":
                return "(SUM(CASE WHEN rl.status = 'success' THEN 1 ELSE 0 END) * 100.0 / COUNT(rl.id))";
            case "lastExecutionTime":
                return "last_execution_time";
            case "avgDurationMs":
                return "avg_duration_ms";
            default:
                return "total_executions";
        }
    }

    /**
     * 获取用户总数
     */
    private int getTotalUsersCount(Connection connection, String searchUsername, String startDate, String endDate) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(DISTINCT u.username) FROM users u ");
        sql.append("LEFT JOIN reservation_logs rl ON u.username = rl.username ");
        sql.append("WHERE 1=1 ");

        List<Object> params = new ArrayList<>();

        if (searchUsername != null && !searchUsername.trim().isEmpty()) {
            sql.append("AND u.username LIKE ? ");
            params.add("%" + searchUsername + "%");
        }

        if (startDate != null && !startDate.trim().isEmpty()) {
            sql.append("AND DATE(rl.reserve_date) >= ? ");
            params.add(startDate);
        }

        if (endDate != null && !endDate.trim().isEmpty()) {
            sql.append("AND DATE(rl.reserve_date) <= ? ");
            params.add(endDate);
        }

        sql.append("AND rl.id IS NOT NULL");

        try (PreparedStatement stmt = connection.prepareStatement(sql.toString())) {
            setParameters(stmt, params);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }

        return 0;
    }

    @Override
    public Map<String, Object> getSchoolsOverview(int page, int size, String searchSchoolName,
                                                 String startDate, String endDate, String sortBy, String sortOrder) {
        log.debug("获取学校概览: page={}, size={}, search={}, sortBy={}", page, size, searchSchoolName, sortBy);

        try (Connection connection = dataSource.getConnection()) {
            // 构建学校统计查询
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT s.id as school_id, s.name as school_name, ");
            sql.append("COUNT(rl.id) as total_executions, ");
            sql.append("SUM(CASE WHEN rl.status = 'success' THEN 1 ELSE 0 END) as success_executions, ");
            sql.append("AVG(CASE WHEN rl.api_response_time IS NOT NULL AND r.reservation_open_time IS NOT NULL ");
            sql.append("AND TIMESTAMPDIFF(SECOND, ");
            sql.append("CONCAT(rl.reserve_date, ' ', COALESCE(r.reservation_open_time, '08:00'), ':00'), ");
            sql.append("rl.api_response_time) BETWEEN 0 AND 300 ");
            sql.append("THEN TIMESTAMPDIFF(MICROSECOND, ");
            sql.append("CONCAT(rl.reserve_date, ' ', COALESCE(r.reservation_open_time, '08:00'), ':00'), ");
            sql.append("rl.api_response_time) / 1000 ELSE NULL END) as avg_duration_ms, ");

            // 今日统计
            sql.append("SUM(CASE WHEN DATE(rl.reserve_date) = CURDATE() THEN 1 ELSE 0 END) as today_executions, ");
            sql.append("SUM(CASE WHEN DATE(rl.reserve_date) = CURDATE() AND rl.status = 'success' THEN 1 ELSE 0 END) as today_success_executions ");

            sql.append("FROM schools s ");
            sql.append("LEFT JOIN rooms rm ON s.id = rm.school_id ");
            sql.append("LEFT JOIN reservation_logs rl ON rm.roomNum = rl.roomid ");
            sql.append("LEFT JOIN reservations r ON rl.reservation_id = r.id ");

            List<Object> params = new ArrayList<>();

            // 添加筛选条件
            StringBuilder whereClause = new StringBuilder("WHERE 1=1 ");

            if (searchSchoolName != null && !searchSchoolName.trim().isEmpty()) {
                whereClause.append("AND s.name LIKE ? ");
                params.add("%" + searchSchoolName + "%");
            }

            if (startDate != null && !startDate.trim().isEmpty()) {
                whereClause.append("AND DATE(rl.reserve_date) >= ? ");
                params.add(startDate);
            }

            if (endDate != null && !endDate.trim().isEmpty()) {
                whereClause.append("AND DATE(rl.reserve_date) <= ? ");
                params.add(endDate);
            }

            sql.append(whereClause);
            sql.append("GROUP BY s.id, s.name ");
            sql.append("HAVING COUNT(rl.id) > 0 ");

            // 添加排序
            String orderColumn = getSchoolSortColumn(sortBy);
            String orderDirection = "desc".equalsIgnoreCase(sortOrder) ? "DESC" : "ASC";
            sql.append("ORDER BY ").append(orderColumn).append(" ").append(orderDirection).append(" ");

            // 分页
            int offset = (page - 1) * size;
            sql.append("LIMIT ? OFFSET ?");
            params.add(size);
            params.add(offset);

            // 执行查询
            List<Map<String, Object>> schools = new ArrayList<>();
            try (PreparedStatement stmt = connection.prepareStatement(sql.toString())) {
                setParameters(stmt, params);

                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        Map<String, Object> school = new HashMap<>();
                        school.put("schoolId", rs.getLong("school_id"));
                        school.put("schoolName", rs.getString("school_name"));
                        school.put("totalExecutions", rs.getInt("total_executions"));
                        school.put("successExecutions", rs.getInt("success_executions"));
                        school.put("avgDurationMs", rs.getDouble("avg_duration_ms"));
                        school.put("todayExecutions", rs.getInt("today_executions"));
                        school.put("todaySuccessExecutions", rs.getInt("today_success_executions"));

                        // 计算总成功率
                        int total = rs.getInt("total_executions");
                        int success = rs.getInt("success_executions");
                        double successRate = total > 0 ? (double) success / total * 100 : 0;
                        school.put("successRate", Math.round(successRate * 100.0) / 100.0);

                        // 计算今日成功率
                        int todayTotal = rs.getInt("today_executions");
                        int todaySuccess = rs.getInt("today_success_executions");
                        double todaySuccessRate = todayTotal > 0 ? (double) todaySuccess / todayTotal * 100 : 0;
                        school.put("todaySuccessRate", Math.round(todaySuccessRate * 100.0) / 100.0);

                        schools.add(school);
                    }
                }
            }

            // 获取总数
            int totalCount = getTotalSchoolsCount(connection, searchSchoolName, startDate, endDate);

            Map<String, Object> result = new HashMap<>();
            result.put("schools", schools);
            result.put("totalCount", totalCount);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) totalCount / size));

            return result;

        } catch (SQLException e) {
            log.error("获取学校概览失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取学校概览失败", e);
        }
    }

    @Override
    public Map<String, Object> getSchoolDailyStats(Long schoolId, String startDate, String endDate, int page, int size) {
        log.debug("获取学校日期统计: schoolId={}, startDate={}, endDate={}", schoolId, startDate, endDate);

        try (Connection connection = dataSource.getConnection()) {
            // 设置默认日期范围（最近30天）
            if (startDate == null || startDate.trim().isEmpty()) {
                startDate = LocalDate.now().minusDays(29).toString();
            }
            if (endDate == null || endDate.trim().isEmpty()) {
                endDate = LocalDate.now().toString();
            }

            // 构建学校日期统计查询
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT DATE(rl.reserve_date) as stat_date, ");
            sql.append("COUNT(rl.id) as daily_executions, ");
            sql.append("SUM(CASE WHEN rl.status = 'success' THEN 1 ELSE 0 END) as daily_success_executions, ");
            sql.append("AVG(CASE WHEN rl.api_response_time IS NOT NULL AND r.reservation_open_time IS NOT NULL ");
            sql.append("AND TIMESTAMPDIFF(SECOND, ");
            sql.append("CONCAT(rl.reserve_date, ' ', COALESCE(r.reservation_open_time, '08:00'), ':00'), ");
            sql.append("rl.api_response_time) BETWEEN 0 AND 300 ");
            sql.append("THEN TIMESTAMPDIFF(MICROSECOND, ");
            sql.append("CONCAT(rl.reserve_date, ' ', COALESCE(r.reservation_open_time, '08:00'), ':00'), ");
            sql.append("rl.api_response_time) / 1000 ELSE NULL END) as daily_avg_duration_ms ");

            sql.append("FROM reservation_logs rl ");
            sql.append("LEFT JOIN reservations r ON rl.reservation_id = r.id ");
            sql.append("LEFT JOIN rooms rm ON rl.roomid = rm.roomNum ");
            sql.append("WHERE rm.school_id = ? ");
            sql.append("AND DATE(rl.reserve_date) >= ? ");
            sql.append("AND DATE(rl.reserve_date) <= ? ");
            sql.append("GROUP BY DATE(rl.reserve_date) ");
            sql.append("ORDER BY stat_date DESC ");

            // 分页
            int offset = (page - 1) * size;
            sql.append("LIMIT ? OFFSET ?");

            List<Object> params = new ArrayList<>();
            params.add(schoolId);
            params.add(startDate);
            params.add(endDate);
            params.add(size);
            params.add(offset);

            // 执行查询
            List<Map<String, Object>> dailyStats = new ArrayList<>();
            try (PreparedStatement stmt = connection.prepareStatement(sql.toString())) {
                setParameters(stmt, params);

                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        Map<String, Object> stat = new HashMap<>();
                        stat.put("date", rs.getDate("stat_date"));
                        stat.put("dailyExecutions", rs.getInt("daily_executions"));
                        stat.put("dailySuccessExecutions", rs.getInt("daily_success_executions"));
                        stat.put("dailyAvgDurationMs", rs.getDouble("daily_avg_duration_ms"));

                        // 计算日成功率
                        int dailyTotal = rs.getInt("daily_executions");
                        int dailySuccess = rs.getInt("daily_success_executions");
                        double dailySuccessRate = dailyTotal > 0 ? (double) dailySuccess / dailyTotal * 100 : 0;
                        stat.put("dailySuccessRate", Math.round(dailySuccessRate * 100.0) / 100.0);

                        dailyStats.add(stat);
                    }
                }
            }

            // 获取总记录数
            int totalCount = getTotalSchoolDailyStatsCount(connection, schoolId, startDate, endDate);

            // 获取学校基本信息
            Map<String, Object> schoolInfo = getSchoolBasicInfo(connection, schoolId);

            Map<String, Object> result = new HashMap<>();
            result.put("schoolInfo", schoolInfo);
            result.put("dailyStats", dailyStats);
            result.put("totalCount", totalCount);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) totalCount / size));
            result.put("dateRange", Map.of("startDate", startDate, "endDate", endDate));

            return result;

        } catch (SQLException e) {
            log.error("获取学校日期统计失败: schoolId={}", schoolId, e);
            throw new RuntimeException("获取学校日期统计失败", e);
        }
    }

    /**
     * 获取学校排序字段映射
     */
    private String getSchoolSortColumn(String sortBy) {
        switch (sortBy) {
            case "successRate":
                return "(SUM(CASE WHEN rl.status = 'success' THEN 1 ELSE 0 END) * 100.0 / COUNT(rl.id))";
            case "avgDurationMs":
                return "avg_duration_ms";
            case "todayExecutions":
                return "today_executions";
            case "todaySuccessRate":
                return "(SUM(CASE WHEN DATE(rl.reserve_date) = CURDATE() AND rl.status = 'success' THEN 1 ELSE 0 END) * 100.0 / NULLIF(SUM(CASE WHEN DATE(rl.reserve_date) = CURDATE() THEN 1 ELSE 0 END), 0))";
            case "totalExecutions":
            default:
                return "total_executions";
        }
    }

    /**
     * 获取学校总数
     */
    private int getTotalSchoolsCount(Connection connection, String searchSchoolName, String startDate, String endDate) throws SQLException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(DISTINCT s.id) FROM schools s ");
        sql.append("LEFT JOIN rooms rm ON s.id = rm.school_id ");
        sql.append("LEFT JOIN reservation_logs rl ON rm.roomNum = rl.roomid ");
        sql.append("WHERE 1=1 ");

        List<Object> params = new ArrayList<>();

        if (searchSchoolName != null && !searchSchoolName.trim().isEmpty()) {
            sql.append("AND s.name LIKE ? ");
            params.add("%" + searchSchoolName + "%");
        }

        if (startDate != null && !startDate.trim().isEmpty()) {
            sql.append("AND DATE(rl.reserve_date) >= ? ");
            params.add(startDate);
        }

        if (endDate != null && !endDate.trim().isEmpty()) {
            sql.append("AND DATE(rl.reserve_date) <= ? ");
            params.add(endDate);
        }

        sql.append("AND rl.id IS NOT NULL");

        try (PreparedStatement stmt = connection.prepareStatement(sql.toString())) {
            setParameters(stmt, params);
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }

        return 0;
    }

    /**
     * 获取学校日期统计总记录数
     */
    private int getTotalSchoolDailyStatsCount(Connection connection, Long schoolId, String startDate, String endDate) throws SQLException {
        String sql = "SELECT COUNT(DISTINCT DATE(rl.reserve_date)) " +
                    "FROM reservation_logs rl " +
                    "LEFT JOIN rooms rm ON rl.roomid = rm.roomNum " +
                    "WHERE rm.school_id = ? " +
                    "AND DATE(rl.reserve_date) >= ? " +
                    "AND DATE(rl.reserve_date) <= ?";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setLong(1, schoolId);
            stmt.setString(2, startDate);
            stmt.setString(3, endDate);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1);
                }
            }
        }

        return 0;
    }

    /**
     * 获取学校基本信息
     */
    private Map<String, Object> getSchoolBasicInfo(Connection connection, Long schoolId) throws SQLException {
        String sql = "SELECT id, name FROM schools WHERE id = ?";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setLong(1, schoolId);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    Map<String, Object> schoolInfo = new HashMap<>();
                    schoolInfo.put("schoolId", rs.getLong("id"));
                    schoolInfo.put("schoolName", rs.getString("name"));
                    return schoolInfo;
                }
            }
        }

        // 如果没找到学校信息，返回默认值
        Map<String, Object> defaultInfo = new HashMap<>();
        defaultInfo.put("schoolId", schoolId);
        defaultInfo.put("schoolName", "未知学校");
        return defaultInfo;
    }
}
