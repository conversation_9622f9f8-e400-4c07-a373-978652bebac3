package com.seatmaster.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seatmaster.dto.AnnouncementDTO;
import com.seatmaster.entity.Announcement;
import com.seatmaster.entity.UserAnnouncementRead;
import com.seatmaster.mapper.AnnouncementMapper;
import com.seatmaster.mapper.UserAnnouncementReadMapper;
import com.seatmaster.service.AnnouncementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 公告服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AnnouncementServiceImpl implements AnnouncementService {
    
    private final AnnouncementMapper announcementMapper;
    private final UserAnnouncementReadMapper userAnnouncementReadMapper;
    
    @Override
    @Transactional
    public AnnouncementDTO createAnnouncement(AnnouncementDTO.CreateRequest request, Long createdBy) {
        log.info("创建公告: title={}, createdBy={}", request.getTitle(), createdBy);
        
        Announcement announcement = new Announcement();
        BeanUtils.copyProperties(request, announcement);
        announcement.setCreatedBy(createdBy);
        announcement.setViewCount(0);
        announcement.setCreatedTime(LocalDateTime.now());
        announcement.setUpdatedTime(LocalDateTime.now());
        
        // 设置默认值
        if (announcement.getPriority() == null) {
            announcement.setPriority(0);
        }
        if (announcement.getEnabled() == null) {
            announcement.setEnabled(true);
        }
        if (announcement.getPopup() == null) {
            announcement.setPopup(true);
        }
        
        announcementMapper.insert(announcement);
        
        log.info("公告创建成功: id={}, title={}", announcement.getId(), announcement.getTitle());
        return getAnnouncementById(announcement.getId());
    }
    
    @Override
    @Transactional
    public AnnouncementDTO updateAnnouncement(Long id, AnnouncementDTO.UpdateRequest request) {
        log.info("更新公告: id={}, title={}", id, request.getTitle());
        
        Announcement announcement = announcementMapper.selectById(id);
        if (announcement == null) {
            throw new RuntimeException("公告不存在");
        }
        
        BeanUtils.copyProperties(request, announcement);
        announcement.setUpdatedTime(LocalDateTime.now());
        
        announcementMapper.updateById(announcement);
        
        log.info("公告更新成功: id={}, title={}", id, announcement.getTitle());
        return getAnnouncementById(id);
    }
    
    @Override
    @Transactional
    public boolean deleteAnnouncement(Long id) {
        log.info("删除公告: id={}", id);
        
        Announcement announcement = announcementMapper.selectById(id);
        if (announcement == null) {
            throw new RuntimeException("公告不存在");
        }
        
        // 删除公告
        int result = announcementMapper.deleteById(id);
        
        // 删除相关的阅读记录
        QueryWrapper<UserAnnouncementRead> wrapper = new QueryWrapper<>();
        wrapper.eq("announcement_id", id);
        userAnnouncementReadMapper.delete(wrapper);
        
        log.info("公告删除成功: id={}, title={}", id, announcement.getTitle());
        return result > 0;
    }
    
    @Override
    public AnnouncementDTO getAnnouncementById(Long id) {
        AnnouncementDTO dto = announcementMapper.selectAnnouncementById(id);
        if (dto == null) {
            throw new RuntimeException("公告不存在");
        }
        return dto;
    }
    
    @Override
    public IPage<AnnouncementDTO> getAnnouncementPage(AnnouncementDTO.QueryRequest query) {
        log.info("分页查询公告: page={}, size={}", query.getPage(), query.getSize());
        
        Page<AnnouncementDTO> page = new Page<>(query.getPage(), query.getSize());
        return announcementMapper.selectAnnouncementPage(page);
    }
    
    @Override
    public List<AnnouncementDTO> getActiveAnnouncements(String userRole) {
        log.debug("获取活跃公告: userRole={}", userRole);
        return announcementMapper.selectActiveAnnouncements(userRole);
    }
    
    @Override
    public List<AnnouncementDTO> getUnreadPopupAnnouncements(Long userId, String userRole) {
        log.debug("获取用户未读弹窗公告: userId={}, userRole={}", userId, userRole);
        return announcementMapper.selectUnreadPopupAnnouncements(userId, userRole);
    }

    @Override
    @Transactional
    public boolean markAnnouncementAsRead(Long userId, Long announcementId) {
        log.debug("标记公告为已读: userId={}, announcementId={}", userId, announcementId);

        // 检查是否已存在阅读记录
        UserAnnouncementRead existingRead = userAnnouncementReadMapper.selectByUserAndAnnouncement(userId, announcementId);
        if (existingRead != null) {
            log.debug("用户已读过该公告: userId={}, announcementId={}", userId, announcementId);
            return true;
        }

        // 创建阅读记录
        UserAnnouncementRead read = new UserAnnouncementRead();
        read.setUserId(userId);
        read.setAnnouncementId(announcementId);
        read.setReadTime(LocalDateTime.now());
        read.setDismissed(false);

        int result = userAnnouncementReadMapper.insert(read);

        // 增加公告查看次数
        if (result > 0) {
            incrementViewCount(announcementId);
        }

        return result > 0;
    }

    @Override
    @Transactional
    public boolean dismissAnnouncementPopup(Long userId, Long announcementId) {
        log.debug("关闭公告弹窗: userId={}, announcementId={}", userId, announcementId);

        // 先标记为已读
        markAnnouncementAsRead(userId, announcementId);

        // 更新为已关闭弹窗
        UserAnnouncementRead read = userAnnouncementReadMapper.selectByUserAndAnnouncement(userId, announcementId);
        if (read != null) {
            read.setDismissed(true);
            return userAnnouncementReadMapper.updateById(read) > 0;
        }

        return false;
    }

    @Override
    @Transactional
    public boolean toggleAnnouncementStatus(Long id, boolean enabled) {
        log.info("切换公告状态: id={}, enabled={}", id, enabled);

        Announcement announcement = announcementMapper.selectById(id);
        if (announcement == null) {
            throw new RuntimeException("公告不存在");
        }

        announcement.setEnabled(enabled);
        announcement.setUpdatedTime(LocalDateTime.now());

        return announcementMapper.updateById(announcement) > 0;
    }

    @Override
    public void incrementViewCount(Long id) {
        announcementMapper.incrementViewCount(id);
    }

    @Override
    @Transactional
    public int batchDeleteAnnouncements(List<Long> ids) {
        log.info("批量删除公告: ids={}", ids);

        if (ids == null || ids.isEmpty()) {
            return 0;
        }

        int deletedCount = 0;
        for (Long id : ids) {
            try {
                if (deleteAnnouncement(id)) {
                    deletedCount++;
                }
            } catch (Exception e) {
                log.error("删除公告失败: id={}", id, e);
            }
        }

        log.info("批量删除公告完成: 总数={}, 成功={}", ids.size(), deletedCount);
        return deletedCount;
    }
}
