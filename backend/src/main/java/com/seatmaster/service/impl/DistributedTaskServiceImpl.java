package com.seatmaster.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seatmaster.dto.ReservationTaskDTO;
import com.seatmaster.dto.TaskStatisticsDTO;
import com.seatmaster.dto.WorkerServerDTO;
import com.seatmaster.entity.Reservation;
import com.seatmaster.entity.WorkerServer;
import com.seatmaster.mapper.ReservationMapper;
import com.seatmaster.mapper.WorkerServerMapper;
import com.seatmaster.service.DistributedTaskService;
import com.seatmaster.service.WorkerServerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分布式任务服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DistributedTaskServiceImpl implements DistributedTaskService {

    private final ReservationMapper reservationMapper;
    private final WorkerServerMapper workerServerMapper;
    private final WorkerServerService workerServerService;
    private final RestTemplate restTemplate;

    @Qualifier("fastRestTemplate")
    private final RestTemplate fastRestTemplate;

    @Value("${seatmaster.auto-assignment.strategy:LOAD_BALANCED}")
    private String assignmentStrategy;

    @Value("${seatmaster.auto-assignment.max-retry-count:3}")
    private int maxRetryCount;
    
    @Override
    public TaskStatisticsDTO getTaskStatistics() {
        log.info("获取任务统计数据");
        
        TaskStatisticsDTO statistics = new TaskStatisticsDTO();
        
        try {
            // 先测试基础查询
            QueryWrapper<Reservation> testWrapper = new QueryWrapper<>();
            List<Reservation> allReservations = reservationMapper.selectList(testWrapper);
            log.info("=== 调试信息 ===");
            log.info("数据库中总预约记录数: {}", allReservations.size());

            if (!allReservations.isEmpty()) {
                Reservation sample = allReservations.get(0);
                log.info("示例记录: id={}, userId={}, roomId={}, seatId={}, workerId={}, createdTime={}",
                        sample.getId(), sample.getUserId(), sample.getRoomId(),
                        sample.getSeatId(), sample.getWorkerId(), sample.getCreatedTime());
            }

            // 获取各状态任务数量
            QueryWrapper<Reservation> wrapper = new QueryWrapper<>();

            // 总任务数 (所有预约) - 先测试这个最简单的
            wrapper.clear();
            long totalCount = reservationMapper.selectCount(wrapper);
            log.info("总任务数查询结果: {}", totalCount);
            statistics.setTotalTasks(totalCount);

            // 简化统计逻辑，暂时不使用复杂的状态判断
            statistics.setCompletedTasks(0L);
            statistics.setPendingTasks(totalCount);
            statistics.setRunningTasks(0L);
            statistics.setFailedTasks(0L);

            // 简化今日统计
            statistics.setTodayNewTasks(0L);
            statistics.setTodayCompletedTasks(0L);
            
            // 获取副服务器统计
            QueryWrapper<WorkerServer> workerWrapper = new QueryWrapper<>();
            workerWrapper.eq("status", "ONLINE");
            statistics.setOnlineWorkers(Math.toIntExact(workerServerMapper.selectCount(workerWrapper)));
            
            workerWrapper.clear();
            statistics.setTotalWorkers(Math.toIntExact(workerServerMapper.selectCount(workerWrapper)));
            
            // 计算成功率和负载率
            statistics.calculateSuccessRate();
            statistics.calculateSystemLoadRate();
            
            log.info("任务统计数据获取成功: 总任务={}, 已完成={}, 待执行={}, 执行中={}, 失败={}", 
                    statistics.getTotalTasks(), statistics.getCompletedTasks(), 
                    statistics.getPendingTasks(), statistics.getRunningTasks(), statistics.getFailedTasks());
            
        } catch (Exception e) {
            log.error("获取任务统计数据失败", e);
            // 返回默认值
        }
        
        return statistics;
    }
    
    @Override
    public Page<ReservationTaskDTO> getReservationTasks(int page, int size, String status, String keyword, String workerId) {
        log.info("获取预约任务列表: page={}, size={}, status={}, keyword={}, workerId={}", page, size, status, keyword, workerId);

        Page<ReservationTaskDTO> result = new Page<>(page, size);

        try {
            // 先测试简单查询
            QueryWrapper<Reservation> wrapper = new QueryWrapper<>();
            List<Reservation> allReservations = reservationMapper.selectList(wrapper);
            log.info("数据库中总预约记录数: {}", allReservations.size());

            // 如果没有数据，直接返回空结果
            if (allReservations.isEmpty()) {
                log.info("数据库中没有预约记录，返回空结果");
                return result;
            }

            try {
                // 使用正确的关联查询
                log.info("使用MyBatis关联查询获取任务列表");
                Page<ReservationTaskDTO> taskPage = reservationMapper.selectReservationTasksPage(
                    result, status, keyword, workerId);

                log.info("关联查询结果: 总记录数={}, 当前页记录数={}",
                        taskPage.getTotal(), taskPage.getRecords().size());

                // 直接返回查询结果，即使是0条记录也是正确的
                return taskPage;

            } catch (Exception e) {
                log.error("关联查询失败: {}", e.getMessage(), e);
                // 查询失败时返回空结果，不使用包含虚假数据的简化查询
                return result;
            }

        } catch (Exception e) {
            log.error("获取预约任务列表完全失败", e);
            e.printStackTrace();
            return result;
        }
    }
    
    @Override
    public ReservationTaskDTO getTaskDetail(Long reservationId) {
        log.info("获取任务详情: reservationId={}", reservationId);
        
        try {
            ReservationTaskDTO taskDetail = reservationMapper.selectTaskDetailById(reservationId);
            
            if (taskDetail != null) {
                log.info("任务详情获取成功: reservationId={}", reservationId);
            } else {
                log.warn("任务不存在: reservationId={}", reservationId);
            }
            
            return taskDetail;
            
        } catch (Exception e) {
            log.error("获取任务详情失败: reservationId={}", reservationId, e);
            return null;
        }
    }
    
    @Override
    @Transactional
    public boolean assignTaskToWorker(Long reservationId, String workerId) {
        log.info("手动分配任务: reservationId={}, workerId={}", reservationId, workerId);
        
        try {
            // 检查预约是否存在
            Reservation reservation = reservationMapper.selectById(reservationId);
            if (reservation == null) {
                log.warn("预约不存在: reservationId={}", reservationId);
                return false;
            }
            
            // 检查副服务器是否存在且在线
            QueryWrapper<WorkerServer> workerWrapper = new QueryWrapper<>();
            workerWrapper.eq("id", workerId).eq("status", "ONLINE");
            WorkerServer worker = workerServerMapper.selectOne(workerWrapper);
            
            if (worker == null) {
                log.warn("副服务器不存在或不在线: workerId={}", workerId);
                return false;
            }
            
            // 检查副服务器负载
            if (worker.getCurrentLoad() >= worker.getMaxConcurrentTasks()) {
                log.warn("副服务器负载已满: workerId={}, currentLoad={}, maxTasks={}", 
                        workerId, worker.getCurrentLoad(), worker.getMaxConcurrentTasks());
                return false;
            }
            
            // 更新预约的分配信息
            reservation.setWorkerId(workerId);
            // 清除之前的执行状态
            reservation.setStartedTime(null);
            reservation.setActualExecutionTime(null);
            reservation.setErrorMessage(null);
            reservation.setRetryCount(0);
            
            int updated = reservationMapper.updateById(reservation);
            
            if (updated > 0) {
                log.info("任务分配成功: reservationId={}, workerId={}", reservationId, workerId);
                return true;
            } else {
                log.warn("任务分配失败: reservationId={}, workerId={}", reservationId, workerId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("手动分配任务失败: reservationId={}, workerId={}", reservationId, workerId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean retryFailedTask(Long reservationId) {
        log.info("立即执行任务: reservationId={}", reservationId);

        try {
            Reservation reservation = reservationMapper.selectById(reservationId);
            if (reservation == null) {
                log.warn("预约不存在: reservationId={}", reservationId);
                return false;
            }

            // 检查任务是否已分配给副服务器（支持立即执行所有已分配的任务）
            if (!StringUtils.hasText(reservation.getWorkerId())) {
                log.warn("任务未分配给副服务器，无法立即执行: reservationId={}", reservationId);
                return false;
            }

            if (reservation.getRetryCount() != null && reservation.getRetryCount() >= 3) {
                log.warn("任务重试次数已达上限: reservationId={}, retryCount={}",
                        reservationId, reservation.getRetryCount());
                return false;
            }

            // 重置任务状态以支持立即执行
            boolean isFailedTask = StringUtils.hasText(reservation.getErrorMessage());

            // 清除执行状态，准备重新执行
            reservation.setErrorMessage(null);
            reservation.setExecutionResult(null);
            reservation.setStartedTime(null);
            reservation.setActualExecutionTime(null);
            reservation.setLastExecutionTime(null);

            // 只有失败任务才增加重试计数
            if (isFailedTask) {
                if (reservation.getRetryCount() == null) {
                    reservation.setRetryCount(1);
                } else {
                    reservation.setRetryCount(reservation.getRetryCount() + 1);
                }
            }

            int updated = reservationMapper.updateById(reservation);

            if (updated > 0) {
                if (isFailedTask) {
                    log.info("失败任务重试设置成功: reservationId={}, retryCount={}",
                            reservationId, reservation.getRetryCount());
                } else {
                    log.info("任务立即执行设置成功: reservationId={}", reservationId);
                }

                // 立即调用副服务器执行任务
                log.info("调用副服务器立即执行任务: reservationId={}, workerId={}",
                        reservationId, reservation.getWorkerId());

                boolean executeSuccess = callWorkerExecuteTask(reservation.getWorkerId(), reservationId);
                if (executeSuccess) {
                    log.info("副服务器立即执行任务成功: reservationId={}", reservationId);
                } else {
                    log.warn("副服务器立即执行任务失败: reservationId={}", reservationId);
                    // 即使调用失败，也返回true，因为任务状态已重置，副服务器会在定时任务中处理
                }

                return true;
            } else {
                log.warn("任务立即执行设置失败: reservationId={}", reservationId);
                return false;
            }

        } catch (Exception e) {
            log.error("重新执行失败任务异常: reservationId={}", reservationId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public Map<String, Object> batchAssignTasks(List<Long> reservationIds, String workerId) {
        log.info("批量分配任务: reservationIds={}, workerId={}", reservationIds, workerId);

        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        List<String> errors = new ArrayList<>();

        try {
            for (Long reservationId : reservationIds) {
                boolean success = assignTaskToWorker(reservationId, workerId);
                if (success) {
                    successCount++;
                } else {
                    failCount++;
                    errors.add("任务 " + reservationId + " 分配失败");
                }
            }

            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("errors", errors);

            log.info("批量分配任务完成: 成功={}, 失败={}", successCount, failCount);

        } catch (Exception e) {
            log.error("批量分配任务异常", e);
            result.put("successCount", 0);
            result.put("failCount", reservationIds.size());
            result.put("errors", Arrays.asList("批量分配过程中发生异常: " + e.getMessage()));
        }

        return result;
    }

    @Override
    public List<WorkerServerDTO> getAvailableWorkers() {
        log.info("获取可用副服务器列表");

        try {
            List<WorkerServerDTO> availableWorkers = workerServerService.getOnlineServers();

            // 过滤出有空闲容量的服务器
            List<WorkerServerDTO> result = availableWorkers.stream()
                    .filter(worker -> worker.getCurrentLoad() < worker.getMaxConcurrentTasks())
                    .collect(Collectors.toList());

            log.info("可用副服务器数量: {}", result.size());
            return result;

        } catch (Exception e) {
            log.error("获取可用副服务器列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional
    public Map<String, Object> autoAssignPendingTasks() {
        log.info("【定时任务】自动分配待执行任务");

        Map<String, Object> result = new HashMap<>();
        int assignedCount = 0;

        try {
            // 获取当前时间（格式：HH:mm）
            String currentTime = LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("HH:mm"));
            log.info("【定时任务】当前时间: {}", currentTime);

            // 获取待执行任务（没有分配worker_id且没有开始执行的）
            QueryWrapper<Reservation> wrapper = new QueryWrapper<>();
            wrapper.isNull("worker_id")
                   .isNull("started_time")
                   .isNull("actual_execution_time")
                   .isNull("error_message")
                   .orderByAsc("created_time");
            List<Reservation> allPendingTasks = reservationMapper.selectList(wrapper);

            log.info("【定时任务】🔍 查询到所有待分配任务数量: {}", allPendingTasks.size());

            // 筛选出预约开放时间已到达的任务
            List<Reservation> readyTasks = allPendingTasks.stream()
                .filter(task -> {
                    String openTime = task.getReservationOpenTime();
                    if (openTime == null || openTime.trim().isEmpty()) {
                        log.debug("【定时任务】任务 {} 没有设置预约开放时间，跳过", task.getId());
                        return false;
                    }
                    
                    // 比较时间（格式：HH:mm）
                    boolean isReady = currentTime.compareTo(openTime.trim()) >= 0;
                    log.debug("【定时任务】任务 {} 预约开放时间: {}, 当前时间: {}, 是否就绪: {}",
                        task.getId(), openTime, currentTime, isReady);
                    return isReady;
                })
                .collect(Collectors.toList());

            log.info("【定时任务】🎯 筛选出预约时间已到达的任务数量: {}", readyTasks.size());
            if (!readyTasks.isEmpty()) {
                log.info("【定时任务】📋 就绪任务详情: {}", readyTasks.stream()
                    .map(task -> String.format("ID=%d,开放时间=%s,类型=%s",
                        task.getId(), task.getReservationOpenTime(), task.getReservationType()))
                    .collect(Collectors.toList()));
            }

            // 获取可用副服务器
            List<WorkerServerDTO> availableWorkers = getAvailableWorkers();
            log.info("【定时任务】🖥️ 可用副服务器数量: {}", availableWorkers.size());

            if (availableWorkers.isEmpty()) {
                log.warn("【定时任务】没有可用的副服务器");
                result.put("assignedCount", 0);
                result.put("message", "没有可用的副服务器");
                return result;
            }

            if (readyTasks.isEmpty()) {
                log.info("【定时任务】没有预约时间已到达的任务需要分配");
                result.put("assignedCount", 0);
                result.put("totalPendingTasks", allPendingTasks.size());
                result.put("readyTasks", 0);
                result.put("message", "没有预约时间已到达的任务");
                return result;
            }

            // 使用智能分配策略分配就绪的任务
            assignedCount = assignTasksWithStrategy(readyTasks, availableWorkers);

            result.put("assignedCount", assignedCount);
            result.put("totalPendingTasks", allPendingTasks.size());
            result.put("readyTasks", readyTasks.size());
            result.put("message", "自动分配完成");

            log.info("【定时任务】自动分配任务完成: 分配数量={}, 就绪任务数={}, 总待分配数={}",
                assignedCount, readyTasks.size(), allPendingTasks.size());

        } catch (Exception e) {
            log.error("【定时任务】自动分配待执行任务失败", e);
            result.put("assignedCount", 0);
            result.put("message", "自动分配失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public List<Map<String, Object>> getWorkerStatusOverview() {
        log.info("获取副服务器状态概览");

        try {
            List<WorkerServerDTO> allWorkers = workerServerService.getAllServers();

            return allWorkers.stream().map(worker -> {
                Map<String, Object> status = new HashMap<>();
                status.put("workerId", worker.getWorkerId());
                status.put("name", worker.getName());
                status.put("status", worker.getStatus());
                status.put("currentLoad", worker.getCurrentLoad());
                status.put("maxConcurrentTasks", worker.getMaxConcurrentTasks());
                status.put("totalTasksCompleted", worker.getTotalTasksCompleted());
                status.put("totalTasksFailed", worker.getTotalTasksFailed());
                status.put("lastHeartbeat", worker.getLastHeartbeat());

                // 计算负载率
                double loadRate = 0.0;
                if (worker.getMaxConcurrentTasks() > 0) {
                    loadRate = (worker.getCurrentLoad().doubleValue() / worker.getMaxConcurrentTasks()) * 100;
                }
                status.put("loadRate", loadRate);

                // 计算成功率
                double successRate = 0.0;
                long totalTasks = worker.getTotalTasksCompleted() + worker.getTotalTasksFailed();
                if (totalTasks > 0) {
                    successRate = (worker.getTotalTasksCompleted().doubleValue() / totalTasks) * 100;
                }
                status.put("successRate", successRate);

                return status;
            }).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取副服务器状态概览失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional
    public boolean cancelTask(Long reservationId) {
        log.info("取消任务执行: reservationId={}", reservationId);

        try {
            Reservation reservation = reservationMapper.selectById(reservationId);
            if (reservation == null) {
                log.warn("预约不存在: reservationId={}", reservationId);
                return false;
            }

            // 检查是否已成功完成
            if (reservation.getActualExecutionTime() != null) {
                log.warn("任务已成功完成，无法取消: reservationId={}", reservationId);
                return false;
            }

            // 更新任务状态为取消
            reservation.setErrorMessage("任务已被手动取消");
            reservation.setExecutionResult("任务已被手动取消");
            reservation.setLastExecutionTime(LocalDateTime.now());

            int updated = reservationMapper.updateById(reservation);

            if (updated > 0) {
                log.info("任务取消成功: reservationId={}", reservationId);
                return true;
            } else {
                log.warn("任务取消失败: reservationId={}", reservationId);
                return false;
            }

        } catch (Exception e) {
            log.error("取消任务执行异常: reservationId={}", reservationId, e);
            return false;
        }
    }

    @Override
    public List<String> getTaskExecutionLogs(Long reservationId) {
        log.info("获取任务执行日志: reservationId={}", reservationId);

        try {
            Reservation reservation = reservationMapper.selectById(reservationId);
            if (reservation == null) {
                return Arrays.asList("任务不存在");
            }

            List<String> logs = new ArrayList<>();
            logs.add("=== 任务执行日志 ===");
            logs.add("任务ID: " + reservationId);
            logs.add("创建时间: " + reservation.getCreatedTime());
            logs.add("分配服务器: " + (reservation.getWorkerId() != null ? reservation.getWorkerId() : "未分配"));
            // 根据现有字段判断执行状态
            String status = "PENDING";
            if (reservation.getActualExecutionTime() != null) {
                status = "SUCCESS";
            } else if (reservation.getErrorMessage() != null) {
                status = "FAILED";
            } else if (reservation.getStartedTime() != null) {
                status = "RUNNING";
            } else if (reservation.getWorkerId() != null) {
                status = "PENDING";
            }
            logs.add("执行状态: " + status);
            logs.add("重试次数: " + (reservation.getRetryCount() != null ? reservation.getRetryCount() : 0));

            if (reservation.getLastExecutionTime() != null) {
                logs.add("最后执行时间: " + reservation.getLastExecutionTime());
            }

            if (StringUtils.hasText(reservation.getExecutionResult())) {
                logs.add("执行结果: " + reservation.getExecutionResult());
            }

            return logs;

        } catch (Exception e) {
            log.error("获取任务执行日志失败: reservationId={}", reservationId, e);
            return Arrays.asList("获取日志失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public int cleanupExpiredTasks(int daysToKeep) {
        log.info("清理过期任务记录: daysToKeep={}", daysToKeep);

        try {
            LocalDateTime cutoffDate = LocalDateTime.now().minus(daysToKeep, ChronoUnit.DAYS);

            QueryWrapper<Reservation> wrapper = new QueryWrapper<>();
            wrapper.and(w -> w.isNotNull("actual_execution_time").or().isNotNull("error_message"))
                   .lt("last_execution_time", cutoffDate);

            // 这里只清理执行结果，不删除预约记录本身
            List<Reservation> expiredTasks = reservationMapper.selectList(wrapper);

            int cleanedCount = 0;
            for (Reservation task : expiredTasks) {
                task.setExecutionResult(null); // 清理执行结果详情
                if (reservationMapper.updateById(task) > 0) {
                    cleanedCount++;
                }
            }

            log.info("清理过期任务记录完成: 清理数量={}", cleanedCount);
            return cleanedCount;

        } catch (Exception e) {
            log.error("清理过期任务记录失败", e);
            return 0;
        }
    }

    /**
     * 清理分配给指定Worker的任务
     * @param workerId Worker ID
     * @return 清理的任务数量
     */
    @Override
    @Transactional
    public int clearWorkerTasks(String workerId) {
        log.info("清理分配给已删除Worker的任务: workerId={}", workerId);

        try {
            // 查询分配给该Worker的所有未完成任务（包括已开始但未完成的）
            QueryWrapper<Reservation> wrapper = new QueryWrapper<>();
            wrapper.eq("worker_id", workerId)
                   .isNull("actual_execution_time"); // 只清理未完成的任务

            List<Reservation> workerTasks = reservationMapper.selectList(wrapper);

            log.info("查询到分配给Worker {}的未完成任务数量: {}", workerId, workerTasks.size());

            if (workerTasks.isEmpty()) {
                log.info("没有分配给Worker的未完成任务: workerId={}", workerId);
                return 0;
            }

            // 记录要清理的任务详情
            log.info("即将清理的任务详情: {}", workerTasks.stream()
                .map(task -> String.format("ID=%d,时间段=%s,类型=%s",
                    task.getId(), task.getReservationOpenTime(), task.getReservationType()))
                .collect(Collectors.toList()));

            int clearedCount = 0;
            for (Reservation task : workerTasks) {
                // 记录清理前的状态
                log.debug("清理任务前状态: reservationId={}, workerId={}, startedTime={}, errorMessage={}, executionResult={}",
                         task.getId(), task.getWorkerId(), task.getStartedTime(), task.getErrorMessage(), task.getExecutionResult());

                // 使用UpdateWrapper明确更新所有字段，包括NULL值
                UpdateWrapper<Reservation> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("id", task.getId())
                           .set("worker_id", null)
                           .set("started_time", null)
                           .set("actual_execution_time", null)
                           .set("last_execution_time", null)
                           .set("error_message", null)
                           .set("execution_result", null)
                           .set("retry_count", 0);

                if (reservationMapper.update(null, updateWrapper) > 0) {
                    clearedCount++;
                    log.info("✅ 清理任务成功: reservationId={}, 时间段={}, 原workerId={}",
                            task.getId(), task.getReservationOpenTime(), workerId);
                } else {
                    log.warn("❌ 清理任务失败: reservationId={}, workerId={}", task.getId(), workerId);
                }
            }

            log.info("清理Worker任务完成: workerId={}, 清理数量={}", workerId, clearedCount);
            return clearedCount;

        } catch (Exception e) {
            log.error("清理Worker任务失败: workerId={}", workerId, e);
            return 0;
        }
    }

    /**
     * 使用智能分配策略分配任务
     */
    private int assignTasksWithStrategy(List<Reservation> pendingTasks, List<WorkerServerDTO> availableWorkers) {
        int assignedCount = 0;

        switch (assignmentStrategy) {
            case "ROUND_ROBIN":
                assignedCount = assignTasksRoundRobin(pendingTasks, availableWorkers);
                break;
            case "LOAD_BALANCED":
                assignedCount = assignTasksLoadBalanced(pendingTasks, availableWorkers);
                break;
            case "TIME_OPTIMIZED":
                assignedCount = assignTasksTimeOptimized(pendingTasks, availableWorkers);
                break;
            default:
                log.warn("未知的分配策略: {}, 使用负载均衡策略", assignmentStrategy);
                assignedCount = assignTasksLoadBalanced(pendingTasks, availableWorkers);
                break;
        }

        return assignedCount;
    }

    /**
     * 轮询分配策略
     */
    private int assignTasksRoundRobin(List<Reservation> pendingTasks, List<WorkerServerDTO> availableWorkers) {
        int assignedCount = 0;
        int workerIndex = 0;

        for (Reservation task : pendingTasks) {
            if (availableWorkers.isEmpty()) {
                break;
            }

            if (workerIndex >= availableWorkers.size()) {
                workerIndex = 0;
            }

            WorkerServerDTO worker = availableWorkers.get(workerIndex);
            boolean success = assignTaskToWorker(task.getId(), worker.getWorkerId());

            if (success) {
                assignedCount++;
                worker.setCurrentLoad(worker.getCurrentLoad() + 1);

                if (worker.getCurrentLoad() >= worker.getMaxConcurrentTasks()) {
                    availableWorkers.remove(workerIndex);
                } else {
                    workerIndex++;
                }
            } else {
                workerIndex++;
            }
        }

        return assignedCount;
    }

    /**
     * 负载均衡分配策略 - 优先分配给负载最低的服务器
     */
    private int assignTasksLoadBalanced(List<Reservation> pendingTasks, List<WorkerServerDTO> availableWorkers) {
        int assignedCount = 0;

        for (Reservation task : pendingTasks) {
            if (availableWorkers.isEmpty()) {
                break;
            }

            // 按负载率排序，优先选择负载最低的服务器
            availableWorkers.sort((w1, w2) -> {
                double loadRate1 = (double) w1.getCurrentLoad() / w1.getMaxConcurrentTasks();
                double loadRate2 = (double) w2.getCurrentLoad() / w2.getMaxConcurrentTasks();
                return Double.compare(loadRate1, loadRate2);
            });

            WorkerServerDTO bestWorker = availableWorkers.get(0);
            boolean success = assignTaskToWorker(task.getId(), bestWorker.getWorkerId());

            if (success) {
                assignedCount++;
                bestWorker.setCurrentLoad(bestWorker.getCurrentLoad() + 1);

                // 如果服务器负载已满，从可用列表中移除
                if (bestWorker.getCurrentLoad() >= bestWorker.getMaxConcurrentTasks()) {
                    availableWorkers.remove(0);
                }

                log.debug("任务 {} 分配给服务器 {} (负载: {}/{})",
                         task.getId(), bestWorker.getWorkerId(),
                         bestWorker.getCurrentLoad(), bestWorker.getMaxConcurrentTasks());
            }
        }

        return assignedCount;
    }

    /**
     * 时间优化分配策略 - 根据预约时间段分配到不同服务器
     */
    private int assignTasksTimeOptimized(List<Reservation> pendingTasks, List<WorkerServerDTO> availableWorkers) {
        int assignedCount = 0;

        // 按预约开放时间分组
        Map<String, List<Reservation>> timeGroups = pendingTasks.stream()
                .collect(Collectors.groupingBy(task -> {
                    if (task.getReservationOpenTime() != null && !task.getReservationOpenTime().isEmpty()) {
                        // 提取小时部分作为分组键
                        String[] timeParts = task.getReservationOpenTime().split(":");
                        return timeParts.length > 0 ? timeParts[0] : "default";
                    }
                    return "default";
                }));

        log.debug("时间优化分配: 发现 {} 个时间段分组", timeGroups.size());

        // 为每个时间段分配专门的服务器
        int workerIndex = 0;
        for (Map.Entry<String, List<Reservation>> entry : timeGroups.entrySet()) {
            String timeSlot = entry.getKey();
            List<Reservation> tasksInSlot = entry.getValue();

            log.debug("处理时间段 {} 的 {} 个任务", timeSlot, tasksInSlot.size());

            for (Reservation task : tasksInSlot) {
                if (availableWorkers.isEmpty()) {
                    break;
                }

                if (workerIndex >= availableWorkers.size()) {
                    workerIndex = 0;
                }

                WorkerServerDTO worker = availableWorkers.get(workerIndex);
                boolean success = assignTaskToWorker(task.getId(), worker.getWorkerId());

                if (success) {
                    assignedCount++;
                    worker.setCurrentLoad(worker.getCurrentLoad() + 1);

                    if (worker.getCurrentLoad() >= worker.getMaxConcurrentTasks()) {
                        availableWorkers.remove(workerIndex);
                    } else {
                        workerIndex++;
                    }

                    log.debug("时间段 {} 的任务 {} 分配给服务器 {}",
                             timeSlot, task.getId(), worker.getWorkerId());
                }
            }
        }

        return assignedCount;
    }

    /**
     * 🚀 调用副服务器立即执行任务（优化版）
     * 使用快速RestTemplate，大幅减少调用延迟
     * @param workerId 副服务器ID
     * @param reservationId 预约ID
     * @return 调用结果
     */
    private boolean callWorkerExecuteTask(String workerId, Long reservationId) {
        long methodStartTime = System.currentTimeMillis();
        String methodStartTimeStr = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss.SSS"));

        try {
            log.info("🔗 开始调用副服务器: workerId={}, reservationId={}, 开始时间: [{}]",
                workerId, reservationId, methodStartTimeStr);

            // 1. 查询副服务器信息
            long dbQueryStartTime = System.currentTimeMillis();
            String dbQueryStartTimeStr = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss.SSS"));
            log.info("🗄️ 开始查询副服务器信息: workerId={}, 查询开始时间: [{}]", workerId, dbQueryStartTimeStr);

            WorkerServer worker = getOnlineWorkerByWorkerId(workerId);
            long dbQueryTime = System.currentTimeMillis() - dbQueryStartTime;
            String dbQueryEndTimeStr = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss.SSS"));

            if (worker == null) {
                log.warn("❌ 副服务器不存在或不在线: workerId={}, 查询耗时: {}ms [{}->{}]",
                    workerId, dbQueryTime, dbQueryStartTimeStr, dbQueryEndTimeStr);
                return false;
            }

            log.info("✅ 找到副服务器: workerId={}, host={}, port={}, status={}, 查询耗时: {}ms [{}->{}]",
                    workerId, worker.getHost(), worker.getPort(), worker.getStatus(),
                    dbQueryTime, dbQueryStartTimeStr, dbQueryEndTimeStr);

            // 2. 构建请求参数
            long prepareStartTime = System.currentTimeMillis();
            String taskId = reservationId.toString();
            String url = String.format("http://%s:%d/api/tasks/execute/%s",
                worker.getHost(), worker.getPort(), taskId);
            long prepareTime = System.currentTimeMillis() - prepareStartTime;

            log.info("📡 准备调用副服务器: workerId={}, reservationId={}, taskId={}, url={}, 准备耗时: {}ms",
                    workerId, reservationId, taskId, url, prepareTime);

            // 3. 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            headers.set("User-Agent", "SeatMaster-MainServer/1.0");
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 4. 🚀 发送POST请求（使用快速RestTemplate）
            long httpStartTime = System.currentTimeMillis();
            String httpStartTimeStr = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss.SSS"));
            log.info("🚀 开始HTTP调用副服务器: url={}, 调用开始时间: [{}]", url, httpStartTimeStr);

            ResponseEntity<String> response = fastRestTemplate.exchange(
                url, HttpMethod.POST, entity, String.class);

            long httpTime = System.currentTimeMillis() - httpStartTime;
            String httpEndTimeStr = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss.SSS"));
            log.info("📡 HTTP调用完成: url={}, 耗时: {}ms [{}->{}], 状态码: {}",
                url, httpTime, httpStartTimeStr, httpEndTimeStr, response.getStatusCode());

            // 5. 处理响应结果
            boolean success = response.getStatusCode().is2xxSuccessful();
            long totalTime = System.currentTimeMillis() - methodStartTime;
            String methodEndTimeStr = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss.SSS"));

            if (success) {
                String responseBody = response.getBody();
                String message = parseResponseMessage(responseBody);
                log.info("✅ 副服务器立即执行任务成功: workerId={}, reservationId={}, message={}",
                    workerId, reservationId, message);
                log.info("⏱️ 主服务器调用时间统计: workerId={}, reservationId={}", workerId, reservationId);
                log.info("   - 数据库查询: {}ms", dbQueryTime);
                log.info("   - 请求准备: {}ms", prepareTime);
                log.info("   - HTTP调用: {}ms [{}->{}]", httpTime, httpStartTimeStr, httpEndTimeStr);
                log.info("   - 总调用时间: {}ms [{}->{}]", totalTime, methodStartTimeStr, methodEndTimeStr);
            } else {
                log.error("❌ 副服务器立即执行任务失败: workerId={}, reservationId={}, statusCode={}",
                    workerId, reservationId, response.getStatusCode());
                log.error("⏱️ 主服务器调用时间统计(失败): workerId={}, reservationId={}", workerId, reservationId);
                log.error("   - 数据库查询: {}ms", dbQueryTime);
                log.error("   - 请求准备: {}ms", prepareTime);
                log.error("   - HTTP调用: {}ms [{}->{}]", httpTime, httpStartTimeStr, httpEndTimeStr);
                log.error("   - 总调用时间: {}ms [{}->{}]", totalTime, methodStartTimeStr, methodEndTimeStr);
            }

            return success;

        } catch (RestClientException e) {
            long totalTime = System.currentTimeMillis() - methodStartTime;
            String methodEndTimeStr = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss.SSS"));

            log.error("🌐 调用副服务器网络异常: workerId={}, reservationId={}, error={}, 异常前耗时: {}ms [{}->{}]",
                    workerId, reservationId, e.getMessage(), totalTime, methodStartTimeStr, methodEndTimeStr);
            return false;
        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - methodStartTime;
            String methodEndTimeStr = java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss.SSS"));

            log.error("💥 调用副服务器立即执行任务异常: workerId={}, reservationId={}, 异常前耗时: {}ms [{}->{}]",
                    workerId, reservationId, totalTime, methodStartTimeStr, methodEndTimeStr, e);
            return false;
        }
    }

    /**
     * 获取在线的副服务器信息
     * @param workerId 副服务器ID
     * @return 副服务器信息，如果不存在或离线则返回null
     */
    private WorkerServer getOnlineWorkerByWorkerId(String workerId) {
        QueryWrapper<WorkerServer> wrapper = new QueryWrapper<>();
        wrapper.eq("id", workerId).eq("status", "ONLINE");  // 修复：使用id而不是worker_id
        return workerServerMapper.selectOne(wrapper);
    }

    /**
     * 调试副服务器调用
     * @param reservationId 预约ID
     * @return 调试信息
     */
    public Map<String, Object> debugWorkerCall(Long reservationId) {
        Map<String, Object> debugInfo = new HashMap<>();

        try {
            log.info("🔧 开始调试副服务器调用: reservationId={}", reservationId);

            // 1. 获取预约信息
            Reservation reservation = reservationMapper.selectById(reservationId);
            if (reservation == null) {
                debugInfo.put("error", "预约信息不存在");
                debugInfo.put("reservationId", reservationId);
                return debugInfo;
            }

            debugInfo.put("reservationId", reservationId);
            debugInfo.put("workerId", reservation.getWorkerId());
            debugInfo.put("executionResult", reservation.getExecutionResult());

            // 2. 检查副服务器信息
            if (reservation.getWorkerId() == null) {
                debugInfo.put("error", "任务未分配给副服务器");
                return debugInfo;
            }

            WorkerServer worker = getOnlineWorkerByWorkerId(reservation.getWorkerId());
            if (worker == null) {
                debugInfo.put("error", "副服务器不存在或不在线");
                debugInfo.put("workerId", reservation.getWorkerId());
                return debugInfo;
            }

            debugInfo.put("workerInfo", Map.of(
                "workerId", worker.getWorkerId(),
                "name", worker.getName(),
                "host", worker.getHost(),
                "port", worker.getPort(),
                "status", worker.getStatus(),
                "lastHeartbeat", worker.getLastHeartbeat()
            ));

            // 3. 测试副服务器连接（使用真实的reservationId作为taskId）
            String taskId = reservationId.toString();
            String url = String.format("http://%s:%d/api/tasks/execute/%s",
                worker.getHost(), worker.getPort(), taskId);

            debugInfo.put("testUrl", url);
            debugInfo.put("testTaskId", taskId);

            try {
                HttpHeaders headers = new HttpHeaders();
                headers.set("Content-Type", "application/json");
                headers.set("User-Agent", "SeatMaster-MainServer/1.0");
                HttpEntity<String> entity = new HttpEntity<>(headers);

                long startTime = System.currentTimeMillis();
                ResponseEntity<String> response = restTemplate.exchange(
                    url, HttpMethod.POST, entity, String.class);
                long duration = System.currentTimeMillis() - startTime;

                debugInfo.put("httpCallSuccess", true);
                debugInfo.put("httpStatus", response.getStatusCode().value());
                debugInfo.put("httpDuration", duration + "ms");
                debugInfo.put("httpResponse", response.getBody());

                log.info("🔧 副服务器调用测试成功: url={}, status={}, duration={}ms",
                        url, response.getStatusCode(), duration);

            } catch (Exception e) {
                debugInfo.put("httpCallSuccess", false);
                debugInfo.put("httpError", e.getMessage());
                debugInfo.put("httpErrorType", e.getClass().getSimpleName());

                log.error("🔧 副服务器调用测试失败: url={}, error={}", url, e.getMessage());
            }

            debugInfo.put("debugTime", LocalDateTime.now().toString());
            debugInfo.put("success", true);

        } catch (Exception e) {
            debugInfo.put("success", false);
            debugInfo.put("error", e.getMessage());
            debugInfo.put("errorType", e.getClass().getSimpleName());
            log.error("🔧 调试副服务器调用异常: reservationId={}", reservationId, e);
        }

        return debugInfo;
    }

    /**
     * 解析副服务器响应消息
     * @param responseBody 响应体
     * @return 解析出的消息，如果解析失败则返回原始响应
     */
    private String parseResponseMessage(String responseBody) {
        if (StringUtils.hasText(responseBody)) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                JsonNode jsonNode = mapper.readTree(responseBody);
                JsonNode messageNode = jsonNode.get("message");
                if (messageNode != null) {
                    return messageNode.asText();
                }
            } catch (Exception e) {
                log.debug("解析副服务器响应失败，使用原始响应: {}", e.getMessage());
            }
        }
        return responseBody != null ? responseBody : "无响应内容";
    }
}
