package com.seatmaster.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seatmaster.dto.*;
import com.seatmaster.entity.RedemptionCode;
import com.seatmaster.entity.RedemptionLog;
import com.seatmaster.entity.User;
import com.seatmaster.mapper.RedemptionCodeMapper;
import com.seatmaster.mapper.RedemptionLogMapper;
import com.seatmaster.service.RedemptionService;
import com.seatmaster.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 兑换码服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RedemptionServiceImpl implements RedemptionService {
    
    private final RedemptionCodeMapper redemptionCodeMapper;
    private final RedemptionLogMapper redemptionLogMapper;
    private final UserService userService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RedeemCodeResponse redeemCode(String code, Long userId, String userIp, String userAgent) {
        log.info("用户{}尝试兑换码: {}", userId, code);
        
        // 1. 验证兑换码
        RedemptionCode redemptionCode = validateCode(code);
        
        // 2. 获取用户信息
        User user = userService.findById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        
        Integer daysBefore = user.getRemainingDays();
        Integer daysToAdd = redemptionCode.getDaysToAdd();
        Integer daysAfter = daysBefore + daysToAdd;
        
        // 3. 更新用户剩余天数
        user.setRemainingDays(daysAfter);
        boolean updateSuccess = userService.updateById(user);
        if (!updateSuccess) {
            throw new RuntimeException("更新用户天数失败");
        }
        
        // 4. 标记兑换码为已使用
        redemptionCode.setIsUsed(true);
        redemptionCode.setUsedByUserId(userId);
        redemptionCode.setUsedTime(LocalDateTime.now());
        redemptionCodeMapper.updateById(redemptionCode);
        
        // 5. 记录兑换日志
        RedemptionLog redemptionLog = new RedemptionLog();
        redemptionLog.setUserId(userId);
        redemptionLog.setCode(code);
        redemptionLog.setDaysAdded(daysToAdd);
        redemptionLog.setDaysBefore(daysBefore);
        redemptionLog.setDaysAfter(daysAfter);
        redemptionLog.setRedemptionTime(LocalDateTime.now());
        redemptionLog.setUserIp(userIp);
        redemptionLog.setUserAgent(userAgent);
        redemptionLogMapper.insert(redemptionLog);

        log.info("用户{}成功兑换码{}, 增加{}天, 从{}天变为{}天",
                userId, code, daysToAdd, daysBefore, daysAfter);
        
        return new RedeemCodeResponse(daysToAdd, daysBefore, daysAfter, 
                LocalDateTime.now(), redemptionCode.getDescription());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public GenerateCodeResponse generateCodes(GenerateCodeRequest request, Long adminId) {
        log.info("管理员{}生成兑换码: {}个, {}天", adminId, request.getQuantity(), request.getDaysToAdd());
        
        String batchId = generateBatchId();
        List<String> codes = new ArrayList<>();
        
        for (int i = 0; i < request.getQuantity(); i++) {
            String code = generateUniqueCode();
            
            RedemptionCode redemptionCode = new RedemptionCode();
            redemptionCode.setCode(code);
            redemptionCode.setDaysToAdd(request.getDaysToAdd());
            redemptionCode.setIsUsed(false);
            redemptionCode.setCreatedByAdminId(adminId);
            redemptionCode.setCreatedTime(LocalDateTime.now());
            redemptionCode.setExpireTime(request.getExpireTime());
            redemptionCode.setDescription(request.getDescription());
            redemptionCode.setBatchId(batchId);
            
            redemptionCodeMapper.insert(redemptionCode);
            codes.add(code);
        }
        
        log.info("成功生成{}个兑换码, 批次ID: {}", codes.size(), batchId);
        
        return new GenerateCodeResponse(batchId, codes, codes.size(),
                request.getDaysToAdd(), request.getDescription());
    }

    @Override
    public IPage<RedemptionCode> getCodesList(int page, int size, Boolean isUsed, String batchId) {
        Page<RedemptionCode> pageParam = new Page<>(page, size);
        return redemptionCodeMapper.selectPageWithStatus(pageParam, isUsed, batchId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCode(Long codeId) {
        RedemptionCode code = redemptionCodeMapper.selectById(codeId);
        if (code == null) {
            throw new RuntimeException("兑换码不存在");
        }

        if (code.getIsUsed()) {
            throw new RuntimeException("已使用的兑换码不能删除");
        }

        int result = redemptionCodeMapper.deleteById(codeId);
        log.info("删除兑换码: {}, 结果: {}", code.getCode(), result > 0 ? "成功" : "失败");

        return result > 0;
    }

    @Override
    public IPage<RedemptionHistoryResponse> getUserRedemptionHistory(Long userId, int page, int size) {
        Page<RedemptionLog> pageParam = new Page<>(page, size);
        IPage<RedemptionLog> logPage = redemptionLogMapper.selectPageByUserId(pageParam, userId);

        // 转换为响应DTO
        Page<RedemptionHistoryResponse> responsePage = new Page<>(page, size);
        responsePage.setTotal(logPage.getTotal());

        List<RedemptionHistoryResponse> responseList = new ArrayList<>();
        for (RedemptionLog log : logPage.getRecords()) {
            RedemptionHistoryResponse response = new RedemptionHistoryResponse();
            response.setId(log.getId());
            response.setCode(RedemptionHistoryResponse.maskCode(log.getCode())); // 脱敏处理
            response.setDaysAdded(log.getDaysAdded());
            response.setRedemptionTime(log.getRedemptionTime());
            response.setDaysBefore(log.getDaysBefore());
            response.setDaysAfter(log.getDaysAfter());
            responseList.add(response);
        }

        responsePage.setRecords(responseList);
        return responsePage;
    }

    @Override
    public String generateUniqueCode() {
        String code;
        int attempts = 0;
        int maxAttempts = 10;

        do {
            code = generateCodeString();
            attempts++;

            if (attempts > maxAttempts) {
                throw new RuntimeException("生成唯一兑换码失败，请重试");
            }
        } while (redemptionCodeMapper.findByCode(code) != null);

        return code;
    }

    @Override
    public RedemptionCode validateCode(String code) {
        // 1. 检查兑换码是否存在
        RedemptionCode redemptionCode = redemptionCodeMapper.findByCode(code);
        if (redemptionCode == null) {
            throw new RuntimeException("兑换码不存在");
        }

        // 2. 检查兑换码是否已使用
        if (redemptionCode.getIsUsed()) {
            throw new RuntimeException("兑换码已被使用");
        }

        // 3. 检查兑换码是否已过期
        if (redemptionCode.isExpired()) {
            throw new RuntimeException("兑换码已过期");
        }

        return redemptionCode;
    }

    /**
     * 生成兑换码字符串
     */
    private String generateCodeString() {
        // 使用时间戳 + UUID + 随机字符确保唯一性
        String timestamp = String.valueOf(System.currentTimeMillis()).substring(8); // 取后5位
        String uuid = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 8).toUpperCase();
        String random = generateRandomString(3);

        return timestamp + uuid + random;
    }

    /**
     * 生成随机字符串
     */
    private String generateRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < length; i++) {
            int index = (int) (Math.random() * chars.length());
            sb.append(chars.charAt(index));
        }

        return sb.toString();
    }

    /**
     * 生成批次ID
     */
    private String generateBatchId() {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String timeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmss"));
        return "BATCH_" + dateStr + "_" + timeStr;
    }
}
