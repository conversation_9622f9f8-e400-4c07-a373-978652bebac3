package com.seatmaster.service;

import com.seatmaster.dto.AuthResponse;
import com.seatmaster.dto.LoginRequest;
import com.seatmaster.dto.RegisterRequest;
import com.seatmaster.entity.User;

public interface UserService {
    
    /**
     * 用户注册
     */
    AuthResponse register(RegisterRequest request);
    
    /**
     * 用户登录
     */
    AuthResponse login(LoginRequest request);
    
    /**
     * 根据用户名查找用户
     */
    User findByUsername(String username);
    
    /**
     * 根据用户ID查找用户
     */
    User findById(Long userId);
    
    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 更新用户信息
     */
    boolean updateById(User user);
    
    /**
     * 检查密码是否正确
     */
    boolean checkPassword(String rawPassword, String encodedPassword);
    
    /**
     * 修改密码
     */
    boolean changePassword(Long userId, String newPassword);
} 