package com.seatmaster.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.seatmaster.dto.*;
import com.seatmaster.entity.RedemptionCode;
import com.seatmaster.entity.RedemptionLog;

/**
 * 兑换码服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface RedemptionService {
    
    /**
     * 兑换码兑换
     * 
     * @param code 兑换码
     * @param userId 用户ID
     * @param userIp 用户IP
     * @param userAgent 用户代理
     * @return 兑换结果
     */
    RedeemCodeResponse redeemCode(String code, Long userId, String userIp, String userAgent);
    
    /**
     * 生成兑换码
     * 
     * @param request 生成请求
     * @param adminId 管理员ID
     * @return 生成结果
     */
    GenerateCodeResponse generateCodes(GenerateCodeRequest request, Long adminId);
    
    /**
     * 分页查询兑换码列表（管理员）
     * 
     * @param page 页码
     * @param size 页大小
     * @param isUsed 使用状态筛选（null表示全部）
     * @param batchId 批次ID筛选
     * @return 分页结果
     */
    IPage<RedemptionCode> getCodesList(int page, int size, Boolean isUsed, String batchId);
    
    /**
     * 删除兑换码（仅未使用的）
     * 
     * @param codeId 兑换码ID
     * @return 是否删除成功
     */
    boolean deleteCode(Long codeId);
    
    /**
     * 获取用户兑换历史
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 页大小
     * @return 分页结果
     */
    IPage<RedemptionHistoryResponse> getUserRedemptionHistory(Long userId, int page, int size);
    
    /**
     * 生成唯一兑换码
     * 
     * @return 兑换码字符串
     */
    String generateUniqueCode();
    
    /**
     * 验证兑换码是否有效
     * 
     * @param code 兑换码
     * @return 兑换码实体（如果有效）
     * @throws RuntimeException 如果无效
     */
    RedemptionCode validateCode(String code);
}
