package com.seatmaster.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.seatmaster.dto.AnnouncementDTO;
import com.seatmaster.entity.Announcement;

import java.util.List;

/**
 * 公告服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
public interface AnnouncementService {
    
    /**
     * 创建公告
     * 
     * @param request 创建请求
     * @param createdBy 创建者用户ID
     * @return 公告DTO
     */
    AnnouncementDTO createAnnouncement(AnnouncementDTO.CreateRequest request, Long createdBy);
    
    /**
     * 更新公告
     * 
     * @param id 公告ID
     * @param request 更新请求
     * @return 公告DTO
     */
    AnnouncementDTO updateAnnouncement(Long id, AnnouncementDTO.UpdateRequest request);
    
    /**
     * 删除公告
     * 
     * @param id 公告ID
     * @return 是否删除成功
     */
    boolean deleteAnnouncement(Long id);
    
    /**
     * 根据ID获取公告详情
     * 
     * @param id 公告ID
     * @return 公告DTO
     */
    AnnouncementDTO getAnnouncementById(Long id);
    
    /**
     * 分页查询公告
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<AnnouncementDTO> getAnnouncementPage(AnnouncementDTO.QueryRequest query);
    
    /**
     * 获取活跃的公告列表（用于用户查看）
     * 
     * @param userRole 用户角色
     * @return 公告列表
     */
    List<AnnouncementDTO> getActiveAnnouncements(String userRole);
    
    /**
     * 获取用户未读的弹窗公告
     * 
     * @param userId 用户ID
     * @param userRole 用户角色
     * @return 未读公告列表
     */
    List<AnnouncementDTO> getUnreadPopupAnnouncements(Long userId, String userRole);
    
    /**
     * 标记公告为已读
     * 
     * @param userId 用户ID
     * @param announcementId 公告ID
     * @return 是否标记成功
     */
    boolean markAnnouncementAsRead(Long userId, Long announcementId);
    
    /**
     * 关闭公告弹窗
     * 
     * @param userId 用户ID
     * @param announcementId 公告ID
     * @return 是否关闭成功
     */
    boolean dismissAnnouncementPopup(Long userId, Long announcementId);
    
    /**
     * 切换公告启用状态
     * 
     * @param id 公告ID
     * @param enabled 是否启用
     * @return 是否切换成功
     */
    boolean toggleAnnouncementStatus(Long id, boolean enabled);
    
    /**
     * 增加公告查看次数
     * 
     * @param id 公告ID
     */
    void incrementViewCount(Long id);
    
    /**
     * 批量删除公告
     * 
     * @param ids 公告ID列表
     * @return 删除成功的数量
     */
    int batchDeleteAnnouncements(List<Long> ids);
}
