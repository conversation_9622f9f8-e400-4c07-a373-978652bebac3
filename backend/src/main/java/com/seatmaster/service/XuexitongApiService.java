package com.seatmaster.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 学习通API服务
 * 负责与学习通系统进行交互，实现自动预约功能
 */
@Service
public class XuexitongApiService {
    
    private static final Logger logger = LoggerFactory.getLogger(XuexitongApiService.class);
    
    // 学习通API基础URL（需要根据实际情况调整）
    private static final String BASE_URL = "https://api.xuexitong.com";
    private static final String LOGIN_URL = BASE_URL + "/auth/login";
    private static final String ROOMS_URL = BASE_URL + "/api/rooms";
    private static final String SEATS_URL = BASE_URL + "/api/seats";
    private static final String RESERVATION_URL = BASE_URL + "/api/reservations";
    
    // HTTP客户端配置
    private static final Duration TIMEOUT = Duration.ofSeconds(30);

    private final HttpClient httpClient;
    private final Map<String, XuexitongSession> sessionCache;

    public XuexitongApiService() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(TIMEOUT)
                .build();
        this.sessionCache = new ConcurrentHashMap<>();
    }
    
    /**
     * 执行学习通预约
     * @param reservationData 预约数据
     * @return 预约结果
     */
    public XuexitongResponse executeReservation(ReservationData reservationData) {
        logger.info("开始执行学习通预约: 用户={}, 房间={}, 座位={}, 时间={}-{}", 
                   reservationData.getUsername(), 
                   reservationData.getRoomNum(), 
                   reservationData.getSeatId(),
                   reservationData.getStartTime(), 
                   reservationData.getEndTime());
        
        try {
            // 1. 获取或创建会话
            XuexitongSession session = getOrCreateSession(reservationData.getUsername(), reservationData.getPassword());
            if (session == null || !session.isValid()) {
                return XuexitongResponse.failure("登录失败，无法获取有效会话");
            }
            
            // 2. 查询房间信息
            String roomId = getRoomId(session, reservationData.getRoomNum());
            if (roomId == null) {
                return XuexitongResponse.failure("未找到房间: " + reservationData.getRoomNum());
            }
            
            // 3. 检查座位可用性
            if (!isSeatAvailable(session, roomId, reservationData.getSeatId(), reservationData.getReservationDate())) {
                return XuexitongResponse.failure("座位不可用: " + reservationData.getSeatId());
            }
            
            // 4. 提交预约
            boolean success = submitReservation(session, roomId, reservationData);
            if (success) {
                logger.info("学习通预约成功: 用户={}, 座位={}", reservationData.getUsername(), reservationData.getSeatId());
                return XuexitongResponse.success("预约成功", createSuccessData(reservationData));
            } else {
                return XuexitongResponse.failure("预约提交失败");
            }
            
        } catch (Exception e) {
            logger.error("学习通预约异常: 用户={}, 错误={}", reservationData.getUsername(), e.getMessage(), e);
            return XuexitongResponse.failure("预约异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取或创建会话
     */
    private XuexitongSession getOrCreateSession(String username, String password) {
        // 检查缓存中的会话
        XuexitongSession cachedSession = sessionCache.get(username);
        if (cachedSession != null && cachedSession.isValid()) {
            logger.debug("使用缓存会话: {}", username);
            return cachedSession;
        }
        
        // 创建新会话
        logger.info("创建新的学习通会话: {}", username);
        XuexitongSession newSession = login(username, password);
        if (newSession != null && newSession.isValid()) {
            sessionCache.put(username, newSession);
        }
        
        return newSession;
    }
    
    /**
     * 学习通登录
     */
    private XuexitongSession login(String username, String password) {
        try {
            // 构建登录请求体
            String loginData = String.format(
                "{\"username\":\"%s\",\"password\":\"%s\",\"loginType\":\"student\"}",
                username, password);

            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(LOGIN_URL))
                    .timeout(TIMEOUT)
                    .header("Content-Type", "application/json")
                    .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                    .POST(HttpRequest.BodyPublishers.ofString(loginData))
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() == 200) {
                String responseBody = response.body();
                // 简化的JSON解析（实际项目中应使用JSON库）
                if (responseBody.contains("\"success\":true")) {
                    // 提取token和sessionId（简化实现）
                    String token = extractJsonValue(responseBody, "token");
                    String sessionId = extractJsonValue(responseBody, "sessionId");

                    if (token != null && sessionId != null) {
                        logger.info("学习通登录成功: {}", username);
                        return new XuexitongSession(username, token, sessionId);
                    }
                }
            }

            logger.error("学习通登录失败: {}, 状态码: {}, 响应: {}", username, response.statusCode(), response.body());
            return null;

        } catch (Exception e) {
            logger.error("学习通登录异常: {}", username, e);
            return null;
        }
    }
    
    /**
     * 获取房间ID
     */
    private String getRoomId(XuexitongSession session, String roomNum) {
        try {
            String url = ROOMS_URL + "?roomNum=" + roomNum;
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(TIMEOUT)
                    .header("Authorization", "Bearer " + session.getToken())
                    .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() == 200) {
                String responseBody = response.body();
                if (responseBody.contains("\"success\":true")) {
                    // 提取房间ID（简化实现）
                    String roomId = extractJsonValue(responseBody, "id");
                    if (roomId != null) {
                        return roomId;
                    }
                }
            }

            logger.warn("未找到房间: {}, 状态码: {}, 响应: {}", roomNum, response.statusCode(), response.body());
            return null;

        } catch (Exception e) {
            logger.error("查询房间异常: {}", roomNum, e);
            return null;
        }
    }
    
    /**
     * 检查座位可用性
     */
    private boolean isSeatAvailable(XuexitongSession session, String roomId, String seatId, LocalDate date) {
        try {
            String url = SEATS_URL + "/" + roomId + "/availability?date=" + date.toString() + "&seatId=" + seatId;
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .timeout(TIMEOUT)
                    .header("Authorization", "Bearer " + session.getToken())
                    .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() == 200) {
                String responseBody = response.body();
                if (responseBody.contains("\"success\":true")) {
                    // 检查座位是否可用
                    return responseBody.contains("\"available\":true");
                }
            }

            logger.warn("座位可用性检查失败: 房间={}, 座位={}, 状态码: {}", roomId, seatId, response.statusCode());
            return false;

        } catch (Exception e) {
            logger.error("座位可用性检查异常: 房间={}, 座位={}", roomId, seatId, e);
            return false;
        }
    }
    
    /**
     * 提交预约
     */
    private boolean submitReservation(XuexitongSession session, String roomId, ReservationData data) {
        try {
            // 构建预约请求体
            String reservationData = String.format(
                "{\"roomId\":\"%s\",\"seatId\":\"%s\",\"date\":\"%s\",\"startTime\":\"%s\",\"endTime\":\"%s\",\"reservationType\":\"%s\"}",
                roomId, data.getSeatId(), data.getReservationDate().toString(),
                data.getStartTime().toString(), data.getEndTime().toString(), data.getReservationType());

            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(RESERVATION_URL))
                    .timeout(TIMEOUT)
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer " + session.getToken())
                    .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                    .POST(HttpRequest.BodyPublishers.ofString(reservationData))
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() == 200) {
                String responseBody = response.body();
                if (responseBody.contains("\"success\":true")) {
                    logger.info("预约提交成功: 房间={}, 座位={}", roomId, data.getSeatId());
                    return true;
                }
            }

            logger.error("预约提交失败: 房间={}, 座位={}, 状态码: {}, 响应: {}",
                        roomId, data.getSeatId(), response.statusCode(), response.body());
            return false;

        } catch (Exception e) {
            logger.error("预约提交异常: 房间={}, 座位={}", roomId, data.getSeatId(), e);
            return false;
        }
    }
    
    /**
     * 创建成功响应数据
     */
    private Map<String, Object> createSuccessData(ReservationData data) {
        Map<String, Object> result = new HashMap<>();
        result.put("username", data.getUsername());
        result.put("roomNum", data.getRoomNum());
        result.put("seatId", data.getSeatId());
        result.put("date", data.getReservationDate().toString());
        result.put("startTime", data.getStartTime().toString());
        result.put("endTime", data.getEndTime().toString());
        result.put("reservationType", data.getReservationType());
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
    
    /**
     * 清理过期会话
     */
    public void cleanupExpiredSessions() {
        sessionCache.entrySet().removeIf(entry -> !entry.getValue().isValid());
        logger.debug("清理过期会话完成，当前会话数: {}", sessionCache.size());
    }

    /**
     * 简化的JSON值提取方法
     */
    private String extractJsonValue(String json, String key) {
        try {
            String searchPattern = "\"" + key + "\":\"";
            int startIndex = json.indexOf(searchPattern);
            if (startIndex != -1) {
                startIndex += searchPattern.length();
                int endIndex = json.indexOf("\"", startIndex);
                if (endIndex != -1) {
                    return json.substring(startIndex, endIndex);
                }
            }
        } catch (Exception e) {
            logger.debug("JSON值提取失败: key={}, json={}", key, json);
        }
        return null;
    }
}
