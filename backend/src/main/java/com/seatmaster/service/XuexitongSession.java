package com.seatmaster.service;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * 学习通会话管理
 * 管理用户的登录状态和会话信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class XuexitongSession {
    
    /**
     * 会话有效期（分钟）
     */
    private static final long SESSION_TIMEOUT_MINUTES = 30;
    
    /**
     * 用户信息
     */
    private String username;        // 用户名
    private String token;          // 认证令牌
    private String sessionId;      // 会话ID
    
    /**
     * 会话状态
     */
    private LocalDateTime createTime;    // 创建时间
    private LocalDateTime lastUsedTime;  // 最后使用时间
    private boolean active;             // 是否活跃
    
    /**
     * 构造函数
     */
    public XuexitongSession(String username, String token, String sessionId) {
        this.username = username;
        this.token = token;
        this.sessionId = sessionId;
        this.createTime = LocalDateTime.now();
        this.lastUsedTime = LocalDateTime.now();
        this.active = true;
    }
    
    /**
     * 检查会话是否有效
     * @return 是否有效
     */
    public boolean isValid() {
        if (!active || token == null || token.trim().isEmpty()) {
            return false;
        }
        
        // 检查是否超时
        if (lastUsedTime != null) {
            long minutesSinceLastUse = ChronoUnit.MINUTES.between(lastUsedTime, LocalDateTime.now());
            if (minutesSinceLastUse > SESSION_TIMEOUT_MINUTES) {
                active = false;
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 更新最后使用时间
     */
    public void updateLastUsedTime() {
        this.lastUsedTime = LocalDateTime.now();
    }
    
    /**
     * 标记会话为无效
     */
    public void invalidate() {
        this.active = false;
    }
    
    /**
     * 获取会话剩余有效时间（分钟）
     */
    public long getRemainingMinutes() {
        if (!isValid()) {
            return 0;
        }
        
        long minutesSinceLastUse = ChronoUnit.MINUTES.between(lastUsedTime, LocalDateTime.now());
        return Math.max(0, SESSION_TIMEOUT_MINUTES - minutesSinceLastUse);
    }
    
    /**
     * 获取会话持续时间（分钟）
     */
    public long getSessionDurationMinutes() {
        if (createTime == null) {
            return 0;
        }
        return ChronoUnit.MINUTES.between(createTime, LocalDateTime.now());
    }
    
    /**
     * 获取会话状态描述
     */
    public String getStatusDescription() {
        if (!active) {
            return "已失效";
        }
        
        if (!isValid()) {
            return "已超时";
        }
        
        return String.format("活跃（剩余%d分钟）", getRemainingMinutes());
    }
    
    /**
     * 获取会话摘要信息
     */
    public String getSummary() {
        return String.format("用户=%s, 状态=%s, 创建时间=%s, 持续时间=%d分钟",
                           username, getStatusDescription(), 
                           createTime != null ? createTime.toString() : "未知",
                           getSessionDurationMinutes());
    }
    
    @Override
    public String toString() {
        return getSummary();
    }
}
