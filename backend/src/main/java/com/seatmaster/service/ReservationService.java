package com.seatmaster.service;

import com.seatmaster.entity.Reservation;
import com.seatmaster.dto.UserProfileResponse;

import java.time.LocalTime;
import java.util.List;

public interface ReservationService {
    
    /**
     * 创建或更新预约 - 用户自选座位号
     * @param userId 用户ID
     * @param roomId 房间ID
     * @param seatId 座位号（用户自选）
     * @param startTime 开始时间（仅时间，格式HH:MM:SS）
     * @param endTime 结束时间（仅时间，格式HH:MM:SS）
     * @param reservationOpenTime 预约开放时间
     * @param reservationType 预约类型
     * @return 预约信息
     */
    Reservation createOrUpdateReservation(Long userId, Long roomId, String seatId,
                                        LocalTime startTime, LocalTime endTime,
                                        String reservationOpenTime, String reservationType);

    /**
     * 更新现有预约
     * @param userId 用户ID
     * @param roomId 房间ID
     * @param seatId 座位号（用户自选）
     * @param startTime 开始时间（仅时间，格式HH:MM:SS）
     * @param endTime 结束时间（仅时间，格式HH:MM:SS）
     * @param reservationOpenTime 预约开放时间
     * @param reservationType 预约类型
     * @return 预约信息
     */
    Reservation updateReservation(Long userId, Long roomId, String seatId,
                                LocalTime startTime, LocalTime endTime,
                                String reservationOpenTime, String reservationType);
    
    /**
     * 取消预约
     * @param reservationId 预约ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean cancelReservation(Long reservationId, Long userId);
    
    /**
     * 获取用户当前预约
     * @param userId 用户ID
     * @return 当前预约信息
     */
    UserProfileResponse.CurrentReservation getCurrentReservation(Long userId);
    
    /**
     * 获取房间可用座位数
     * @param roomId 房间ID
     * @param startTime 开始时间（仅时间，格式HH:MM:SS）
     * @param endTime 结束时间（仅时间，格式HH:MM:SS）
     * @return 可用座位数
     */
    int getAvailableSeatsCount(Long roomId, LocalTime startTime, LocalTime endTime);
    
    /**
     * 获取房间活跃预约列表
     * @param roomId 房间ID
     * @return 活跃预约列表
     */
    List<Reservation> getActiveReservations(Long roomId);
} 