package com.seatmaster.service;

import com.seatmaster.dto.ExecutionLogDTO;
import com.seatmaster.dto.ExecutionLogQueryDTO;

import java.util.List;
import java.util.Map;

/**
 * 执行日志查询服务接口
 */
public interface ExecutionLogQueryService {
    
    /**
     * 获取用户的执行历史
     */
    Map<String, Object> getUserExecutionLogs(ExecutionLogQueryDTO queryDTO);
    

    

    
    /**
     * 导出用户执行日志
     */
    String exportUserLogs(Long userId, String startDate, String endDate);
    
    /**
     * 获取最近的执行日志
     */
    List<ExecutionLogDTO> getRecentExecutionLogs(Long userId, int limit);
}
