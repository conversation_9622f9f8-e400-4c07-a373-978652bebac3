package com.seatmaster.service;

import com.seatmaster.entity.Reservation;
import com.seatmaster.entity.Room;
import com.seatmaster.entity.User;
import com.seatmaster.mapper.ReservationMapper;
import com.seatmaster.mapper.RoomMapper;
import com.seatmaster.mapper.UserMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 预约数据服务
 * 负责从数据库查询预约相关信息并构建ReservationData对象
 */
@Service
public class ReservationDataService {
    
    private static final Logger logger = LoggerFactory.getLogger(ReservationDataService.class);
    
    @Autowired
    private ReservationMapper reservationMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private RoomMapper roomMapper;
    
    /**
     * 根据预约ID构建预约数据
     * @param reservationId 预约ID
     * @return 预约数据，如果查询失败返回null
     */
    public ReservationData buildReservationData(Long reservationId) {
        try {
            logger.info("开始构建预约数据: reservationId={}", reservationId);
            
            // 1. 查询预约记录
            Reservation reservation = reservationMapper.selectById(reservationId);
            if (reservation == null) {
                logger.error("预约记录不存在: reservationId={}", reservationId);
                return null;
            }
            
            // 2. 查询用户信息
            User user = userMapper.selectById(reservation.getUserId());
            if (user == null) {
                logger.error("用户不存在: userId={}", reservation.getUserId());
                return null;
            }
            
            // 3. 查询房间信息
            Room room = roomMapper.selectById(reservation.getRoomId());
            if (room == null) {
                logger.error("房间不存在: roomId={}", reservation.getRoomId());
                return null;
            }
            
            // 4. 构建预约数据
            ReservationData reservationData = ReservationData.Builder.fromDatabase(
                user.getUsername(),           // 学习通用户名
                user.getPassword(),           // 学习通密码
                user.getId(),                 // 用户ID
                reservation.getId(),          // 预约ID
                room.getRoomId(),            // 房间号（使用roomId字段作为roomNum）
                reservation.getSeatId(),      // 座位号
                reservation.getStartTime(),   // 开始时间
                reservation.getEndTime(),     // 结束时间
                reservation.getReservationOpenTime(),  // 预约开放时间
                reservation.getReservationType()       // 预约类型
            );
            
            // 5. 验证数据完整性
            if (!reservationData.isValid()) {
                logger.error("预约数据不完整: {}", reservationData.getSummary());
                return null;
            }
            
            logger.info("预约数据构建成功: {}", reservationData.getSummary());
            return reservationData;
            
        } catch (Exception e) {
            logger.error("构建预约数据异常: reservationId={}", reservationId, e);
            return null;
        }
    }
    
    /**
     * 根据用户ID和预约状态查询预约数据
     * @param userId 用户ID
     * @param status 预约状态（如：ACTIVE）
     * @return 预约数据列表
     */
    public java.util.List<ReservationData> buildReservationDataByUser(Long userId, String status) {
        try {
            logger.info("查询用户预约数据: userId={}, status={}", userId, status);
            
            // 查询用户的预约记录
            com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<Reservation> wrapper =
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
            wrapper.eq("user_id", userId);
            // 注意：reservations表中没有status字段，所以移除status条件
            wrapper.orderByDesc("created_time");
            
            java.util.List<Reservation> reservations = reservationMapper.selectList(wrapper);
            java.util.List<ReservationData> result = new java.util.ArrayList<>();
            
            for (Reservation reservation : reservations) {
                ReservationData data = buildReservationData(reservation.getId());
                if (data != null) {
                    result.add(data);
                }
            }
            
            logger.info("查询用户预约数据完成: userId={}, 数量={}", userId, result.size());
            return result;
            
        } catch (Exception e) {
            logger.error("查询用户预约数据异常: userId={}", userId, e);
            return new java.util.ArrayList<>();
        }
    }
    
    /**
     * 检查预约数据的有效性
     * @param reservationData 预约数据
     * @return 检查结果
     */
    public ValidationResult validateReservationData(ReservationData reservationData) {
        if (reservationData == null) {
            return ValidationResult.failure("预约数据为空");
        }
        
        // 检查基本字段
        if (!reservationData.isValid()) {
            return ValidationResult.failure("预约数据不完整: " + reservationData.getSummary());
        }
        
        // 检查时间逻辑
        if (reservationData.getStartTime().isAfter(reservationData.getEndTime())) {
            return ValidationResult.failure("开始时间不能晚于结束时间");
        }
        
        // 检查预约类型
        String type = reservationData.getReservationType();
        if (!"SAME_DAY".equals(type) && !"ADVANCE_ONE_DAY".equals(type)) {
            return ValidationResult.failure("无效的预约类型: " + type);
        }
        
        return ValidationResult.success("验证通过");
    }
    
    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String message;
        
        private ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }
        
        public static ValidationResult success(String message) {
            return new ValidationResult(true, message);
        }
        
        public static ValidationResult failure(String message) {
            return new ValidationResult(false, message);
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getMessage() {
            return message;
        }
    }
}
