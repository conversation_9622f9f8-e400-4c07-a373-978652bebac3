package com.seatmaster.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;

/**
 * 异步任务重新分配服务
 * 处理Worker删除后的任务清理和重新分配
 */
@Slf4j
@Service
public class AsyncTaskReassignmentService {

    @Autowired
    private DistributedTaskService distributedTaskService;

    // 存储异步处理的状态信息
    private final Map<String, ReassignmentStatus> reassignmentStatusMap = new ConcurrentHashMap<>();

    /**
     * 异步处理Worker删除后的任务重新分配
     * @param workerId 被删除的Worker ID
     * @return 异步处理的Future对象
     */
    @Async("taskReassignmentExecutor")
    public CompletableFuture<ReassignmentResult> reassignTasksFromDeletedWorker(String workerId) {
        String taskId = generateTaskId(workerId);
        String currentThread = Thread.currentThread().getName();
        log.info("🔥 开始异步处理Worker删除后的任务重新分配: workerId={}, taskId={}, thread={}", workerId, taskId, currentThread);

        // 初始化状态
        ReassignmentStatus status = new ReassignmentStatus();
        status.setWorkerId(workerId);
        status.setTaskId(taskId);
        status.setStatus("PROCESSING");
        status.setStartTime(LocalDateTime.now());
        status.setMessage("正在清理分配给已删除Worker的任务...");
        reassignmentStatusMap.put(taskId, status);

        try {
            // 1. 清理分配给该Worker的任务
            log.info("步骤1: 清理Worker任务 - workerId={}", workerId);
            status.setMessage("正在清理分配给Worker的任务...");
            int clearedCount = distributedTaskService.clearWorkerTasks(workerId);
            status.setClearedTaskCount(clearedCount);
            
            log.info("Worker任务清理完成: workerId={}, 清理数量={}", workerId, clearedCount);

            // 2. 重新分配任务
            if (clearedCount > 0) {
                log.info("步骤2: 重新分配任务 - 清理的任务数={}", clearedCount);
                status.setMessage("正在重新分配任务给其他在线Worker...");
                
                Map<String, Object> assignResult = distributedTaskService.autoAssignPendingTasks();
                Integer reassignedCount = (Integer) assignResult.get("assignedCount");
                status.setReassignedTaskCount(reassignedCount != null ? reassignedCount : 0);
                
                log.info("任务重新分配完成: 重新分配数量={}", status.getReassignedTaskCount());
            } else {
                log.info("没有需要重新分配的任务: workerId={}", workerId);
                status.setReassignedTaskCount(0);
            }

            // 3. 完成处理
            status.setStatus("COMPLETED");
            status.setEndTime(LocalDateTime.now());
            status.setMessage("任务重新分配完成");

            ReassignmentResult result = new ReassignmentResult();
            result.setSuccess(true);
            result.setWorkerId(workerId);
            result.setClearedCount(status.getClearedTaskCount());
            result.setReassignedCount(status.getReassignedTaskCount());
            result.setMessage("任务重新分配成功完成");

            log.info("Worker删除后任务重新分配完成: workerId={}, 清理={}, 重新分配={}", 
                    workerId, result.getClearedCount(), result.getReassignedCount());

            return CompletableFuture.completedFuture(result);

        } catch (Exception e) {
            log.error("Worker删除后任务重新分配失败: workerId={}", workerId, e);
            
            status.setStatus("FAILED");
            status.setEndTime(LocalDateTime.now());
            status.setMessage("任务重新分配失败: " + e.getMessage());

            ReassignmentResult result = new ReassignmentResult();
            result.setSuccess(false);
            result.setWorkerId(workerId);
            result.setMessage("任务重新分配失败: " + e.getMessage());

            return CompletableFuture.completedFuture(result);
        }
    }

    /**
     * 获取任务重新分配的状态
     * @param taskId 任务ID
     * @return 重新分配状态
     */
    public ReassignmentStatus getReassignmentStatus(String taskId) {
        return reassignmentStatusMap.get(taskId);
    }

    /**
     * 清理已完成的状态记录（避免内存泄漏）
     */
    public void cleanupCompletedStatus() {
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(1); // 保留1小时
        reassignmentStatusMap.entrySet().removeIf(entry -> {
            ReassignmentStatus status = entry.getValue();
            return status.getEndTime() != null && status.getEndTime().isBefore(cutoffTime);
        });
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId(String workerId) {
        return "reassign_" + workerId + "_" + System.currentTimeMillis();
    }

    /**
     * 重新分配状态信息
     */
    public static class ReassignmentStatus {
        private String taskId;
        private String workerId;
        private String status; // PROCESSING, COMPLETED, FAILED
        private String message;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private int clearedTaskCount = 0;
        private int reassignedTaskCount = 0;

        // Getters and Setters
        public String getTaskId() { return taskId; }
        public void setTaskId(String taskId) { this.taskId = taskId; }
        
        public String getWorkerId() { return workerId; }
        public void setWorkerId(String workerId) { this.workerId = workerId; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
        
        public int getClearedTaskCount() { return clearedTaskCount; }
        public void setClearedTaskCount(int clearedTaskCount) { this.clearedTaskCount = clearedTaskCount; }
        
        public int getReassignedTaskCount() { return reassignedTaskCount; }
        public void setReassignedTaskCount(int reassignedTaskCount) { this.reassignedTaskCount = reassignedTaskCount; }
    }

    /**
     * 重新分配结果
     */
    public static class ReassignmentResult {
        private boolean success;
        private String workerId;
        private int clearedCount;
        private int reassignedCount;
        private String message;

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getWorkerId() { return workerId; }
        public void setWorkerId(String workerId) { this.workerId = workerId; }
        
        public int getClearedCount() { return clearedCount; }
        public void setClearedCount(int clearedCount) { this.clearedCount = clearedCount; }
        
        public int getReassignedCount() { return reassignedCount; }
        public void setReassignedCount(int reassignedCount) { this.reassignedCount = reassignedCount; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}
