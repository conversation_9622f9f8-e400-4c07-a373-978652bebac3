package com.seatmaster.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.seatmaster.dto.ReservationTaskDTO;
import com.seatmaster.dto.TaskStatisticsDTO;
import com.seatmaster.dto.WorkerServerDTO;

import java.util.List;
import java.util.Map;

/**
 * 分布式任务服务接口
 * 提供分布式任务管理相关的业务逻辑
 */
public interface DistributedTaskService {
    
    /**
     * 获取任务统计数据
     * @return 任务统计信息
     */
    TaskStatisticsDTO getTaskStatistics();
    
    /**
     * 获取预约任务列表（分页）
     * @param page 页码
     * @param size 每页大小
     * @param status 执行状态筛选（可选）
     * @param keyword 搜索关键词（可选）
     * @param workerId 副服务器ID筛选（可选）
     * @return 分页的任务列表
     */
    Page<ReservationTaskDTO> getReservationTasks(int page, int size, String status, String keyword, String workerId);
    
    /**
     * 获取任务详情
     * @param reservationId 预约ID
     * @return 任务详情
     */
    ReservationTaskDTO getTaskDetail(Long reservationId);
    
    /**
     * 手动分配任务到指定副服务器
     * @param reservationId 预约ID
     * @param workerId 副服务器ID
     * @return 分配结果
     */
    boolean assignTaskToWorker(Long reservationId, String workerId);
    
    /**
     * 重新执行失败的任务
     * @param reservationId 预约ID
     * @return 重新执行结果
     */
    boolean retryFailedTask(Long reservationId);
    
    /**
     * 批量分配任务
     * @param reservationIds 预约ID列表
     * @param workerId 副服务器ID
     * @return 分配结果统计
     */
    Map<String, Object> batchAssignTasks(List<Long> reservationIds, String workerId);
    
    /**
     * 获取可用的副服务器列表
     * @return 在线且有空闲容量的副服务器列表
     */
    List<WorkerServerDTO> getAvailableWorkers();
    
    /**
     * 自动分配待执行任务
     * @return 分配结果统计
     */
    Map<String, Object> autoAssignPendingTasks();
    
    /**
     * 获取副服务器状态概览
     * @return 副服务器状态信息
     */
    List<Map<String, Object>> getWorkerStatusOverview();
    
    /**
     * 取消任务执行
     * @param reservationId 预约ID
     * @return 取消结果
     */
    boolean cancelTask(Long reservationId);
    
    /**
     * 获取任务执行日志
     * @param reservationId 预约ID
     * @return 执行日志
     */
    List<String> getTaskExecutionLogs(Long reservationId);
    
    /**
     * 清理过期的任务记录
     * @param daysToKeep 保留天数
     * @return 清理的记录数
     */
    int cleanupExpiredTasks(int daysToKeep);

    /**
     * 清理分配给指定Worker的任务
     * @param workerId Worker ID
     * @return 清理的任务数量
     */
    int clearWorkerTasks(String workerId);

    /**
     * 调试副服务器调用
     * @param reservationId 预约ID
     * @return 调试信息
     */
    Map<String, Object> debugWorkerCall(Long reservationId);
}
