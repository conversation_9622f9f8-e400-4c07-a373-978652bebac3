package com.seatmaster.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.seatmaster.dto.WorkerServerDTO;
import com.seatmaster.entity.Reservation;
import com.seatmaster.entity.WorkerServer;
import com.seatmaster.mapper.ReservationMapper;
import com.seatmaster.mapper.WorkerServerMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 副服务器管理服务
 */
@Slf4j
@Service
public class WorkerServerService extends ServiceImpl<WorkerServerMapper, WorkerServer> {

    private final WorkerServerMapper workerServerMapper;
    private final RestTemplate restTemplate;

    @Autowired
    private ReservationMapper reservationMapper;

    @Autowired
    @Lazy
    private AsyncTaskReassignmentService asyncTaskReassignmentService;

    @Autowired
    @Lazy
    private DistributedTaskService distributedTaskService;

    // 任务数量缓存，减少频繁的数据库查询
    private final Map<String, Integer> taskCountCache = new ConcurrentHashMap<>();
    private final Map<String, Long> cacheTimestamp = new ConcurrentHashMap<>();

    // 配置化参数
    @Value("${seatmaster.worker-monitoring.load-difference-threshold:3}")
    private int loadDifferenceThreshold;

    @Value("${seatmaster.worker-monitoring.task-count-cache-expire:30000}")
    private long cacheExpireTime;

    public WorkerServerService(WorkerServerMapper workerServerMapper) {
        this.workerServerMapper = workerServerMapper;
        this.restTemplate = new RestTemplate();
    }

    /**
     * 获取所有服务器列表
     */
    public List<WorkerServerDTO> getAllServers() {
        List<WorkerServer> servers = list();
        return servers.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 根据ID获取服务器
     */
    public WorkerServerDTO getServerById(String id) {
        WorkerServer server = getById(id);
        return server != null ? convertToDTO(server) : null;
    }

    /**
     * 根据workerId获取服务器
     */
    public WorkerServerDTO getServerByWorkerId(String workerId) {
        WorkerServer server = workerServerMapper.selectByWorkerId(workerId);
        return server != null ? convertToDTO(server) : null;
    }

    /**
     * 创建新服务器
     */
    @Transactional
    public WorkerServerDTO createServer(WorkerServerDTO.CreateRequest request) {
        // 验证workerId唯一性
        if (getById(request.getWorkerId()) != null) {
            throw new RuntimeException("服务器ID已存在: " + request.getWorkerId());
        }

        WorkerServer server = new WorkerServer();
        server.setId(request.getWorkerId());
        server.setName(request.getName());
        server.setServerUrl("http://" + request.getHost() + ":" + request.getPort());
        server.setPriority(5); // 默认优先级
        server.setMaxConcurrentTasks(request.getMaxConcurrentTasks());
        server.setStatus("OFFLINE");
        server.setCurrentLoad(0);
        server.setTotalTasksCompleted(0L);
        server.setTotalTasksFailed(0L);
        server.setAverageExecutionTime(0.0);
        server.setEnabled(true);
        server.setDescription("通过管理界面创建的副服务器");
        server.setCreatedTime(LocalDateTime.now());
        server.setUpdatedTime(LocalDateTime.now());

        save(server);
        log.info("创建副服务器成功: workerId={}, name={}", server.getWorkerId(), server.getName());

        return convertToDTO(server);
    }

    /**
     * 更新服务器配置
     */
    @Transactional
    public WorkerServerDTO updateServer(WorkerServerDTO.UpdateRequest request) {
        WorkerServer existingServer = getById(request.getId());
        if (existingServer == null) {
            throw new RuntimeException("服务器不存在: " + request.getId());
        }

        // 更新配置
        existingServer.setName(request.getName());
        existingServer.setServerUrl("http://" + request.getHost() + ":" + request.getPort());
        existingServer.setMaxConcurrentTasks(request.getMaxConcurrentTasks());
        existingServer.setUpdatedTime(LocalDateTime.now());

        updateById(existingServer);
        log.info("更新副服务器配置成功: workerId={}, name={}", existingServer.getWorkerId(), existingServer.getName());

        return convertToDTO(existingServer);
    }

    /**
     * 删除服务器
     */
    @Transactional
    public boolean deleteServer(String id) {
        WorkerServer server = getById(id);
        if (server == null) {
            throw new RuntimeException("服务器不存在: " + id);
        }

        // 在线状态检查限制已移除 - 允许直接删除在线服务器
        // 注释原因：根据管理员需求，移除删除在线服务器的限制
        /*
        if (server.isOnline()) {
            throw new RuntimeException("无法删除在线服务器，请先停止服务器: " + server.getName());
        }
        */

        String workerId = server.getId(); // 保存workerId用于后续异步处理
        String workerName = server.getName();

        boolean result = removeById(id);
        if (result) {
            log.info("删除副服务器成功: workerId={}, name={}", workerId, workerName);

            // 同步处理任务重新分配（修复异步问题）
            try {
                log.info("🔥 开始同步处理Worker删除后的任务重新分配: workerId={}", workerId);

                // 1. 清理分配给该Worker的任务
                int clearedCount = distributedTaskService.clearWorkerTasks(workerId);
                log.info("🔥 Worker任务清理完成: workerId={}, 清理数量={}", workerId, clearedCount);

                // 2. 重新分配任务
                if (clearedCount > 0) {
                    Map<String, Object> assignResult = distributedTaskService.autoAssignPendingTasks();
                    Integer reassignedCount = (Integer) assignResult.get("assignedCount");
                    log.info("🔥 任务重新分配完成: workerId={}, 清理={}, 重新分配={}",
                            workerId, clearedCount, reassignedCount != null ? reassignedCount : 0);
                } else {
                    log.info("🔥 没有需要重新分配的任务: workerId={}", workerId);
                }
            } catch (Exception e) {
                log.error("🔥 任务重新分配失败: workerId={}", workerId, e);
                // 不影响删除操作的成功
            }
        }

        return result;
    }

    /**
     * 批量删除服务器
     */
    @Transactional
    public int batchDeleteServers(List<String> ids) {
        // 在线状态检查限制已移除 - 允许批量删除在线服务器
        // 注释原因：根据管理员需求，移除删除在线服务器的限制
        List<WorkerServer> servers = listByIds(ids);
        /*
        List<String> onlineServers = servers.stream()
                .filter(WorkerServer::isOnline)
                .map(WorkerServer::getName)
                .collect(Collectors.toList());

        if (!onlineServers.isEmpty()) {
            throw new RuntimeException("无法删除在线服务器: " + String.join(", ", onlineServers));
        }
        */

        // 保存要删除的服务器ID列表，用于后续异步处理
        List<String> workerIds = servers.stream()
                .map(WorkerServer::getId)
                .collect(Collectors.toList());

        int deletedCount = workerServerMapper.deleteBatchByIds(ids);
        log.info("批量删除副服务器成功: 删除数量={}", deletedCount);

        // 为每个删除的Worker同步处理任务重新分配
        if (deletedCount > 0) {
            for (String workerId : workerIds) {
                try {
                    log.info("🔥 开始同步处理Worker删除后的任务重新分配: workerId={}", workerId);

                    // 1. 清理分配给该Worker的任务
                    int clearedCount = distributedTaskService.clearWorkerTasks(workerId);
                    log.info("🔥 Worker任务清理完成: workerId={}, 清理数量={}", workerId, clearedCount);

                    // 2. 重新分配任务
                    if (clearedCount > 0) {
                        Map<String, Object> assignResult = distributedTaskService.autoAssignPendingTasks();
                        Integer reassignedCount = (Integer) assignResult.get("assignedCount");
                        log.info("🔥 任务重新分配完成: workerId={}, 清理={}, 重新分配={}",
                                workerId, clearedCount, reassignedCount != null ? reassignedCount : 0);
                    } else {
                        log.info("🔥 没有需要重新分配的任务: workerId={}", workerId);
                    }
                } catch (Exception e) {
                    log.error("🔥 任务重新分配失败: workerId={}", workerId, e);
                    // 不影响删除操作的成功
                }
            }
        }

        return deletedCount;
    }

    /**
     * 健康检查 - 已禁用主动健康检查功能
     * 注释原因：根据需求，移除主服务器主动发送心跳的功能
     */
    /*
    public WorkerServerDTO.HealthCheckResponse healthCheck(String id) {
        WorkerServer server = getById(id);
        if (server == null) {
            throw new RuntimeException("服务器不存在: " + id);
        }

        return performHealthCheck(server);
    }
    */

    /**
     * 批量健康检查 - 已禁用主动健康检查功能
     * 注释原因：根据需求，移除主服务器主动发送心跳的功能
     */
    /*
    public List<WorkerServerDTO.HealthCheckResponse> batchHealthCheck(List<String> ids) {
        List<WorkerServer> servers = listByIds(ids);

        return servers.stream()
                .map(this::performHealthCheck)
                .collect(Collectors.toList());
    }
    */



    /**
     * 批量操作
     */
    @Transactional
    public Map<String, Object> batchOperation(WorkerServerDTO.BatchOperationRequest request) {
        List<String> successIds = new java.util.ArrayList<>();
        List<String> errors = new java.util.ArrayList<>();

        for (String id : request.getIds()) {
            try {
                boolean success = false;
                switch (request.getOperation()) {
                    case "delete":
                        success = deleteServer(id);
                        break;
                    default:
                        errors.add("ID " + id + ": 不支持的操作类型: " + request.getOperation());
                        continue;
                }

                if (success) {
                    successIds.add(id);
                }
            } catch (Exception e) {
                errors.add("ID " + id + ": " + e.getMessage());
            }
        }

        Map<String, Object> result = new java.util.HashMap<>();
        result.put("successCount", successIds.size());
        result.put("successIds", successIds);
        result.put("errorCount", errors.size());
        result.put("errors", errors);

        return result;
    }

    /**
     * 获取服务器统计信息
     */
    public Map<String, Object> getServerStatistics() {
        return workerServerMapper.getServerStatistics();
    }

    /**
     * 获取在线服务器列表
     */
    public List<WorkerServerDTO> getOnlineServers() {
        List<WorkerServer> servers = workerServerMapper.selectOnlineServers();
        return servers.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 获取可用服务器列表
     */
    public List<WorkerServerDTO> getAvailableServers() {
        List<WorkerServer> servers = workerServerMapper.selectAvailableServers();
        return servers.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 更新心跳超时的服务器状态
     */
    @Transactional
    public int updateTimeoutServers(int timeoutMinutes) {
        LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(timeoutMinutes);
        int updatedCount = workerServerMapper.updateTimeoutServersToOffline(timeoutThreshold);
        
        if (updatedCount > 0) {
            log.info("更新心跳超时服务器状态: 超时阈值={}分钟, 更新数量={}", timeoutMinutes, updatedCount);
        }
        
        return updatedCount;
    }

    /**
     * 执行健康检查 - 已禁用主动健康检查功能
     * 注释原因：根据需求，移除主服务器主动发送心跳的功能
     */
    /*
    private WorkerServerDTO.HealthCheckResponse performHealthCheck(WorkerServer server) {
        WorkerServerDTO.HealthCheckResponse response = new WorkerServerDTO.HealthCheckResponse();
        response.setTimestamp(LocalDateTime.now());

        String newStatus = null;
        LocalDateTime heartbeatTime = LocalDateTime.now();

        try {
            long startTime = System.currentTimeMillis();

            // 调用健康检查接口
            String healthUrl = server.getHealthCheckUrl();
            ResponseEntity<String> result = restTemplate.getForEntity(healthUrl, String.class);

            long responseTime = System.currentTimeMillis() - startTime;
            response.setResponseTime(responseTime);

            if (result.getStatusCode().is2xxSuccessful()) {
                response.setSuccess(true);
                response.setStatus("ONLINE");
                response.setMessage("健康检查通过");
                response.setData(result.getBody());
                newStatus = "ONLINE";

                log.info("副服务器健康检查成功: workerId={}, responseTime={}ms",
                        server.getWorkerId(), responseTime);
            } else {
                response.setSuccess(false);
                response.setStatus("ERROR");
                response.setMessage("健康检查失败: HTTP " + result.getStatusCodeValue());
                newStatus = "ERROR";

                log.warn("副服务器健康检查失败: workerId={}, status={}",
                        server.getWorkerId(), result.getStatusCodeValue());
            }

        } catch (Exception e) {
            response.setSuccess(false);
            response.setStatus("OFFLINE");
            response.setMessage("连接失败: " + e.getMessage());
            response.setResponseTime(5000L); // 超时时间
            newStatus = "OFFLINE";

            log.warn("副服务器连接失败: workerId={}, error={}",
                    server.getWorkerId(), e.getMessage());
        }

        // 更新数据库中的服务器状态和心跳时间
        if (newStatus != null) {
            try {
                int updatedRows = workerServerMapper.updateStatus(server.getWorkerId(), newStatus, heartbeatTime);
                if (updatedRows > 0) {
                    log.debug("副服务器状态更新成功: workerId={}, status={}",
                            server.getWorkerId(), newStatus);
                } else {
                    log.warn("副服务器状态更新失败，未找到记录: workerId={}", server.getWorkerId());
                }
            } catch (Exception e) {
                log.error("更新副服务器状态失败: workerId={}, status={}",
                        server.getWorkerId(), newStatus, e);
            }
        }

        return response;
    }
    */

    /**
     * 更新Worker心跳（向后兼容版本）
     */
    @Transactional
    public void updateHeartbeat(String workerId, Map<String, Object> heartbeatData) {
        updateHeartbeat(workerId, heartbeatData, null);
    }

    /**
     * 更新Worker心跳 - 修改为显示所有分配的任务数量（支持真实IP）
     */
    @Transactional
    public void updateHeartbeat(String workerId, Map<String, Object> heartbeatData, String clientIp) {
        WorkerServer server = getById(workerId);
        if (server == null) {
            // 如果服务器不存在，尝试自动注册，传递真实IP
            log.info("收到未注册Worker的心跳，尝试自动注册: workerId={}, clientIp={}", workerId, clientIp);
            autoRegisterWorker(workerId, heartbeatData, clientIp);
            return;
        }

        // 更新心跳时间和状态
        String status = (String) heartbeatData.get("status");
        Integer workerReportedLoad = (Integer) heartbeatData.get("currentLoad");

        // 从数据库统计实际分配给该Worker的所有未完成任务数量
        int actualAssignedTasks = getActualAssignedTasksCount(workerId);

        // 优先使用数据库统计的准确数据，Worker报告的负载仅作为参考
        // 数据库统计是权威数据源，确保显示的活跃任务数准确
        int finalLoad = actualAssignedTasks;

        log.debug("负载计算详情: workerId={}, Worker报告负载={}, 数据库未完成任务={}, 最终负载={}",
                workerId, workerReportedLoad, actualAssignedTasks, finalLoad);

        // 配置化的差异阈值，用于监控Worker报告与实际数据的一致性
        if (workerReportedLoad != null && Math.abs(workerReportedLoad - actualAssignedTasks) > loadDifferenceThreshold) {
            log.warn("Worker报告负载与数据库统计差异超过阈值: workerId={}, 报告={}, 实际={}, 差异={}, 阈值={}",
                    workerId, workerReportedLoad, actualAssignedTasks,
                    Math.abs(workerReportedLoad - actualAssignedTasks), loadDifferenceThreshold);
        }

        server.setLastHeartbeat(LocalDateTime.now());
        if (status != null) {
            server.setStatus(status);
        }
        server.setCurrentLoad(finalLoad); // 使用计算后的负载

        updateById(server);

        // 只在负载发生变化或状态变化时记录INFO日志，减少日志量
        Integer currentLoad = server.getCurrentLoad();
        boolean loadChanged = currentLoad == null || !currentLoad.equals(finalLoad);
        boolean statusChanged = status != null && !status.equals(server.getStatus());

        if (loadChanged || statusChanged) {
            log.info("Worker心跳更新: workerId={}, status={}, 负载变化: {} -> {} (数据库统计:{})",
                    workerId, status, currentLoad, finalLoad, actualAssignedTasks);
        } else {
            log.debug("Worker心跳更新: workerId={}, status={}, 负载={}",
                    workerId, status, finalLoad);
        }
    }

    /**
     * 注销Worker
     */
    @Transactional
    public void unregisterWorker(String workerId) {
        WorkerServer server = getById(workerId);
        if (server != null) {
            server.setStatus("OFFLINE");
            server.setCurrentLoad(0);
            server.setLastHeartbeat(LocalDateTime.now());
            updateById(server);
            log.info("Worker注销成功: workerId={}", workerId);
        } else {
            log.warn("尝试注销不存在的Worker: workerId={}", workerId);
        }
    }

    /**
     * 获取实际分配给Worker的任务数量（带缓存优化）
     */
    private int getActualAssignedTasksCount(String workerId) {
        try {
            // 检查缓存是否有效
            Long lastCacheTime = cacheTimestamp.get(workerId);
            long currentTime = System.currentTimeMillis();

            if (lastCacheTime != null && (currentTime - lastCacheTime) < cacheExpireTime) {
                Integer cachedCount = taskCountCache.get(workerId);
                if (cachedCount != null) {
                    log.debug("Worker {} 使用缓存的任务数量: {}", workerId, cachedCount);
                    return cachedCount;
                }
            }

            // 缓存过期或不存在，查询数据库
            QueryWrapper<Reservation> wrapper = new QueryWrapper<>();
            wrapper.eq("worker_id", workerId)
                   .isNull("actual_execution_time");  // 只统计未完成执行的任务

            int taskCount = Math.toIntExact(reservationMapper.selectCount(wrapper));

            // 更新缓存
            taskCountCache.put(workerId, taskCount);
            cacheTimestamp.put(workerId, currentTime);

            log.debug("Worker {} 查询数据库获取任务数量: {} (已缓存)", workerId, taskCount);
            return taskCount;
        } catch (Exception e) {
            log.error("统计Worker分配任务数量失败: workerId={}", workerId, e);
            return 0;
        }
    }

    /**
     * 清除指定Worker的任务数量缓存
     */
    public void clearTaskCountCache(String workerId) {
        taskCountCache.remove(workerId);
        cacheTimestamp.remove(workerId);
        log.debug("清除Worker {} 的任务数量缓存", workerId);
    }

    /**
     * 自动注册Worker（向后兼容版本）
     */
    @Transactional
    public void autoRegisterWorker(String workerId, Map<String, Object> heartbeatData) {
        autoRegisterWorker(workerId, heartbeatData, null);
    }

    /**
     * 自动注册Worker（支持真实IP）
     */
    @Transactional
    public void autoRegisterWorker(String workerId, Map<String, Object> heartbeatData, String clientIp) {
        try {
            // IP地址优先级逻辑：真实IP > Worker报告的host > localhost
            String finalHost = clientIp;
            String reportedHost = (String) heartbeatData.getOrDefault("host", "localhost");

            if (finalHost == null || finalHost.trim().isEmpty()) {
                finalHost = reportedHost;
                log.debug("使用Worker报告的host: {}", reportedHost);
            } else {
                log.info("使用真实客户端IP: {} (Worker报告: {})", finalHost, reportedHost);
            }

            // 从注册数据中获取port信息
            Object portObj = heartbeatData.get("port");
            int port = 8082; // 默认端口

            if (portObj != null) {
                if (portObj instanceof Integer) {
                    port = (Integer) portObj;
                } else if (portObj instanceof String) {
                    try {
                        port = Integer.parseInt((String) portObj);
                    } catch (NumberFormatException e) {
                        log.warn("无法解析端口号: {}, 使用默认端口8082", portObj);
                    }
                }
            }

            // 构建正确的serverUrl，使用最终确定的IP地址
            String serverUrl = "http://" + finalHost + ":" + port;

            WorkerServer server = new WorkerServer();
            server.setId(workerId);
            server.setName("自动注册-" + workerId);
            server.setServerUrl(serverUrl);
            server.setStatus("ONLINE");
            server.setCurrentLoad(0);

            // 从注册数据中获取maxConcurrentTasks，如果有的话
            Object maxTasksObj = heartbeatData.get("maxConcurrentTasks");
            int maxTasks = 10; // 默认值
            if (maxTasksObj != null) {
                if (maxTasksObj instanceof Integer) {
                    maxTasks = (Integer) maxTasksObj;
                } else if (maxTasksObj instanceof String) {
                    try {
                        maxTasks = Integer.parseInt((String) maxTasksObj);
                    } catch (NumberFormatException e) {
                        log.warn("无法解析最大并发任务数: {}, 使用默认值10", maxTasksObj);
                    }
                }
            }

            server.setMaxConcurrentTasks(maxTasks);
            server.setTotalTasksCompleted(0L);
            server.setTotalTasksFailed(0L);
            server.setEnabled(true);
            server.setLastHeartbeat(LocalDateTime.now());
            server.setDescription("通过心跳自动注册的Worker");

            save(server);

            // 详细的注册成功日志，包含IP来源信息
            String ipSource = (clientIp != null && !clientIp.trim().isEmpty()) ?
                "真实IP" : "Worker报告";
            log.info("Worker自动注册成功: workerId={}, serverUrl={}, maxTasks={}, IP来源={}",
                    workerId, serverUrl, maxTasks, ipSource);

            if (clientIp != null && !clientIp.equals(reportedHost)) {
                log.info("IP地址修正: workerId={}, Worker报告={}, 实际使用={}",
                        workerId, reportedHost, finalHost);
            }
        } catch (Exception e) {
            log.error("Worker自动注册失败: workerId={}", workerId, e);
            throw new RuntimeException("Worker自动注册失败: " + e.getMessage());
        }
    }

    /**
     * 转换为DTO
     */
    private WorkerServerDTO convertToDTO(WorkerServer server) {
        WorkerServerDTO dto = new WorkerServerDTO();

        // 只映射 DTO 中实际存在的字段
        dto.setId(server.getId());
        dto.setWorkerId(server.getId()); // 使用 id 作为 workerId
        dto.setName(server.getName());

        // 从 serverUrl 解析 host 和 port
        if (server.getServerUrl() != null) {
            try {
                String url = server.getServerUrl();
                if (url.startsWith("http://")) {
                    url = url.substring(7); // 移除 "http://"
                }
                String[] parts = url.split(":");
                if (parts.length == 2) {
                    dto.setHost(parts[0]);
                    dto.setPort(Integer.parseInt(parts[1]));
                }
            } catch (Exception e) {
                log.warn("解析服务器URL失败: {}", server.getServerUrl(), e);
            }
        }

        dto.setMaxConcurrentTasks(server.getMaxConcurrentTasks()); // 确保这个字段被正确设置
        dto.setStatus(server.getStatus());
        dto.setCurrentLoad(server.getCurrentLoad());
        dto.setTotalTasksCompleted(server.getTotalTasksCompleted());
        dto.setTotalTasksFailed(server.getTotalTasksFailed());
        dto.setLastHeartbeat(server.getLastHeartbeat());
        dto.setCreatedTime(server.getCreatedTime());
        dto.setUpdatedTime(server.getUpdatedTime());

        log.debug("转换DTO: workerId={}, maxConcurrentTasks={}, currentLoad={}",
                server.getId(), server.getMaxConcurrentTasks(), server.getCurrentLoad());

        return dto;
    }
}
