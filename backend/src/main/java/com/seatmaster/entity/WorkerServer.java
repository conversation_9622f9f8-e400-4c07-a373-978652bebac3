package com.seatmaster.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 副服务器实体类
 * 对应数据库表 worker_servers
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("worker_servers")
public class WorkerServer {

    /**
     * 主键ID（字符串类型）
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 副服务器名称
     */
    @TableField("name")
    private String name;

    /**
     * 服务器URL
     */
    @TableField("server_url")
    private String serverUrl;

    /**
     * 优先级
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 支持的操作（JSON格式）
     */
    @TableField("supported_operations")
    private String supportedOperations;

    /**
     * 服务器状态: ONLINE/OFFLINE/BUSY/MAINTENANCE
     */
    @TableField("status")
    private String status;

    /**
     * 当前负载(正在处理的任务数)
     */
    @TableField("current_load")
    private Integer currentLoad;

    /**
     * 最大并发任务数
     */
    @TableField("max_concurrent_tasks")
    private Integer maxConcurrentTasks;

    /**
     * 总完成任务数
     */
    @TableField("total_tasks_completed")
    private Long totalTasksCompleted;

    /**
     * 总失败任务数
     */
    @TableField("total_tasks_failed")
    private Long totalTasksFailed;

    /**
     * 平均执行时间
     */
    @TableField("average_execution_time")
    private Double averageExecutionTime;

    /**
     * 是否启用
     */
    @TableField("enabled")
    private Boolean enabled;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 标签（JSON格式）
     */
    @TableField("tags")
    private String tags;

    /**
     * 最后心跳时间
     */
    @TableField("last_heartbeat")
    private LocalDateTime lastHeartbeat;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    // 虚拟字段（用于兼容）
    @TableField(exist = false)
    private String workerId;

    @TableField(exist = false)
    private String host;

    @TableField(exist = false)
    private Integer port;

    // 业务方法

    /**
     * 获取workerId（兼容方法）
     */
    public String getWorkerId() {
        if (workerId == null) {
            return this.id;
        }
        return workerId;
    }

    /**
     * 设置workerId（兼容方法）
     */
    public void setWorkerId(String workerId) {
        this.workerId = workerId;
        this.id = workerId;
    }

    /**
     * 获取主机地址（从serverUrl解析）
     */
    public String getHost() {
        if (host != null) return host;
        if (serverUrl == null) return null;
        try {
            java.net.URL url = new java.net.URL(serverUrl);
            return url.getHost();
        } catch (Exception e) {
            return "localhost";
        }
    }

    /**
     * 获取端口（从serverUrl解析）
     */
    public Integer getPort() {
        if (port != null) return port;
        if (serverUrl == null) return null;
        try {
            java.net.URL url = new java.net.URL(serverUrl);
            return url.getPort() == -1 ? 80 : url.getPort();
        } catch (Exception e) {
            return 8080;
        }
    }

    /**
     * 设置主机和端口（构建serverUrl）
     */
    public void setHost(String host) {
        this.host = host;
        Integer currentPort = getPort();
        this.serverUrl = "http://" + host + ":" + (currentPort != null ? currentPort : 8080);
    }

    /**
     * 设置端口（更新serverUrl）
     */
    public void setPort(Integer port) {
        this.port = port;
        String currentHost = getHost();
        this.serverUrl = "http://" + (currentHost != null ? currentHost : "localhost") + ":" + port;
    }

    /**
     * 获取健康检查URL
     */
    public String getHealthCheckUrl() {
        return serverUrl + "/api/health";
    }

    /**
     * 获取状态查询URL
     */
    public String getStatusUrl() {
        return serverUrl + "/api/status";
    }

    /**
     * 获取任务管理URL
     */
    public String getTaskUrl() {
        return serverUrl + "/api/tasks";
    }

    /**
     * 计算成功率
     */
    public Double getSuccessRate() {
        if (totalTasksCompleted == null || totalTasksFailed == null) {
            return null;
        }
        
        long totalTasks = totalTasksCompleted + totalTasksFailed;
        if (totalTasks == 0) {
            return null;
        }
        
        return (double) totalTasksCompleted / totalTasks * 100;
    }

    /**
     * 获取负载率
     */
    public Double getLoadRate() {
        if (currentLoad == null || maxConcurrentTasks == null || maxConcurrentTasks == 0) {
            return 0.0;
        }
        
        return (double) currentLoad / maxConcurrentTasks * 100;
    }

    /**
     * 判断是否在线
     */
    public boolean isOnline() {
        return "ONLINE".equals(status);
    }

    /**
     * 判断是否可用（在线且负载未满）
     */
    public boolean isAvailable() {
        return isOnline() && (currentLoad == null || maxConcurrentTasks == null || 
                             currentLoad < maxConcurrentTasks);
    }

    /**
     * 判断心跳是否超时
     * @param timeoutMinutes 超时分钟数
     */
    public boolean isHeartbeatTimeout(int timeoutMinutes) {
        if (lastHeartbeat == null) {
            return true;
        }
        
        LocalDateTime timeoutThreshold = LocalDateTime.now().minusMinutes(timeoutMinutes);
        return lastHeartbeat.isBefore(timeoutThreshold);
    }

    /**
     * 获取显示状态（包含负载信息）
     */
    public String getDisplayStatus() {
        if (status == null) {
            return "UNKNOWN";
        }
        
        switch (status) {
            case "ONLINE":
                if (currentLoad != null && maxConcurrentTasks != null) {
                    return String.format("在线 (%d/%d)", currentLoad, maxConcurrentTasks);
                }
                return "在线";
            case "OFFLINE":
                return "离线";
            case "BUSY":
                return "繁忙";
            case "ERROR":
                return "错误";
            default:
                return status;
        }
    }

    /**
     * 服务器状态枚举
     */
    public enum Status {
        ONLINE("ONLINE", "在线"),
        OFFLINE("OFFLINE", "离线"),
        BUSY("BUSY", "繁忙"),
        ERROR("ERROR", "错误");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Status fromCode(String code) {
            for (Status status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return OFFLINE;
        }
    }
}
