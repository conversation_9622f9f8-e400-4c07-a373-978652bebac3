package com.seatmaster.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 兑换码实体类
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("redemption_codes")
public class RedemptionCode {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 兑换码（唯一）
     */
    private String code;
    
    /**
     * 增加的天数
     */
    private Integer daysToAdd;
    
    /**
     * 是否已使用
     */
    private Boolean isUsed;
    
    /**
     * 使用者用户ID
     */
    private Long usedByUserId;
    
    /**
     * 使用时间
     */
    private LocalDateTime usedTime;
    
    /**
     * 创建者管理员ID
     */
    private Long createdByAdminId;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    /**
     * 过期时间（NULL表示永不过期）
     */
    private LocalDateTime expireTime;
    
    /**
     * 兑换码描述
     */
    private String description;
    
    /**
     * 批次ID（用于批量生成）
     */
    private String batchId;
    
    /**
     * 检查兑换码是否已过期
     */
    public boolean isExpired() {
        if (expireTime == null) {
            return false; // 永不过期
        }
        return LocalDateTime.now().isAfter(expireTime);
    }
    
    /**
     * 检查兑换码是否可用
     */
    public boolean isAvailable() {
        return !isUsed && !isExpired();
    }
}
