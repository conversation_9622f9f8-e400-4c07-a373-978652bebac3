package com.seatmaster.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
@TableName("reservations")
public class Reservation {
    @TableId(type = IdType.AUTO)
    private Long id;

    private Long userId;

    private Long roomId;

    @TableField("seat_num")
    private String seatId; // 座位号，用户自选

    private LocalTime startTime; // 预约开始时间（仅时间，格式HH:MM:SS）

    private LocalTime endTime; // 预约结束时间（仅时间，格式HH:MM:SS）

    private LocalDateTime createdTime;

    // 预约开放时间设置，用户可选填写
    @TableField("reservation_open_time")
    private String reservationOpenTime;

    // 预约类型：SAME_DAY当天预约，ADVANCE_ONE_DAY提前一天预约
    @TableField("reservation_type")
    private String reservationType;

    // ==================== 分布式任务字段 ====================

    /**
     * 分配的副服务器ID
     */
    @TableField("worker_id")
    private String workerId;



    /**
     * 执行结果详情
     */
    @TableField("execution_result")
    private String executionResult;

    /**
     * 最后执行时间
     */
    @TableField("last_execution_time")
    private LocalDateTime lastExecutionTime;

    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 开始执行时间
     */
    @TableField("started_time")
    private LocalDateTime startedTime;

    /**
     * 实际完成时间
     */
    @TableField("actual_execution_time")
    private LocalDateTime actualExecutionTime;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;


}