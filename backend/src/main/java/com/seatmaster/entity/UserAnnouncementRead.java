package com.seatmaster.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户公告阅读记录实体类
 * 
 * <AUTHOR>
 * @since 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_announcement_reads")
public class UserAnnouncementRead {
    
    /**
     * 记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 公告ID
     */
    @TableField("announcement_id")
    private Long announcementId;
    
    /**
     * 阅读时间
     */
    @TableField(value = "read_time", fill = FieldFill.INSERT)
    private LocalDateTime readTime;
    
    /**
     * 是否已关闭弹窗
     */
    @TableField("is_dismissed")
    private Boolean dismissed;
}
