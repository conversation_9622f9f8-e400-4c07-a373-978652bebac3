package com.seatmaster.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@TableName("schools")
public class School {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String name;

    private BigDecimal waitTime;

    private LocalDateTime createdTime;
}