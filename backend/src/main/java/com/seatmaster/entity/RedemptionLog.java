package com.seatmaster.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 兑换记录实体类
 * 用于记录用户兑换历史和审计
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("redemption_logs")
public class RedemptionLog {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 兑换码
     */
    private String code;
    
    /**
     * 增加的天数
     */
    private Integer daysAdded;
    
    /**
     * 兑换前剩余天数
     */
    private Integer daysBefore;
    
    /**
     * 兑换后剩余天数
     */
    private Integer daysAfter;
    
    /**
     * 兑换时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime redemptionTime;
    
    /**
     * 用户IP地址
     */
    private String userIp;
    
    /**
     * 用户代理信息
     */
    private String userAgent;
}
