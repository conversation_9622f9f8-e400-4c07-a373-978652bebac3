package com.seatmaster.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("users")
public class User {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private String username;
    
    private String password;
    
    private String name;
    
    private Role role = Role.USER;
    
    // 剩余可预约天数，默认1天
    private Integer remainingDays = 1;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    public enum Role {
        USER, ADMIN
    }
} 