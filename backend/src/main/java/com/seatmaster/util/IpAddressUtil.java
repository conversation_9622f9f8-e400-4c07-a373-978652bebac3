package com.seatmaster.util;

import com.seatmaster.config.IpDetectionProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.regex.Pattern;

/**
 * IP地址获取工具类
 * 用于从HTTP请求中获取真实的客户端IP地址
 * 处理各种代理和负载均衡器的头部信息
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IpAddressUtil {

    private final IpDetectionProperties ipDetectionProperties;
    
    // IP地址格式验证正则表达式
    private static final Pattern IP_PATTERN = Pattern.compile(
        "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
    );
    
    // 代理头部信息，按优先级排序
    private static final String[] PROXY_HEADERS = {
        "X-Forwarded-For",
        "X-Real-IP", 
        "Proxy-Client-IP",
        "WL-Proxy-Client-IP",
        "X-Original-Forwarded-For",
        "HTTP_X_FORWARDED_FOR",
        "HTTP_X_FORWARDED",
        "HTTP_X_CLUSTER_CLIENT_IP",
        "HTTP_CLIENT_IP",
        "HTTP_FORWARDED_FOR",
        "HTTP_FORWARDED"
    };
    
    /**
     * 获取HTTP请求的真实客户端IP地址
     * 
     * @param request HTTP请求对象
     * @return 真实的客户端IP地址，如果无法获取则返回null
     */
    public String getRealClientIp(HttpServletRequest request) {
        if (request == null) {
            log.warn("HttpServletRequest为null，无法获取客户端IP");
            return null;
        }

        // 检查IP检测功能是否启用
        if (!ipDetectionProperties.isEnabled()) {
            if (ipDetectionProperties.shouldLog("DEBUG")) {
                log.debug("IP自动检测功能已禁用，直接使用RemoteAddr");
            }
            return cleanIpAddress(request.getRemoteAddr());
        }

        if (ipDetectionProperties.shouldLog("DEBUG")) {
            log.debug("开始获取客户端真实IP地址，配置: {}", ipDetectionProperties.getConfigSummary());
        }

        // 1. 按优先级检查代理头部信息（如果启用了代理头部信任）
        if (ipDetectionProperties.isTrustProxyHeaders()) {
            for (String header : PROXY_HEADERS) {
                String ip = getIpFromHeader(request, header);
                if (ip != null) {
                    if (ipDetectionProperties.shouldLog("INFO")) {
                        log.info("从头部 {} 获取到真实IP: {}", header, ip);
                    }
                    return ip;
                }
            }
        } else {
            if (ipDetectionProperties.shouldLog("DEBUG")) {
                log.debug("代理头部信任已禁用，跳过头部检查");
            }
        }
        
        // 2. 最后使用request.getRemoteAddr()作为fallback
        String remoteAddr = request.getRemoteAddr();
        String finalIp = cleanIpAddress(remoteAddr);
        
        if (isValidIp(finalIp) && !isInternalIp(finalIp)) {
            log.info("使用RemoteAddr获取到真实IP: {}", finalIp);
            return finalIp;
        } else if (isValidIp(finalIp)) {
            log.debug("RemoteAddr为内网IP: {}，仍然返回", finalIp);
            return finalIp;
        }
        
        log.warn("无法获取有效的客户端IP地址，RemoteAddr: {}", remoteAddr);
        return finalIp; // 即使无效也返回，让调用方决定如何处理
    }
    
    /**
     * 从指定的HTTP头部获取IP地址
     * 
     * @param request HTTP请求对象
     * @param headerName 头部名称
     * @return 有效的IP地址，如果没有则返回null
     */
    private String getIpFromHeader(HttpServletRequest request, String headerName) {
        String headerValue = request.getHeader(headerName);
        
        if (headerValue == null || headerValue.trim().isEmpty() || "unknown".equalsIgnoreCase(headerValue)) {
            return null;
        }
        
        log.debug("检查头部 {}: {}", headerName, headerValue);
        
        // 处理X-Forwarded-For可能包含多个IP的情况（逗号分隔）
        if (headerValue.contains(",")) {
            String[] ips = headerValue.split(",");
            for (String ip : ips) {
                String cleanIp = cleanIpAddress(ip);
                if (isValidIp(cleanIp) && !isInternalIp(cleanIp)) {
                    log.debug("从多IP头部中选择外网IP: {}", cleanIp);
                    return cleanIp;
                }
            }
            
            // 如果没有外网IP，返回第一个有效的内网IP
            for (String ip : ips) {
                String cleanIp = cleanIpAddress(ip);
                if (isValidIp(cleanIp)) {
                    log.debug("从多IP头部中选择内网IP: {}", cleanIp);
                    return cleanIp;
                }
            }
        } else {
            String cleanIp = cleanIpAddress(headerValue);
            if (isValidIp(cleanIp)) {
                return cleanIp;
            }
        }
        
        return null;
    }
    
    /**
     * 清理IP地址字符串
     * 去除空格、端口号等无关信息
     * 
     * @param ip 原始IP字符串
     * @return 清理后的IP地址
     */
    private String cleanIpAddress(String ip) {
        if (ip == null) {
            return null;
        }
        
        // 去除首尾空格
        ip = ip.trim();
        
        // 去除端口号（如果存在）
        if (ip.contains(":") && !ip.startsWith("[")) { // 排除IPv6地址
            ip = ip.substring(0, ip.indexOf(":"));
        }
        
        return ip;
    }
    
    /**
     * 验证IP地址格式是否有效
     * 
     * @param ip IP地址字符串
     * @return true表示格式有效
     */
    public boolean isValidIp(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            return false;
        }
        
        return IP_PATTERN.matcher(ip.trim()).matches();
    }
    
    /**
     * 判断是否为内网IP地址
     * 
     * @param ip IP地址字符串
     * @return true表示是内网IP
     */
    public boolean isInternalIp(String ip) {
        if (!isValidIp(ip)) {
            return false;
        }
        
        String[] parts = ip.split("\\.");
        int first = Integer.parseInt(parts[0]);
        int second = Integer.parseInt(parts[1]);
        
        // 127.x.x.x - 本地回环地址
        if (first == 127) {
            return true;
        }
        
        // 10.x.x.x - A类私有地址
        if (first == 10) {
            return true;
        }
        
        // 172.16.x.x - 172.31.x.x - B类私有地址
        if (first == 172 && second >= 16 && second <= 31) {
            return true;
        }
        
        // 192.168.x.x - C类私有地址
        if (first == 192 && second == 168) {
            return true;
        }
        
        // 169.254.x.x - 链路本地地址
        if (first == 169 && second == 254) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 获取客户端IP的详细信息（用于调试）
     * 
     * @param request HTTP请求对象
     * @return IP获取的详细信息
     */
    public String getClientIpDebugInfo(HttpServletRequest request) {
        if (request == null) {
            return "HttpServletRequest为null";
        }
        
        StringBuilder info = new StringBuilder();
        info.append("客户端IP获取详情:\n");
        
        // 检查所有代理头部
        for (String header : PROXY_HEADERS) {
            String value = request.getHeader(header);
            info.append(String.format("  %s: %s\n", header, value != null ? value : "null"));
        }
        
        // RemoteAddr信息
        info.append(String.format("  RemoteAddr: %s\n", request.getRemoteAddr()));
        info.append(String.format("  RemoteHost: %s\n", request.getRemoteHost()));
        info.append(String.format("  RemotePort: %d\n", request.getRemotePort()));
        
        // 最终获取的IP
        String realIp = getRealClientIp(request);
        info.append(String.format("  最终IP: %s\n", realIp));
        
        return info.toString();
    }
}
