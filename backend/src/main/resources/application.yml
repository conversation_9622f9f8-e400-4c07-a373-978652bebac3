server:
  port: 8081
  address: 0.0.0.0  # 绑定到所有网络接口，允许外部访问

spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:3306}/${DB_NAME:seat_reservation}?useUnicode=true&characterEncoding=utf8&useSSL=${DB_SSL:false}&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:root5869087}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: ${DB_MAX_POOL_SIZE:30}      # 扩容到30个连接，支持高频查询（每5秒）
      minimum-idle: ${DB_MIN_IDLE:15}           # 最小空闲连接增加到15个
      connection-timeout: ${DB_CONNECTION_TIMEOUT:20000}
      idle-timeout: ${DB_IDLE_TIMEOUT:300000}
      max-lifetime: ${DB_MAX_LIFETIME:1200000}
      leak-detection-threshold: ${DB_LEAK_DETECTION:60000}

  # Jackson日期时间格式配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

  # MVC日期时间格式配置
  mvc:
    format:
      date-time: yyyy-MM-dd HH:mm:ss

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

jwt:
  secret: seatMasterSecretKeyForHS512AlgorithmMustBeAtLeast512BitsLongToMeetJWTSpecificationRequirements2023
  expiration: 604800000 # 7 days (7 * 24 * 60 * 60 * 1000)

logging:
  level:
    com.seatmaster: debug
# backend/src/main/java/com/seatmaster/config/UserDefaultsProperties.java
# SeatMaster 自定义配置
seatmaster:
  # 用户默认配置
  user-defaults:
    trial-days: 3                   # 新注册用户默认试用天数
  auto-assignment:
    enabled: true                    # 是否启用自动分配
    log-level: INFO                  # 日志级别：DEBUG, INFO, WARN, ERROR
    strategy: LOAD_BALANCED          # 分配策略：ROUND_ROBIN, LOAD_BALANCED, TIME_OPTIMIZED
    max-retry-count: 3               # 最大重试次数
    assignment-timeout: 30000        # 分配超时时间（毫秒）
  worker-monitoring:
    load-difference-threshold: 3     # Worker报告负载与数据库统计差异阈值
    task-count-cache-expire: 30000   # 任务数量缓存过期时间（毫秒）
    heartbeat-log-level: DEBUG       # 心跳日志级别：DEBUG, INFO
  async-task-reassignment:
    core-pool-size: 2                # 异步任务重新分配核心线程数
    max-pool-size: 5                 # 异步任务重新分配最大线程数
    queue-capacity: 100              # 异步任务重新分配队列容量
    keep-alive-seconds: 60           # 线程空闲时间（秒）
    status-cleanup-hours: 1          # 状态记录清理时间（小时）
  ip-detection:
    enabled: true                    # 是否启用自动IP检测
    trust-proxy-headers: true        # 是否信任代理头部信息
    fallback-to-reported-host: true  # 无法获取真实IP时是否使用Worker报告的host
    log-level: INFO                  # IP检测日志级别：DEBUG, INFO, WARN, ERROR