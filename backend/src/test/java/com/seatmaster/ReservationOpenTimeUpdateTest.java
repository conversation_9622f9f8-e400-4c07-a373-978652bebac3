package com.seatmaster;

import com.seatmaster.entity.Reservation;
import com.seatmaster.mapper.ReservationMapper;
import com.seatmaster.service.ReservationService;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalTime;

/**
 * 预约开放时间更新测试
 * 验证@TableField注解修复后的字段映射是否正确
 */
@SpringBootTest
@ActiveProfiles("test")
public class ReservationOpenTimeUpdateTest {
    
    private static final Logger logger = LoggerFactory.getLogger(ReservationOpenTimeUpdateTest.class);
    
    @Autowired
    private ReservationService reservationService;
    
    @Autowired
    private ReservationMapper reservationMapper;
    
    /**
     * 测试预约开放时间字段映射
     */
    @Test
    public void testReservationOpenTimeMapping() {
        logger.info("开始测试预约开放时间字段映射");
        
        try {
            // 创建测试预约
            Long testUserId = 1L;
            Long testRoomId = 1L;
            String testSeatId = "TEST001";
            LocalTime startTime = LocalTime.of(9, 0);
            LocalTime endTime = LocalTime.of(12, 0);
            String reservationOpenTime = "08:30:00";
            String reservationType = "SAME_DAY";
            
            logger.info("创建测试预约: 预约开放时间={}, 预约类型={}", reservationOpenTime, reservationType);
            
            Reservation reservation = reservationService.createOrUpdateReservation(
                testUserId, testRoomId, testSeatId, startTime, endTime, 
                reservationOpenTime, reservationType);
            
            if (reservation != null) {
                logger.info("预约创建成功: ID={}", reservation.getId());
                
                // 从数据库重新查询验证
                Reservation dbReservation = reservationMapper.selectById(reservation.getId());
                if (dbReservation != null) {
                    logger.info("数据库查询结果: 预约开放时间={}, 预约类型={}", 
                               dbReservation.getReservationOpenTime(), dbReservation.getReservationType());
                    
                    // 验证字段是否正确保存
                    if (reservationOpenTime.equals(dbReservation.getReservationOpenTime()) &&
                        reservationType.equals(dbReservation.getReservationType())) {
                        logger.info("✅ 字段映射测试通过！");
                    } else {
                        logger.error("❌ 字段映射测试失败！期望: 开放时间={}, 类型={}; 实际: 开放时间={}, 类型={}", 
                                   reservationOpenTime, reservationType,
                                   dbReservation.getReservationOpenTime(), dbReservation.getReservationType());
                    }
                } else {
                    logger.error("❌ 无法从数据库查询到预约记录");
                }
                
                // 测试更新操作
                String newOpenTime = "09:15:00";
                String newType = "ADVANCE_ONE_DAY";
                
                logger.info("测试更新操作: 新开放时间={}, 新类型={}", newOpenTime, newType);
                
                Reservation updatedReservation = reservationService.createOrUpdateReservation(
                    testUserId, testRoomId, testSeatId, startTime, endTime, 
                    newOpenTime, newType);
                
                if (updatedReservation != null) {
                    // 再次从数据库查询验证更新结果
                    Reservation updatedDbReservation = reservationMapper.selectById(updatedReservation.getId());
                    if (updatedDbReservation != null) {
                        logger.info("更新后数据库查询结果: 预约开放时间={}, 预约类型={}", 
                                   updatedDbReservation.getReservationOpenTime(), updatedDbReservation.getReservationType());
                        
                        if (newOpenTime.equals(updatedDbReservation.getReservationOpenTime()) &&
                            newType.equals(updatedDbReservation.getReservationType())) {
                            logger.info("✅ 更新操作测试通过！");
                        } else {
                            logger.error("❌ 更新操作测试失败！期望: 开放时间={}, 类型={}; 实际: 开放时间={}, 类型={}", 
                                       newOpenTime, newType,
                                       updatedDbReservation.getReservationOpenTime(), updatedDbReservation.getReservationType());
                        }
                    }
                }
                
            } else {
                logger.error("❌ 预约创建失败");
            }
            
        } catch (Exception e) {
            logger.error("❌ 测试过程中发生异常", e);
        }
        
        logger.info("预约开放时间字段映射测试完成");
    }
}
