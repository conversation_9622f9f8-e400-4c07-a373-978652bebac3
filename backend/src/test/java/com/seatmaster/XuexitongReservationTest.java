package com.seatmaster;

import com.seatmaster.service.ReservationData;
import com.seatmaster.service.ReservationDataService;
import com.seatmaster.service.XuexitongApiService;
import com.seatmaster.service.XuexitongResponse;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalTime;

/**
 * 学习通预约功能测试
 */
@SpringBootTest
public class XuexitongReservationTest {
    
    private static final Logger logger = LoggerFactory.getLogger(XuexitongReservationTest.class);
    
    @Autowired
    private ReservationDataService reservationDataService;
    
    @Autowired
    private XuexitongApiService xuexitongApiService;
    
    /**
     * 测试第一条预约记录的学习通预约功能
     */
    @Test
    public void testFirstReservationRecord() {
        logger.info("=== 开始测试学习通预约功能 ===");
        
        try {
            // 测试数据 - 来自数据库第一条记录
            Long reservationId = 83L;
            
            logger.info("测试预约ID: {}", reservationId);
            
            // 步骤1: 从数据库构建预约数据
            logger.info("步骤1: 从数据库获取预约数据...");
            ReservationData reservationData = reservationDataService.buildReservationData(reservationId);
            
            if (reservationData == null) {
                logger.error("❌ 无法获取预约数据，测试失败");
                return;
            }
            
            logger.info("✅ 预约数据获取成功:");
            logger.info("   用户: {}", reservationData.getUsername());
            logger.info("   房间: {}", reservationData.getRoomNum());
            logger.info("   座位: {}", reservationData.getSeatId());
            logger.info("   时间: {}", reservationData.getTimeSlot());
            logger.info("   日期: {}", reservationData.getReservationDate());
            logger.info("   类型: {}", reservationData.getReservationTypeText());
            
            // 步骤2: 验证数据完整性
            logger.info("步骤2: 验证数据完整性...");
            ReservationDataService.ValidationResult validation = 
                reservationDataService.validateReservationData(reservationData);
            
            if (!validation.isValid()) {
                logger.error("❌ 数据验证失败: {}", validation.getMessage());
                return;
            }
            
            logger.info("✅ 数据验证通过: {}", validation.getMessage());
            
            // 步骤3: 执行学习通预约
            logger.info("步骤3: 执行学习通预约...");
            long startTime = System.currentTimeMillis();
            
            XuexitongResponse response = xuexitongApiService.executeReservation(reservationData);
            
            long executionTime = System.currentTimeMillis() - startTime;
            logger.info("预约执行耗时: {}ms", executionTime);
            
            // 步骤4: 分析结果
            logger.info("步骤4: 分析预约结果...");
            logger.info("预约结果: {}", response.getSummary());
            
            if (response.isSuccess()) {
                logger.info("🎉 学习通预约成功!");
                logger.info("成功消息: {}", response.getMessage());
                
                if (response.getData() != null) {
                    logger.info("返回数据:");
                    response.getData().forEach((key, value) -> 
                        logger.info("   {}: {}", key, value));
                }
            } else {
                logger.warn("⚠️ 学习通预约失败");
                logger.warn("失败原因: {}", response.getMessage());
                
                if (response.getErrorCode() != null) {
                    logger.warn("错误代码: {}", response.getErrorCode());
                }
                
                // 分析失败类型
                if (response.isNetworkError()) {
                    logger.warn("失败类型: 网络错误");
                } else if (response.isAuthError()) {
                    logger.warn("失败类型: 认证错误");
                } else if (response.isSeatUnavailableError()) {
                    logger.warn("失败类型: 座位不可用");
                } else {
                    logger.warn("失败类型: 其他错误");
                }
            }
            
        } catch (Exception e) {
            logger.error("❌ 测试过程中发生异常", e);
        }
        
        logger.info("=== 学习通预约功能测试完成 ===");
    }
    
    /**
     * 测试手动构建的预约数据
     */
    @Test
    public void testManualReservationData() {
        logger.info("=== 开始测试手动构建的预约数据 ===");
        
        try {
            // 手动构建测试数据
            ReservationData testData = ReservationData.Builder.fromDatabase(
                "18755869972",           // 用户名
                "tcc123698741",          // 密码
                20L,                     // 用户ID
                83L,                     // 预约ID
                "4991",                  // 房间号
                "2",                     // 座位号
                LocalTime.of(15, 0),     // 开始时间
                LocalTime.of(19, 0),     // 结束时间
                "13:53:00",              // 预约开放时间
                "SAME_DAY"               // 预约类型
            );
            
            logger.info("手动构建的预约数据: {}", testData.getSummary());
            
            // 验证数据
            if (!testData.isValid()) {
                logger.error("❌ 手动构建的数据无效");
                return;
            }
            
            logger.info("✅ 手动构建的数据有效");
            
            // 执行预约
            logger.info("执行学习通预约...");
            XuexitongResponse response = xuexitongApiService.executeReservation(testData);
            
            logger.info("预约结果: {}", response.getSummary());
            
        } catch (Exception e) {
            logger.error("❌ 手动测试过程中发生异常", e);
        }
        
        logger.info("=== 手动构建预约数据测试完成 ===");
    }
    
    /**
     * 测试数据库查询功能
     */
    @Test
    public void testDatabaseQuery() {
        logger.info("=== 开始测试数据库查询功能 ===");
        
        try {
            Long reservationId = 83L;
            
            // 测试构建预约数据
            ReservationData data = reservationDataService.buildReservationData(reservationId);
            
            if (data != null) {
                logger.info("✅ 数据库查询成功");
                logger.info("查询结果: {}", data.getSummary());
                
                // 测试验证功能
                ReservationDataService.ValidationResult validation = 
                    reservationDataService.validateReservationData(data);
                
                logger.info("验证结果: {} - {}", validation.isValid(), validation.getMessage());
            } else {
                logger.error("❌ 数据库查询失败");
            }
            
        } catch (Exception e) {
            logger.error("❌ 数据库查询测试异常", e);
        }
        
        logger.info("=== 数据库查询功能测试完成 ===");
    }
}
