#!/bin/bash
set -e

# SeatMaster Backend Docker 启动脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [ "$DEBUG" = "true" ]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# 等待数据库连接
wait_for_database() {
    local host=${DB_HOST:-localhost}
    local port=${DB_PORT:-3306}
    local timeout=${DB_WAIT_TIMEOUT:-60}
    
    log_info "等待数据库连接: $host:$port"
    
    local count=0
    while ! nc -z "$host" "$port"; do
        if [ $count -ge $timeout ]; then
            log_error "数据库连接超时: $host:$port"
            exit 1
        fi
        log_info "等待数据库启动... ($count/$timeout)"
        sleep 1
        count=$((count + 1))
    done
    
    log_info "数据库连接成功: $host:$port"
}

# 检查必要的环境变量
check_environment() {
    log_info "检查环境变量..."
    
    # 必需的环境变量
    local required_vars=(
        "DB_HOST"
        "DB_PORT" 
        "DB_NAME"
        "DB_USERNAME"
        "DB_PASSWORD"
    )
    
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        log_error "缺少必要的环境变量: ${missing_vars[*]}"
        log_error "请设置以下环境变量:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        exit 1
    fi
    
    log_info "环境变量检查通过"
}

# 设置默认环境变量
set_defaults() {
    export SPRING_PROFILES_ACTIVE=${SPRING_PROFILES_ACTIVE:-prod}
    export SERVER_PORT=${SERVER_PORT:-8081}
    export LOG_PATH=${LOG_PATH:-/app/logs}
    export JAVA_OPTS=${JAVA_OPTS:-"-Xms512m -Xmx1g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"}
    
    # 数据库配置
    export DB_HOST=${DB_HOST:-localhost}
    export DB_PORT=${DB_PORT:-3306}
    export DB_NAME=${DB_NAME:-seat_reservation}
    export DB_SSL=${DB_SSL:-false}
    
    # 连接池配置
    export DB_MAX_POOL_SIZE=${DB_MAX_POOL_SIZE:-50}
    export DB_MIN_IDLE=${DB_MIN_IDLE:-20}
    export DB_CONNECTION_TIMEOUT=${DB_CONNECTION_TIMEOUT:-30000}
    
    # JWT配置
    export JWT_SECRET=${JWT_SECRET:-seatmaster-production-secret-key-2024}
    export JWT_EXPIRATION=${JWT_EXPIRATION:-86400000}
    
    log_info "默认环境变量设置完成"
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p "$LOG_PATH"
    mkdir -p /app/uploads
    mkdir -p /app/temp
    
    log_info "目录创建完成"
}

# 显示配置信息
show_config() {
    log_info "=== SeatMaster Backend 配置信息 ==="
    echo "Spring Profile: $SPRING_PROFILES_ACTIVE"
    echo "Server Port: $SERVER_PORT"
    echo "Database: $DB_HOST:$DB_PORT/$DB_NAME"
    echo "Database User: $DB_USERNAME"
    echo "Log Path: $LOG_PATH"
    echo "Java Options: $JAVA_OPTS"
    echo "=================================="
}

# 主函数
main() {
    log_info "启动 SeatMaster Backend..."
    
    # 设置默认值
    set_defaults
    
    # 检查环境变量
    check_environment
    
    # 创建目录
    create_directories
    
    # 等待数据库
    if [ "$SKIP_DB_WAIT" != "true" ]; then
        wait_for_database
    fi
    
    # 显示配置
    show_config
    
    # 构建Java启动命令
    local java_cmd="java"
    
    # 添加JVM参数
    java_cmd="$java_cmd $JAVA_OPTS"
    
    # 添加系统属性
    java_cmd="$java_cmd -Djava.awt.headless=true"
    java_cmd="$java_cmd -Djava.security.egd=file:/dev/./urandom"
    java_cmd="$java_cmd -Dspring.profiles.active=$SPRING_PROFILES_ACTIVE"
    java_cmd="$java_cmd -Dserver.port=$SERVER_PORT"
    java_cmd="$java_cmd -Dlogging.file.path=$LOG_PATH"
    
    # 数据库配置
    java_cmd="$java_cmd -Dspring.datasource.url=*******************************************************************************************************************************************************"
    java_cmd="$java_cmd -Dspring.datasource.username=$DB_USERNAME"
    java_cmd="$java_cmd -Dspring.datasource.password=$DB_PASSWORD"
    
    # 连接池配置
    java_cmd="$java_cmd -Dspring.datasource.hikari.maximum-pool-size=$DB_MAX_POOL_SIZE"
    java_cmd="$java_cmd -Dspring.datasource.hikari.minimum-idle=$DB_MIN_IDLE"
    java_cmd="$java_cmd -Dspring.datasource.hikari.connection-timeout=$DB_CONNECTION_TIMEOUT"
    
    # JWT配置
    java_cmd="$java_cmd -Djwt.secret=$JWT_SECRET"
    java_cmd="$java_cmd -Djwt.expiration=$JWT_EXPIRATION"
    
    # 添加JAR文件
    java_cmd="$java_cmd -jar /app/app.jar"
    
    log_info "启动命令: $java_cmd"
    log_info "正在启动应用..."
    
    # 执行启动命令
    exec $java_cmd
}

# 信号处理
trap 'log_info "收到停止信号，正在关闭应用..."; exit 0' SIGTERM SIGINT

# 执行主函数
main "$@"
