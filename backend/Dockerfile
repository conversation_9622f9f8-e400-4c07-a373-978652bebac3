# SeatMaster Backend Dockerfile
# 多阶段构建，优化镜像大小

# ================================
# 构建阶段
# ================================
FROM maven:3.8.6-openjdk-8-slim AS builder

# 设置工作目录
WORKDIR /app

# 复制pom.xml和源代码
COPY pom.xml .
COPY src ./src

# 下载依赖（利用Docker缓存）
RUN mvn dependency:go-offline -B

# 构建应用
RUN mvn clean package -DskipTests -B

# ================================
# 运行阶段
# ================================
FROM openjdk:8-jre-slim

# 安装必要工具
RUN apt-get update && apt-get install -y \
    curl \
    netcat \
    dumb-init \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 创建应用用户
RUN groupadd -r seatmaster && useradd -r -g seatmaster seatmaster

# 设置工作目录
WORKDIR /app

# 创建必要目录
RUN mkdir -p /app/logs /app/uploads /app/temp \
    && chown -R seatmaster:seatmaster /app

# 复制构建产物
COPY --from=builder /app/target/seat-reservation-backend-*.jar app.jar

# 复制启动脚本
COPY docker-entrypoint.sh /app/docker-entrypoint.sh
RUN chmod +x /app/docker-entrypoint.sh \
    && chown seatmaster:seatmaster /app/docker-entrypoint.sh

# 设置环境变量
ENV JAVA_OPTS="-Xms512m -Xmx1g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication" \
    SPRING_PROFILES_ACTIVE=prod \
    SERVER_PORT=8081 \
    TZ=Asia/Shanghai

# 暴露端口
EXPOSE 8081

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8081/actuator/health || exit 1

# 切换到应用用户
USER seatmaster

# 使用dumb-init作为PID 1
ENTRYPOINT ["/usr/bin/dumb-init", "--"]

# 启动应用
CMD ["/app/docker-entrypoint.sh"]
