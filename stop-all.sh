#!/bin/bash

# 座位预订系统停止脚本
# 作者：Augment Agent
# 版本：1.0.0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_ROOT=$(pwd)
PID_DIR="$PROJECT_ROOT/pids"
BACKEND_PID="$PID_DIR/backend.pid"
FRONTEND_PID="$PID_DIR/frontend.pid"

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] ${message}${NC}"
}

print_success() {
    print_message "$GREEN" "✅ $1"
}

print_error() {
    print_message "$RED" "❌ $1"
}

print_warning() {
    print_message "$YELLOW" "⚠️  $1"
}

print_info() {
    print_message "$BLUE" "ℹ️  $1"
}

print_step() {
    print_message "$BLUE" "🔄 $1"
}

echo ""
echo -e "${YELLOW}================================================${NC}"
echo -e "${YELLOW}    🛑 座位预订系统停止脚本 v1.0.0${NC}"
echo -e "${YELLOW}================================================${NC}"
echo ""

print_step "停止所有服务..."

# 停止后端
if [ -f "$BACKEND_PID" ]; then
    backend_pid=$(cat "$BACKEND_PID")
    if kill -0 "$backend_pid" 2>/dev/null; then
        print_step "停止后端服务 (PID: $backend_pid)"
        kill "$backend_pid" 2>/dev/null || true
        sleep 3
        # 强制杀死如果还在运行
        if kill -0 "$backend_pid" 2>/dev/null; then
            print_warning "强制停止后端服务"
            kill -9 "$backend_pid" 2>/dev/null || true
        fi
        print_success "后端服务已停止"
    else
        print_info "后端服务未运行"
    fi
    rm -f "$BACKEND_PID"
else
    print_info "未找到后端PID文件"
fi

# 停止前端
if [ -f "$FRONTEND_PID" ]; then
    frontend_pid=$(cat "$FRONTEND_PID")
    if kill -0 "$frontend_pid" 2>/dev/null; then
        print_step "停止前端服务 (PID: $frontend_pid)"
        kill "$frontend_pid" 2>/dev/null || true
        sleep 3
        # 强制杀死如果还在运行
        if kill -0 "$frontend_pid" 2>/dev/null; then
            print_warning "强制停止前端服务"
            kill -9 "$frontend_pid" 2>/dev/null || true
        fi
        print_success "前端服务已停止"
    else
        print_info "前端服务未运行"
    fi
    rm -f "$FRONTEND_PID"
else
    print_info "未找到前端PID文件"
fi

# 额外清理：杀死可能残留的进程
print_step "清理残留进程..."
pkill -f "SeatReservationApplication" 2>/dev/null && print_info "清理了残留的后端进程" || true
pkill -f "vue-cli-service serve" 2>/dev/null && print_info "清理了残留的前端进程" || true

# 检查端口是否已释放
sleep 2
if netstat -tlnp 2>/dev/null | grep -q ":8081 "; then
    print_warning "端口8081仍被占用"
else
    print_success "端口8081已释放"
fi

if netstat -tlnp 2>/dev/null | grep -q ":3000 "; then
    print_warning "端口3000仍被占用"
else
    print_success "端口3000已释放"
fi

echo ""
print_success "所有服务已停止"
echo ""
