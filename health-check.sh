#!/bin/bash

# 座位预订系统健康检查脚本
# 作者：Augment Agent
# 版本：1.0.0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
BACKEND_URL="http://localhost:8081/api/admin/worker-management/statistics"
FRONTEND_URL="http://localhost:3000"
BACKEND_SERVICE="seatmaster-backend"
FRONTEND_APP="seatmaster-frontend"
TIMEOUT=5
ALERT_EMAIL="<EMAIL>"
ALERT_THRESHOLD=3

# 日志文件
LOG_FILE="/root/seatMaster/logs/health-check.log"
ALERT_COUNT_FILE="/root/seatMaster/logs/alert-count.txt"

# 创建日志目录
mkdir -p "$(dirname "$LOG_FILE")"
touch "$ALERT_COUNT_FILE" 2>/dev/null || echo "0" > "$ALERT_COUNT_FILE"

# 打印函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log_success() {
    log "${GREEN}✅ $1${NC}"
}

log_error() {
    log "${RED}❌ $1${NC}"
}

log_warning() {
    log "${YELLOW}⚠️  $1${NC}"
}

log_info() {
    log "${BLUE}ℹ️  $1${NC}"
}

# 发送告警
send_alert() {
    local subject="[警告] 座位预订系统服务异常"
    local body="$1"
    
    # 增加告警计数
    local count=$(cat "$ALERT_COUNT_FILE" 2>/dev/null || echo "0")
    count=$((count + 1))
    echo "$count" > "$ALERT_COUNT_FILE"
    
    # 检查是否超过阈值
    if [ "$count" -ge "$ALERT_THRESHOLD" ]; then
        log_warning "发送告警邮件: $subject"
        echo "$body" | mail -s "$subject" "$ALERT_EMAIL" || true
        echo "0" > "$ALERT_COUNT_FILE"  # 重置计数
    else
        log_info "告警计数: $count/$ALERT_THRESHOLD"
    fi
}

# 重启服务
restart_service() {
    local service=$1
    log_warning "尝试重启服务: $service"
    
    case $service in
        "backend")
            sudo systemctl restart $BACKEND_SERVICE
            ;;
        "frontend")
            pm2 restart $FRONTEND_APP
            ;;
        *)
            log_error "未知服务: $service"
            return 1
            ;;
    esac
    
    # 等待服务启动
    sleep 10
    return 0
}

# 检查后端健康
check_backend() {
    log_info "检查后端服务..."
    
    # 检查systemd服务状态
    if ! systemctl is-active --quiet $BACKEND_SERVICE; then
        log_error "后端服务未运行"
        restart_service "backend"
        return 1
    fi
    
    # 检查API可访问性
    if ! curl -s --max-time $TIMEOUT "$BACKEND_URL" | grep -q "code" 2>/dev/null; then
        log_error "后端API不可访问: $BACKEND_URL"
        restart_service "backend"
        return 1
    fi
    
    log_success "后端服务正常"
    return 0
}

# 检查前端健康
check_frontend() {
    log_info "检查前端服务..."
    
    # 检查PM2状态
    if ! pm2 describe $FRONTEND_APP | grep -q "online" 2>/dev/null; then
        log_error "前端服务未运行"
        restart_service "frontend"
        return 1
    fi
    
    # 检查页面可访问性
    if ! curl -s --max-time $TIMEOUT "$FRONTEND_URL" | grep -q "<html" 2>/dev/null; then
        log_error "前端页面不可访问: $FRONTEND_URL"
        restart_service "frontend"
        return 1
    fi
    
    log_success "前端服务正常"
    return 0
}

# 检查数据库健康
check_database() {
    log_info "检查数据库服务..."
    
    if ! mysql -u root -proot5869087 -e "SELECT 1" &>/dev/null; then
        log_error "数据库连接失败"
        return 1
    fi
    
    log_success "数据库服务正常"
    return 0
}

# 检查系统资源
check_resources() {
    log_info "检查系统资源..."
    
    # 检查CPU负载
    local load=$(uptime | awk -F'[a-z]:' '{ print $2}' | awk -F',' '{ print $1}' | tr -d ' ')
    local cpu_cores=$(nproc)
    local load_per_core=$(echo "$load / $cpu_cores" | bc -l)
    
    if (( $(echo "$load_per_core > 2.0" | bc -l) )); then
        log_warning "CPU负载过高: $load (每核心: $load_per_core)"
    else
        log_success "CPU负载正常: $load (每核心: $load_per_core)"
    fi
    
    # 检查内存使用
    local mem_used_percent=$(free | grep Mem | awk '{print $3/$2 * 100.0}')
    
    if (( $(echo "$mem_used_percent > 90.0" | bc -l) )); then
        log_warning "内存使用率过高: ${mem_used_percent}%"
    else
        log_success "内存使用率正常: ${mem_used_percent}%"
    fi
    
    # 检查磁盘使用
    local disk_used_percent=$(df -h / | awk 'NR==2 {print $5}' | tr -d '%')
    
    if [ "$disk_used_percent" -gt 90 ]; then
        log_warning "磁盘使用率过高: ${disk_used_percent}%"
    else
        log_success "磁盘使用率正常: ${disk_used_percent}%"
    fi
    
    return 0
}

# 主函数
main() {
    log_info "开始健康检查..."
    
    local backend_ok=true
    local frontend_ok=true
    local database_ok=true
    
    # 检查各服务
    check_backend || backend_ok=false
    check_frontend || frontend_ok=false
    check_database || database_ok=false
    check_resources
    
    # 汇总结果
    echo ""
    log_info "健康检查结果汇总:"
    
    if [ "$backend_ok" = true ] && [ "$frontend_ok" = true ] && [ "$database_ok" = true ]; then
        log_success "所有服务正常运行"
        echo "0" > "$ALERT_COUNT_FILE"  # 重置告警计数
    else
        local alert_message="座位预订系统健康检查失败:\n"
        
        if [ "$backend_ok" = false ]; then
            alert_message+="- 后端服务异常\n"
        fi
        
        if [ "$frontend_ok" = false ]; then
            alert_message+="- 前端服务异常\n"
        fi
        
        if [ "$database_ok" = false ]; then
            alert_message+="- 数据库服务异常\n"
        fi
        
        log_error "部分服务异常"
        send_alert "$alert_message"
    fi
    
    log_info "健康检查完成"
    echo ""
}

# 运行主函数
main "$@"
