#!/bin/bash

# 座位预订系统后台服务安装脚本
# 作者：Augment Agent
# 版本：1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 项目路径
PROJECT_ROOT=$(pwd)

# 打印函数
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] ${message}${NC}"
}

print_success() { print_message "$GREEN" "✅ $1"; }
print_error() { print_message "$RED" "❌ $1"; }
print_warning() { print_message "$YELLOW" "⚠️  $1"; }
print_info() { print_message "$BLUE" "ℹ️  $1"; }
print_step() { print_message "$PURPLE" "🔄 $1"; }

# 检查root权限
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "此脚本需要root权限运行"
        print_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查系统
check_system() {
    print_step "检查系统环境..."
    
    # 检查操作系统
    if [ ! -f /etc/os-release ]; then
        print_error "无法识别操作系统"
        exit 1
    fi
    
    . /etc/os-release
    print_success "操作系统: $PRETTY_NAME"
    
    # 检查systemd
    if ! command -v systemctl &> /dev/null; then
        print_error "系统不支持systemd"
        exit 1
    fi
    
    print_success "systemd支持: 可用"
}

# 安装依赖
install_dependencies() {
    print_step "安装系统依赖..."
    
    # 更新包列表
    apt update
    
    # 安装必要的包
    apt install -y \
        curl \
        wget \
        git \
        bc \
        jq \
        logrotate \
        mailutils \
        net-tools
    
    print_success "系统依赖安装完成"
}

# 安装PM2
install_pm2() {
    print_step "安装PM2..."
    
    if command -v pm2 &> /dev/null; then
        print_success "PM2已安装: $(pm2 --version)"
        return 0
    fi
    
    # 安装PM2
    npm install -g pm2
    
    # 验证安装
    if command -v pm2 &> /dev/null; then
        print_success "PM2安装成功: $(pm2 --version)"
    else
        print_error "PM2安装失败"
        exit 1
    fi
}

# 安装systemd服务
install_systemd_service() {
    print_step "安装systemd服务..."
    
    # 复制服务文件
    cp "$PROJECT_ROOT/seatmaster-backend.service" /etc/systemd/system/
    
    # 设置权限
    chmod 644 /etc/systemd/system/seatmaster-backend.service
    
    # 重新加载systemd
    systemctl daemon-reload
    
    # 启用服务
    systemctl enable seatmaster-backend
    
    print_success "systemd服务安装完成"
}

# 配置日志轮转
setup_logrotate() {
    print_step "配置日志轮转..."
    
    # 复制logrotate配置
    cp "$PROJECT_ROOT/seatmaster-logrotate" /etc/logrotate.d/seatmaster
    
    # 设置权限
    chmod 644 /etc/logrotate.d/seatmaster
    
    # 测试配置
    logrotate -d /etc/logrotate.d/seatmaster
    
    print_success "日志轮转配置完成"
}

# 设置定时健康检查
setup_health_check() {
    print_step "设置定时健康检查..."
    
    # 给健康检查脚本执行权限
    chmod +x "$PROJECT_ROOT/health-check.sh"
    
    # 添加到crontab（每5分钟检查一次）
    local cron_job="*/5 * * * * $PROJECT_ROOT/health-check.sh"
    
    # 检查是否已存在
    if crontab -l 2>/dev/null | grep -q "health-check.sh"; then
        print_warning "健康检查任务已存在"
    else
        (crontab -l 2>/dev/null; echo "$cron_job") | crontab -
        print_success "健康检查任务已添加到crontab"
    fi
}

# 创建必要目录
create_directories() {
    print_step "创建必要目录..."
    
    local dirs=(
        "$PROJECT_ROOT/logs"
        "$PROJECT_ROOT/pids"
        "/var/log/seatmaster"
    )
    
    for dir in "${dirs[@]}"; do
        mkdir -p "$dir"
        chown root:root "$dir"
        chmod 755 "$dir"
    done
    
    print_success "目录创建完成"
}

# 设置权限
setup_permissions() {
    print_step "设置文件权限..."
    
    # 给脚本执行权限
    chmod +x "$PROJECT_ROOT/start-daemon.sh"
    chmod +x "$PROJECT_ROOT/stop-all.sh"
    chmod +x "$PROJECT_ROOT/status.sh"
    chmod +x "$PROJECT_ROOT/health-check.sh"
    
    # 设置日志目录权限
    chown -R root:root "$PROJECT_ROOT/logs"
    chmod -R 755 "$PROJECT_ROOT/logs"
    
    print_success "权限设置完成"
}

# 配置防火墙
setup_firewall() {
    print_step "配置防火墙..."
    
    # 检查ufw是否安装
    if command -v ufw &> /dev/null; then
        # 允许必要端口
        ufw allow 8081/tcp comment "SeatMaster Backend"
        ufw allow 3000/tcp comment "SeatMaster Frontend"
        
        print_success "防火墙规则已添加"
    else
        print_warning "ufw未安装，请手动配置防火墙"
        print_info "需要开放端口: 8081 (后端), 3000 (前端)"
    fi
}

# 主函数
main() {
    echo ""
    echo -e "${CYAN}================================================${NC}"
    echo -e "${CYAN}    🚀 座位预订系统后台服务安装程序${NC}"
    echo -e "${CYAN}================================================${NC}"
    echo ""
    
    # 检查权限
    check_root
    
    # 检查系统
    check_system
    
    # 安装依赖
    install_dependencies
    
    # 安装PM2
    install_pm2
    
    # 创建目录
    create_directories
    
    # 安装systemd服务
    install_systemd_service
    
    # 配置日志轮转
    setup_logrotate
    
    # 设置健康检查
    setup_health_check
    
    # 设置权限
    setup_permissions
    
    # 配置防火墙
    setup_firewall
    
    echo ""
    echo -e "${GREEN}================================================${NC}"
    echo -e "${GREEN}    🎉 安装完成！${NC}"
    echo -e "${GREEN}================================================${NC}"
    echo ""
    
    print_success "座位预订系统后台服务安装完成"
    echo ""
    print_info "使用方法:"
    print_info "  启动服务: ./start-daemon.sh start"
    print_info "  停止服务: ./start-daemon.sh stop"
    print_info "  查看状态: ./start-daemon.sh status"
    print_info "  查看日志: ./start-daemon.sh logs"
    print_info "  配置自启: ./start-daemon.sh autostart"
    echo ""
    print_info "服务地址:"
    print_info "  前端: http://localhost:3000"
    print_info "  后端: http://localhost:8081"
    echo ""
    print_warning "请确保MySQL服务已启动并配置正确"
    print_warning "首次启动前请检查数据库连接配置"
    echo ""
}

# 运行主函数
main "$@"
