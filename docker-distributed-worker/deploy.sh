#!/bin/bash

# SeatMaster分布式Worker自动化部署脚本
# 用于快速部署Worker到多台机器

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "SeatMaster分布式Worker部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -i, --init          初始化配置文件"
    echo "  -d, --deploy        部署Worker服务"
    echo "  -s, --status        查看服务状态"
    echo "  -l, --logs          查看服务日志"
    echo "  -r, --restart       重启服务"
    echo "  -t, --stop          停止服务"
    echo "  --clean             清理服务和镜像"
    echo ""
    echo "示例:"
    echo "  $0 --init           # 初始化配置"
    echo "  $0 --deploy         # 部署服务"
    echo "  $0 --status         # 查看状态"
}

# 检查Docker环境
check_docker() {
    print_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker服务未启动，请启动Docker服务"
        exit 1
    fi
    
    print_success "Docker环境检查通过"
}

# 初始化配置
init_config() {
    print_info "初始化配置文件..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.template" ]; then
            cp .env.template .env
            print_success "已创建.env配置文件"
            print_warning "请编辑.env文件配置您的环境参数"
        else
            print_error ".env.template文件不存在"
            exit 1
        fi
    else
        print_warning ".env文件已存在，跳过创建"
    fi
    
    # 创建必要目录
    mkdir -p logs config
    print_success "已创建必要目录"
}

# 网络连通性检查
check_network() {
    print_info "检查网络连通性..."
    
    # 读取环境变量
    if [ -f ".env" ]; then
        source .env
    else
        print_error ".env文件不存在，请先运行 --init"
        exit 1
    fi
    
    # 检查数据库连接
    print_info "检查数据库连接: ${DB_HOST:-*************}:${DB_PORT:-3306}"
    if nc -z "${DB_HOST:-*************}" "${DB_PORT:-3306}"; then
        print_success "数据库连接正常"
    else
        print_error "无法连接到数据库，请检查网络和防火墙配置"
        exit 1
    fi
    
    # 检查主服务器连接
    print_info "检查主服务器连接: ${MASTER_HOST:-*************}:${MASTER_PORT:-8081}"
    if nc -z "${MASTER_HOST:-*************}" "${MASTER_PORT:-8081}"; then
        print_success "主服务器连接正常"
    else
        print_error "无法连接到主服务器，请检查网络和防火墙配置"
        exit 1
    fi
}

# 部署服务
deploy_service() {
    print_info "开始部署Worker服务..."
    
    check_docker
    check_network
    
    # 构建镜像
    print_info "构建Docker镜像..."
    docker-compose build
    
    # 启动服务
    print_info "启动Worker服务..."
    docker-compose up -d
    
    # 等待服务启动
    print_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        print_success "Worker服务部署成功！"
        show_status
    else
        print_error "Worker服务启动失败"
        docker-compose logs
        exit 1
    fi
}

# 查看服务状态
show_status() {
    print_info "Worker服务状态:"
    docker-compose ps
    
    print_info "容器资源使用情况:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
}

# 查看日志
show_logs() {
    print_info "Worker服务日志:"
    docker-compose logs -f --tail=100
}

# 重启服务
restart_service() {
    print_info "重启Worker服务..."
    docker-compose restart
    print_success "服务重启完成"
}

# 停止服务
stop_service() {
    print_info "停止Worker服务..."
    docker-compose down
    print_success "服务已停止"
}

# 清理服务
clean_service() {
    print_warning "这将删除所有容器、镜像和数据，确定继续吗？(y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_info "清理服务和镜像..."
        docker-compose down -v --rmi all
        print_success "清理完成"
    else
        print_info "取消清理操作"
    fi
}

# 主程序
main() {
    case "$1" in
        -h|--help)
            show_help
            ;;
        -i|--init)
            init_config
            ;;
        -d|--deploy)
            deploy_service
            ;;
        -s|--status)
            show_status
            ;;
        -l|--logs)
            show_logs
            ;;
        -r|--restart)
            restart_service
            ;;
        -t|--stop)
            stop_service
            ;;
        --clean)
            clean_service
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 如果没有参数，显示帮助
if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

main "$@"
