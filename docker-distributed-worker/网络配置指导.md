# SeatMaster分布式Worker网络配置指导

## 🌐 网络架构概述

```
┌─────────────────────────────────────────────────────────────────┐
│                        网络拓扑图                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐ │
│  │   主服务器机器    │    │   Worker机器1    │    │   Worker机器2    │ │
│  │ *************   │    │ *************   │    │ *************   │ │
│  │                │    │                │    │                │ │
│  │ ┌─────────────┐  │    │ ┌─────────────┐  │    │ ┌─────────────┐  │ │
│  │ │主服务器:8081 │  │◄──►│ │Worker:8085  │  │    │ │Worker:8085  │  │ │
│  │ └─────────────┘  │    │ └─────────────┘  │    │ └─────────────┘  │ │
│  │ ┌─────────────┐  │    │                │    │                │ │
│  │ │MySQL:3306   │  │◄──►│                │    │                │ │
│  │ └─────────────┘  │    │                │    │                │ │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘ │
│                                                                │
└─────────────────────────────────────────────────────────────────┘
```

## 🔧 端口配置要求

### 主服务器机器 (*************)
| 服务 | 端口 | 协议 | 访问方向 | 说明 |
|------|------|------|----------|------|
| 主服务器 | 8081 | TCP | 入站 | Worker连接主服务器 |
| MySQL | 3306 | TCP | 入站 | Worker连接数据库 |

### Worker机器 (*************, *************, ...)
| 服务 | 端口 | 协议 | 访问方向 | 说明 |
|------|------|------|----------|------|
| Worker服务 | 8085 | TCP | 入站 | 主服务器管理Worker |
| Docker API | 2376 | TCP | 入站(可选) | 远程Docker管理 |

## 🛡️ 防火墙配置

### Ubuntu/Debian系统 (使用ufw)

#### 主服务器机器
```bash
# 开放主服务器端口
sudo ufw allow 8081/tcp comment "SeatMaster主服务器"

# 开放MySQL端口（仅允许Worker机器访问）
sudo ufw allow from ************* to any port 3306 comment "Worker1访问MySQL"
sudo ufw allow from ************* to any port 3306 comment "Worker2访问MySQL"
# 根据实际Worker机器数量添加更多规则

# 启用防火墙
sudo ufw enable

# 查看规则
sudo ufw status numbered
```

#### Worker机器
```bash
# 开放Worker服务端口
sudo ufw allow 8085/tcp comment "SeatMaster Worker服务"

# 允许访问主服务器（出站规则，通常默认允许）
sudo ufw allow out 8081/tcp comment "访问主服务器"
sudo ufw allow out 3306/tcp comment "访问MySQL"

# 启用防火墙
sudo ufw enable
```

### CentOS/RHEL系统 (使用firewalld)

#### 主服务器机器
```bash
# 开放主服务器端口
sudo firewall-cmd --permanent --add-port=8081/tcp
sudo firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='*************' port protocol='tcp' port='3306' accept"
sudo firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='*************' port protocol='tcp' port='3306' accept"

# 重载防火墙配置
sudo firewall-cmd --reload

# 查看配置
sudo firewall-cmd --list-all
```

#### Worker机器
```bash
# 开放Worker服务端口
sudo firewall-cmd --permanent --add-port=8085/tcp

# 重载防火墙配置
sudo firewall-cmd --reload
```

### Windows系统 (使用Windows防火墙)

#### 主服务器机器
```powershell
# 开放主服务器端口
netsh advfirewall firewall add rule name="SeatMaster主服务器" dir=in action=allow protocol=TCP localport=8081

# 开放MySQL端口（限制来源IP）
netsh advfirewall firewall add rule name="MySQL-Worker1" dir=in action=allow protocol=TCP localport=3306 remoteip=*************
netsh advfirewall firewall add rule name="MySQL-Worker2" dir=in action=allow protocol=TCP localport=3306 remoteip=*************
```

#### Worker机器
```powershell
# 开放Worker服务端口
netsh advfirewall firewall add rule name="SeatMaster Worker" dir=in action=allow protocol=TCP localport=8085
```

## 🔍 网络连通性测试

### 从Worker机器测试连接

#### 测试数据库连接
```bash
# 使用telnet测试
telnet ************* 3306

# 使用nc测试
nc -zv ************* 3306

# 使用nmap测试
nmap -p 3306 *************
```

#### 测试主服务器连接
```bash
# 测试主服务器端口
nc -zv ************* 8081

# 测试HTTP连接
curl -I http://*************:8081/api/health
```

### 从主服务器测试Worker连接
```bash
# 测试Worker端口
nc -zv ************* 8085
nc -zv ************* 8085

# 测试Worker健康检查
curl -I http://*************:8085/api/health
curl -I http://*************:8085/api/health
```

## 📝 配置文件示例

### Worker机器1 (.env文件)
```bash
# 机器标识
MACHINE_ID=001

# Worker配置
WORKER_ID=worker-001
WORKER_NAME=分布式Worker-001
WORKER_PORT=8085

# 数据库配置
DB_HOST=*************
DB_PORT=3306
DB_NAME=seatmaster
DB_USER=root
DB_PASSWORD=123456

# 主服务器配置
MASTER_HOST=*************
MASTER_PORT=8081
```

### Worker机器2 (.env文件)
```bash
# 机器标识
MACHINE_ID=002

# Worker配置
WORKER_ID=worker-002
WORKER_NAME=分布式Worker-002
WORKER_PORT=8085

# 数据库配置
DB_HOST=*************
DB_PORT=3306
DB_NAME=seatmaster
DB_USER=root
DB_PASSWORD=123456

# 主服务器配置
MASTER_HOST=*************
MASTER_PORT=8081
```

## 🚀 部署流程

### 1. 准备阶段
```bash
# 在每台Worker机器上
1. 安装Docker和Docker Compose
2. 复制部署文件到机器
3. 配置.env文件
4. 配置防火墙规则
```

### 2. 网络测试
```bash
# 运行网络连通性测试
./deploy.sh --init
# 手动测试网络连接
```

### 3. 部署服务
```bash
# 在每台Worker机器上执行
./deploy.sh --deploy
```

### 4. 验证部署
```bash
# 查看服务状态
./deploy.sh --status

# 查看日志
./deploy.sh --logs
```

## ⚠️ 常见问题和解决方案

### 问题1: 无法连接数据库
**症状**: Worker启动时报数据库连接错误
**解决方案**:
1. 检查数据库服务是否启动
2. 检查防火墙3306端口是否开放
3. 检查数据库用户权限
4. 验证网络连通性

### 问题2: 无法连接主服务器
**症状**: Worker无法注册到主服务器
**解决方案**:
1. 检查主服务器是否启动
2. 检查防火墙8081端口是否开放
3. 检查主服务器配置
4. 验证网络路由

### 问题3: Worker服务无法访问
**症状**: 主服务器无法连接到Worker
**解决方案**:
1. 检查Worker防火墙8085端口
2. 检查Docker端口映射
3. 检查容器网络配置
4. 验证服务健康状态

## 📊 监控和维护

### 服务监控脚本
```bash
#!/bin/bash
# 创建监控脚本 monitor.sh

while true; do
    echo "=== $(date) ==="
    ./deploy.sh --status
    sleep 60
done
```

### 日志轮转配置
```bash
# 配置logrotate
sudo tee /etc/logrotate.d/seatmaster-worker << EOF
/path/to/worker/logs/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
EOF
```
