# SeatMaster分布式Worker环境变量配置模板
# 复制此文件为.env并根据实际环境修改配置

# ========================================
# 机器标识配置
# ========================================
# 机器ID（用于区分不同的Worker机器）
MACHINE_ID=001

# ========================================
# Worker服务配置
# ========================================
# Worker唯一标识符
WORKER_ID=worker-001

# Worker显示名称
WORKER_NAME=分布式Worker-001

# Worker服务端口（建议每台机器使用相同端口8085）
WORKER_PORT=8085

# ========================================
# 数据库配置
# ========================================
# 数据库服务器IP地址（主服务器IP）
DB_HOST=*************

# 数据库端口
DB_PORT=3306

# 数据库名称
DB_NAME=seatmaster

# 数据库用户名
DB_USER=root

# 数据库密码
DB_PASSWORD=123456

# ========================================
# 主服务器配置
# ========================================
# 主服务器IP地址
MASTER_HOST=*************

# 主服务器端口
MASTER_PORT=8081

# ========================================
# 网络配置说明
# ========================================
# 需要开放的端口:
# - Worker端口: 8085 (TCP)
# - 数据库端口: 3306 (TCP) - 仅Worker到主服务器
# - 主服务器端口: 8081 (TCP) - 仅Worker到主服务器
#
# 防火墙配置示例 (Ubuntu/CentOS):
# sudo ufw allow 8085/tcp
# sudo firewall-cmd --permanent --add-port=8085/tcp
# sudo firewall-cmd --reload

# ========================================
# 部署示例
# ========================================
# 1. 复制此文件为.env: cp .env.template .env
# 2. 修改配置参数
# 3. 启动服务: docker-compose up -d
# 4. 查看日志: docker-compose logs -f
# 5. 停止服务: docker-compose down
