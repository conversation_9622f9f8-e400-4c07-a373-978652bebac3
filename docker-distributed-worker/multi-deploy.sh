#!/bin/bash

# SeatMaster多机器分布式Worker批量部署脚本
# 用于同时管理多台Worker机器的部署

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 配置文件路径
MACHINES_CONFIG="machines.conf"

# 显示帮助信息
show_help() {
    echo "SeatMaster多机器分布式Worker批量部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  -c, --config        生成机器配置文件模板"
    echo "  -t, --test          测试所有机器连通性"
    echo "  -d, --deploy        批量部署到所有机器"
    echo "  -s, --status        查看所有机器状态"
    echo "  -r, --restart       重启所有机器服务"
    echo "  -l, --logs          查看指定机器日志"
    echo "  --stop              停止所有机器服务"
    echo "  --clean             清理所有机器服务"
    echo ""
    echo "配置文件: $MACHINES_CONFIG"
    echo "格式: IP地址 SSH用户 SSH端口 机器ID Worker名称"
}

# 生成机器配置文件模板
generate_config() {
    if [ -f "$MACHINES_CONFIG" ]; then
        print_warning "配置文件 $MACHINES_CONFIG 已存在"
        return
    fi
    
    cat > "$MACHINES_CONFIG" << 'EOF'
# SeatMaster Worker机器配置文件
# 格式: IP地址 SSH用户 SSH端口 机器ID Worker名称
# 示例:
# ************* root 22 001 分布式Worker-001
# ************* ubuntu 22 002 分布式Worker-002
# ************* centos 22 003 分布式Worker-003

# 请在下面添加您的Worker机器配置
************* root 22 001 分布式Worker-001
************* root 22 002 分布式Worker-002
************* root 22 003 分布式Worker-003
EOF
    
    print_success "已生成配置文件模板: $MACHINES_CONFIG"
    print_info "请编辑此文件添加您的Worker机器信息"
}

# 读取机器配置
read_machines() {
    if [ ! -f "$MACHINES_CONFIG" ]; then
        print_error "配置文件 $MACHINES_CONFIG 不存在，请先运行 --config 生成"
        exit 1
    fi
    
    # 过滤注释和空行
    grep -v '^#' "$MACHINES_CONFIG" | grep -v '^$'
}

# 在远程机器上执行命令
execute_remote() {
    local ip=$1
    local user=$2
    local port=$3
    local command=$4
    
    ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no -p "$port" "$user@$ip" "$command"
}

# 复制文件到远程机器
copy_to_remote() {
    local ip=$1
    local user=$2
    local port=$3
    local local_path=$4
    local remote_path=$5
    
    scp -o ConnectTimeout=10 -o StrictHostKeyChecking=no -P "$port" -r "$local_path" "$user@$ip:$remote_path"
}

# 测试机器连通性
test_connectivity() {
    print_info "测试所有机器连通性..."
    
    local success_count=0
    local total_count=0
    
    while IFS=' ' read -r ip user port machine_id worker_name; do
        [ -z "$ip" ] && continue
        total_count=$((total_count + 1))
        
        print_info "测试机器 $machine_id ($ip)..."
        
        if execute_remote "$ip" "$user" "$port" "echo 'SSH连接成功'" >/dev/null 2>&1; then
            print_success "机器 $machine_id ($ip) 连接正常"
            success_count=$((success_count + 1))
        else
            print_error "机器 $machine_id ($ip) 连接失败"
        fi
    done < <(read_machines)
    
    print_info "连通性测试完成: $success_count/$total_count 台机器可连接"
    
    if [ $success_count -eq $total_count ]; then
        print_success "所有机器连接正常"
        return 0
    else
        print_error "部分机器连接失败"
        return 1
    fi
}

# 批量部署
batch_deploy() {
    print_info "开始批量部署Worker服务..."
    
    # 检查必要文件
    local required_files=("Dockerfile" "docker-compose.yml" "docker-entrypoint.sh" ".env.template" "deploy.sh")
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "缺少必要文件: $file"
            exit 1
        fi
    done
    
    while IFS=' ' read -r ip user port machine_id worker_name; do
        [ -z "$ip" ] && continue
        
        print_info "部署到机器 $machine_id ($ip)..."
        
        # 创建远程目录
        execute_remote "$ip" "$user" "$port" "mkdir -p /opt/seatmaster-worker"
        
        # 复制部署文件
        copy_to_remote "$ip" "$user" "$port" "." "/opt/seatmaster-worker/"
        
        # 生成环境配置文件
        execute_remote "$ip" "$user" "$port" "cd /opt/seatmaster-worker && cp .env.template .env"
        
        # 更新环境变量
        execute_remote "$ip" "$user" "$port" "cd /opt/seatmaster-worker && sed -i 's/MACHINE_ID=001/MACHINE_ID=$machine_id/g' .env"
        execute_remote "$ip" "$user" "$port" "cd /opt/seatmaster-worker && sed -i 's/WORKER_ID=worker-001/WORKER_ID=worker-$machine_id/g' .env"
        execute_remote "$ip" "$user" "$port" "cd /opt/seatmaster-worker && sed -i 's/分布式Worker-001/$worker_name/g' .env"
        
        # 设置执行权限
        execute_remote "$ip" "$user" "$port" "cd /opt/seatmaster-worker && chmod +x deploy.sh docker-entrypoint.sh"
        
        # 部署服务
        if execute_remote "$ip" "$user" "$port" "cd /opt/seatmaster-worker && ./deploy.sh --deploy" >/dev/null 2>&1; then
            print_success "机器 $machine_id ($ip) 部署成功"
        else
            print_error "机器 $machine_id ($ip) 部署失败"
        fi
        
    done < <(read_machines)
    
    print_success "批量部署完成"
}

# 查看所有机器状态
show_all_status() {
    print_info "查看所有机器Worker状态..."
    
    while IFS=' ' read -r ip user port machine_id worker_name; do
        [ -z "$ip" ] && continue
        
        echo ""
        print_info "机器 $machine_id ($ip) - $worker_name"
        echo "----------------------------------------"
        
        if execute_remote "$ip" "$user" "$port" "cd /opt/seatmaster-worker && ./deploy.sh --status" 2>/dev/null; then
            print_success "机器 $machine_id 状态正常"
        else
            print_error "机器 $machine_id 状态异常或服务未部署"
        fi
        
    done < <(read_machines)
}

# 重启所有服务
restart_all() {
    print_info "重启所有机器Worker服务..."
    
    while IFS=' ' read -r ip user port machine_id worker_name; do
        [ -z "$ip" ] && continue
        
        print_info "重启机器 $machine_id ($ip)..."
        
        if execute_remote "$ip" "$user" "$port" "cd /opt/seatmaster-worker && ./deploy.sh --restart" >/dev/null 2>&1; then
            print_success "机器 $machine_id 重启成功"
        else
            print_error "机器 $machine_id 重启失败"
        fi
        
    done < <(read_machines)
}

# 查看指定机器日志
show_machine_logs() {
    if [ -z "$2" ]; then
        print_error "请指定机器ID，例如: $0 --logs 001"
        exit 1
    fi
    
    local target_machine_id=$2
    local found=false
    
    while IFS=' ' read -r ip user port machine_id worker_name; do
        [ -z "$ip" ] && continue
        
        if [ "$machine_id" = "$target_machine_id" ]; then
            found=true
            print_info "查看机器 $machine_id ($ip) 日志..."
            execute_remote "$ip" "$user" "$port" "cd /opt/seatmaster-worker && ./deploy.sh --logs"
            break
        fi
    done < <(read_machines)
    
    if [ "$found" = false ]; then
        print_error "未找到机器ID: $target_machine_id"
        exit 1
    fi
}

# 停止所有服务
stop_all() {
    print_info "停止所有机器Worker服务..."
    
    while IFS=' ' read -r ip user port machine_id worker_name; do
        [ -z "$ip" ] && continue
        
        print_info "停止机器 $machine_id ($ip)..."
        execute_remote "$ip" "$user" "$port" "cd /opt/seatmaster-worker && ./deploy.sh --stop" >/dev/null 2>&1
        
    done < <(read_machines)
    
    print_success "所有服务已停止"
}

# 清理所有服务
clean_all() {
    print_warning "这将清理所有机器的Worker服务和镜像，确定继续吗？(y/N)"
    read -r response
    if [[ ! "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_info "取消清理操作"
        return
    fi
    
    print_info "清理所有机器Worker服务..."
    
    while IFS=' ' read -r ip user port machine_id worker_name; do
        [ -z "$ip" ] && continue
        
        print_info "清理机器 $machine_id ($ip)..."
        execute_remote "$ip" "$user" "$port" "cd /opt/seatmaster-worker && echo 'y' | ./deploy.sh --clean" >/dev/null 2>&1
        
    done < <(read_machines)
    
    print_success "所有服务已清理"
}

# 主程序
main() {
    case "$1" in
        -h|--help)
            show_help
            ;;
        -c|--config)
            generate_config
            ;;
        -t|--test)
            test_connectivity
            ;;
        -d|--deploy)
            batch_deploy
            ;;
        -s|--status)
            show_all_status
            ;;
        -r|--restart)
            restart_all
            ;;
        -l|--logs)
            show_machine_logs "$@"
            ;;
        --stop)
            stop_all
            ;;
        --clean)
            clean_all
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

main "$@"
