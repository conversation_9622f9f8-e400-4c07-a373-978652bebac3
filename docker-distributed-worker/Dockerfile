# SeatMaster分布式Worker Dockerfile
# 用于一台机器一个Worker的分布式部署

FROM openjdk:11-jre-slim

# 安装必要工具
RUN apt-get update && apt-get install -y \
    curl \
    netcat \
    && rm -rf /var/lib/apt/lists/*

# 创建应用目录
WORKDIR /app

# 复制JAR文件
COPY worker-server-simple-1.0.0.jar app.jar

# 创建日志目录
RUN mkdir -p /app/logs

# 创建启动脚本
COPY docker-entrypoint.sh /app/docker-entrypoint.sh
RUN chmod +x /app/docker-entrypoint.sh

# 暴露端口
EXPOSE 8085

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8085/api/health || exit 1

# 使用启动脚本
ENTRYPOINT ["/app/docker-entrypoint.sh"]
