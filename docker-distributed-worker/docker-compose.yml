# SeatMaster分布式Worker Docker Compose配置
# 用于单台机器部署单个Worker实例

version: '3.8'

services:
  seatmaster-worker:
    build: .
    container_name: seatmaster-worker-${MACHINE_ID:-001}
    hostname: worker-${MACHINE_ID:-001}
    
    # 端口映射
    ports:
      - "${WORKER_PORT:-8085}:8085"
    
    # 环境变量配置
    environment:
      # Worker基本配置
      - WORKER_ID=${WORKER_ID:-worker-${MACHINE_ID:-001}}
      - WORKER_NAME=${WORKER_NAME:-分布式Worker-${MACHINE_ID:-001}}
      - WORKER_PORT=8085
      
      # 数据库配置
      - DB_HOST=${DB_HOST:-*************}
      - DB_PORT=${DB_PORT:-3306}
      - DB_NAME=${DB_NAME:-seatmaster}
      - DB_USER=${DB_USER:-root}
      - DB_PASSWORD=${DB_PASSWORD:-123456}
      
      # 主服务器配置
      - MASTER_HOST=${MASTER_HOST:-*************}
      - MASTER_PORT=${MASTER_PORT:-8081}
    
    # 数据卷映射
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    
    # 重启策略
    restart: unless-stopped
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8085/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # 网络配置
    networks:
      - seatmaster-network
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"

networks:
  seatmaster-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  worker-logs:
    driver: local
