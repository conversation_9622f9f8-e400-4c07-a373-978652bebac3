# SeatMaster分布式Worker Docker部署方案

## 📋 概述

本方案实现**一台机器一个Worker**的分布式部署架构，每台物理机器运行一个Worker实例，通过Docker容器化部署，支持自动化批量管理。

## 🏗️ 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   主服务器机器    │    │   Worker机器1    │    │   Worker机器2    │
│ *************   │    │ *************   │    │ *************   │
│                │    │                │    │                │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │主服务器    │  │◄──►│  │Worker-1   │  │◄──►│  │Worker-2   │  │
│  │(8081)     │  │    │  │(8085)     │  │    │  │(8085)     │  │
│  └───────────┘  │    │  └───────────┘  │    │  └───────────┘  │
│  ┌───────────┐  │    │                │    │                │
│  │MySQL      │  │    │                │    │                │
│  │(3306)     │  │    │                │    │                │
│  └───────────┘  │    │                │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 文件结构

```
docker-distributed-worker/
├── Dockerfile                 # Worker容器镜像定义
├── docker-compose.yml         # 单机器Docker Compose配置
├── docker-entrypoint.sh       # 容器启动脚本
├── .env.template              # 环境变量配置模板
├── deploy.sh                  # 单机器部署脚本
├── multi-deploy.sh            # 多机器批量部署脚本
├── 网络配置指导.md              # 网络配置详细指导
└── README.md                  # 本文档
```

## 🚀 快速开始

### 1. 准备工作

#### 环境要求
- **主服务器**: 运行SeatMaster主服务和MySQL数据库
- **Worker机器**: 安装Docker和Docker Compose
- **网络**: 所有机器可以互相通信

#### 安装Docker (在每台Worker机器上)
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 单机器部署

#### 步骤1: 准备部署文件
```bash
# 将部署文件复制到Worker机器
scp -r docker-distributed-worker/ user@*************:/opt/seatmaster-worker/
```

#### 步骤2: 配置环境变量
```bash
cd /opt/seatmaster-worker
./deploy.sh --init
```

#### 步骤3: 编辑配置文件
```bash
vim .env
```

配置示例:
```bash
# 机器标识
MACHINE_ID=001

# Worker配置
WORKER_ID=worker-001
WORKER_NAME=分布式Worker-001
WORKER_PORT=8085

# 数据库配置
DB_HOST=*************
DB_PORT=3306
DB_NAME=seatmaster
DB_USER=root
DB_PASSWORD=123456

# 主服务器配置
MASTER_HOST=*************
MASTER_PORT=8081
```

#### 步骤4: 部署服务
```bash
./deploy.sh --deploy
```

#### 步骤5: 验证部署
```bash
# 查看服务状态
./deploy.sh --status

# 查看日志
./deploy.sh --logs
```

### 3. 多机器批量部署

#### 步骤1: 生成机器配置文件
```bash
./multi-deploy.sh --config
```

#### 步骤2: 编辑机器配置
```bash
vim machines.conf
```

配置示例:
```
# IP地址 SSH用户 SSH端口 机器ID Worker名称
************* root 22 001 分布式Worker-001
************* root 22 002 分布式Worker-002
************* root 22 003 分布式Worker-003
```

#### 步骤3: 测试连通性
```bash
./multi-deploy.sh --test
```

#### 步骤4: 批量部署
```bash
./multi-deploy.sh --deploy
```

#### 步骤5: 查看所有机器状态
```bash
./multi-deploy.sh --status
```

## 🔧 管理命令

### 单机器管理 (deploy.sh)
```bash
./deploy.sh --help          # 显示帮助
./deploy.sh --init          # 初始化配置
./deploy.sh --deploy        # 部署服务
./deploy.sh --status        # 查看状态
./deploy.sh --logs          # 查看日志
./deploy.sh --restart       # 重启服务
./deploy.sh --stop          # 停止服务
./deploy.sh --clean         # 清理服务
```

### 多机器管理 (multi-deploy.sh)
```bash
./multi-deploy.sh --help     # 显示帮助
./multi-deploy.sh --config   # 生成配置文件
./multi-deploy.sh --test     # 测试连通性
./multi-deploy.sh --deploy   # 批量部署
./multi-deploy.sh --status   # 查看所有状态
./multi-deploy.sh --restart  # 重启所有服务
./multi-deploy.sh --logs 001 # 查看指定机器日志
./multi-deploy.sh --stop     # 停止所有服务
./multi-deploy.sh --clean    # 清理所有服务
```

## 🌐 网络配置

### 端口要求
- **主服务器**: 8081 (主服务), 3306 (MySQL)
- **Worker机器**: 8085 (Worker服务)

### 防火墙配置
详细的防火墙配置请参考 [网络配置指导.md](网络配置指导.md)

#### Ubuntu快速配置
```bash
# 主服务器
sudo ufw allow 8081/tcp
sudo ufw allow from ***********/24 to any port 3306

# Worker机器
sudo ufw allow 8085/tcp
```

## 📊 监控和维护

### 服务监控
```bash
# 实时监控所有机器状态
watch -n 30 './multi-deploy.sh --status'

# 查看资源使用情况
docker stats --no-stream
```

### 日志管理
```bash
# 查看实时日志
./deploy.sh --logs

# 查看历史日志
docker-compose logs --tail=1000 seatmaster-worker
```

### 健康检查
```bash
# 检查Worker健康状态
curl -f http://localhost:8085/api/health

# 检查容器状态
docker ps
docker inspect seatmaster-worker-001
```

## 🔍 故障排除

### 常见问题

#### 1. 容器启动失败
```bash
# 查看详细错误信息
docker-compose logs seatmaster-worker

# 检查配置文件
cat .env

# 重新构建镜像
docker-compose build --no-cache
```

#### 2. 网络连接问题
```bash
# 测试数据库连接
nc -zv ************* 3306

# 测试主服务器连接
curl -I http://*************:8081/api/health

# 检查防火墙状态
sudo ufw status
```

#### 3. 服务注册失败
```bash
# 检查Worker ID是否重复
grep WORKER_ID .env

# 检查数据库中的Worker注册记录
mysql -h ************* -u root -p -e "SELECT * FROM workers;"
```

## 📈 性能优化

### 资源限制调整
编辑 `docker-compose.yml`:
```yaml
deploy:
  resources:
    limits:
      memory: 2G        # 根据实际需求调整
      cpus: '2.0'
    reservations:
      memory: 1G
      cpus: '1.0'
```

### JVM参数优化
编辑 `docker-entrypoint.sh`:
```bash
exec java \
    -Xms512m \
    -Xmx1024m \
    -XX:+UseG1GC \
    -XX:MaxGCPauseMillis=200 \
    -jar app.jar
```

## 🔄 升级和维护

### 服务升级
```bash
# 停止服务
./deploy.sh --stop

# 更新镜像
docker-compose pull

# 重新部署
./deploy.sh --deploy
```

### 批量升级
```bash
# 停止所有服务
./multi-deploy.sh --stop

# 重新批量部署
./multi-deploy.sh --deploy
```

## 📝 注意事项

1. **Worker ID唯一性**: 确保每台机器的WORKER_ID不重复
2. **网络安全**: 配置适当的防火墙规则，限制不必要的访问
3. **资源监控**: 定期监控CPU、内存使用情况
4. **日志管理**: 配置日志轮转，避免磁盘空间不足
5. **备份策略**: 定期备份配置文件和重要数据

## 🆘 技术支持

如遇到问题，请检查:
1. 网络连通性
2. 防火墙配置
3. Docker服务状态
4. 配置文件正确性
5. 日志错误信息

更多详细信息请参考 [网络配置指导.md](网络配置指导.md)
