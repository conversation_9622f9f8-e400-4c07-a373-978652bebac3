#!/bin/bash

# SeatMaster分布式Worker启动脚本
# 支持环境变量配置和网络检查

set -e

echo "=========================================="
echo "SeatMaster分布式Worker启动中..."
echo "=========================================="

# 默认配置
DEFAULT_WORKER_ID="worker-$(hostname)"
DEFAULT_WORKER_NAME="分布式Worker-$(hostname)"
DEFAULT_WORKER_PORT="8085"
DEFAULT_DB_HOST="*************"
DEFAULT_DB_PORT="3306"
DEFAULT_DB_NAME="seatmaster"
DEFAULT_DB_USER="root"
DEFAULT_DB_PASSWORD="123456"
DEFAULT_MASTER_HOST="*************"
DEFAULT_MASTER_PORT="8081"

# 环境变量配置（可通过docker-compose或docker run覆盖）
export WORKER_ID="${WORKER_ID:-$DEFAULT_WORKER_ID}"
export WORKER_NAME="${WORKER_NAME:-$DEFAULT_WORKER_NAME}"
export WORKER_PORT="${WORKER_PORT:-$DEFAULT_WORKER_PORT}"
export DB_HOST="${DB_HOST:-$DEFAULT_DB_HOST}"
export DB_PORT="${DB_PORT:-$DEFAULT_DB_PORT}"
export DB_NAME="${DB_NAME:-$DEFAULT_DB_NAME}"
export DB_USER="${DB_USER:-$DEFAULT_DB_USER}"
export DB_PASSWORD="${DB_PASSWORD:-$DEFAULT_DB_PASSWORD}"
export MASTER_HOST="${MASTER_HOST:-$DEFAULT_MASTER_HOST}"
export MASTER_PORT="${MASTER_PORT:-$DEFAULT_MASTER_PORT}"

# 显示配置信息
echo "Worker配置信息:"
echo "  Worker ID: $WORKER_ID"
echo "  Worker Name: $WORKER_NAME"
echo "  Worker Port: $WORKER_PORT"
echo "  Database: $DB_HOST:$DB_PORT/$DB_NAME"
echo "  Database User: $DB_USER"
echo "  Master Server: $MASTER_HOST:$MASTER_PORT"
echo "=========================================="

# 网络连通性检查
echo "检查网络连通性..."

# 检查数据库连接
echo "检查数据库连接: $DB_HOST:$DB_PORT"
if ! nc -z "$DB_HOST" "$DB_PORT"; then
    echo "❌ 错误: 无法连接到数据库 $DB_HOST:$DB_PORT"
    echo "请检查:"
    echo "  1. 数据库服务是否启动"
    echo "  2. 网络是否可达"
    echo "  3. 防火墙端口$DB_PORT是否开放"
    exit 1
fi
echo "✅ 数据库连接正常"

# 检查主服务器连接
echo "检查主服务器连接: $MASTER_HOST:$MASTER_PORT"
if ! nc -z "$MASTER_HOST" "$MASTER_PORT"; then
    echo "❌ 错误: 无法连接到主服务器 $MASTER_HOST:$MASTER_PORT"
    echo "请检查:"
    echo "  1. 主服务器是否启动"
    echo "  2. 网络是否可达"
    echo "  3. 防火墙端口$MASTER_PORT是否开放"
    exit 1
fi
echo "✅ 主服务器连接正常"

echo "=========================================="
echo "网络检查完成，启动Worker服务..."

# 构建数据库URL
DB_URL="************************************************************************************************************************"
MASTER_URL="http://$MASTER_HOST:$MASTER_PORT"

# 启动Java应用
exec java \
    -Dserver.port="$WORKER_PORT" \
    -Dworker.id="$WORKER_ID" \
    -Dworker.name="$WORKER_NAME" \
    -Dspring.datasource.url="$DB_URL" \
    -Dspring.datasource.username="$DB_USER" \
    -Dspring.datasource.password="$DB_PASSWORD" \
    -Dmaster.url="$MASTER_URL" \
    -Djava.awt.headless=true \
    -Xms256m \
    -Xmx512m \
    -jar app.jar
