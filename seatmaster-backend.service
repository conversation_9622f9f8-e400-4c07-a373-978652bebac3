[Unit]
Description=SeatMaster Backend Service
Documentation=https://github.com/your-repo/seatmaster
After=network.target mysql.service
Wants=mysql.service
Requires=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/root/seatMaster/backend
Environment=JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64
Environment=MAVEN_HOME=/usr/share/maven
Environment=PATH=/usr/lib/jvm/java-11-openjdk-amd64/bin:/usr/share/maven/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
Environment=SPRING_PROFILES_ACTIVE=production

# 启动命令
ExecStart=/usr/share/maven/bin/mvn spring-boot:run
ExecStop=/bin/kill -TERM $MAINPID
ExecReload=/bin/kill -HUP $MAINPID

# 重启策略
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096
MemoryLimit=2G
CPUQuota=200%

# 安全设置（放宽限制以解决启动问题）
NoNewPrivileges=false
PrivateTmp=false
# ProtectSystem=strict  # 暂时禁用严格保护
# ProtectHome=true      # 暂时禁用家目录保护
ReadWritePaths=/root/seatMaster/logs /root/seatMaster/backend/target /root/.m2

# 日志设置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=seatmaster-backend

# 超时设置
TimeoutStartSec=300
TimeoutStopSec=30

# PID文件
PIDFile=/root/seatMaster/pids/backend.pid

[Install]
WantedBy=multi-user.target
