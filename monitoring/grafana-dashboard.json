{"dashboard": {"id": null, "title": "SeatMaster 监控仪表板", "tags": ["seatmaster", "monitoring"], "timezone": "Asia/Shanghai", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "系统概览", "type": "stat", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "targets": [{"expr": "up{job=~\"seatmaster-.*\"}", "legendFormat": "{{job}} 状态"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}}, {"id": 2, "title": "HTTP请求率", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "rate(http_requests_total{job=\"seatmaster-backend\"}[5m])", "legendFormat": "{{method}} {{uri}}"}], "yAxes": [{"label": "请求/秒", "min": 0}]}, {"id": 3, "title": "响应时间", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"seatmaster-backend\"}[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"seatmaster-backend\"}[5m]))", "legendFormat": "50th percentile"}], "yAxes": [{"label": "秒", "min": 0}]}, {"id": 4, "title": "错误率", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "rate(http_requests_total{job=\"seatmaster-backend\",status=~\"4..|5..\"}[5m]) / rate(http_requests_total{job=\"seatmaster-backend\"}[5m])", "legendFormat": "错误率"}], "yAxes": [{"label": "百分比", "min": 0, "max": 1}]}, {"id": 5, "title": "JVM内存使用", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "jvm_memory_used_bytes{area=\"heap\"}", "legendFormat": "堆内存使用"}, {"expr": "jvm_memory_max_bytes{area=\"heap\"}", "legendFormat": "堆内存最大值"}], "yAxes": [{"label": "字节", "min": 0}]}, {"id": 6, "title": "CPU使用率", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "targets": [{"expr": "100 - (avg by(instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)", "legendFormat": "CPU使用率"}], "yAxes": [{"label": "百分比", "min": 0, "max": 100}]}, {"id": 7, "title": "内存使用率", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "targets": [{"expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100", "legendFormat": "内存使用率"}], "yAxes": [{"label": "百分比", "min": 0, "max": 100}]}, {"id": 8, "title": "磁盘使用率", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "targets": [{"expr": "(1 - (node_filesystem_avail_bytes{fstype!=\"tmpfs\"} / node_filesystem_size_bytes{fstype!=\"tmpfs\"})) * 100", "legendFormat": "{{mountpoint}}"}], "yAxes": [{"label": "百分比", "min": 0, "max": 100}]}, {"id": 9, "title": "数据库连接数", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "targets": [{"expr": "mysql_global_status_threads_connected", "legendFormat": "当前连接数"}, {"expr": "mysql_global_variables_max_connections", "legendFormat": "最大连接数"}], "yAxes": [{"label": "连接数", "min": 0}]}, {"id": 10, "title": "业务指标", "type": "graph", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 40}, "targets": [{"expr": "seatmaster_active_users", "legendFormat": "活跃用户数"}, {"expr": "rate(seatmaster_reservation_attempts_total[5m])", "legendFormat": "预约尝试率"}, {"expr": "rate(seatmaster_reservation_successes_total[5m])", "legendFormat": "预约成功率"}], "yAxes": [{"label": "数量/率", "min": 0}]}], "templating": {"list": [{"name": "instance", "type": "query", "query": "label_values(up, instance)", "refresh": 1}, {"name": "job", "type": "query", "query": "label_values(up, job)", "refresh": 1}]}, "annotations": {"list": [{"name": "部署事件", "datasource": "Prometheus", "expr": "changes(up[1h]) > 0", "titleFormat": "服务重启", "textFormat": "{{job}} 服务重启"}]}}}