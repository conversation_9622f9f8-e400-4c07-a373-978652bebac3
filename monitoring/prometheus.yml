# SeatMaster Prometheus 配置文件

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'seatmaster'
    environment: 'production'

# 告警规则文件
rule_files:
  - "alert_rules.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # SeatMaster后端应用监控
  - job_name: 'seatmaster-backend'
    static_configs:
      - targets: ['localhost:8081']
    scrape_interval: 15s
    metrics_path: /actuator/prometheus
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # Node Exporter (系统监控)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']
    scrape_interval: 15s
    metrics_path: /metrics

  # MySQL监控
  - job_name: 'mysql-exporter'
    static_configs:
      - targets: ['localhost:9104']
    scrape_interval: 30s
    metrics_path: /metrics

  # Nginx监控
  - job_name: 'nginx-exporter'
    static_configs:
      - targets: ['localhost:9113']
    scrape_interval: 30s
    metrics_path: /metrics

  # Redis监控
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['localhost:9121']
    scrape_interval: 30s
    metrics_path: /metrics

  # JVM监控 (如果使用JMX Exporter)
  - job_name: 'jmx-exporter'
    static_configs:
      - targets: ['localhost:8082']
    scrape_interval: 30s
    metrics_path: /metrics

  # 黑盒监控 (HTTP端点检查)
  - job_name: 'blackbox-http'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - http://localhost:3000
        - http://localhost:8081/actuator/health
        - http://localhost:8081/api/health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: localhost:9115

  # 自定义业务指标
  - job_name: 'seatmaster-business'
    static_configs:
      - targets: ['localhost:8081']
    scrape_interval: 60s
    metrics_path: /actuator/prometheus
    params:
      include: ['seatmaster_*']

# 远程写入配置 (可选，用于长期存储)
# remote_write:
#   - url: "http://localhost:8086/api/v1/prom/write?db=prometheus"
#     queue_config:
#       max_samples_per_send: 1000
#       max_shards: 200
#       capacity: 2500

# 远程读取配置 (可选)
# remote_read:
#   - url: "http://localhost:8086/api/v1/prom/read?db=prometheus"
