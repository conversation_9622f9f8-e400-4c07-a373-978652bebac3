# SeatMaster Prometheus 告警规则

groups:
  # 应用服务告警
  - name: seatmaster-application
    rules:
      # 应用服务下线
      - alert: ApplicationDown
        expr: up{job=~"seatmaster-.*"} == 0
        for: 1m
        labels:
          severity: critical
          service: "{{ $labels.job }}"
        annotations:
          summary: "SeatMaster应用服务下线"
          description: "{{ $labels.job }} 服务已下线超过1分钟"

      # 应用响应时间过长
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="seatmaster-backend"}[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "后端响应时间过长"
          description: "95%的请求响应时间超过2秒，当前值: {{ $value }}秒"

      # 错误率过高
      - alert: HighErrorRate
        expr: rate(http_requests_total{job="seatmaster-backend",status=~"5.."}[5m]) / rate(http_requests_total{job="seatmaster-backend"}[5m]) > 0.05
        for: 3m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "后端错误率过高"
          description: "5xx错误率超过5%，当前值: {{ $value | humanizePercentage }}"

      # JVM堆内存使用率过高
      - alert: HighJVMHeapUsage
        expr: jvm_memory_used_bytes{area="heap"} / jvm_memory_max_bytes{area="heap"} > 0.85
        for: 5m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "JVM堆内存使用率过高"
          description: "堆内存使用率超过85%，当前值: {{ $value | humanizePercentage }}"

      # GC时间过长
      - alert: HighGCTime
        expr: rate(jvm_gc_collection_seconds_sum[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: backend
        annotations:
          summary: "GC时间过长"
          description: "GC时间占比超过10%，当前值: {{ $value | humanizePercentage }}"

  # 系统资源告警
  - name: system-resources
    rules:
      # CPU使用率过高
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率过高"
          description: "CPU使用率超过80%，当前值: {{ $value }}%"

      # 内存使用率过高
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "内存使用率超过85%，当前值: {{ $value }}%"

      # 磁盘使用率过高
      - alert: HighDiskUsage
        expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "磁盘使用率过高"
          description: "磁盘 {{ $labels.mountpoint }} 使用率超过85%，当前值: {{ $value }}%"

      # 磁盘空间严重不足
      - alert: CriticalDiskUsage
        expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 95
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "磁盘空间严重不足"
          description: "磁盘 {{ $labels.mountpoint }} 使用率超过95%，当前值: {{ $value }}%"

      # 系统负载过高
      - alert: HighSystemLoad
        expr: node_load15 / count(node_cpu_seconds_total{mode="idle"}) by (instance) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "系统负载过高"
          description: "15分钟平均负载超过CPU核心数的2倍，当前值: {{ $value }}"

  # 数据库告警
  - name: database
    rules:
      # MySQL服务下线
      - alert: MySQLDown
        expr: mysql_up == 0
        for: 1m
        labels:
          severity: critical
          service: mysql
        annotations:
          summary: "MySQL服务下线"
          description: "MySQL数据库服务不可用"

      # MySQL连接数过多
      - alert: MySQLTooManyConnections
        expr: mysql_global_status_threads_connected / mysql_global_variables_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
          service: mysql
        annotations:
          summary: "MySQL连接数过多"
          description: "MySQL连接数超过最大连接数的80%，当前值: {{ $value | humanizePercentage }}"

      # MySQL慢查询过多
      - alert: MySQLSlowQueries
        expr: rate(mysql_global_status_slow_queries[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: mysql
        annotations:
          summary: "MySQL慢查询过多"
          description: "慢查询频率过高，当前值: {{ $value }}/秒"

      # MySQL复制延迟
      - alert: MySQLReplicationLag
        expr: mysql_slave_lag_seconds > 30
        for: 1m
        labels:
          severity: warning
          service: mysql
        annotations:
          summary: "MySQL复制延迟"
          description: "MySQL主从复制延迟超过30秒，当前值: {{ $value }}秒"

  # 网络和HTTP告警
  - name: network-http
    rules:
      # HTTP端点不可用
      - alert: HTTPEndpointDown
        expr: probe_success{job="blackbox-http"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "HTTP端点不可用"
          description: "{{ $labels.instance }} 端点检查失败"

      # HTTP响应时间过长
      - alert: HTTPSlowResponse
        expr: probe_duration_seconds{job="blackbox-http"} > 5
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "HTTP响应时间过长"
          description: "{{ $labels.instance }} 响应时间超过5秒，当前值: {{ $value }}秒"

      # Nginx错误率过高
      - alert: NginxHighErrorRate
        expr: rate(nginx_http_requests_total{status=~"4..|5.."}[5m]) / rate(nginx_http_requests_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: nginx
        annotations:
          summary: "Nginx错误率过高"
          description: "Nginx 4xx/5xx错误率超过10%，当前值: {{ $value | humanizePercentage }}"

  # 业务指标告警
  - name: business-metrics
    rules:
      # 预约失败率过高
      - alert: HighReservationFailureRate
        expr: rate(seatmaster_reservation_failures_total[5m]) / rate(seatmaster_reservation_attempts_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "预约失败率过高"
          description: "预约失败率超过10%，当前值: {{ $value | humanizePercentage }}"

      # 活跃用户数异常下降
      - alert: ActiveUsersDropped
        expr: seatmaster_active_users < 10
        for: 10m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "活跃用户数异常下降"
          description: "活跃用户数低于10，当前值: {{ $value }}"

      # 任务执行失败率过高
      - alert: HighTaskFailureRate
        expr: rate(seatmaster_task_failures_total[5m]) / rate(seatmaster_task_executions_total[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
          service: business
        annotations:
          summary: "任务执行失败率过高"
          description: "任务执行失败率超过5%，当前值: {{ $value | humanizePercentage }}"

  # 安全告警
  - name: security
    rules:
      # 异常登录尝试
      - alert: SuspiciousLoginAttempts
        expr: rate(seatmaster_login_failures_total[5m]) > 5
        for: 2m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "异常登录尝试"
          description: "登录失败频率过高，可能存在暴力破解攻击，当前值: {{ $value }}/秒"

      # API调用频率异常
      - alert: HighAPICallRate
        expr: rate(http_requests_total{job="seatmaster-backend"}[1m]) > 100
        for: 5m
        labels:
          severity: warning
          service: security
        annotations:
          summary: "API调用频率异常"
          description: "API调用频率异常高，可能存在攻击，当前值: {{ $value }}/秒"
