# 座位预订系统启动脚本使用指南

## 📋 概述

本项目提供了一套完整的一键启动脚本，可以快速启动和管理座位预订系统的前后端服务。

## 🚀 快速开始

### 1. 一键启动所有服务

```bash
./start-all.sh
```

这个命令会：
- ✅ 检查运行环境（Java、Maven、Node.js、npm）
- ✅ 检查项目结构和配置文件
- ✅ 停止现有服务（避免端口冲突）
- ✅ 检查端口占用情况（8081、3000）
- ✅ 自动安装前端依赖（如果需要）
- ✅ 启动后端服务（Spring Boot）
- ✅ 启动前端服务（Vue.js）
- ✅ 等待服务启动完成
- ✅ 持续监控服务状态

### 2. 停止所有服务

```bash
./stop-all.sh
```

或者使用：

```bash
./start-all.sh stop
```

### 3. 检查服务状态

```bash
./status.sh
```

或者使用：

```bash
./start-all.sh status
```

### 4. 查看帮助信息

```bash
./start-all.sh help
```

## 📁 文件结构

```
seatMaster/
├── start-all.sh          # 主启动脚本
├── stop-all.sh           # 停止脚本
├── status.sh             # 状态检查脚本
├── logs/                 # 日志目录
│   ├── backend.log       # 后端日志
│   └── frontend.log      # 前端日志
├── pids/                 # 进程ID目录
│   ├── backend.pid       # 后端进程ID
│   └── frontend.pid      # 前端进程ID
├── backend/              # 后端项目
└── frontend/             # 前端项目
```

## 🔧 环境要求

### 必需软件

- **Java 11+**: 运行Spring Boot后端
- **Maven 3.6+**: 构建和运行后端
- **Node.js 14+**: 运行Vue.js前端
- **npm 6+**: 管理前端依赖

### 检查环境

脚本会自动检查环境，也可以手动检查：

```bash
java -version
mvn -version
node --version
npm --version
```

## 🌐 服务地址

启动成功后，可以通过以下地址访问：

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8081
- **API文档**: http://localhost:8081/swagger-ui.html（如果配置了）

## 📊 监控和日志

### 实时查看日志

```bash
# 查看后端日志
tail -f logs/backend.log

# 查看前端日志
tail -f logs/frontend.log

# 同时查看两个日志
tail -f logs/backend.log logs/frontend.log
```

### 状态检查

`status.sh` 脚本提供详细的状态信息：

- 🖥️ 系统信息
- 🔌 端口状态
- 🔄 进程状态
- 🌐 服务可访问性
- 📝 日志文件状态
- 🗄️ 数据库状态
- 📋 状态总结

## 🛠️ 故障排除

### 常见问题

#### 1. 端口被占用

**错误**: `端口8081/3000被占用`

**解决方案**:
```bash
# 查看端口占用
netstat -tlnp | grep 8081
netstat -tlnp | grep 3000

# 停止占用进程
./stop-all.sh

# 或手动杀死进程
kill -9 <PID>
```

#### 2. 环境依赖缺失

**错误**: `java/mvn/node/npm 未安装`

**解决方案**:
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install openjdk-11-jdk maven nodejs npm

# CentOS/RHEL
sudo yum install java-11-openjdk maven nodejs npm
```

#### 3. 前端依赖安装失败

**错误**: `前端依赖安装失败`

**解决方案**:
```bash
cd frontend
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

#### 4. 数据库连接失败

**错误**: `数据库连接失败`

**解决方案**:
```bash
# 检查MySQL服务
sudo systemctl status mysql

# 启动MySQL服务
sudo systemctl start mysql

# 检查数据库配置
cat backend/src/main/resources/application.yml
```

#### 5. 服务启动超时

**错误**: `服务启动超时`

**解决方案**:
```bash
# 查看详细日志
tail -50 logs/backend.log
tail -50 logs/frontend.log

# 手动启动测试
cd backend && mvn spring-boot:run
cd frontend && npm run serve
```

### 日志分析

#### 后端常见错误

- **数据库连接错误**: 检查MySQL服务和配置
- **端口占用**: 检查8081端口是否被其他程序占用
- **依赖缺失**: 运行 `mvn clean install`

#### 前端常见错误

- **依赖安装失败**: 删除node_modules重新安装
- **端口占用**: 检查3000端口是否被占用
- **代理错误**: 检查vue.config.js中的代理配置

## 🔄 高级用法

### 自定义配置

#### 修改端口

**后端端口** (默认8081):
```yaml
# backend/src/main/resources/application.yml
server:
  port: 8081
```

**前端端口** (默认3000):
```javascript
// frontend/vue.config.js
module.exports = {
  devServer: {
    port: 3000
  }
}
```

#### 修改代理配置

```javascript
// frontend/vue.config.js
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:8081',
        changeOrigin: true
      }
    }
  }
}
```

### 生产环境部署

对于生产环境，建议：

1. **使用进程管理器**: PM2、systemd等
2. **配置反向代理**: nginx、Apache等
3. **使用生产构建**: `npm run build`
4. **配置HTTPS**: SSL证书
5. **数据库优化**: 连接池、索引等

## 📞 技术支持

如果遇到问题：

1. 首先运行 `./status.sh` 检查状态
2. 查看日志文件了解详细错误信息
3. 参考本文档的故障排除部分
4. 检查项目的README.md文件

## 🔄 更新日志

### v1.0.0 (2025-07-09)
- ✨ 初始版本发布
- ✅ 支持一键启动前后端服务
- ✅ 完整的环境检查和错误处理
- ✅ 详细的状态监控和日志管理
- ✅ 优雅的服务停止和清理
- ✅ 彩色输出和用户友好的界面
