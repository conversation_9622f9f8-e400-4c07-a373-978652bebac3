# SeatMaster 生产环境 Docker Compose 配置
version: '3.8'

services:
  # ================================
  # 数据库服务
  # ================================
  mysql:
    image: mysql:8.0
    container_name: seatmaster-mysql
    restart: unless-stopped
    
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-root5869087}
      MYSQL_DATABASE: ${DB_NAME:-seat_reservation}
      MYSQL_USER: ${DB_USERNAME:-seatmaster_app}
      MYSQL_PASSWORD: ${DB_PASSWORD:-your_secure_password}
      TZ: Asia/Shanghai
    
    ports:
      - "${DB_PORT:-3306}:3306"
    
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database:/docker-entrypoint-initdb.d:ro
      - ./mysql/conf.d:/etc/mysql/conf.d:ro
      - ./logs/mysql:/var/log/mysql
    
    command: >
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --default-authentication-plugin=mysql_native_password
      --innodb-buffer-pool-size=512M
      --innodb-log-file-size=128M
      --max-connections=200
      --slow-query-log=1
      --slow-query-log-file=/var/log/mysql/slow.log
      --long-query-time=2
    
    networks:
      - seatmaster-network
    
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${DB_ROOT_PASSWORD:-root5869087}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # ================================
  # Redis缓存服务
  # ================================
  redis:
    image: redis:7-alpine
    container_name: seatmaster-redis
    restart: unless-stopped
    
    ports:
      - "${REDIS_PORT:-6379}:6379"
    
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    
    command: redis-server /usr/local/etc/redis/redis.conf
    
    networks:
      - seatmaster-network
    
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ================================
  # 后端服务
  # ================================
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: seatmaster-backend
    restart: unless-stopped
    
    environment:
      # Spring配置
      SPRING_PROFILES_ACTIVE: prod
      SERVER_PORT: 8081
      
      # 数据库配置
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: ${DB_NAME:-seat_reservation}
      DB_USERNAME: ${DB_USERNAME:-seatmaster_app}
      DB_PASSWORD: ${DB_PASSWORD:-your_secure_password}
      DB_SSL: false
      
      # 连接池配置
      DB_MAX_POOL_SIZE: 50
      DB_MIN_IDLE: 20
      DB_CONNECTION_TIMEOUT: 30000
      
      # Redis配置
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      
      # JWT配置
      JWT_SECRET: ${JWT_SECRET:-seatmaster-production-secret-key-2024-very-long-and-secure}
      JWT_EXPIRATION: 86400000
      
      # 日志配置
      LOG_PATH: /app/logs
      
      # JVM配置
      JAVA_OPTS: >
        -Xms1g -Xmx2g
        -XX:+UseG1GC
        -XX:MaxGCPauseMillis=200
        -XX:+UseStringDeduplication
        -XX:+OptimizeStringConcat
        -Djava.awt.headless=true
        -Dfile.encoding=UTF-8
        -Duser.timezone=Asia/Shanghai
    
    ports:
      - "${BACKEND_PORT:-8081}:8081"
    
    volumes:
      - ./logs/backend:/app/logs
      - ./uploads:/app/uploads
      - ./temp:/app/temp
    
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    
    networks:
      - seatmaster-network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

  # ================================
  # 前端服务
  # ================================
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: seatmaster-frontend
    restart: unless-stopped
    
    ports:
      - "${FRONTEND_PORT:-80}:80"
    
    volumes:
      - ./logs/nginx:/var/log/nginx
    
    depends_on:
      backend:
        condition: service_healthy
    
    networks:
      - seatmaster-network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ================================
  # Nginx反向代理（可选，用于生产环境）
  # ================================
  nginx:
    image: nginx:1.21-alpine
    container_name: seatmaster-nginx
    restart: unless-stopped
    
    ports:
      - "80:80"
      - "443:443"
    
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
      - ./static:/var/www/static:ro
    
    depends_on:
      - frontend
      - backend
    
    networks:
      - seatmaster-network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    
    profiles:
      - production

# ================================
# 网络配置
# ================================
networks:
  seatmaster-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ================================
# 数据卷配置
# ================================
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
