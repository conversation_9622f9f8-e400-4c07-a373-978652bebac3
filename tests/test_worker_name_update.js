const axios = require('axios');

async function testWorkerNameUpdate() {
    const baseURL = 'http://localhost:8081';
    
    try {
        console.log('=== 测试副服务器名字修改功能 ===\n');
        
        // 1. 登录获取token
        console.log('1. 登录获取认证token...');
        const loginResponse = await axios.post(`${baseURL}/api/auth/login`, {
            username: 'admin',
            password: 'admin123'
        });
        
        if (loginResponse.data.code !== 200) {
            console.log('❌ 登录失败:', loginResponse.data.message);
            return;
        }
        
        const token = loginResponse.data.data.token;
        console.log('✅ 登录成功\n');
        
        // 2. 获取副服务器列表
        console.log('2. 获取副服务器列表...');
        const listResponse = await axios.get(`${baseURL}/api/admin/worker-management/servers`, {
            headers: { 'Authorization': `Bearer ${token}` }
        });
        
        if (listResponse.data.code === 200 && listResponse.data.data.length > 0) {
            const server = listResponse.data.data[0];
            console.log(`找到副服务器: ${server.workerId} - ${server.name}`);
            console.log(`当前配置: host=${server.host}, port=${server.port}\n`);
            
            // 3. 测试更新名字
            console.log('3. 测试更新副服务器名字...');
            const newName = `测试服务器-${Date.now()}`;
            const updateData = {
                name: newName,
                host: server.host,
                port: server.port,
                maxConcurrentTasks: server.maxConcurrentTasks
            };
            
            console.log('更新数据:', JSON.stringify(updateData, null, 2));
            
            const updateResponse = await axios.put(
                `${baseURL}/api/admin/worker-management/servers/${server.workerId}`,
                updateData,
                {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                }
            );
            
            if (updateResponse.data.code === 200) {
                console.log('✅ 更新成功!');
                console.log('新名字:', updateResponse.data.data.name);
                
                // 4. 验证更新结果
                console.log('\n4. 验证更新结果...');
                const verifyResponse = await axios.get(`${baseURL}/api/admin/worker-management/servers`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                const updatedServer = verifyResponse.data.data.find(s => s.workerId === server.workerId);
                if (updatedServer && updatedServer.name === newName) {
                    console.log('✅ 验证成功: 名字已正确更新');
                } else {
                    console.log('❌ 验证失败: 名字未正确更新');
                }
                
            } else {
                console.log('❌ 更新失败:', updateResponse.data.message);
            }
            
        } else {
            console.log('❌ 没有找到副服务器');
        }
        
    } catch (error) {
        console.log('❌ 测试失败:', error.message);
        if (error.response) {
            console.log('状态码:', error.response.status);
            console.log('错误详情:', JSON.stringify(error.response.data, null, 2));
        }
    }
}

// 运行测试
testWorkerNameUpdate();
