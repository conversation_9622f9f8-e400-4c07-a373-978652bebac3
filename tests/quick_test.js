const axios = require('axios');

async function quickTest() {
    try {
        console.log('测试后端服务连接...');
        
        // 测试健康检查
        const healthResponse = await axios.get('http://localhost:8081/api/health');
        console.log('✅ 后端服务正常运行');
        
        // 测试登录
        const loginResponse = await axios.post('http://localhost:8081/api/auth/login', {
            username: 'admin',
            password: 'admin123'
        });
        
        if (loginResponse.data.code === 200) {
            console.log('✅ 登录成功');
            
            const token = loginResponse.data.data.token;
            
            // 测试获取副服务器列表
            const serversResponse = await axios.get('http://localhost:8081/api/admin/worker-management/servers', {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            
            if (serversResponse.data.code === 200) {
                console.log('✅ 获取副服务器列表成功');
                console.log('副服务器数量:', serversResponse.data.data.length);
                
                if (serversResponse.data.data.length > 0) {
                    const server = serversResponse.data.data[0];
                    console.log('第一个副服务器:', {
                        id: server.workerId,
                        name: server.name,
                        host: server.host,
                        port: server.port,
                        status: server.status
                    });
                    
                    // 测试更新副服务器名字
                    const updateData = {
                        name: '测试更新-' + Date.now(),
                        host: server.host,
                        port: server.port,
                        maxConcurrentTasks: server.maxConcurrentTasks
                    };
                    
                    try {
                        const updateResponse = await axios.put(
                            `http://localhost:8081/api/admin/worker-management/servers/${server.workerId}`,
                            updateData,
                            {
                                headers: {
                                    'Authorization': `Bearer ${token}`,
                                    'Content-Type': 'application/json'
                                }
                            }
                        );
                        
                        if (updateResponse.data.code === 200) {
                            console.log('✅ 副服务器名字更新成功！');
                            console.log('新名字:', updateResponse.data.data.name);
                        } else {
                            console.log('❌ 更新失败:', updateResponse.data.message);
                        }
                        
                    } catch (updateError) {
                        console.log('❌ 更新请求失败:', updateError.response?.status);
                        console.log('错误详情:', JSON.stringify(updateError.response?.data, null, 2));
                    }
                }
                
            } else {
                console.log('❌ 获取副服务器列表失败:', serversResponse.data.message);
            }
            
        } else {
            console.log('❌ 登录失败:', loginResponse.data.message);
        }
        
    } catch (error) {
        console.log('❌ 连接失败:', error.message);
        if (error.code === 'ECONNREFUSED') {
            console.log('后端服务未启动或端口8081不可访问');
        }
    }
}

quickTest();
