# SeatMaster 测试工具

本目录包含SeatMaster系统的测试工具和脚本。

## 测试工具说明

### 1. API测试
- 使用Postman或类似工具测试API接口
- 参考 `../API测试集合.md` 中的接口文档

### 2. 数据库测试
- 数据库相关测试脚本位于 `../database/` 目录
- 包含结构检查、数据验证等功能

### 3. 功能测试
- 前端功能测试通过浏览器访问系统进行
- 后端功能测试通过API调用进行

## 快速测试命令

### 检查服务状态
```bash
# 检查后端服务
curl http://localhost:8081/api/health

# 检查前端服务  
curl http://localhost:3000

# 检查数据库连接
mysql -u root -proot -e "USE seat_reservation; SHOW TABLES;"
```

### 常用测试场景
1. **用户注册登录测试**
2. **预约功能测试**
3. **副服务器管理测试**
4. **执行日志查看测试**

## 注意事项
- 测试前确保所有服务正常运行
- 测试数据请使用测试账号，避免影响生产数据
- 测试完成后及时清理测试数据
