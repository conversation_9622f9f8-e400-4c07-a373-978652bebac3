const axios = require('axios');

async function testAnnouncementModule() {
    const baseURL = 'http://localhost:8081';
    
    console.log('=== SeatMaster 公告模块测试 ===\n');
    
    try {
        // 1. 测试后端服务连接
        console.log('1. 测试后端服务连接...');
        try {
            const healthResponse = await axios.get(`${baseURL}/api/health`);
            console.log('✅ 后端服务正常运行\n');
        } catch (error) {
            console.log('❌ 后端服务未启动，请先启动后端服务');
            console.log('启动命令: cd backend && mvn spring-boot:run\n');
            return;
        }
        
        // 2. 管理员登录
        console.log('2. 管理员登录...');
        const loginResponse = await axios.post(`${baseURL}/api/auth/login`, {
            username: 'admin',
            password: 'admin123'
        });
        
        if (loginResponse.data.code !== 200) {
            console.log('❌ 管理员登录失败:', loginResponse.data.message);
            return;
        }
        
        const token = loginResponse.data.data.token;
        console.log('✅ 管理员登录成功\n');
        
        // 3. 测试创建公告
        console.log('3. 测试创建公告...');
        const createData = {
            title: '测试公告 - ' + new Date().toLocaleString(),
            content: '<h3>这是一个测试公告</h3><p>公告模块功能测试中...</p><ul><li>支持HTML格式</li><li>支持优先级设置</li><li>支持目标用户选择</li></ul>',
            summary: '这是一个用于测试公告模块功能的测试公告',
            priority: 100,
            enabled: true,
            popup: true,
            targetUsers: 'ALL'
        };
        
        try {
            const createResponse = await axios.post(`${baseURL}/api/admin/announcements`, createData, {
                headers: { 'Authorization': `Bearer ${token}` }
            });
            
            if (createResponse.data.code === 200) {
                console.log('✅ 公告创建成功');
                console.log('   公告ID:', createResponse.data.data.id);
                console.log('   公告标题:', createResponse.data.data.title);
                
                const announcementId = createResponse.data.data.id;
                
                // 4. 测试获取公告列表
                console.log('\n4. 测试获取公告列表...');
                const listResponse = await axios.get(`${baseURL}/api/admin/announcements`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (listResponse.data.code === 200) {
                    console.log('✅ 获取公告列表成功');
                    console.log('   公告总数:', listResponse.data.data.total);
                    console.log('   当前页公告数:', listResponse.data.data.records.length);
                } else {
                    console.log('❌ 获取公告列表失败:', listResponse.data.message);
                }
                
                // 5. 测试更新公告
                console.log('\n5. 测试更新公告...');
                const updateData = {
                    title: createData.title + ' (已更新)',
                    content: createData.content + '<p><strong>更新时间:</strong> ' + new Date().toLocaleString() + '</p>',
                    summary: createData.summary + ' (已更新)',
                    priority: 90,
                    enabled: true,
                    popup: true,
                    targetUsers: 'ALL'
                };
                
                const updateResponse = await axios.put(`${baseURL}/api/admin/announcements/${announcementId}`, updateData, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (updateResponse.data.code === 200) {
                    console.log('✅ 公告更新成功');
                    console.log('   新标题:', updateResponse.data.data.title);
                } else {
                    console.log('❌ 公告更新失败:', updateResponse.data.message);
                }
                
                // 6. 测试用户获取公告
                console.log('\n6. 测试用户获取公告...');
                const userAnnouncementsResponse = await axios.get(`${baseURL}/api/user/announcements`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (userAnnouncementsResponse.data.code === 200) {
                    console.log('✅ 用户获取公告成功');
                    console.log('   活跃公告数:', userAnnouncementsResponse.data.data.length);
                } else {
                    console.log('❌ 用户获取公告失败:', userAnnouncementsResponse.data.message);
                }
                
                // 7. 测试获取未读弹窗公告
                console.log('\n7. 测试获取未读弹窗公告...');
                const unreadResponse = await axios.get(`${baseURL}/api/user/announcements/unread-popup`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (unreadResponse.data.code === 200) {
                    console.log('✅ 获取未读弹窗公告成功');
                    console.log('   未读公告数:', unreadResponse.data.data.length);
                } else {
                    console.log('❌ 获取未读弹窗公告失败:', unreadResponse.data.message);
                }
                
                // 8. 测试标记公告为已读
                console.log('\n8. 测试标记公告为已读...');
                const markReadResponse = await axios.post(`${baseURL}/api/user/announcements/${announcementId}/mark-read`, {}, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (markReadResponse.data.code === 200) {
                    console.log('✅ 标记公告已读成功');
                } else {
                    console.log('❌ 标记公告已读失败:', markReadResponse.data.message);
                }
                
                // 9. 测试切换公告状态
                console.log('\n9. 测试切换公告状态...');
                const toggleResponse = await axios.put(`${baseURL}/api/admin/announcements/${announcementId}/toggle-status`, 
                    { enabled: false }, 
                    { headers: { 'Authorization': `Bearer ${token}` } }
                );
                
                if (toggleResponse.data.code === 200) {
                    console.log('✅ 切换公告状态成功');
                } else {
                    console.log('❌ 切换公告状态失败:', toggleResponse.data.message);
                }
                
                // 10. 清理测试数据
                console.log('\n10. 清理测试数据...');
                const deleteResponse = await axios.delete(`${baseURL}/api/admin/announcements/${announcementId}`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (deleteResponse.data.code === 200) {
                    console.log('✅ 测试公告删除成功');
                } else {
                    console.log('❌ 测试公告删除失败:', deleteResponse.data.message);
                }
                
            } else {
                console.log('❌ 公告创建失败:', createResponse.data.message);
            }
            
        } catch (error) {
            console.log('❌ 公告API测试失败:', error.response?.data?.message || error.message);
        }
        
        console.log('\n=== 测试完成 ===');
        console.log('如果所有测试都通过，说明公告模块功能正常！');
        
    } catch (error) {
        console.log('❌ 测试过程中发生错误:', error.message);
    }
}

// 运行测试
testAnnouncementModule();
