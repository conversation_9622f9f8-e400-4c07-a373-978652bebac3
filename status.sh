#!/bin/bash

# 座位预订系统状态检查脚本
# 作者：Augment Agent
# 版本：1.0.0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_ROOT=$(pwd)
PID_DIR="$PROJECT_ROOT/pids"
LOG_DIR="$PROJECT_ROOT/logs"
BACKEND_PID="$PID_DIR/backend.pid"
FRONTEND_PID="$PID_DIR/frontend.pid"
BACKEND_LOG="$LOG_DIR/backend.log"
FRONTEND_LOG="$LOG_DIR/frontend.log"

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_success() {
    print_message "$GREEN" "✅ $1"
}

print_error() {
    print_message "$RED" "❌ $1"
}

print_warning() {
    print_message "$YELLOW" "⚠️  $1"
}

print_info() {
    print_message "$BLUE" "ℹ️  $1"
}

print_header() {
    print_message "$CYAN" "$1"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
        return 0  # 端口被占用
    else
        return 1  # 端口空闲
    fi
}

# 检查URL是否可访问
check_url() {
    local url=$1
    local timeout=5
    if curl -s --max-time $timeout "$url" > /dev/null 2>&1; then
        return 0  # 可访问
    else
        return 1  # 不可访问
    fi
}

# 获取进程信息
get_process_info() {
    local pid=$1
    if kill -0 "$pid" 2>/dev/null; then
        local process_info=$(ps -p "$pid" -o pid,ppid,cmd --no-headers 2>/dev/null)
        echo "$process_info"
        return 0
    else
        return 1
    fi
}

echo ""
echo -e "${CYAN}================================================${NC}"
echo -e "${CYAN}    📊 座位预订系统状态检查 v1.0.0${NC}"
echo -e "${CYAN}================================================${NC}"
echo ""

# 1. 系统信息
print_header "🖥️  系统信息"
echo "  时间: $(date)"
echo "  主机: $(hostname)"
echo "  用户: $(whoami)"
echo "  工作目录: $PROJECT_ROOT"
echo ""

# 2. 端口状态
print_header "🔌 端口状态"
if check_port 8081; then
    print_success "后端端口 8081 正在监听"
    local backend_process=$(netstat -tlnp 2>/dev/null | grep ":8081 " | awk '{print $7}')
    print_info "  进程: $backend_process"
else
    print_error "后端端口 8081 未监听"
fi

if check_port 3000; then
    print_success "前端端口 3000 正在监听"
    local frontend_process=$(netstat -tlnp 2>/dev/null | grep ":3000 " | awk '{print $7}')
    print_info "  进程: $frontend_process"
else
    print_error "前端端口 3000 未监听"
fi
echo ""

# 3. 进程状态
print_header "🔄 进程状态"

# 检查后端进程
if [ -f "$BACKEND_PID" ]; then
    backend_pid=$(cat "$BACKEND_PID")
    if process_info=$(get_process_info "$backend_pid"); then
        print_success "后端服务运行中 (PID: $backend_pid)"
        print_info "  进程信息: $process_info"
    else
        print_error "后端PID文件存在但进程未运行 (PID: $backend_pid)"
        print_warning "  建议删除过期的PID文件: $BACKEND_PID"
    fi
else
    print_warning "未找到后端PID文件: $BACKEND_PID"
fi

# 检查前端进程
if [ -f "$FRONTEND_PID" ]; then
    frontend_pid=$(cat "$FRONTEND_PID")
    if process_info=$(get_process_info "$frontend_pid"); then
        print_success "前端服务运行中 (PID: $frontend_pid)"
        print_info "  进程信息: $process_info"
    else
        print_error "前端PID文件存在但进程未运行 (PID: $frontend_pid)"
        print_warning "  建议删除过期的PID文件: $FRONTEND_PID"
    fi
else
    print_warning "未找到前端PID文件: $FRONTEND_PID"
fi
echo ""

# 4. 服务可访问性
print_header "🌐 服务可访问性"

# 检查后端API
if check_url "http://localhost:8081/api/admin/worker-management/statistics"; then
    print_success "后端API可访问: http://localhost:8081"
else
    print_error "后端API不可访问: http://localhost:8081"
fi

# 检查前端页面
if check_url "http://localhost:3000"; then
    print_success "前端页面可访问: http://localhost:3000"
else
    print_error "前端页面不可访问: http://localhost:3000"
fi
echo ""

# 5. 日志文件状态
print_header "📝 日志文件状态"

if [ -f "$BACKEND_LOG" ]; then
    local backend_log_size=$(du -h "$BACKEND_LOG" | cut -f1)
    local backend_log_lines=$(wc -l < "$BACKEND_LOG")
    print_success "后端日志: $BACKEND_LOG"
    print_info "  大小: $backend_log_size, 行数: $backend_log_lines"
    
    # 显示最近的错误
    local recent_errors=$(tail -100 "$BACKEND_LOG" | grep -i "error\|exception" | tail -3)
    if [ -n "$recent_errors" ]; then
        print_warning "  最近的错误:"
        echo "$recent_errors" | while read line; do
            print_info "    $line"
        done
    fi
else
    print_warning "后端日志文件不存在: $BACKEND_LOG"
fi

if [ -f "$FRONTEND_LOG" ]; then
    local frontend_log_size=$(du -h "$FRONTEND_LOG" | cut -f1)
    local frontend_log_lines=$(wc -l < "$FRONTEND_LOG")
    print_success "前端日志: $FRONTEND_LOG"
    print_info "  大小: $frontend_log_size, 行数: $frontend_log_lines"
    
    # 显示最近的错误
    local recent_errors=$(tail -100 "$FRONTEND_LOG" | grep -i "error\|failed" | tail -3)
    if [ -n "$recent_errors" ]; then
        print_warning "  最近的错误:"
        echo "$recent_errors" | while read line; do
            print_info "    $line"
        done
    fi
else
    print_warning "前端日志文件不存在: $FRONTEND_LOG"
fi
echo ""

# 6. 数据库连接状态
print_header "🗄️  数据库状态"
if mysql -u root -proot5869087 -e "USE seat_reservation; SELECT 'Database connection OK' as status;" 2>/dev/null | grep -q "Database connection OK"; then
    print_success "数据库连接正常"
    
    # 检查Worker服务器数量
    worker_count=$(mysql -u root -proot5869087 -e "USE seat_reservation; SELECT COUNT(*) FROM worker_servers WHERE status='ONLINE';" 2>/dev/null | tail -1)
    if [ -n "$worker_count" ] && [ "$worker_count" -gt 0 ]; then
        print_info "  在线Worker数量: $worker_count"
    else
        print_warning "  没有在线的Worker服务器"
    fi
else
    print_error "数据库连接失败"
fi
echo ""

# 7. 总结
print_header "📋 状态总结"

all_good=true

if check_port 8081 && check_url "http://localhost:8081/api/admin/worker-management/statistics"; then
    print_success "后端服务: 正常运行"
else
    print_error "后端服务: 异常"
    all_good=false
fi

if check_port 3000 && check_url "http://localhost:3000"; then
    print_success "前端服务: 正常运行"
else
    print_error "前端服务: 异常"
    all_good=false
fi

if [ "$all_good" = true ]; then
    echo ""
    print_success "🎉 所有服务运行正常！"
    echo ""
    print_info "访问地址:"
    print_info "  前端: http://localhost:3000"
    print_info "  后端: http://localhost:8081"
else
    echo ""
    print_error "⚠️  部分服务异常，请检查日志文件"
    echo ""
    print_info "故障排除:"
    print_info "  1. 查看日志: tail -f $BACKEND_LOG"
    print_info "  2. 查看日志: tail -f $FRONTEND_LOG"
    print_info "  3. 重启服务: ./start-all.sh"
fi

echo ""
