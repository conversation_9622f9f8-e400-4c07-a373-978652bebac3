# SeatMaster MySQL 生产环境配置

[mysql]
default-character-set = utf8mb4

[mysqld]
# ================================
# 基础配置
# ================================
user = mysql
port = 3306
bind-address = 0.0.0.0
socket = /var/run/mysqld/mysqld.sock
pid-file = /var/run/mysqld/mysqld.pid
datadir = /var/lib/mysql
tmpdir = /tmp

# ================================
# 字符集配置
# ================================
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# ================================
# 连接配置
# ================================
max_connections = 200
max_connect_errors = 100000
max_user_connections = 180
back_log = 100
wait_timeout = 28800
interactive_timeout = 28800

# ================================
# 缓冲区配置
# ================================
# InnoDB缓冲池大小（建议设为可用内存的70-80%）
innodb_buffer_pool_size = 512M
innodb_buffer_pool_instances = 4

# 查询缓存
query_cache_type = 1
query_cache_size = 64M
query_cache_limit = 2M

# 其他缓冲区
key_buffer_size = 32M
read_buffer_size = 2M
read_rnd_buffer_size = 8M
sort_buffer_size = 8M
join_buffer_size = 8M
thread_cache_size = 16

# ================================
# InnoDB配置
# ================================
innodb_file_per_table = 1
innodb_flush_log_at_trx_commit = 2
innodb_log_buffer_size = 16M
innodb_log_file_size = 128M
innodb_log_files_in_group = 2
innodb_max_dirty_pages_pct = 75
innodb_lock_wait_timeout = 50
innodb_io_capacity = 200
innodb_io_capacity_max = 400

# ================================
# 日志配置
# ================================
# 错误日志
log-error = /var/log/mysql/error.log

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 1
log_slow_admin_statements = 1

# 二进制日志（用于复制和备份）
log-bin = /var/log/mysql/mysql-bin
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M

# 通用查询日志（生产环境通常关闭）
# general_log = 1
# general_log_file = /var/log/mysql/general.log

# ================================
# 安全配置
# ================================
# 禁用本地文件加载
local_infile = 0

# 禁用符号链接
symbolic-links = 0

# 设置SQL模式
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION

# ================================
# 性能优化
# ================================
# 表缓存
table_open_cache = 2000
table_definition_cache = 1400

# 临时表
tmp_table_size = 64M
max_heap_table_size = 64M

# MyISAM配置
myisam_sort_buffer_size = 64M
myisam_max_sort_file_size = 10G
myisam_repair_threads = 1

# ================================
# 复制配置（如果需要主从复制）
# ================================
# server-id = 1
# log-slave-updates = 1
# relay-log = /var/log/mysql/relay-bin
# relay-log-index = /var/log/mysql/relay-bin.index

[mysqldump]
quick
max_allowed_packet = 16M

[mysql]
no-auto-rehash

[myisamchk]
key_buffer_size = 256M
sort_buffer_size = 256M
read_buffer = 2M
write_buffer = 2M

[mysqlhotcopy]
interactive-timeout
