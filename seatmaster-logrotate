# SeatMaster 日志轮转配置
# 放置在 /etc/logrotate.d/seatmaster

/root/seatMaster/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        # 重新加载PM2日志
        /usr/local/bin/pm2 reloadLogs 2>/dev/null || true
        # 通知systemd重新打开日志文件
        /bin/systemctl reload-or-restart seatmaster-backend 2>/dev/null || true
    endscript
}

# PM2日志特殊处理
/root/.pm2/logs/*.log {
    daily
    missingok
    rotate 15
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        /usr/local/bin/pm2 reloadLogs 2>/dev/null || true
    endscript
}
