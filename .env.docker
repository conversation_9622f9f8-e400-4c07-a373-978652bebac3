# SeatMaster Docker Compose 环境变量配置
# 复制此文件为 .env 并根据实际环境修改配置

# ================================
# 基础配置
# ================================
COMPOSE_PROJECT_NAME=seatmaster
COMPOSE_FILE=docker-compose.yml

# ================================
# 端口配置
# ================================
FRONTEND_PORT=3000
BACKEND_PORT=8081
DB_PORT=3306
REDIS_PORT=6379

# ================================
# 数据库配置
# ================================
DB_ROOT_PASSWORD=root5869087
DB_NAME=seat_reservation
DB_USERNAME=seatmaster_app
DB_PASSWORD=your_secure_password_here

# ================================
# Redis配置
# ================================
REDIS_PASSWORD=

# ================================
# 安全配置
# ================================
JWT_SECRET=seatmaster-production-secret-key-2024-very-long-and-secure-please-change-this

# ================================
# 域名配置（生产环境）
# ================================
DOMAIN_NAME=yourdomain.com
SSL_EMAIL=<EMAIL>

# ================================
# 备份配置
# ================================
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# ================================
# 监控配置
# ================================
ENABLE_MONITORING=true
GRAFANA_PASSWORD=admin123
PROMETHEUS_RETENTION=15d
