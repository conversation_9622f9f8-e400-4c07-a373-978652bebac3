// 测试学校筛选功能的脚本
const axios = require('axios');

const BASE_URL = 'http://localhost:8081/api';

// 测试获取学校列表
async function testGetSchools() {
  try {
    console.log('测试获取学校列表...');
    const response = await axios.get(`${BASE_URL}/admin/schools`, {
      headers: {
        'Authorization': 'Bearer test-token' // 需要替换为实际的管理员token
      }
    });
    console.log('学校列表:', response.data);
    return response.data.data;
  } catch (error) {
    console.error('获取学校列表失败:', error.response?.data || error.message);
    return [];
  }
}

// 测试获取用户列表（无筛选）
async function testGetUsers() {
  try {
    console.log('测试获取用户列表（无筛选）...');
    const response = await axios.get(`${BASE_URL}/admin/users`, {
      headers: {
        'Authorization': 'Bearer test-token' // 需要替换为实际的管理员token
      }
    });
    console.log('用户列表数量:', response.data.data?.length || 0);
    return response.data.data;
  } catch (error) {
    console.error('获取用户列表失败:', error.response?.data || error.message);
    return [];
  }
}

// 测试按学校筛选用户
async function testFilterUsersBySchool(schoolName) {
  try {
    console.log(`测试按学校筛选用户: ${schoolName}...`);
    const response = await axios.get(`${BASE_URL}/admin/users?schoolName=${encodeURIComponent(schoolName)}`, {
      headers: {
        'Authorization': 'Bearer test-token' // 需要替换为实际的管理员token
      }
    });
    console.log(`筛选结果数量: ${response.data.data?.length || 0}`);
    return response.data.data;
  } catch (error) {
    console.error('按学校筛选失败:', error.response?.data || error.message);
    return [];
  }
}

// 测试筛选无预约用户
async function testFilterUsersWithoutReservation() {
  try {
    console.log('测试筛选无预约用户...');
    const response = await axios.get(`${BASE_URL}/admin/users?schoolName=no-reservation`, {
      headers: {
        'Authorization': 'Bearer test-token' // 需要替换为实际的管理员token
      }
    });
    console.log(`无预约用户数量: ${response.data.data?.length || 0}`);
    return response.data.data;
  } catch (error) {
    console.error('筛选无预约用户失败:', error.response?.data || error.message);
    return [];
  }
}

// 主测试函数
async function runTests() {
  console.log('开始测试学校筛选功能...\n');
  
  // 测试获取学校列表
  const schools = await testGetSchools();
  console.log('\n');
  
  // 测试获取所有用户
  const allUsers = await testGetUsers();
  console.log('\n');
  
  // 如果有学校数据，测试按学校筛选
  if (schools && schools.length > 0) {
    const firstSchool = schools[0];
    await testFilterUsersBySchool(firstSchool.name);
    console.log('\n');
  }
  
  // 测试筛选无预约用户
  await testFilterUsersWithoutReservation();
  console.log('\n');
  
  console.log('测试完成！');
}

// 运行测试
runTests().catch(console.error);
