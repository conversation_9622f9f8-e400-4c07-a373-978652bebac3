module.exports = {
  apps: [{
    name: 'seatmaster-frontend',
    script: 'server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    log_file: '/var/log/seatmaster/frontend-combined.log',
    out_file: '/var/log/seatmaster/frontend-out.log',
    error_file: '/var/log/seatmaster/frontend-error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z'
  }]
};
