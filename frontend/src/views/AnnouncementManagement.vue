<template>
  <div class="announcement-management-container">
    <el-container>
      <el-header class="announcement-header">
        <div class="header-left">
          <el-button type="info" plain @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回主页
          </el-button>
          <h2>公告管理</h2>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            新建公告
          </el-button>
          <el-button type="success" @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </el-header>
      
      <el-main class="announcement-main">
        <div class="announcement-content">
          <!-- 筛选条件 -->
          <el-card class="filter-card">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索公告标题"
                  @input="handleSearch"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </el-col>
              <el-col :span="4">
                <el-select v-model="statusFilter" placeholder="启用状态" @change="handleFilter">
                  <el-option label="全部" value="" />
                  <el-option label="已启用" :value="true" />
                  <el-option label="已禁用" :value="false" />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select v-model="targetUsersFilter" placeholder="目标用户" @change="handleFilter">
                  <el-option label="全部" value="" />
                  <el-option label="所有用户" value="ALL" />
                  <el-option label="普通用户" value="USER" />
                  <el-option label="管理员" value="ADMIN" />
                </el-select>
              </el-col>
              <el-col :span="6">
                <el-button type="danger" @click="batchDelete" :disabled="selectedRows.length === 0">
                  <el-icon><Delete /></el-icon>
                  批量删除 ({{ selectedRows.length }})
                </el-button>
              </el-col>
            </el-row>
          </el-card>

          <!-- 公告列表 -->
          <el-card class="table-card">
            <el-table
              v-loading="loading"
              :data="announcements"
              @selection-change="handleSelectionChange"
              stripe
              style="width: 100%"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="title" label="标题" min-width="200">
                <template #default="{ row }">
                  <div class="title-cell">
                    <span class="title-text" @click="viewAnnouncement(row)" style="cursor: pointer; color: #409EFF;">
                      {{ row.title }}
                    </span>
                    <el-tag v-if="row.priority > 50" type="danger" size="small" style="margin-left: 8px;">
                      高优先级
                    </el-tag>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="summary" label="摘要" min-width="250" show-overflow-tooltip />
              <el-table-column prop="targetUsers" label="目标用户" width="100">
                <template #default="{ row }">
                  <el-tag :type="getTargetUsersTagType(row.targetUsers)" size="small">
                    {{ getTargetUsersText(row.targetUsers) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="priority" label="优先级" width="80" sortable />
              <el-table-column prop="viewCount" label="查看次数" width="100" sortable />
              <el-table-column prop="enabled" label="状态" width="80">
                <template #default="{ row }">
                  <el-switch
                    v-model="row.enabled"
                    @change="toggleStatus(row)"
                    :loading="row.statusLoading"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="createdByName" label="创建者" width="100" />
              <el-table-column prop="createdTime" label="创建时间" width="160">
                <template #default="{ row }">
                  {{ formatDateTime(row.createdTime) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                  <el-button type="primary" size="small" @click="editAnnouncement(row)">
                    编辑
                  </el-button>
                  <el-button type="info" size="small" @click="viewAnnouncement(row)">
                    查看
                  </el-button>
                  <el-button type="danger" size="small" @click="deleteAnnouncement(row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </el-card>
        </div>
      </el-main>
    </el-container>

    <!-- 创建/编辑公告对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="80%"
      :before-close="handleDialogClose"
    >
      <el-form
        ref="announcementFormRef"
        :model="announcementForm"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="公告标题" prop="title">
              <el-input v-model="announcementForm.title" placeholder="请输入公告标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-input-number
                v-model="announcementForm.priority"
                :min="0"
                :max="999"
                placeholder="数字越大优先级越高"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="目标用户" prop="targetUsers">
              <el-select v-model="announcementForm.targetUsers" placeholder="请选择目标用户">
                <el-option label="所有用户" value="ALL" />
                <el-option label="普通用户" value="USER" />
                <el-option label="管理员" value="ADMIN" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否启用">
              <el-switch v-model="announcementForm.enabled" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="弹窗显示">
              <el-switch v-model="announcementForm.popup" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生效开始时间">
              <el-date-picker
                v-model="announcementForm.startTime"
                type="datetime"
                placeholder="选择开始时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效结束时间">
              <el-date-picker
                v-model="announcementForm.endTime"
                type="datetime"
                placeholder="选择结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="公告摘要">
          <el-input
            v-model="announcementForm.summary"
            type="textarea"
            :rows="3"
            placeholder="请输入公告摘要（用于列表显示）"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="公告内容" prop="content">
          <div class="editor-container">
            <div ref="editorRef" class="editor"></div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看公告对话框 -->
    <el-dialog v-model="viewDialogVisible" title="查看公告" width="70%">
      <div v-if="viewAnnouncementData" class="view-content">
        <h2>{{ viewAnnouncementData.title }}</h2>
        <div class="announcement-meta">
          <el-tag :type="getTargetUsersTagType(viewAnnouncementData.targetUsers)">
            {{ getTargetUsersText(viewAnnouncementData.targetUsers) }}
          </el-tag>
          <span class="meta-item">优先级: {{ viewAnnouncementData.priority }}</span>
          <span class="meta-item">查看次数: {{ viewAnnouncementData.viewCount }}</span>
          <span class="meta-item">创建者: {{ viewAnnouncementData.createdByName }}</span>
          <span class="meta-item">创建时间: {{ formatDateTime(viewAnnouncementData.createdTime) }}</span>
        </div>
        <div class="announcement-html-content" v-html="viewAnnouncementData.content"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Plus, Refresh, Search, Delete } from '@element-plus/icons-vue'
import announcementApi from '@/api/announcement'
import Quill from 'quill'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const announcements = ref([])
const selectedRows = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 搜索和筛选
const searchKeyword = ref('')
const statusFilter = ref('')
const targetUsersFilter = ref('')

// 对话框相关
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const submitLoading = ref(false)
const currentAnnouncementId = ref(null)
const viewAnnouncementData = ref(null)

// 富文本编辑器
const editorRef = ref(null)
let editor = null

// 表单数据
const announcementForm = reactive({
  title: '',
  content: '',
  summary: '',
  priority: 0,
  enabled: true,
  popup: true,
  startTime: null,
  endTime: null,
  targetUsers: 'ALL'
})

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入公告标题', trigger: 'blur' },
    { max: 200, message: '标题长度不能超过200个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入公告内容', trigger: 'blur' }
  ],
  targetUsers: [
    { required: true, message: '请选择目标用户', trigger: 'change' }
  ]
}

const announcementFormRef = ref(null)

// 方法
const goBack = () => {
  router.push('/dashboard')
}

const refreshData = () => {
  loadAnnouncements()
}

const showCreateDialog = () => {
  isEdit.value = false
  dialogTitle.value = '新建公告'
  resetForm()
  dialogVisible.value = true
  nextTick(() => {
    initEditor()
  })
}

const resetForm = () => {
  Object.assign(announcementForm, {
    title: '',
    content: '',
    summary: '',
    priority: 0,
    enabled: true,
    popup: true,
    startTime: null,
    endTime: null,
    targetUsers: 'ALL'
  })
  if (announcementFormRef.value) {
    announcementFormRef.value.clearValidate()
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadAnnouncements()
}

const handleFilter = () => {
  currentPage.value = 1
  loadAnnouncements()
}

const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

const handleSizeChange = (size) => {
  pageSize.value = size
  loadAnnouncements()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadAnnouncements()
}

const getTargetUsersTagType = (targetUsers) => {
  const typeMap = {
    'ALL': '',
    'USER': 'success',
    'ADMIN': 'warning'
  }
  return typeMap[targetUsers] || ''
}

const getTargetUsersText = (targetUsers) => {
  const textMap = {
    'ALL': '所有用户',
    'USER': '普通用户',
    'ADMIN': '管理员'
  }
  return textMap[targetUsers] || targetUsers
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 加载公告列表
const loadAnnouncements = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      title: searchKeyword.value || undefined,
      enabled: statusFilter.value !== '' ? statusFilter.value : undefined,
      targetUsers: targetUsersFilter.value || undefined,
      sortBy: 'priority',
      sortOrder: 'desc'
    }

    const response = await announcementApi.getAnnouncementPage(params)
    const responseData = response.data
    if (responseData.code === 200) {
      announcements.value = responseData.data.records.map(item => ({
        ...item,
        statusLoading: false
      }))
      total.value = responseData.data.total
    } else {
      ElMessage.error(responseData.message || '获取公告列表失败')
    }
  } catch (error) {
    console.error('获取公告列表失败:', error)
    ElMessage.error('获取公告列表失败')
  } finally {
    loading.value = false
  }
}

// 切换公告状态
const toggleStatus = async (row) => {
  try {
    row.statusLoading = true
    const response = await announcementApi.toggleAnnouncementStatus(row.id, row.enabled)
    const responseData = response.data
    if (responseData.code === 200) {
      ElMessage.success('状态更新成功')
    } else {
      // 恢复原状态
      row.enabled = !row.enabled
      ElMessage.error(responseData.message || '状态更新失败')
    }
  } catch (error) {
    // 恢复原状态
    row.enabled = !row.enabled
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
  } finally {
    row.statusLoading = false
  }
}

// 编辑公告
const editAnnouncement = async (row) => {
  try {
    isEdit.value = true
    currentAnnouncementId.value = row.id
    dialogTitle.value = '编辑公告'

    // 获取公告详情
    const response = await announcementApi.getAnnouncementById(row.id)
    const responseData = response.data
    if (responseData.code === 200) {
      const data = responseData.data
      Object.assign(announcementForm, {
        title: data.title,
        content: data.content,
        summary: data.summary || '',
        priority: data.priority,
        enabled: data.enabled,
        popup: data.popup,
        startTime: data.startTime,
        endTime: data.endTime,
        targetUsers: data.targetUsers
      })

      dialogVisible.value = true
      nextTick(() => {
        initEditor()
        if (editor && data.content) {
          editor.root.innerHTML = data.content
        }
      })
    } else {
      ElMessage.error(responseData.message || '获取公告详情失败')
    }
  } catch (error) {
    console.error('获取公告详情失败:', error)
    ElMessage.error('获取公告详情失败')
  }
}

// 初始化编辑器
const initEditor = () => {
  if (!editorRef.value) return

  // 如果已存在实例，先销毁
  if (editor) {
    editor = null
  }

  editor = new Quill(editorRef.value, {
    theme: 'snow',
    modules: {
      toolbar: [
        ['bold', 'italic', 'underline', 'strike'],
        ['blockquote', 'code-block'],
        [{ 'header': 1 }, { 'header': 2 }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        [{ 'script': 'sub'}, { 'script': 'super' }],
        [{ 'indent': '-1'}, { 'indent': '+1' }],
        [{ 'direction': 'rtl' }],
        [{ 'size': ['small', false, 'large', 'huge'] }],
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'font': [] }],
        [{ 'align': [] }],
        ['clean'],
        ['link', 'image']
      ]
    },
    placeholder: '请输入公告内容...'
  })

  // 监听内容变化
  editor.on('text-change', () => {
    announcementForm.content = editor.root.innerHTML
  })
}

// 销毁编辑器
const destroyEditor = () => {
  if (editor) {
    editor = null
  }
}

// 查看公告
const viewAnnouncement = async (row) => {
  try {
    const response = await announcementApi.getAnnouncementById(row.id)
    const responseData = response.data
    if (responseData.code === 200) {
      viewAnnouncementData.value = responseData.data
      viewDialogVisible.value = true
    } else {
      ElMessage.error(responseData.message || '获取公告详情失败')
    }
  } catch (error) {
    console.error('获取公告详情失败:', error)
    ElMessage.error('获取公告详情失败')
  }
}

// 删除公告
const deleteAnnouncement = (row) => {
  ElMessageBox.confirm(`确定要删除公告 "${row.title}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await announcementApi.deleteAnnouncement(row.id)
      const responseData = response.data
      if (responseData.code === 200) {
        ElMessage.success('删除成功')
        loadAnnouncements()
      } else {
        ElMessage.error(responseData.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 批量删除
const batchDelete = () => {
  const ids = selectedRows.value.map(row => row.id)
  if (ids.length === 0) {
    ElMessage.warning('请先选择要删除的公告')
    return
  }
  ElMessageBox.confirm(`确定要删除选中的 ${ids.length} 条公告吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await announcementApi.batchDeleteAnnouncements(ids)
      const responseData = response.data
      if (responseData.code === 200) {
        ElMessage.success('批量删除成功')
        loadAnnouncements()
      } else {
        ElMessage.error(responseData.message || '批量删除失败')
      }
    } catch (error) {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }).catch(() => {})
}

// 对话框关闭
const handleDialogClose = () => {
  dialogVisible.value = false
  destroyEditor()
}

// 提交表单
const submitForm = async () => {
  if (!announcementFormRef.value) return
  
  // 手动触发一次内容更新，确保拿到最新的HTML
  if (editor) {
    announcementForm.content = editor.root.innerHTML
  }

  await announcementFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        submitLoading.value = true
        const payload = { ...announcementForm }
        let response

        if (isEdit.value) {
          response = await announcementApi.updateAnnouncement(currentAnnouncementId.value, payload)
        } else {
          response = await announcementApi.createAnnouncement(payload)
        }

        const responseData = response.data
        if (responseData.code === 200) {
          ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
          dialogVisible.value = false
          destroyEditor()
          loadAnnouncements()
        } else {
          ElMessage.error(responseData.message || '操作失败')
        }
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败')
      } finally {
        submitLoading.value = false
      }
    }
  })
}

onMounted(() => {
  loadAnnouncements()
})

onBeforeUnmount(() => {
  destroyEditor()
})
</script>

<style scoped>
.announcement-management-container {
  padding: 20px;
  background-color: #f5f7fa;
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-left h2 {
  margin-left: 16px;
  font-size: 24px;
}

.filter-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.title-cell {
  display: flex;
  align-items: center;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  width: 100%;
}

.editor {
  height: 300px;
}

.view-content h2 {
  margin-bottom: 16px;
}

.announcement-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  color: #606266;
  font-size: 14px;
}

.announcement-html-content {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
  margin-top: 16px;
}

:deep(.ql-editor) {
  min-height: 300px;
}
</style>
