<template>
  <div class="admin-execution-logs">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h1>📋 执行日志管理</h1>
        <p>管理员专用 - 查看所有用户的预约执行记录和完整技术调试信息</p>
      </div>
      <div class="header-actions">
        <!-- 视图模式切换 -->
        <div class="view-mode-switch">
          <el-radio-group v-model="viewMode" @change="onViewModeChange">
            <el-radio-button value="task">任务视图</el-radio-button>
            <el-radio-button value="user">用户视图</el-radio-button>
            <el-radio-button value="school">学校视图</el-radio-button>
          </el-radio-group>
        </div>

        <el-button type="primary" @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="success" @click="exportLogs">
          <el-icon><Download /></el-icon>
          导出日志
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ stats.totalExecutions || 0 }}</div>
          <div class="stat-label">总执行次数</div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ stats.successRate || 0 }}%</div>
          <div class="stat-label">成功率</div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ formatDuration(stats.averageDurationMs) }}</div>
          <div class="stat-label">平均耗时</div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ stats.successExecutions || 0 }}</div>
          <div class="stat-label">成功次数</div>
        </div>
      </el-card>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <div class="filter-row">
        <!-- 任务视图筛选条件 -->
        <template v-if="viewMode === 'task'">
          <div class="filter-item">
            <label>用户名</label>
            <el-input
              v-model="filters.username"
              placeholder="输入用户名"
              clearable
              @change="onFilterChange"
            />
          </div>

          <div class="filter-item">
            <label>状态</label>
            <el-select
              v-model="filters.status"
              placeholder="选择状态"
              clearable
              @change="onFilterChange"
            >
              <el-option label="全部状态" value="" />
              <el-option label="成功" value="success" />
              <el-option label="失败" value="failed" />
              <el-option label="异常" value="error" />
            </el-select>
          </div>

          <div class="filter-item">
            <label>房间ID</label>
            <el-input
              v-model="filters.roomid"
              placeholder="输入房间ID"
              clearable
              @change="onFilterChange"
            />
          </div>

          <div class="filter-item">
            <label>座位ID</label>
            <el-input
              v-model="filters.seatid"
              placeholder="输入座位ID"
              clearable
              @change="onFilterChange"
            />
          </div>

          <div class="filter-item">
            <label>学校</label>
            <el-select
              v-model="filters.schoolId"
              placeholder="选择学校"
              clearable
              filterable
              :loading="schoolsLoading"
              @change="onFilterChange"
            >
              <el-option label="全部学校" :value="null" />
              <el-option
                v-for="school in schoolsList"
                :key="school.id"
                :label="school.name"
                :value="school.id"
              />
            </el-select>
          </div>
        </template>

        <!-- 用户视图筛选条件 -->
        <template v-if="viewMode === 'user'">
          <div class="filter-item">
            <label>搜索用户</label>
            <el-input
              v-model="userFilters.searchUsername"
              placeholder="输入用户名搜索"
              clearable
              @change="onUserFilterChange"
            />
          </div>

          <div class="filter-item">
            <label>排序方式</label>
            <el-select
              v-model="userFilters.sortBy"
              placeholder="选择排序字段"
              @change="onUserFilterChange"
            >
              <el-option label="总执行次数" value="totalExecutions" />
              <el-option label="成功率" value="successRate" />
              <el-option label="最近执行时间" value="lastExecutionTime" />
              <el-option label="平均耗时" value="avgDurationMs" />
              <el-option label="用户名" value="username" />
            </el-select>
          </div>

          <div class="filter-item">
            <label>排序顺序</label>
            <el-select
              v-model="userFilters.sortOrder"
              @change="onUserFilterChange"
            >
              <el-option label="降序" value="desc" />
              <el-option label="升序" value="asc" />
            </el-select>
          </div>

          <div class="filter-item">
            <label>学校</label>
            <el-select
              v-model="userFilters.schoolId"
              placeholder="选择学校"
              clearable
              filterable
              :loading="schoolsLoading"
              @change="onUserFilterChange"
            >
              <el-option label="全部学校" :value="null" />
              <el-option
                v-for="school in schoolsList"
                :key="school.id"
                :label="school.name"
                :value="school.id"
              />
            </el-select>
          </div>
        </template>

        <!-- 学校视图筛选条件 -->
        <template v-if="viewMode === 'school'">
          <div class="filter-item">
            <label>搜索学校</label>
            <el-input
              v-model="schoolFilters.searchSchoolName"
              placeholder="输入学校名称搜索"
              clearable
              @change="onSchoolFilterChange"
            />
          </div>

          <div class="filter-item">
            <label>排序方式</label>
            <el-select
              v-model="schoolFilters.sortBy"
              placeholder="选择排序字段"
              @change="onSchoolFilterChange"
            >
              <el-option label="总执行次数" value="totalExecutions" />
              <el-option label="总成功率" value="successRate" />
              <el-option label="平均耗时" value="avgDurationMs" />
              <el-option label="今日执行次数" value="todayExecutions" />
              <el-option label="今日成功率" value="todaySuccessRate" />
            </el-select>
          </div>

          <div class="filter-item">
            <label>排序顺序</label>
            <el-select
              v-model="schoolFilters.sortOrder"
              @change="onSchoolFilterChange"
            >
              <el-option label="降序" value="desc" />
              <el-option label="升序" value="asc" />
            </el-select>
          </div>
        </template>

        <!-- 共同的日期筛选 -->
        <div class="filter-item">
          <label>日期范围</label>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="onDateRangeChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 执行日志列表 - 任务视图 -->
    <div v-if="viewMode === 'task'" class="logs-section">
      <el-card v-loading="loading" class="history-list">
        <template v-if="logs.length > 0">
          <div v-for="log in logs" :key="log.id" class="log-item">
            <el-card class="normal-log" :class="getStatusClass(log.status)">
              <div class="log-header">
                <div class="log-time">
                  ⏰ {{ formatDateTime(log.apiResponseTime || log.createdAt) }}
                </div>
                <div class="log-status">
                  {{ log.statusIcon }} {{ log.statusText }}
                </div>
                <div class="log-duration">
                  ⏱️ {{ log.durationText }}
                </div>
              </div>

              <div class="log-basic-info">
                <span class="info-item">👤 用户: {{ log.userInfo }}</span>
                <span class="info-item">💺 座位: {{ log.seatInfo }}</span>
                <span class="info-item">🏠 房间: {{ log.roomInfo }}</span>
                <span class="info-item">⏰ 时间: {{ log.timeRange }}</span>
                <span class="info-item">🔄 尝试: {{ log.attemptCount || 1 }}次</span>
              </div>

              <div class="api-response">
                <div class="response-label">📱 执行结果:</div>
                <div class="response-content">{{ formatExecutionResult(log) }}</div>
              </div>

              <div class="execution-details">
                <div class="details-label">📊 技术详情:</div>
                <div class="details-content">{{ formatTechnicalDetails(log) }}</div>
              </div>

              <div v-if="log.errorMessage" class="error-details">
                <div class="error-label">⚠️ 错误详情:</div>
                <div class="error-content">{{ log.errorMessage }}</div>
              </div>

              <!-- 管理员专用：完整API响应 -->
              <div class="admin-technical-info">
                <el-collapse>
                  <el-collapse-item title="🔧 完整技术信息" name="technical">
                    <div class="technical-details">
                      <div class="tech-item">
                        <strong>预约ID:</strong> {{ log.reservationId }}
                      </div>
                      <div class="tech-item">
                        <strong>日志ID:</strong> {{ log.id }}
                      </div>
                      <div class="tech-item">
                        <strong>用户ID:</strong> {{ log.userId }}
                      </div>
                      <div class="tech-item">
                        <strong>学校:</strong> {{ log.schoolName || '未知' }}
                      </div>
                      <div class="tech-item">
                        <strong>创建时间:</strong> {{ formatDateTime(log.createdAt) }}
                      </div>
                      <div class="tech-item">
                        <strong>API响应时间:</strong> {{ formatDateTime(log.apiResponseTime) }}
                      </div>
                      <div v-if="log.apiResponse" class="tech-item">
                        <strong>完整API响应:</strong>
                        <pre class="api-response-raw">{{ formatApiResponse(log.apiResponse) }}</pre>
                      </div>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </el-card>
          </div>
        </template>

        <template v-else-if="!loading">
          <div class="empty-container">
            <el-empty description="暂无执行日志记录" />
          </div>
        </template>
      </el-card>

      <!-- 任务视图分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onPageSizeChange"
          @current-change="onPageChange"
        />
      </div>
    </div>

    <!-- 用户视图 -->
    <div v-if="viewMode === 'user'" class="users-section">
      <el-card v-loading="userLoading" class="users-list">
        <template v-if="users.length > 0">
          <div v-for="user in users" :key="user.username" class="user-item">
            <el-card class="user-card" @click="toggleUserExpand(user.username)">
              <div class="user-header">
                <div class="user-info">
                  <div class="user-name">
                    <el-icon class="expand-icon" :class="{ 'expanded': expandedUsers.has(user.username) }">
                      <ArrowRight />
                    </el-icon>
                    👤 {{ user.userDisplayName || user.username }}
                    <span class="username-tag">@{{ user.username }}</span>
                  </div>
                  <div class="user-stats">
                    <span class="stat-item">📊 总执行: {{ user.totalExecutions }}</span>
                    <span class="stat-item">✅ 成功: {{ user.successExecutions }}</span>
                    <span class="stat-item">📈 成功率: {{ user.successRate }}%</span>
                    <span class="stat-item">⏱️ 平均耗时: {{ formatDuration(user.avgDurationMs) }}</span>
                    <span class="stat-item">🕒 最近执行: {{ formatDateTime(user.lastExecutionTime) }}</span>
                  </div>
                </div>
              </div>

              <!-- 用户展开详情 -->
              <el-collapse-transition>
                <div v-if="expandedUsers.has(user.username)" class="user-details">
                  <div class="details-header">
                    <h4>最近执行记录</h4>
                    <el-button
                      size="small"
                      type="primary"
                      @click.stop="loadUserRecentLogs(user.username)"
                      :loading="userDetailLoading[user.username]"
                    >
                      刷新记录
                    </el-button>
                  </div>

                  <div v-if="userRecentLogs[user.username]?.length > 0" class="user-logs">
                    <div
                      v-for="log in userRecentLogs[user.username]"
                      :key="log.id"
                      class="user-log-item"
                      :class="getStatusClass(log.status)"
                    >
                      <div class="log-summary">
                        <span class="log-time">{{ formatDateTime(log.apiResponseTime || log.createdAt) }}</span>
                        <span class="log-status">{{ log.statusIcon }} {{ log.statusText }}</span>
                        <span class="log-info">{{ log.seatInfo }} @ {{ log.roomInfo }}</span>
                        <span class="log-duration">{{ log.durationText }}</span>
                      </div>
                      <div v-if="log.errorMessage" class="log-error">
                        ⚠️ {{ log.errorMessage }}
                      </div>
                    </div>
                  </div>

                  <div v-else-if="!userDetailLoading[user.username]" class="no-logs">
                    <el-empty description="暂无执行记录" :image-size="60" />
                  </div>

                  <div v-if="userDetailLoading[user.username]" class="loading-logs">
                    <el-skeleton :rows="3" animated />
                  </div>
                </div>
              </el-collapse-transition>
            </el-card>
          </div>
        </template>

        <template v-else-if="!userLoading">
          <div class="empty-container">
            <el-empty description="暂无用户数据" />
          </div>
        </template>
      </el-card>

      <!-- 用户视图分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="userPagination.page"
          v-model:page-size="userPagination.size"
          :page-sizes="[10, 20, 50]"
          :total="userPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onUserPageSizeChange"
          @current-change="onUserPageChange"
        />
      </div>
    </div>

    <!-- 学校视图 -->
    <div v-if="viewMode === 'school'" class="schools-section">
      <el-card v-loading="schoolLoading" class="schools-list">
        <template v-if="schools.length > 0">
          <div v-for="school in schools" :key="school.schoolId" class="school-item">
            <el-card class="school-card" @click="toggleSchoolExpand(school.schoolId)">
              <div class="school-header">
                <div class="school-info">
                  <div class="school-name">
                    <el-icon class="expand-icon" :class="{ 'expanded': expandedSchools.has(school.schoolId) }">
                      <ArrowRight />
                    </el-icon>
                    🏫 {{ school.schoolName }}
                  </div>
                  <div class="school-stats">
                    <span class="stat-item" :title="`总执行次数: ${school.totalExecutions}`">
                      📊 总执行: {{ school.totalExecutions.toLocaleString() }}
                    </span>
                    <span class="stat-item" :title="`成功执行次数: ${school.successExecutions}`">
                      ✅ 成功: {{ school.successExecutions.toLocaleString() }}
                    </span>
                    <span class="stat-item" :title="`总成功率: ${school.successRate}%`"
                          :class="{ 'success-high': school.successRate >= 90, 'success-medium': school.successRate >= 70 && school.successRate < 90, 'success-low': school.successRate < 70 }">
                      📈 总成功率: {{ school.successRate }}%
                    </span>
                    <span class="stat-item" :title="`平均执行耗时: ${formatDuration(school.avgDurationMs)}`">
                      ⏱️ 平均耗时: {{ formatDuration(school.avgDurationMs) }}
                    </span>
                    <span class="stat-item" :title="`今日执行次数: ${school.todayExecutions}`">
                      📅 今日执行: {{ school.todayExecutions.toLocaleString() }}
                    </span>
                    <span class="stat-item" :title="`今日成功率: ${school.todaySuccessRate}%`"
                          :class="{ 'success-high': school.todaySuccessRate >= 90, 'success-medium': school.todaySuccessRate >= 70 && school.todaySuccessRate < 90, 'success-low': school.todaySuccessRate < 70 }">
                      🎯 今日成功率: {{ school.todaySuccessRate }}%
                    </span>
                  </div>
                </div>
              </div>

              <!-- 学校展开详情 -->
              <el-collapse-transition>
                <div v-if="expandedSchools.has(school.schoolId)" class="school-details">
                  <div class="details-header">
                    <h4>日期维度统计</h4>
                    <el-button
                      size="small"
                      type="primary"
                      @click.stop="loadSchoolDailyStats(school.schoolId)"
                      :loading="schoolDetailLoading[school.schoolId]"
                    >
                      刷新统计
                    </el-button>
                  </div>

                  <div v-if="schoolDailyStats[school.schoolId]?.length > 0" class="school-daily-stats">
                    <div class="stats-table">
                      <div class="table-header">
                        <span class="col-date">日期</span>
                        <span class="col-executions">执行次数</span>
                        <span class="col-success-rate">成功率</span>
                        <span class="col-avg-duration">平均耗时</span>
                      </div>
                      <div
                        v-for="stat in schoolDailyStats[school.schoolId]"
                        :key="stat.date"
                        class="table-row"
                      >
                        <span class="col-date">{{ formatDate(stat.date) }}</span>
                        <span class="col-executions">{{ stat.dailyExecutions }}</span>
                        <span class="col-success-rate">{{ stat.dailySuccessRate }}%</span>
                        <span class="col-avg-duration">{{ formatDuration(stat.dailyAvgDurationMs) }}</span>
                      </div>
                    </div>
                  </div>

                  <div v-else-if="!schoolDetailLoading[school.schoolId]" class="no-stats">
                    <el-empty description="暂无统计数据" :image-size="60" />
                  </div>

                  <div v-if="schoolDetailLoading[school.schoolId]" class="loading-stats">
                    <el-skeleton :rows="5" animated />
                  </div>
                </div>
              </el-collapse-transition>
            </el-card>
          </div>
        </template>

        <template v-else-if="!schoolLoading">
          <div class="empty-container">
            <el-empty description="暂无学校数据">
              <template #description>
                <p>没有找到符合条件的学校数据</p>
                <p>请尝试调整筛选条件或检查数据源</p>
              </template>
              <el-button type="primary" @click="loadSchoolsData">重新加载</el-button>
            </el-empty>
          </div>
        </template>
      </el-card>

      <!-- 学校视图分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="schoolPagination.page"
          v-model:page-size="schoolPagination.size"
          :page-sizes="[10, 20, 50]"
          :total="schoolPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="onSchoolPageSizeChange"
          @current-change="onSchoolPageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Download, ArrowRight } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import api from '@/utils/api'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const userLoading = ref(false)
const logs = ref([])
const stats = ref([])

// 视图模式
const viewMode = ref(route.query.view || 'task')

// 任务视图相关数据
const filters = reactive({
  username: '',
  status: '',
  roomid: '',
  seatid: '',
  schoolId: null
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 用户视图相关数据
const users = ref([])
const userFilters = reactive({
  searchUsername: '',
  sortBy: 'totalExecutions',
  sortOrder: 'desc',
  schoolId: null
})

const userPagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 用户展开状态
const expandedUsers = ref(new Set())
const userRecentLogs = ref({})
const userDetailLoading = ref({})

// 学校视图相关数据
const schoolLoading = ref(false)
const schools = ref([]) // 学校概览数据
const schoolsLoading = ref(false) // 学校列表加载状态
const schoolsList = ref([]) // 用于筛选的学校列表
const schoolFilters = reactive({
  searchSchoolName: '',
  sortBy: 'totalExecutions',
  sortOrder: 'desc'
})

const schoolPagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 学校展开状态
const expandedSchools = ref(new Set())
const schoolDailyStats = ref({})
const schoolDetailLoading = ref({})

const dateRange = ref([])

// 性能监控方法
const logPerformanceStats = () => {
  const stats = performanceStats.getStats()
  console.log('📊 formatDateTime 性能统计:', stats)
  return stats
}

const clearCache = () => {
  dateTimeCache.clear()
  performanceStats.reset()
  console.log('🧹 缓存已清空，性能统计已重置')
}

// 暴露调试方法到全局（仅开发环境）
if (process.env.NODE_ENV === 'development') {
  window.adminLogsDebug = {
    logPerformanceStats,
    clearCache,
    getCache: () => dateTimeCache,
    getStats: () => performanceStats.getStats()
  }
}

// 页面加载时获取数据
onMounted(() => {
  // 首先获取学校列表
  fetchSchools()

  if (viewMode.value === 'task') {
    loadData()
  } else if (viewMode.value === 'user') {
    loadUsersData()
  } else if (viewMode.value === 'school') {
    loadSchoolsData()
  }
  loadStats()

  // 开发环境下显示性能统计
  if (process.env.NODE_ENV === 'development') {
    console.log('🚀 formatDateTime 缓存系统已启用')
    console.log('💡 使用 window.adminLogsDebug 查看性能统计')
  }
})

// 监听视图模式变化
watch(viewMode, (newMode) => {
  // 更新URL参数
  router.replace({
    query: {
      ...route.query,
      view: newMode
    }
  })
})

// 方法
const fetchSchools = async () => {
  if (schoolsList.value.length > 0) {
    return // 避免重复加载
  }

  schoolsLoading.value = true
  try {
    const response = await api.get('/admin/execution-logs/schools')
    if (response?.data?.data) {
      schoolsList.value = response.data.data
      console.log('Successfully loaded schools:', schoolsList.value.length)
    }
  } catch (error) {
    console.error('获取学校列表失败:', error)
    ElMessage.error('获取学校列表失败')
  } finally {
    schoolsLoading.value = false
  }
}

const loadData = async () => {
  loading.value = true
  
  try {
    const params = buildQueryParams()
    console.log('Loading admin execution logs:', params)

    const response = await api.get('/admin/execution-logs', { 
      params,
      timeout: 15000 // 15秒超时
    })

    if (response?.data?.data) {
      logs.value = Array.isArray(response.data.data.logs) ? response.data.data.logs : []
      pagination.total = Number(response.data.data.totalCount) || 0
      
      console.log(`Successfully loaded ${logs.value.length} admin logs, total: ${pagination.total}`)
    } else {
      throw new Error('响应数据格式错误')
    }

  } catch (error) {
    handleLoadError(error)
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    const params = {
      username: filters.username || undefined,
      startDate: dateRange.value?.[0] || undefined,
      endDate: dateRange.value?.[1] || undefined
    }

    const response = await api.get('/admin/execution-logs/stats', {
      params,
      timeout: 10000
    })
    
    if (response?.data?.data) {
      stats.value = {
        totalExecutions: Number(response.data.data.totalExecutions) || 0,
        successExecutions: Number(response.data.data.successExecutions) || 0,
        successRate: Number(response.data.data.successRate) || 0,
        averageDurationMs: Number(response.data.data.averageDurationMs) || 0,
        recentStats: Array.isArray(response.data.data.recentStats) ? response.data.data.recentStats : []
      }
      
      console.log('Admin statistics loaded:', stats.value)
    }
  } catch (error) {
    console.warn('加载统计信息失败:', error)
    // 统计信息加载失败不影响主要功能
    stats.value = {
      totalExecutions: 0,
      successExecutions: 0,
      successRate: 0,
      averageDurationMs: 0,
      recentStats: []
    }
  }
}

const buildQueryParams = () => {
  return {
    page: pagination.page,
    size: pagination.size,
    username: filters.username || undefined,
    status: filters.status || undefined,
    roomid: filters.roomid || undefined,
    seatid: filters.seatid || undefined,
    schoolId: filters.schoolId || undefined,
    startDate: dateRange.value?.[0] || undefined,
    endDate: dateRange.value?.[1] || undefined
  }
}

const handleLoadError = (error) => {
  console.error('加载管理员执行日志失败:', error)
  
  let errorMessage = '加载执行日志失败'
  
  if (error.code === 'ECONNABORTED') {
    errorMessage = '请求超时，请检查网络连接'
  } else if (error.response) {
    const status = error.response.status
    const data = error.response.data
    
    switch (status) {
      case 401:
        errorMessage = '登录已过期，请重新登录'
        break
      case 403:
        errorMessage = '没有管理员权限'
        break
      case 404:
        errorMessage = '请求的资源不存在'
        break
      case 500:
        errorMessage = '服务器内部错误'
        break
      default:
        errorMessage = data?.message || `请求失败 (${status})`
    }
  } else if (error.request) {
    errorMessage = '网络连接失败，请检查网络'
  }
  
  ElMessage.error(errorMessage)
}

const refreshData = () => {
  if (viewMode.value === 'task') {
    loadData()
  } else if (viewMode.value === 'user') {
    loadUsersData()
  } else if (viewMode.value === 'school') {
    loadSchoolsData()
  }
  loadStats()
}

// 用户视图数据加载
const loadUsersData = async () => {
  userLoading.value = true

  try {
    const params = buildUserQueryParams()
    console.log('Loading users overview:', params)

    const response = await api.get('/admin/execution-logs/users-overview', {
      params,
      timeout: 15000
    })

    if (response?.data?.data) {
      users.value = Array.isArray(response.data.data.users) ? response.data.data.users : []
      userPagination.total = Number(response.data.data.totalCount) || 0

      console.log(`Successfully loaded ${users.value.length} users, total: ${userPagination.total}`)
    } else {
      throw new Error('响应数据格式错误')
    }

  } catch (error) {
    handleLoadError(error)
  } finally {
    userLoading.value = false
  }
}

const buildUserQueryParams = () => {
  return {
    page: userPagination.page,
    size: userPagination.size,
    searchUsername: userFilters.searchUsername || undefined,
    startDate: dateRange.value?.[0] || undefined,
    endDate: dateRange.value?.[1] || undefined,
    sortBy: userFilters.sortBy,
    sortOrder: userFilters.sortOrder,
    schoolId: userFilters.schoolId || undefined
  }
}

// 视图模式切换
const onViewModeChange = (newMode) => {
  console.log('切换视图模式:', newMode)

  if (newMode === 'task') {
    loadData()
  } else if (newMode === 'user') {
    loadUsersData()
  } else if (newMode === 'school') {
    loadSchoolsData()
  }
}

// 用户展开/折叠
const toggleUserExpand = (username) => {
  if (expandedUsers.value.has(username)) {
    expandedUsers.value.delete(username)
  } else {
    expandedUsers.value.add(username)
    // 首次展开时加载用户详情
    if (!userRecentLogs.value[username]) {
      loadUserRecentLogs(username)
    }
  }
}

// 加载用户最近记录
const loadUserRecentLogs = async (username) => {
  userDetailLoading.value[username] = true

  try {
    const params = {
      limit: 15,
      startDate: dateRange.value?.[0] || undefined,
      endDate: dateRange.value?.[1] || undefined
    }

    const response = await api.get(`/admin/execution-logs/user/${username}/recent`, {
      params,
      timeout: 10000
    })

    if (response?.data?.data) {
      userRecentLogs.value[username] = Array.isArray(response.data.data) ? response.data.data : []
      console.log(`Loaded ${userRecentLogs.value[username].length} recent logs for user ${username}`)
    }

  } catch (error) {
    console.error(`加载用户 ${username} 最近记录失败:`, error)
    ElMessage.error(`加载用户记录失败: ${error.message}`)
  } finally {
    userDetailLoading.value[username] = false
  }
}

// 用户视图筛选变化（防抖处理）
let userFilterChangeTimer = null
const onUserFilterChange = () => {
  if (userFilterChangeTimer) {
    clearTimeout(userFilterChangeTimer)
  }

  userFilterChangeTimer = setTimeout(() => {
    userPagination.page = 1
    loadUsersData()
  }, 300) // 300ms防抖
}

// 用户视图分页
const onUserPageSizeChange = () => {
  userPagination.page = 1
  loadUsersData()
}

const onUserPageChange = () => {
  loadUsersData()
}

// ==================== 学校视图相关方法 ====================

// 学校视图数据加载
const loadSchoolsData = async () => {
  schoolLoading.value = true

  try {
    const params = buildSchoolQueryParams()
    console.log('Loading schools overview:', params)

    const response = await api.get('/admin/execution-logs/schools-overview', {
      params,
      timeout: 15000 // 15秒超时
    })

    if (response?.data?.data) {
      schools.value = Array.isArray(response.data.data.schools) ? response.data.data.schools : []
      schoolPagination.total = Number(response.data.data.totalCount) || 0

      console.log(`Successfully loaded ${schools.value.length} schools, total: ${schoolPagination.total}`)
    } else {
      throw new Error('响应数据格式错误')
    }

  } catch (error) {
    console.error('获取学校概览失败:', error)
    ElMessage.error('获取学校概览失败')
    schools.value = []
    schoolPagination.total = 0
  } finally {
    schoolLoading.value = false
  }
}

// 构建学校查询参数
const buildSchoolQueryParams = () => {
  return {
    page: schoolPagination.page,
    size: schoolPagination.size,
    searchSchoolName: schoolFilters.searchSchoolName || undefined,
    startDate: dateRange.value?.[0] || undefined,
    endDate: dateRange.value?.[1] || undefined,
    sortBy: schoolFilters.sortBy,
    sortOrder: schoolFilters.sortOrder
  }
}

// 学校展开/折叠
const toggleSchoolExpand = (schoolId) => {
  if (expandedSchools.value.has(schoolId)) {
    expandedSchools.value.delete(schoolId)
  } else {
    expandedSchools.value.add(schoolId)
    // 首次展开时加载学校详细统计
    if (!schoolDailyStats.value[schoolId]) {
      loadSchoolDailyStats(schoolId)
    }
  }
}

// 加载学校日期统计
const loadSchoolDailyStats = async (schoolId) => {
  schoolDetailLoading.value[schoolId] = true

  try {
    const params = {
      startDate: dateRange.value?.[0] || undefined,
      endDate: dateRange.value?.[1] || undefined,
      page: 1,
      size: 30
    }

    const response = await api.get(`/admin/execution-logs/school/${schoolId}/daily-stats`, {
      params,
      timeout: 10000
    })

    if (response?.data?.data) {
      schoolDailyStats.value[schoolId] = response.data.data.dailyStats || []
      console.log(`Loaded daily stats for school ${schoolId}:`, schoolDailyStats.value[schoolId].length)
    }

  } catch (error) {
    console.error(`获取学校${schoolId}日期统计失败:`, error)
    ElMessage.error('获取学校统计失败')
    schoolDailyStats.value[schoolId] = []
  } finally {
    schoolDetailLoading.value[schoolId] = false
  }
}

// 学校视图分页
const onSchoolPageSizeChange = () => {
  schoolPagination.page = 1
  loadSchoolsData()
}

const onSchoolPageChange = () => {
  loadSchoolsData()
}

// 学校筛选变更防抖
let schoolFilterChangeTimer = null
const onSchoolFilterChange = () => {
  if (schoolFilterChangeTimer) {
    clearTimeout(schoolFilterChangeTimer)
  }

  schoolFilterChangeTimer = setTimeout(() => {
    schoolPagination.page = 1
    loadSchoolsData()
  }, 300) // 300ms防抖
}

const exportLogs = async () => {
  try {
    const params = {
      username: filters.username || undefined,
      status: filters.status || undefined,
      startDate: dateRange.value?.[0] || undefined,
      endDate: dateRange.value?.[1] || undefined,
      format: 'csv'
    }
    
    const response = await api.get('/admin/execution-logs/export', { params })
    ElMessage.success('日志导出成功: ' + response.data.data)
  } catch (error) {
    console.error('导出日志失败:', error)
    ElMessage.error('导出日志失败')
  }
}

// 防抖处理筛选变更
let filterChangeTimer = null
const onFilterChange = () => {
  if (filterChangeTimer) {
    clearTimeout(filterChangeTimer)
  }

  filterChangeTimer = setTimeout(() => {
    pagination.page = 1
    loadData()
    loadStats()
  }, 300) // 300ms防抖
}

const onDateRangeChange = () => {
  pagination.page = 1
  loadData()
  loadStats()
}

const onPageSizeChange = () => {
  pagination.page = 1
  loadData()
}

const onPageChange = () => {
  loadData()
}

// 工具方法 - 格式化函数

// 时间格式化缓存系统
class DateTimeCache {
  constructor(maxSize = 1000) {
    this.cache = new Map()
    this.maxSize = maxSize
    this.accessOrder = new Map() // 用于LRU跟踪
    this.accessCounter = 0
  }

  get(key) {
    if (this.cache.has(key)) {
      // 更新访问顺序（LRU）
      this.accessOrder.set(key, ++this.accessCounter)
      return this.cache.get(key)
    }
    return null
  }

  set(key, value) {
    // 如果缓存已满，删除最少使用的项
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this._evictLRU()
    }

    this.cache.set(key, value)
    this.accessOrder.set(key, ++this.accessCounter)
  }

  _evictLRU() {
    let lruKey = null
    let lruAccess = Infinity

    for (const [key, accessTime] of this.accessOrder) {
      if (accessTime < lruAccess) {
        lruAccess = accessTime
        lruKey = key
      }
    }

    if (lruKey !== null) {
      this.cache.delete(lruKey)
      this.accessOrder.delete(lruKey)
    }
  }

  clear() {
    this.cache.clear()
    this.accessOrder.clear()
    this.accessCounter = 0
  }

  size() {
    return this.cache.size
  }
}

// 创建全局缓存实例
const dateTimeCache = new DateTimeCache(1000)

// 性能监控对象
const performanceStats = {
  totalCalls: 0,
  cacheHits: 0,
  cacheMisses: 0,
  formatTime: 0,

  reset() {
    this.totalCalls = 0
    this.cacheHits = 0
    this.cacheMisses = 0
    this.formatTime = 0
  },

  getHitRate() {
    return this.totalCalls > 0 ? (this.cacheHits / this.totalCalls * 100).toFixed(2) : 0
  },

  getStats() {
    return {
      totalCalls: this.totalCalls,
      cacheHits: this.cacheHits,
      cacheMisses: this.cacheMisses,
      hitRate: this.getHitRate() + '%',
      avgFormatTime: this.totalCalls > 0 ? (this.formatTime / this.totalCalls).toFixed(3) + 'ms' : '0ms',
      cacheSize: dateTimeCache.size()
    }
  }
}

// 创建高性能的日期格式化器
const createDateTimeFormatter = () => {
  // 使用 Intl.DateTimeFormat 提供更好的性能
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    fractionalSecondDigits: 3,
    hour12: false
  })
}

const dateFormatter = createDateTimeFormatter()

/**
 * 高性能的日期时间格式化（包含毫秒，带缓存）
 * 性能优化：
 * 1. LRU缓存机制，避免重复计算
 * 2. 使用 Intl.DateTimeFormat 替代手动字符串拼接
 * 3. 性能监控和统计
 */
const formatDateTime = (dateTime) => {
  const startTime = performance.now()
  performanceStats.totalCalls++

  try {
    if (!dateTime) return '未知'

    // 标准化输入，支持字符串和Date对象
    const inputKey = typeof dateTime === 'string' ? dateTime : dateTime.toString()

    // 检查缓存
    const cached = dateTimeCache.get(inputKey)
    if (cached !== null) {
      performanceStats.cacheHits++
      performanceStats.formatTime += performance.now() - startTime
      return cached
    }

    performanceStats.cacheMisses++

    const date = new Date(dateTime)
    if (isNaN(date.getTime())) {
      const errorResult = '无效时间'
      dateTimeCache.set(inputKey, errorResult)
      performanceStats.formatTime += performance.now() - startTime
      return errorResult
    }

    // 使用 Intl.DateTimeFormat 进行高性能格式化
    let formatted = dateFormatter.format(date)

    // 调整格式以匹配期望的 YYYY/MM/DD HH:mm:ss.SSS 格式
    // Intl.DateTimeFormat 可能返回不同的分隔符，需要标准化
    formatted = formatted
      .replace(/年|月/g, '/')
      .replace(/日/g, '')
      .replace(/\s+/g, ' ')
      .trim()

    // 确保使用正确的分隔符格式
    if (formatted.includes('-')) {
      formatted = formatted.replace(/-/g, '/')
    }

    // 缓存结果
    dateTimeCache.set(inputKey, formatted)

    performanceStats.formatTime += performance.now() - startTime
    return formatted
  } catch (error) {
    console.warn('时间格式化失败:', error, 'Input:', dateTime)
    const errorResult = '格式错误'
    // 即使是错误也缓存，避免重复处理相同的错误输入
    if (dateTime) {
      const inputKey = typeof dateTime === 'string' ? dateTime : dateTime.toString()
      dateTimeCache.set(inputKey, errorResult)
    }
    performanceStats.formatTime += performance.now() - startTime
    return errorResult
  }
}

/**
 * 安全的时长格式化
 */
const formatDuration = (ms) => {
  try {
    if (!ms || isNaN(ms)) return '未知'
    const num = Number(ms)
    if (num < 1000) return `${Math.round(num)}ms`
    return `${(num / 1000).toFixed(1)}秒`
  } catch (error) {
    console.warn('时长格式化失败:', error)
    return '格式错误'
  }
}

/**
 * 安全的日期格式化
 */
const formatDate = (date) => {
  try {
    if (!date) return '未知'
    const dateObj = new Date(date)
    if (isNaN(dateObj.getTime())) return '未知'

    return dateObj.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    console.warn('日期格式化失败:', error)
    return '未知'
  }
}

/**
 * 状态样式类映射
 */
const STATUS_CLASS_MAP = {
  success: 'status-success',
  failed: 'status-failed',
  error: 'status-error'
}

const getStatusClass = (status) => {
  return STATUS_CLASS_MAP[status?.toLowerCase()] || 'status-unknown'
}

/**
 * 格式化执行结果 - 管理员版本（显示更多技术信息）
 */
const formatExecutionResult = (log) => {
  try {
    if (log.status === 'success') {
      return formatSuccessResult(log)
    }

    if (log.status === 'failed' || log.status === 'error') {
      return formatFailureResult(log)
    }

    return formatApiResponse(log.apiResponse)
  } catch (error) {
    console.warn('执行结果格式化失败:', error)
    return '格式化失败'
  }
}

/**
 * 格式化成功结果 - 管理员版本
 */
const formatSuccessResult = (log) => {
  const parts = []

  parts.push('🎉 预约成功！')

  if (log.startTime && log.endTime) {
    parts.push(`⏰ 时间：${log.startTime} - ${log.endTime}`)
  }

  if (log.roomName || log.roomid) {
    const roomInfo = log.roomName || `房间${log.roomid}`
    parts.push(`🏠 房间：${roomInfo}`)
  }

  if (log.seatid) {
    parts.push(`💺 座位：${log.seatid}号`)
  }

  // 管理员版本：显示更多技术信息
  if (log.reservationId) {
    parts.push(`🆔 预约ID：${log.reservationId}`)
  }

  if (log.userId) {
    parts.push(`👤 用户ID：${log.userId}`)
  }

  const apiData = parseApiResponse(log.apiResponse)
  if (apiData?.data?.seatReserve) {
    const reserve = apiData.data.seatReserve
    if (reserve.duration) {
      parts.push(`⏱️ 时长：${reserve.duration}小时`)
    }
    if (reserve.firstLevelName) {
      parts.push(`📍 位置：${reserve.firstLevelName}`)
    }
  }

  return parts.join('\n')
}

/**
 * 格式化失败结果 - 管理员版本
 */
const formatFailureResult = (log) => {
  const parts = []

  parts.push('❌ 预约失败')

  if (log.errorMessage) {
    parts.push(`原因：${log.errorMessage}`)
  }

  // 管理员版本：显示更多技术信息
  if (log.reservationId) {
    parts.push(`🆔 预约ID：${log.reservationId}`)
  }

  if (log.attemptCount > 1) {
    parts.push(`🔄 重试次数：${log.attemptCount}`)
  }

  const apiData = parseApiResponse(log.apiResponse)
  if (apiData?.message) {
    parts.push(`详情：${apiData.message}`)
  }

  return parts.join('\n')
}

/**
 * 格式化技术详情 - 管理员专用
 */
const formatTechnicalDetails = (log) => {
  try {
    const details = []

    if (log.id) {
      details.push(`日志ID: ${log.id}`)
    }

    if (log.reservationId) {
      details.push(`预约ID: ${log.reservationId}`)
    }

    if (log.attemptCount > 1) {
      details.push(`重试${log.attemptCount}次`)
    }

    if (log.executionTime) {
      const seconds = parseFloat(log.executionTime)
      if (!isNaN(seconds)) {
        const timeText = seconds < 1
          ? `${Math.round(seconds * 1000)}ms`
          : `${seconds.toFixed(1)}秒`
        details.push(`耗时${timeText}`)
      }
    }

    if (log.apiResponseTime) {
      details.push(`响应时间${formatDateTime(log.apiResponseTime)}`)
    }

    if (log.createdAt) {
      details.push(`创建时间${formatDateTime(log.createdAt)}`)
    }

    return details.length > 0 ? details.join(' | ') : '无技术详情'
  } catch (error) {
    console.warn('技术详情格式化失败:', error)
    return '格式化失败'
  }
}

/**
 * 安全解析API响应
 */
const parseApiResponse = (apiResponse) => {
  try {
    if (!apiResponse) return null

    if (typeof apiResponse === 'string') {
      return JSON.parse(apiResponse)
    }

    return apiResponse
  } catch {
    return null
  }
}

/**
 * 原始API响应格式化
 */
const formatApiResponse = (apiResponse) => {
  try {
    if (!apiResponse) return '无响应数据'

    if (typeof apiResponse === 'string') {
      try {
        const parsed = JSON.parse(apiResponse)
        return JSON.stringify(parsed, null, 2)
      } catch {
        return apiResponse.length > 500 ? apiResponse.substring(0, 500) + '...' : apiResponse
      }
    }

    return JSON.stringify(apiResponse, null, 2)
  } catch (error) {
    console.warn('API响应格式化失败:', error)
    return '格式化失败'
  }
}
</script>

<style scoped>
.admin-execution-logs {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-header h1 {
  margin: 0 0 5px 0;
  color: #303133;
  font-size: 24px;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

.view-mode-switch {
  margin-right: 10px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 10px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
  min-width: 150px;
}

.filter-item label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
  font-weight: 500;
}

.logs-section {
  margin-bottom: 20px;
}

.loading-container {
  padding: 40px;
}

.empty-container {
  padding: 40px;
  text-align: center;
}

.log-item {
  margin-bottom: 15px;
}

.normal-log {
  transition: all 0.3s;
}

.normal-log:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #606266;
}

.log-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.log-duration {
  font-size: 12px;
  color: #909399;
}

.log-basic-info {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.info-item {
  font-size: 14px;
  color: #606266;
}

.api-response, .execution-details, .error-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 10px;
}

.response-label, .details-label, .error-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.response-content {
  font-size: 13px;
  color: #303133;
  background-color: #f5f7fa;
  padding: 12px 16px;
  border-radius: 6px;
  border-left: 3px solid #409eff;
  word-break: break-word;
  line-height: 1.6;
  white-space: pre-line;
}

.details-content {
  font-size: 13px;
  color: #606266;
  font-family: 'Courier New', monospace;
}

.error-content {
  font-size: 13px;
  color: #f56c6c;
  background-color: #fef0f0;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #f56c6c;
}

/* 状态样式 */
.status-success .log-status {
  background-color: #f0f9ff;
  color: #67c23a;
}

.status-success .response-content {
  background-color: #f0f9ff;
  border-left-color: #67c23a;
  color: #529b2e;
}

.status-failed .log-status,
.status-error .log-status {
  background-color: #fef0f0;
  color: #f56c6c;
}

.status-failed .response-content,
.status-error .response-content {
  background-color: #fef0f0;
  border-left-color: #f56c6c;
  color: #c45656;
}

/* 管理员专用技术信息 */
.admin-technical-info {
  margin-top: 15px;
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
}

.technical-details {
  background-color: #fafbfc;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.tech-item {
  margin-bottom: 8px;
  font-size: 13px;
  color: #606266;
}

.tech-item strong {
  color: #303133;
  margin-right: 8px;
}

.api-response-raw {
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  font-size: 12px;
  color: #495057;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300px;
  overflow-y: auto;
  margin-top: 5px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .filter-row {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .stats-cards {
    grid-template-columns: 1fr;
  }

  .log-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .log-basic-info {
    flex-direction: column;
    gap: 8px;
  }

  .header-actions {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .view-mode-switch {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .user-stats {
    flex-direction: column;
    gap: 5px;
  }

  .user-header {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* 用户视图样式 */
.users-section {
  margin-bottom: 20px;
}

.user-item {
  margin-bottom: 15px;
}

.user-card {
  cursor: pointer;
  transition: all 0.3s;
}

.user-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  flex: 1;
}

.user-name {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.expand-icon {
  margin-right: 8px;
  transition: transform 0.3s;
  color: #909399;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.username-tag {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: normal;
}

.user-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat-item {
  font-size: 14px;
  color: #606266;
}

.user-details {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.details-header h4 {
  margin: 0;
  color: #303133;
  font-size: 14px;
}

.user-logs {
  max-height: 400px;
  overflow-y: auto;
}

.user-log-item {
  padding: 10px;
  margin-bottom: 8px;
  border-radius: 6px;
  border-left: 3px solid #e4e7ed;
  background-color: #fafbfc;
  transition: all 0.3s;
}

.user-log-item:hover {
  background-color: #f5f7fa;
}

.user-log-item.status-success {
  border-left-color: #67c23a;
  background-color: #f0f9ff;
}

.user-log-item.status-failed,
.user-log-item.status-error {
  border-left-color: #f56c6c;
  background-color: #fef0f0;
}

.log-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.log-summary .log-time {
  font-size: 12px;
  color: #909399;
}

.log-summary .log-status {
  font-size: 12px;
  font-weight: bold;
}

.log-summary .log-info {
  font-size: 12px;
  color: #606266;
  flex: 1;
}

.log-summary .log-duration {
  font-size: 12px;
  color: #909399;
}

.log-error {
  margin-top: 5px;
  font-size: 12px;
  color: #f56c6c;
}

.no-logs {
  text-align: center;
  padding: 20px;
}

.loading-logs {
  padding: 15px;
}

/* 学校视图样式 */
.schools-section {
  margin-bottom: 20px;
}

.school-item {
  margin-bottom: 15px;
}

.school-card {
  cursor: pointer;
  transition: all 0.3s;
}

.school-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.school-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.school-info {
  flex: 1;
}

.school-name {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 10px;
}

.school-name .expand-icon {
  margin-right: 8px;
  transition: transform 0.3s;
  color: #909399;
}

.school-name .expand-icon.expanded {
  transform: rotate(90deg);
}

.school-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.school-stats .stat-item {
  transition: all 0.3s ease;
}

.school-stats .stat-item:hover {
  transform: translateY(-1px);
  color: #409eff;
}

.success-high {
  color: #67c23a !important;
  font-weight: 600;
}

.success-medium {
  color: #e6a23c !important;
  font-weight: 600;
}

.success-low {
  color: #f56c6c !important;
  font-weight: 600;
}

.school-details {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.details-header h4 {
  margin: 0;
  color: #303133;
  font-size: 14px;
}

.school-daily-stats {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
}

.stats-table {
  width: 100%;
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 15px;
  padding: 10px 0;
  border-bottom: 2px solid #e4e7ed;
  font-weight: 600;
  color: #606266;
  font-size: 13px;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 15px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f2f5;
  font-size: 13px;
  color: #303133;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background-color: #f5f7fa;
}

.col-date {
  font-weight: 500;
}

.col-executions {
  text-align: center;
  color: #409eff;
  font-weight: 500;
}

.col-success-rate {
  text-align: center;
  color: #67c23a;
  font-weight: 500;
}

.col-avg-duration {
  text-align: center;
  color: #e6a23c;
  font-weight: 500;
}

.no-stats {
  text-align: center;
  padding: 20px;
}

.loading-stats {
  padding: 15px;
}

/* 响应式设计 - 学校视图 */
@media (max-width: 768px) {
  .school-stats {
    flex-direction: column;
    gap: 5px;
  }

  .school-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .stats-table {
    font-size: 12px;
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 5px;
  }

  .table-header {
    display: none;
  }

  .table-row {
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    margin-bottom: 8px;
    padding: 10px;
  }

  .col-date::before {
    content: "日期: ";
    font-weight: 600;
    color: #606266;
  }

  .col-executions::before {
    content: "执行次数: ";
    font-weight: 600;
    color: #606266;
  }

  .col-success-rate::before {
    content: "成功率: ";
    font-weight: 600;
    color: #606266;
  }

  .col-avg-duration::before {
    content: "平均耗时: ";
    font-weight: 600;
    color: #606266;
  }
}
</style>
