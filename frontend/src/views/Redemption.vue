<template>
  <div class="redemption-page">
    <!-- 页面标题 -->
    <el-card class="header-card" shadow="hover">
      <div class="header-content">
        <h2><el-icon><Present /></el-icon> 兑换码兑换</h2>
        <p>输入兑换码来增加您的使用天数</p>
      </div>
    </el-card>

    <!-- 兑换表单 -->
    <el-card class="redemption-form" shadow="hover">
      <h3>兑换兑换码</h3>
      <el-form 
        ref="redeemFormRef" 
        :model="redeemForm" 
        :rules="redeemRules"
        label-width="100px"
        @submit.prevent="handleRedeem">
        <el-form-item label="兑换码" prop="code">
          <el-input 
            v-model="redeemForm.code" 
            placeholder="请输入兑换码"
            :maxlength="20"
            clearable
            size="large"
            style="width: 400px;"
          >
            <template #prefix>
              <el-icon><Key /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button 
            type="primary" 
            size="large"
            @click="handleRedeem"
            :loading="loading"
            :disabled="!redeemForm.code"
          >
            <el-icon><Check /></el-icon>
            立即兑换
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 兑换历史 -->
    <el-card class="history-card" shadow="hover">
      <div class="history-header">
        <h3><el-icon><Clock /></el-icon> 兑换历史</h3>
        <el-button @click="loadHistory" :loading="historyLoading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      
      <el-table 
        :data="redemptionHistory" 
        stripe 
        v-loading="historyLoading"
        empty-text="暂无兑换记录">
        <el-table-column prop="code" label="兑换码" width="150" />
        <el-table-column prop="daysAdded" label="增加天数" width="100">
          <template #default="scope">
            <el-tag type="success">+{{ scope.row.daysAdded }}天</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="daysBefore" label="兑换前" width="100">
          <template #default="scope">
            {{ scope.row.daysBefore }}天
          </template>
        </el-table-column>
        <el-table-column prop="daysAfter" label="兑换后" width="100">
          <template #default="scope">
            {{ scope.row.daysAfter }}天
          </template>
        </el-table-column>
        <el-table-column prop="redemptionTime" label="兑换时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.redemptionTime) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container" v-if="historyTotal > 0">
        <el-pagination
          v-model:current-page="historyPage"
          v-model:page-size="historySize"
          :page-sizes="[10, 20, 50]"
          :total="historyTotal"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadHistory"
          @current-change="loadHistory"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Present, Key, Check, Clock, Refresh } from '@element-plus/icons-vue'
import api from '@/utils/api'

// 兑换表单
const redeemFormRef = ref()
const redeemForm = reactive({
  code: ''
})

const redeemRules = {
  code: [
    { required: true, message: '请输入兑换码', trigger: 'blur' },
    { min: 8, max: 20, message: '兑换码长度应在8-20位之间', trigger: 'blur' }
  ]
}

// 加载状态
const loading = ref(false)
const historyLoading = ref(false)

// 兑换历史
const redemptionHistory = ref([])
const historyPage = ref(1)
const historySize = ref(10)
const historyTotal = ref(0)

// 兑换码兑换
const handleRedeem = async () => {
  if (!redeemFormRef.value) return
  
  try {
    await redeemFormRef.value.validate()
    
    loading.value = true
    
    const response = await api.post('/user/redemption/redeem', {
      code: redeemForm.code
    })
    
    if (response.data.code === 200) {
      ElMessage.success(response.data.message)
      
      // 清空表单
      redeemForm.code = ''
      redeemFormRef.value.resetFields()
      
      // 刷新兑换历史
      await loadHistory()
      
      // 显示兑换结果
      const data = response.data.data
      ElMessageBox.alert(
        `恭喜您！兑换成功！\n增加天数：${data.daysAdded}天\n兑换前：${data.daysBefore}天\n兑换后：${data.daysAfter}天`,
        '兑换成功',
        {
          confirmButtonText: '确定',
          type: 'success'
        }
      )
    } else {
      ElMessage.error(response.data.message || '兑换失败')
    }
  } catch (error) {
    console.error('兑换失败:', error)
    ElMessage.error(error.response?.data?.message || '兑换失败，请重试')
  } finally {
    loading.value = false
  }
}

// 加载兑换历史
const loadHistory = async () => {
  try {
    historyLoading.value = true
    
    const response = await api.get('/user/redemption/history', {
      params: {
        page: historyPage.value,
        size: historySize.value
      }
    })
    
    if (response.data.code === 200) {
      const data = response.data.data
      redemptionHistory.value = data.records || []
      historyTotal.value = data.total || 0
    } else {
      ElMessage.error('获取兑换历史失败')
    }
  } catch (error) {
    console.error('获取兑换历史失败:', error)
    ElMessage.error('获取兑换历史失败')
  } finally {
    historyLoading.value = false
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 页面加载时获取兑换历史
onMounted(() => {
  loadHistory()
})
</script>

<style scoped>
.redemption-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header-card {
  margin-bottom: 20px;
}

.header-content {
  text-align: center;
}

.header-content h2 {
  margin: 0 0 10px 0;
  color: #409EFF;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.header-content p {
  margin: 0;
  color: #666;
}

.redemption-form {
  margin-bottom: 20px;
}

.redemption-form h3 {
  margin-top: 0;
  color: #333;
}

.history-card h3 {
  margin-top: 0;
  color: #333;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.history-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.el-card {
  border-radius: 8px;
}

.el-table {
  border-radius: 8px;
}
</style>
