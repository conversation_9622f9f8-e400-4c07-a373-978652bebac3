<template>
  <div class="room-management-container">
    <el-container>
      <el-header class="room-management-header">
        <div class="header-left">
          <el-button type="info" plain @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回首页
          </el-button>
          <h2>房间管理</h2>
        </div>
      </el-header>
      
      <el-main class="room-management-main">
        <el-tabs v-model="activeTab" class="management-tabs">
          <!-- 学校管理 -->
          <el-tab-pane label="学校管理" name="schools">
            <div class="tab-content">
              <div class="action-bar">
                <el-button type="primary" @click="showCreateSchoolDialog">
                  <el-icon><Plus /></el-icon>
                  添加学校
                </el-button>
                <el-button @click="refreshSchools">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
              
              <el-table 
                :data="schools" 
                style="width: 100%" 
                v-loading="schoolsLoading"
                stripe
              >
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="name" label="学校名称" />
                <el-table-column prop="waitTime" label="等待时间(秒)" width="150">
                  <template #default="{ row }">
                    {{ row.waitTime ? row.waitTime.toFixed(2) : '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="createdTime" label="创建时间" width="180">
                  <template #default="{ row }">
                    {{ formatDate(row.createdTime) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                  <template #default="{ row }">
                    <el-button size="small" @click="editSchool(row)">编辑</el-button>
                    <el-button size="small" type="danger" @click="deleteSchool(row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
          
          <!-- 房间管理 -->
          <el-tab-pane label="房间管理" name="rooms">
            <div class="tab-content">
              <div class="action-bar">
                <el-button type="primary" @click="showCreateRoomDialog">
                  <el-icon><Plus /></el-icon>
                  添加房间
                </el-button>
                <el-button @click="refreshRooms">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
                <el-select 
                  v-model="selectedSchoolFilter" 
                  placeholder="筛选学校" 
                  clearable
                  @change="filterRoomsBySchool"
                  style="width: 200px; margin-left: 10px;"
                >
                  <el-option
                    v-for="school in schools"
                    :key="school.id"
                    :label="school.name"
                    :value="school.id"
                  />
                </el-select>
              </div>
              
              <el-table 
                :data="filteredRooms" 
                style="width: 100%" 
                v-loading="roomsLoading"
                stripe
              >
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="name" label="房间名称" />
                <el-table-column label="所属学校" width="150">
                  <template #default="{ row }">
                    {{ getSchoolName(row.schoolId) }}
                  </template>
                </el-table-column>
                <el-table-column prop="roomId" label="房间编号" width="120" />
                <el-table-column label="版本" width="80">
                  <template #default="{ row }">
                    <el-tag :type="row.version === 0 ? 'success' : 'warning'" size="small">
                      v{{ row.version || 0 }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="座位ID" width="120">
                  <template #default="{ row }">
                    <span v-if="(row.version || 0) === 0" style="color: #409EFF; font-weight: 500;">
                      {{ row.seatId || '-' }}
                    </span>
                    <span v-else style="color: #909399; font-style: italic;">不适用</span>
                  </template>
                </el-table-column>
                <el-table-column prop="maxReservationHours" label="最大预约时长(小时)" width="150" />
                <el-table-column prop="description" label="描述" />
                <el-table-column prop="createdTime" label="创建时间" width="180">
                  <template #default="{ row }">
                    {{ formatDate(row.createdTime) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200">
                  <template #default="{ row }">
                    <el-button size="small" @click="editRoom(row)">编辑</el-button>
                    <el-button size="small" type="danger" @click="deleteRoom(row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-main>
    </el-container>
    
    <!-- 学校创建/编辑对话框 -->
    <el-dialog 
      v-model="schoolDialogVisible" 
      :title="schoolDialogMode === 'create' ? '添加学校' : '编辑学校'"
      width="500px"
    >
      <el-form 
        ref="schoolFormRef" 
        :model="schoolForm" 
        :rules="schoolRules" 
        label-width="100px"
      >
        <el-form-item label="学校名称" prop="name">
          <el-input v-model="schoolForm.name" placeholder="请输入学校名称" />
        </el-form-item>
        <el-form-item label="等待时间" prop="waitTime">
          <el-input-number
            v-model="schoolForm.waitTime"
            :min="0.00"
            :max="10.00"
            :step="0.01"
            :precision="2"
            placeholder="0.00 - 10.00 秒"
            style="width: 100%"
          />
          <div class="form-item-tip">学校预约操作的等待时间（秒），范围0.00-10.00秒，支持小数点后两位精度。设置为0表示无等待。</div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="schoolDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveSchool" :loading="saving">
            {{ schoolDialogMode === 'create' ? '创建' : '保存' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 房间创建/编辑对话框 -->
    <el-dialog 
      v-model="roomDialogVisible" 
      :title="roomDialogMode === 'create' ? '添加房间' : '编辑房间'"
      width="600px"
    >
      <el-form 
        ref="roomFormRef" 
        :model="roomForm" 
        :rules="roomRules" 
        label-width="120px"
      >
        <el-form-item label="所属学校" prop="schoolId">
          <el-select v-model="roomForm.schoolId" placeholder="请选择学校" style="width: 100%">
            <el-option
              v-for="school in schools"
              :key="school.id"
              :label="school.name"
              :value="school.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="房间名称" prop="name">
          <el-input v-model="roomForm.name" placeholder="请输入房间名称" />
        </el-form-item>
        
        <el-form-item label="房间编号" prop="roomId">
          <el-input v-model="roomForm.roomId" placeholder="请输入房间编号（可选）" />
        </el-form-item>

        <el-form-item label="版本类型" prop="version">
          <el-radio-group v-model="roomForm.version" @change="onVersionChange">
            <el-radio :label="0">
              <span style="color: #67C23A; font-weight: 500;">版本 0</span>
              <span style="color: #909399; font-size: 12px; margin-left: 8px;">（支持座位ID）</span>
            </el-radio>
            <el-radio :label="1">
              <span style="color: #E6A23C; font-weight: 500;">版本 1</span>
              <span style="color: #909399; font-size: 12px; margin-left: 8px;">（不使用座位ID）</span>
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          label="座位ID"
          v-if="roomForm.version === 0"
          prop="seatId"
        >
          <el-input
            v-model="roomForm.seatId"
            placeholder="请输入座位ID"
          />
        </el-form-item>

        <el-form-item label="最大预约时长" prop="maxReservationHours">
          <el-input-number 
            v-model="roomForm.maxReservationHours" 
            :min="0.5" 
            :max="24" 
            :step="0.5"
            placeholder="请输入最大预约时长（小时）"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input 
            v-model="roomForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入房间描述（可选）"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="roomDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRoom" :loading="saving">
            {{ roomDialogMode === 'create' ? '创建' : '保存' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Plus, Refresh } from '@element-plus/icons-vue'
import api from '@/utils/api'

const router = useRouter()

// 响应式数据
const activeTab = ref('schools')
const schools = ref([])
const rooms = ref([])
const schoolsLoading = ref(false)
const roomsLoading = ref(false)
const saving = ref(false)
const selectedSchoolFilter = ref(null)

// 学校对话框
const schoolDialogVisible = ref(false)
const schoolDialogMode = ref('create') // 'create' | 'edit'
const schoolFormRef = ref(null)
const schoolForm = reactive({
  id: null,
  name: '',
  waitTime: 0.3
})

const schoolRules = {
  name: [
    { required: true, message: '请输入学校名称', trigger: 'blur' },
    { min: 2, max: 50, message: '学校名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  waitTime: [
    { required: true, message: '请输入等待时间', trigger: 'blur' },
    {
      type: 'number',
      min: 0.00,
      max: 10.00,
      message: '等待时间必须在 0.00 到 10.00 秒之间，支持小数点后两位精度',
      trigger: ['blur', 'change']
    }
  ]
}

// 房间对话框
const roomDialogVisible = ref(false)
const roomDialogMode = ref('create') // 'create' | 'edit'
const roomFormRef = ref(null)
const roomForm = reactive({
  id: null,
  schoolId: null,
  name: '',
  roomId: '',
  version: 0,
  seatId: '',
  maxReservationHours: 8,
  description: ''
})

const roomRules = {
  schoolId: [
    { required: true, message: '请选择所属学校', trigger: 'change' }
  ],
  name: [
    { required: true, message: '请输入房间名称', trigger: 'blur' },
    { min: 2, max: 50, message: '房间名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  version: [
    { required: true, message: '请选择版本类型', trigger: 'change' }
  ],
  seatId: [
    {
      validator: (rule, value, callback) => {
        if (roomForm.version === 0 && (!value || value.trim() === '')) {
          callback(new Error('版本0需要输入座位ID'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 计算属性
const filteredRooms = computed(() => {
  if (!selectedSchoolFilter.value) {
    return rooms.value || []
  }
  return (rooms.value || []).filter(room => room.schoolId === selectedSchoolFilter.value)
})

// 方法
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

const getSchoolName = (schoolId) => {
  if (!schools.value || !Array.isArray(schools.value)) {
    return '未知学校'
  }
  const school = schools.value.find(s => s.id === schoolId)
  return school ? school.name : '未知学校'
}

const goBack = () => {
  router.push('/dashboard')
}

// 版本变化处理
const onVersionChange = (version) => {
  if (version === 1) {
    // 版本1不使用座位ID，清空座位ID字段
    roomForm.seatId = ''
  }
}

// 学校管理方法
const fetchSchools = async () => {
  schoolsLoading.value = true
  try {
    const response = await api.get('/admin/room-management/schools')
    if (response.data.code === 200) {
      schools.value = response.data.data || []
    } else {
      console.error('获取学校列表失败:', response.data.message)
      ElMessage.error('获取学校列表失败: ' + response.data.message)
      schools.value = [] // 确保schools是数组
    }
  } catch (error) {
    console.error('获取学校列表失败:', error)
    ElMessage.error('获取学校列表失败，请检查后端服务是否正常运行')
    schools.value = [] // 确保schools是数组
  } finally {
    schoolsLoading.value = false
  }
}

const refreshSchools = () => {
  fetchSchools()
}

const showCreateSchoolDialog = () => {
  schoolDialogMode.value = 'create'
  Object.assign(schoolForm, {
    id: null,
    name: '',
    waitTime: 0.50
  })
  schoolDialogVisible.value = true
}

const editSchool = (school) => {
  schoolDialogMode.value = 'edit'
  Object.assign(schoolForm, {
    id: school.id,
    name: school.name,
    waitTime: school.waitTime
  })
  schoolDialogVisible.value = true
}

const saveSchool = async () => {
  try {
    await schoolFormRef.value?.validate()
    saving.value = true

    let response
    if (schoolDialogMode.value === 'create') {
      response = await api.post('/admin/room-management/schools', {
        name: schoolForm.name,
        waitTime: schoolForm.waitTime
      })
    } else {
      response = await api.put(`/admin/room-management/schools/${schoolForm.id}`, {
        name: schoolForm.name,
        waitTime: schoolForm.waitTime
      })
    }

    if (response.data.code === 200) {
      ElMessage.success(schoolDialogMode.value === 'create' ? '创建学校成功' : '更新学校成功')
      schoolDialogVisible.value = false
      await fetchSchools()
    } else {
      ElMessage.error(response.data.message || '操作失败')
    }
  } catch (error) {
    if (error !== false) {
      ElMessage.error('操作失败，请重试')
    }
  } finally {
    saving.value = false
  }
}

const deleteSchool = async (school) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除学校"${school.name}"吗？删除后该学校下的所有房间也将被删除！`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await api.delete(`/admin/room-management/schools/${school.id}`)
    if (response.data.code === 200) {
      ElMessage.success('删除学校成功')
      await fetchSchools()
      await fetchRooms() // 刷新房间列表
    } else {
      ElMessage.error(response.data.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败，请重试')
    }
  }
}

// 房间管理方法
const fetchRooms = async () => {
  roomsLoading.value = true
  try {
    const response = await api.get('/admin/room-management/rooms')
    if (response.data.code === 200) {
      rooms.value = response.data.data || []
    } else {
      console.error('获取房间列表失败:', response.data.message)
      ElMessage.error('获取房间列表失败: ' + response.data.message)
      rooms.value = [] // 确保rooms是数组
    }
  } catch (error) {
    console.error('获取房间列表失败:', error)
    ElMessage.error('获取房间列表失败，请检查后端服务是否正常运行')
    rooms.value = [] // 确保rooms是数组
  } finally {
    roomsLoading.value = false
  }
}

const refreshRooms = () => {
  fetchRooms()
}

const filterRoomsBySchool = () => {
  // 筛选逻辑已在计算属性中实现
}

const showCreateRoomDialog = () => {
  roomDialogMode.value = 'create'
  Object.assign(roomForm, {
    id: null,
    schoolId: null,
    name: '',
    roomId: '',
    version: 0,
    seatId: '',
    maxReservationHours: 8,
    description: ''
  })
  roomDialogVisible.value = true
}

const editRoom = (room) => {
  roomDialogMode.value = 'edit'
  Object.assign(roomForm, {
    id: room.id,
    schoolId: room.schoolId,
    name: room.name,
    roomId: room.roomId,
    version: room.version !== undefined ? room.version : 0,
    seatId: room.seatId || '',
    maxReservationHours: room.maxReservationHours,
    description: room.description
  })
  roomDialogVisible.value = true
}

const saveRoom = async () => {
  try {
    await roomFormRef.value?.validate()
    saving.value = true

    const roomData = {
      schoolId: roomForm.schoolId,
      name: roomForm.name,
      roomId: roomForm.roomId,
      version: roomForm.version,
      seatId: roomForm.version === 0 ? roomForm.seatId : null,
      maxReservationHours: roomForm.maxReservationHours,
      description: roomForm.description
    }

    let response
    if (roomDialogMode.value === 'create') {
      response = await api.post('/admin/room-management/rooms', roomData)
    } else {
      response = await api.put(`/admin/room-management/rooms/${roomForm.id}`, roomData)
    }

    if (response.data.code === 200) {
      ElMessage.success(roomDialogMode.value === 'create' ? '创建房间成功' : '更新房间成功')
      roomDialogVisible.value = false
      await fetchRooms()
    } else {
      ElMessage.error(response.data.message || '操作失败')
    }
  } catch (error) {
    if (error !== false) {
      ElMessage.error('操作失败，请重试')
    }
  } finally {
    saving.value = false
  }
}

const deleteRoom = async (room) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除房间"${room.name}"吗？删除后该房间的所有预约记录也将被删除！`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await api.delete(`/admin/room-management/rooms/${room.id}`)
    if (response.data.code === 200) {
      ElMessage.success('删除房间成功')
      await fetchRooms()
    } else {
      ElMessage.error(response.data.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败，请重试')
    }
  }
}

// 生命周期
onMounted(async () => {
  await fetchSchools()
  await fetchRooms()
})
</script>

<style scoped>
.room-management-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.room-management-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-left h2 {
  margin: 0;
  color: #333;
  font-weight: 600;
}

.room-management-main {
  padding: 20px;
}

.management-tabs {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.tab-content {
  margin-top: 20px;
}

.action-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 10px;
}

.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .room-management-main {
    padding: 10px;
  }

  .management-tabs {
    padding: 15px;
  }

  .action-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .action-bar .el-select {
    margin-left: 0 !important;
    margin-top: 10px;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  /* 防止 ResizeObserver 循环 */
  contain: layout style;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #e9ecef;
}

:deep(.el-table tr:hover > td) {
  background-color: #f8f9fa;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 12px;
  /* 防止 ResizeObserver 循环 */
  contain: layout style;
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  padding: 20px;
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
}

:deep(.el-dialog__body) {
  padding: 30px 20px;
}

/* 标签页样式 */
:deep(.el-tabs__header) {
  margin-bottom: 0;
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: #e4e7ed;
}

:deep(.el-tabs__active-bar) {
  background-color: #667eea;
}

:deep(.el-tabs__item.is-active) {
  color: #667eea;
  font-weight: 600;
}

:deep(.el-tabs__item:hover) {
  color: #667eea;
}
</style>
