<template>
  <div class="profile-container">
    <el-container>
      <el-header class="profile-header">
        <div class="header-left">
          <el-button type="info" plain @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回首页
          </el-button>
          <h2>个人信息</h2>
        </div>
        <div class="header-right">
          <span class="welcome-text">{{ user?.name }}</span>
          <el-button type="primary" @click="handleLogout">退出登录</el-button>
        </div>
      </el-header>
      
      <el-main class="profile-main">
        <div class="profile-content">
          <el-row :gutter="30">
            <!-- 当前预约信息 -->
            <el-col :span="24" v-if="currentReservation">
              <el-card class="reservation-card">
                <template #header>
                  <div class="card-header">
                    <span>当前预约信息</span>
                    <el-tag type="success">进行中</el-tag>
                  </div>
                </template>
                
                <el-row :gutter="20">
                  <el-col :xs="12" :sm="6">
                    <div class="info-item">
                      <span class="label">学校：</span>
                      <span class="value">{{ currentReservation.schoolName }}</span>
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="6">
                    <div class="info-item">
                      <span class="label">房间：</span>
                      <span class="value">{{ currentReservation.roomName }}</span>
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="6">
                    <div class="info-item">
                      <span class="label">座位号：</span>
                      <span class="value">{{ currentReservation.seatId }}</span>
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="6">
                    <div class="info-item">
                      <span class="label">状态：</span>
                      <el-tag :type="getStatusType(currentReservation.status)">
                        {{ getStatusText(currentReservation.status) }}
                      </el-tag>
                    </div>
                  </el-col>
                </el-row>
                
                <el-divider />
                
                <el-row :gutter="20">
                  <el-col :xs="24" :sm="12">
                    <div class="info-item">
                      <span class="label">开始时间：</span>
                      <span class="value">{{ formatDateTime(currentReservation.startTime) }}</span>
                    </div>
                  </el-col>
                  <el-col :xs="24" :sm="12">
                    <div class="info-item">
                      <span class="label">结束时间：</span>
                      <span class="value">{{ formatDateTime(currentReservation.endTime) }}</span>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
            </el-col>
            
            <!-- 无预约提示 -->
            <el-col :span="24" v-else>
              <el-card class="no-reservation-card">
                <el-empty description="暂无预约信息" />
              </el-card>
            </el-col>
            
            <!-- 个人信息展示 -->
            <el-col :xs="24" :md="12">
              <el-card class="info-card">
                <template #header>
                  <div class="card-header">
                    <span>个人信息</span>
                    <el-button 
                      type="primary" 
                      size="small" 
                      @click="toggleEdit"
                      :disabled="loading"
                    >
                      {{ isEditing ? '取消编辑' : '编辑信息' }}
                    </el-button>
                  </div>
                </template>
                
                <el-form 
                  :model="userForm" 
                  label-width="80px" 
                  :disabled="!isEditing"
                  ref="userFormRef"
                  :rules="userRules"
                >
                  <el-form-item label="用户名">
                    <el-input v-model="userForm.username" disabled />
                  </el-form-item>
                  
                  <el-form-item label="姓名" prop="name">
                    <el-input 
                      v-model="userForm.name" 
                      :readonly="!isEditing"
                      placeholder="请输入真实姓名"
                    />
                  </el-form-item>
                  
                  <el-form-item label="角色">
                    <el-tag :type="user?.role === 'ADMIN' ? 'danger' : 'primary'">
                      {{ user?.role === 'ADMIN' ? '管理员' : '普通用户' }}
                    </el-tag>
                  </el-form-item>
                  
                  <el-form-item label="注册时间">
                    <span>{{ formatDate(user?.createdTime) }}</span>
                  </el-form-item>
                  
                  <el-form-item v-if="isEditing">
                    <el-button 
                      type="primary" 
                      @click="saveUserInfo"
                      :loading="loading"
                    >
                      保存修改
                    </el-button>
                    <el-button @click="resetForm">重置</el-button>
                  </el-form-item>
                </el-form>
              </el-card>
            </el-col>
            
            <!-- 修改密码 -->
            <el-col :xs="24" :md="12">
              <el-card class="password-card">
                <template #header>
                  <span>修改密码</span>
                </template>
                
                <el-form 
                  :model="passwordForm" 
                  label-width="100px"
                  ref="passwordFormRef"
                  :rules="passwordRules"
                >
                  <el-form-item label="新密码" prop="newPassword">
                    <el-input 
                      v-model="passwordForm.newPassword" 
                      type="password" 
                      show-password
                      placeholder="请输入新密码"
                    />
                  </el-form-item>
                  
                  <el-form-item label="确认密码" prop="confirmPassword">
                    <el-input 
                      v-model="passwordForm.confirmPassword" 
                      type="password" 
                      show-password
                      placeholder="请再次输入新密码"
                    />
                  </el-form-item>
                  
                  <el-form-item>
                    <el-button 
                      type="primary" 
                      @click="changePassword"
                      :loading="passwordLoading"
                    >
                      修改密码
                    </el-button>
                    <el-button @click="resetPasswordForm">重置</el-button>
                  </el-form-item>
                </el-form>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import api from '@/utils/api'

const router = useRouter()
const authStore = useAuthStore()

const user = computed(() => authStore.user)
const currentReservation = ref(null)
const isEditing = ref(false)
const loading = ref(false)
const passwordLoading = ref(false)

// 表单引用
const userFormRef = ref(null)
const passwordFormRef = ref(null)

// 用户信息表单
const userForm = reactive({
  username: '',
  name: ''
})

// 密码修改表单
const passwordForm = reactive({
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const userRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ]
}

const passwordRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 获取状态类型
const getStatusType = (status) => {
  switch (status) {
    case 'ACTIVE': return 'success'
    case 'CANCELLED': return 'danger'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'ACTIVE': return '进行中'
    case 'CANCELLED': return '已取消'
    default: return '未知'
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 格式化日期
const formatDate = (dateTime) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleDateString('zh-CN')
}

// 获取用户信息
const fetchUserProfile = async () => {
  try {
    loading.value = true
    const response = await api.get('/user/profile')
    console.log('API响应:', response.data)
    if (response.data.code === 200) {
      const userData = response.data.data
      console.log('用户数据:', userData)
      console.log('当前预约:', userData.currentReservation)
      authStore.updateUser(userData)
      currentReservation.value = userData.currentReservation
      initUserForm()
    } else {
      ElMessage.error(response.data.message || '获取用户信息失败')
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  } finally {
    loading.value = false
  }
}

// 初始化用户信息
const initUserForm = () => {
  if (user.value) {
    userForm.username = user.value.username
    userForm.name = user.value.name
  }
}

// 切换编辑状态
const toggleEdit = () => {
  if (isEditing.value) {
    // 取消编辑，重置表单
    resetForm()
  }
  isEditing.value = !isEditing.value
}

// 重置用户信息表单
const resetForm = () => {
  initUserForm()
  userFormRef.value?.clearValidate()
}

// 重置密码表单
const resetPasswordForm = () => {
  Object.assign(passwordForm, {
    newPassword: '',
    confirmPassword: ''
  })
  passwordFormRef.value?.clearValidate()
}

// 保存用户信息
const saveUserInfo = async () => {
  try {
    await userFormRef.value?.validate()
    loading.value = true
    
    // 调用API更新用户信息
    const response = await api.put('/user/profile', {
      name: userForm.name
    })
    
    if (response.data.code === 200) {
      // 更新store中的用户信息
      authStore.updateUser({ name: userForm.name })
      ElMessage.success('个人信息更新成功')
      isEditing.value = false
    } else {
      ElMessage.error(response.data.message || '更新失败')
    }
    
  } catch (error) {
    console.error('更新用户信息失败:', error)
    if (error !== false) { // 不是表单验证失败
      ElMessage.error(error.response?.data?.message || '更新失败，请重试')
    }
  } finally {
    loading.value = false
  }
}

// 修改密码
const changePassword = async () => {
  try {
    await passwordFormRef.value?.validate()
    
    await ElMessageBox.confirm('确定要修改密码吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    passwordLoading.value = true
    
    // 调用API修改密码
    const response = await api.put('/user/password', {
      newPassword: passwordForm.newPassword
    })
    
    if (response.data.code === 200) {
      ElMessage.success('密码修改成功，请重新登录')
      resetPasswordForm()
      
      // 密码修改成功后，退出登录
      setTimeout(() => {
        authStore.logout()
        router.push('/login')
      }, 1500)
    } else {
      ElMessage.error(response.data.message || '修改失败')
    }
    
  } catch (error) {
    console.error('修改密码失败:', error)
    if (error !== 'cancel' && error !== false) {
      ElMessage.error(error.response?.data?.message || '修改失败，请重试')
    }
  } finally {
    passwordLoading.value = false
  }
}

// 返回首页
const goBack = () => {
  router.push('/dashboard')
}

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    authStore.logout()
    ElMessage.success('已退出登录')
    router.push('/login')
  } catch {
    // 用户取消操作
  }
}

onMounted(() => {
  fetchUserProfile()
})
</script>

<style scoped>
.profile-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.profile-header {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h2 {
  color: #333;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.welcome-text {
  color: #666;
  font-size: 14px;
}

.profile-main {
  padding: 20px;
}

.profile-content {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-card, .password-card {
  height: 100%;
}

/* 预约信息卡片样式 */
.reservation-card {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.reservation-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom: none;
}

.reservation-card :deep(.el-card__header) .card-header {
  color: white;
}

.reservation-card :deep(.el-card__header) .card-header span {
  font-weight: 600;
  font-size: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  margin-right: 8px;
}

.info-item .value {
  color: #303133;
  font-weight: 600;
  flex: 1;
}

.no-reservation-card {
  margin-bottom: 20px;
  text-align: center;
}

.no-reservation-card :deep(.el-empty__description) {
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .profile-header {
    padding: 0 10px;
  }
  
  .header-left h2 {
    font-size: 18px;
  }
  
  .profile-main {
    padding: 10px;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .info-item .label {
    min-width: auto;
    margin-bottom: 4px;
  }
}
</style> 