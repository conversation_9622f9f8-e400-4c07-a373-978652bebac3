<template>
  <div class="distributed-task-management-container">
    <el-container>
      <!-- 页面头部 -->
      <el-header class="page-header">
        <div class="header-left">
          <el-button type="info" plain @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回首页
          </el-button>
          <h2>📊 分布式任务管理</h2>
          <p class="header-subtitle">智能分配预约任务，实时监控副服务器，实现高效的分布式处理</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="refreshAllData" :loading="refreshing">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
          <el-button type="success" @click="autoAssignTasks" :loading="autoAssigning">
            <el-icon><Setting /></el-icon>
            自动分配
          </el-button>
        </div>
      </el-header>

      <!-- 主要内容区域 -->
      <el-main class="page-main">
        <div class="main-content">
          
          <!-- 系统状态概览 -->
          <div class="overview-section">
            <h3 class="section-title">
              <el-icon><DataAnalysis /></el-icon>
              系统状态概览
            </h3>
            <div class="status-cards-grid">
              <TaskStatusCard
                type="completed"
                :value="statistics.completedTasks"
                title="已完成任务"
                :subtitle="`今日完成 ${statistics.todayCompletedTasks}`"
                :loading="statisticsLoading"
              />
              <TaskStatusCard
                type="pending"
                :value="statistics.pendingTasks"
                title="待执行任务"
                :subtitle="`今日新增 ${statistics.todayNewTasks}`"
                :loading="statisticsLoading"
              />
              <TaskStatusCard
                type="running"
                :value="statistics.runningTasks"
                title="执行中任务"
                :subtitle="`系统负载 ${statistics.systemLoadRate?.toFixed(1)}%`"
                :loading="statisticsLoading"
              />
              <TaskStatusCard
                type="success-rate"
                :value="statistics.successRate"
                title="成功率"
                :subtitle="`失败任务 ${statistics.failedTasks}`"
                :loading="statisticsLoading"
              />
            </div>
          </div>

          <!-- 预约任务列表 -->
          <div class="tasks-section">
            <el-card class="tasks-card">
              <template #header>
                <div class="card-header">
                  <div class="header-left">
                    <h3 class="section-title">
                      <el-icon><List /></el-icon>
                      预约任务列表
                    </h3>
                  </div>
                  <div class="header-right">
                    <el-input
                      v-model="searchKeyword"
                      placeholder="搜索用户名、座位号..."
                      style="width: 200px; margin-right: 12px;"
                      clearable
                      @input="handleSearch"
                    >
                      <template #prefix>
                        <el-icon><Search /></el-icon>
                      </template>
                    </el-input>
                    <el-select
                      v-model="statusFilter"
                      placeholder="筛选状态"
                      style="width: 120px;"
                      clearable
                      @change="handleFilter"
                    >
                      <el-option label="全部" value="" />
                      <el-option label="待执行" value="PENDING" />
                      <el-option label="执行中" value="RUNNING" />
                      <el-option label="已完成" value="SUCCESS" />
                      <el-option label="失败" value="FAILED" />
                    </el-select>
                  </div>
                </div>
              </template>

              <!-- 任务表格 -->
              <el-table
                v-loading="tasksLoading"
                :data="tasksList"
                @selection-change="handleSelectionChange"
                stripe
                style="width: 100%"
              >
                <el-table-column type="selection" width="55" />
                
                <el-table-column prop="reservationId" label="任务ID" width="80" />
                
                <el-table-column label="用户信息" width="150">
                  <template #default="scope">
                    <div class="user-info">
                      <div class="user-name">{{ scope.row.userName }}</div>
                      <div class="username">{{ scope.row.username }}</div>
                    </div>
                  </template>
                </el-table-column>
                
                <el-table-column label="学校信息" width="120">
                  <template #default="scope">
                    {{ scope.row.schoolName }}
                  </template>
                </el-table-column>
                
                <el-table-column label="座位" width="80">
                  <template #default="scope">
                    {{ scope.row.seatId }}
                  </template>
                </el-table-column>
                
                <el-table-column label="预约开放时间" width="120">
                  <template #default="scope">
                    {{ scope.row.reservationOpenTime }}
                  </template>
                </el-table-column>

                <el-table-column label="使用时间段" width="140">
                  <template #default="scope">
                    {{ formatTimeRange(scope.row.startTime, scope.row.endTime) }}
                  </template>
                </el-table-column>
                
                <el-table-column label="分配服务器" width="120">
                  <template #default="scope">
                    <el-tag
                      v-if="scope.row.workerId"
                      type="info"
                      size="small"
                      class="clickable-server-tag"
                      @click="openServerChangeDialog(scope.row)"
                    >
                      {{ scope.row.workerName || scope.row.workerId }}
                    </el-tag>
                    <el-tag
                      v-else
                      type="warning"
                      size="small"
                      class="clickable-server-tag"
                      @click="openServerChangeDialog(scope.row)"
                    >
                      未分配
                    </el-tag>
                  </template>
                </el-table-column>
                

                
                <el-table-column label="重试次数" width="80">
                  <template #default="scope">
                    {{ scope.row.retryCount || 0 }} / 3
                  </template>
                </el-table-column>
                
                <el-table-column label="创建时间" width="140">
                  <template #default="scope">
                    {{ formatDateTime(scope.row.createdTime) }}
                  </template>
                </el-table-column>
                
                <el-table-column label="操作" width="240" fixed="right">
                  <template #default="scope">
                    <div class="action-buttons">
                      <el-button
                        type="primary"
                        size="small"
                        @click="showTaskDetail(scope.row.reservationId)"
                      >
                        详情
                      </el-button>
                      <el-button
                        v-if="canAssign(scope.row)"
                        type="success"
                        size="small"
                        @click="showAssignDialog(scope.row)"
                      >
                        分配
                      </el-button>

                      <el-button
                        v-if="canExecuteImmediately(scope.row)"
                        type="danger"
                        size="small"
                        @click="executeTaskImmediately(scope.row)"
                        :loading="scope.row.executing"
                      >
                        立即执行
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分页 -->
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="totalTasks"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </el-card>
          </div>

          <!-- 副服务器状态 -->
          <div class="servers-section">
            <h3 class="section-title">
              <el-icon><Monitor /></el-icon>
              副服务器状态
            </h3>
            <div class="servers-grid">
              <ServerStatusCard
                v-for="(server, index) in workerStatus"
                :key="server.workerId || index"
                :server-data="server"
                :type="getServerType(index)"
                @click="handleServerClick"
              />
            </div>
          </div>

        </div>
      </el-main>
    </el-container>

    <!-- 任务详情弹窗 -->
    <TaskDetailDialog
      v-model:visible="taskDetailVisible"
      :task-id="selectedTaskId"
      @refresh="refreshTasksList"
    />

    <!-- 手动分配弹窗 -->
    <el-dialog
      v-model="assignDialogVisible"
      title="手动分配任务"
      width="400px"
    >
      <el-form :model="assignForm" label-width="100px">
        <el-form-item label="任务信息:">
          <div class="task-info">
            <p>任务ID: {{ selectedTask?.reservationId }}</p>
            <p>用户: {{ selectedTask?.userName }}</p>
            <p>座位: {{ selectedTask?.seatId }}</p>
          </div>
        </el-form-item>
        <el-form-item label="选择服务器:">
          <el-select 
            v-model="assignForm.workerId" 
            placeholder="请选择副服务器"
            style="width: 100%"
          >
            <el-option
              v-for="worker in availableWorkers"
              :key="worker.workerId"
              :label="`${worker.name} (负载: ${worker.currentLoad}/${worker.maxConcurrentTasks})`"
              :value="worker.workerId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="assignDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleAssignTask"
          :loading="assignLoading"
        >
          确认分配
        </el-button>
      </template>
    </el-dialog>

    <!-- 修改服务器分配对话框 -->
    <el-dialog
      v-model="changeServerDialogVisible"
      title="修改服务器分配"
      width="500px"
    >
      <el-form :model="changeServerForm" label-width="100px">
        <el-form-item label="任务信息:">
          <div class="task-info">
            <p>任务ID: {{ selectedTask?.reservationId }}</p>
            <p>用户: {{ selectedTask?.username }}</p>
            <p>座位: {{ selectedTask?.seatId }}</p>
          </div>
        </el-form-item>
        <el-form-item label="当前服务器:">
          <el-tag v-if="selectedTask?.workerId" type="info">
            {{ selectedTask?.workerName || selectedTask?.workerId }}
          </el-tag>
          <el-tag v-else type="warning">未分配</el-tag>
        </el-form-item>
        <el-form-item label="新服务器:">
          <el-select
            v-model="changeServerForm.workerId"
            placeholder="请选择新的副服务器"
            style="width: 100%"
          >
            <el-option
              v-for="worker in availableWorkers"
              :key="worker.workerId"
              :label="`${worker.name} (负载: ${worker.currentLoad}/${worker.maxConcurrentTasks})`"
              :value="worker.workerId"
            />
            <el-option
              label="取消分配"
              value=""
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="changeServerDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleChangeServer"
          :loading="changeServerLoading"
        >
          确认修改
        </el-button>
      </template>
    </el-dialog>

    <!-- 服务器时间段分布弹窗 -->
    <el-dialog
      v-model="serverScheduleDialogVisible"
      :title="`时间段任务分布 - ${selectedServer?.name || '未知服务器'}`"
      width="800px"
      top="5vh"
    >
      <div v-loading="scheduleLoading" class="server-schedule-content">
        <div class="schedule-header">
          <div class="server-info">
            <el-tag type="primary" size="large">{{ selectedServer?.workerId }}</el-tag>
            <span class="server-name">{{ selectedServer?.name }}</span>
            <span class="task-summary">共 {{ serverTasks.length }} 个任务</span>
          </div>
        </div>

        <div class="time-slots-container">
          <div v-if="timeSlots.length === 0" class="no-tasks-message">
            <el-empty
              description="该服务器暂无分配任务"
              :image-size="80"
            />
          </div>
          <div
            v-for="(slot, index) in timeSlots"
            :key="slot.timeRange"
            class="time-slot-item"
            :class="{ 'has-tasks': slot.taskCount > 0, 'expanded': slot.expanded }"
          >
            <div
              class="time-slot-header"
              @click="toggleTimeSlot(index)"
            >
              <div class="time-slot-info">
                <el-icon class="expand-icon">
                  <ArrowRight v-if="!slot.expanded" />
                  <ArrowDown v-else />
                </el-icon>
                <span class="time-range">{{ slot.timeRange }}</span>
                <el-tag
                  :type="slot.taskCount > 0 ? 'primary' : 'info'"
                  size="small"
                  round
                >
                  {{ slot.taskCount }}个任务
                </el-tag>
              </div>
            </div>

            <div v-if="slot.expanded" class="time-slot-content">
              <div v-if="slot.taskCount === 0" class="no-tasks">
                <el-empty
                  description="该时间段暂无任务"
                  :image-size="60"
                />
              </div>
              <div v-else class="tasks-list">
                <div
                  v-for="task in slot.tasks"
                  :key="task.reservationId"
                  class="task-item"
                >
                  <div class="task-info">
                    <div class="task-main">
                      <span class="task-user">{{ task.username }}</span>
                      <span class="task-school">{{ task.schoolName }}</span>
                      <span class="task-time">{{ task.reservationOpenTime }}</span>
                    </div>
                    <div class="task-details">
                      <span class="task-room">{{ task.roomName }}</span>
                      <span class="task-seat">座位{{ task.seatId }}</span>
                    </div>
                  </div>
                  <div class="task-actions">
                    <el-button
                      size="small"
                      type="primary"
                      link
                      @click="showTaskDetail(task.reservationId)"
                    >
                      详情
                    </el-button>
                    <el-button
                      v-if="canAssign(task)"
                      size="small"
                      type="success"
                      link
                      @click="showAssignDialog(task)"
                    >
                      分配服务器
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="serverScheduleDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft, Refresh, Setting, DataAnalysis, List, Search, Monitor, ArrowRight, ArrowDown
} from '@element-plus/icons-vue'
import TaskStatusCard from '@/components/TaskStatusCard.vue'
import ServerStatusCard from '@/components/ServerStatusCard.vue'
import TaskDetailDialog from '@/components/TaskDetailDialog.vue'
import distributedTaskApi from '@/api/distributedTask'
import { createWorkerApi } from '@/utils/api' // 直连Worker API


const router = useRouter()

// 响应式数据
const refreshing = ref(false)
const autoAssigning = ref(false)
const statisticsLoading = ref(false)
const tasksLoading = ref(false)
const assignLoading = ref(false)

// 统计数据
const statistics = ref({
  completedTasks: 0,
  pendingTasks: 0,
  runningTasks: 0,
  failedTasks: 0,
  successRate: 0,
  todayNewTasks: 0,
  todayCompletedTasks: 0,
  systemLoadRate: 0
})

// 任务列表
const tasksList = ref([])
const totalTasks = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const searchKeyword = ref('')
const statusFilter = ref('')
const selectedTasks = ref([])

// 副服务器状态
const workerStatus = ref([])

// 弹窗相关
const taskDetailVisible = ref(false)
const selectedTaskId = ref(null)
const assignDialogVisible = ref(false)
const changeServerDialogVisible = ref(false)
const selectedTask = ref(null)
const availableWorkers = ref([])
const changeServerLoading = ref(false)
const assignForm = reactive({
  workerId: ''
})
const changeServerForm = reactive({
  workerId: ''
})

// 服务器时间段分布弹窗
const serverScheduleDialogVisible = ref(false)
const selectedServer = ref(null)
const serverTasks = ref([])
const timeSlots = ref([])
const scheduleLoading = ref(false)

// 自动刷新定时器
let refreshTimer = null

// 计算属性
const canAssign = (task) => {
  return true // 简化逻辑，所有任务都可以分配
}



// 判断是否可以立即执行
const canExecuteImmediately = (task) => {
  // 对所有已分配给副服务器的任务都显示立即执行按钮，方便测试
  return task.workerId && (task.retryCount || 0) < 3
}

// 页面方法
const goBack = () => {
  router.push('/dashboard')
}

const refreshAllData = async () => {
  refreshing.value = true
  try {
    await Promise.all([
      fetchStatistics(),
      fetchTasksList(),
      fetchWorkerStatus()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    console.error('刷新数据失败:', error)
    ElMessage.error('数据刷新失败，请检查网络连接')
  } finally {
    refreshing.value = false
  }
}

const autoAssignTasks = async () => {
  autoAssigning.value = true
  try {
    const response = await distributedTaskApi.autoAssignPendingTasks()
    if (response.data.code === 200) {
      const result = response.data.data
      ElMessage.success(`自动分配完成：成功分配 ${result.assignedCount} 个任务`)
      await refreshTasksList()
      await fetchStatistics()
    } else {
      ElMessage.error('自动分配失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('自动分配失败:', error)
    ElMessage.error('自动分配失败，请检查网络连接')
  } finally {
    autoAssigning.value = false
  }
}

// 数据获取方法
const fetchStatistics = async () => {
  statisticsLoading.value = true
  try {
    const response = await distributedTaskApi.getTaskStatistics()
    if (response.data.code === 200) {
      statistics.value = response.data.data || {}
    } else {
      console.error('获取统计数据失败:', response.data.message)
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  } finally {
    statisticsLoading.value = false
  }
}

const fetchTasksList = async () => {
  tasksLoading.value = true
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      status: statusFilter.value,
      keyword: searchKeyword.value
    }

    const response = await distributedTaskApi.getReservationTasks(params)
    if (response.data.code === 200) {
      const pageData = response.data.data
      tasksList.value = pageData.records || []
      totalTasks.value = pageData.total || 0
    } else {
      console.error('获取任务列表失败:', response.data.message)
      ElMessage.error('获取任务列表失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    ElMessage.error('获取任务列表失败，请检查网络连接')
  } finally {
    tasksLoading.value = false
  }
}

const fetchWorkerStatus = async () => {
  try {
    const response = await distributedTaskApi.getWorkerStatusOverview()
    if (response.data.code === 200) {
      workerStatus.value = response.data.data || []
    } else {
      console.error('获取副服务器状态失败:', response.data.message)
    }
  } catch (error) {
    console.error('获取副服务器状态失败:', error)
  }
}

// 事件处理方法
const handleSearch = () => {
  currentPage.value = 1
  fetchTasksList()
}

const handleFilter = () => {
  currentPage.value = 1
  fetchTasksList()
}

const handleSelectionChange = (selection) => {
  selectedTasks.value = selection
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  currentPage.value = 1
  fetchTasksList()
}

const handleCurrentChange = (newPage) => {
  currentPage.value = newPage
  fetchTasksList()
}

const refreshTasksList = () => {
  fetchTasksList()
  fetchStatistics()
}

// 任务操作方法
const showTaskDetail = (taskId) => {
  selectedTaskId.value = taskId
  taskDetailVisible.value = true
}

const showAssignDialog = async (task) => {
  selectedTask.value = task
  assignForm.workerId = ''

  try {
    const response = await distributedTaskApi.getAvailableWorkers()
    if (response.data.code === 200) {
      availableWorkers.value = response.data.data || []
      assignDialogVisible.value = true
    } else {
      ElMessage.error('获取可用服务器列表失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('获取可用服务器列表失败:', error)
    ElMessage.error('获取可用服务器列表失败，请检查网络连接')
  }
}

const handleAssignTask = async () => {
  if (!assignForm.workerId) {
    ElMessage.warning('请选择副服务器')
    return
  }

  assignLoading.value = true
  try {
    const response = await distributedTaskApi.assignTaskToWorker(
      selectedTask.value.reservationId,
      assignForm.workerId
    )
    if (response.data.code === 200) {
      ElMessage.success('任务分配成功')
      assignDialogVisible.value = false
      await refreshTasksList()
    } else {
      ElMessage.error('任务分配失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('任务分配失败:', error)
    ElMessage.error('任务分配失败，请检查网络连接')
  } finally {
    assignLoading.value = false
  }
}



// 立即执行任务
const executeTaskImmediately = async (task) => {
  console.log('🚀 开始立即执行任务:')
  console.log('   📋 任务ID:', task.reservationId)
  console.log('   🖥️ 副服务器:', task.workerId, '(' + task.workerName + ')')
  console.log('   👤 用户:', task.username)
  console.log('   🏫 学校:', task.schoolName)
  console.log('   💺 座位:', task.seatId)
  console.log('   ⏰ 开始时间:', new Date().toISOString())

  // 设置执行状态
  task.executing = true

  try {
    // 🚀 优先尝试直连Worker（提高执行速度）
    if (task.workerId && task.workerUrl) {
      console.log('⚡ 直连Worker服务器执行（提高速度）...')
      const startTime = Date.now()

      ElMessage.info({
        message: `🚀 正在直连${task.workerName}执行（预计1-3秒）...`,
        duration: 2000
      })

      const workerUrl = task.workerUrl.replace('/api', '') + '/api'
      const workerApi = createWorkerApi(workerUrl)
      const response = await workerApi.post(`/tasks/execute/${task.reservationId}`)

      const endTime = Date.now()
      const duration = endTime - startTime

      console.log('📊 直连Worker执行结果:')
      console.log('   ⏱️ 执行耗时:', `${duration}ms`)
      console.log('   🌐 HTTP状态:', response.status)
      console.log('   📋 响应数据:', response.data)

      if (response.status === 200 && response.data.success !== false) {
        console.log('✅ 直连Worker执行成功:')
        console.log('   📋 任务ID:', task.reservationId)
        console.log('   ⏱️ 总耗时:', `${duration}ms`)

        ElMessage.success(`🚀 直连执行成功！耗时: ${duration}ms`)
        await refreshTasksList()
        return
      }
    }

    // 🔄 回退到主服务器调用
    console.log('📡 回退到主服务器调用...')
    const startTime = Date.now()

    ElMessage.info({
      message: '正在通过主服务器执行任务...',
      duration: 3000
    })

    const response = await distributedTaskApi.retryFailedTask(task.reservationId)

    const endTime = Date.now()
    const duration = endTime - startTime

    console.log('📊 API调用完成:')
    console.log('   ⏱️ 响应时间:', `${duration}ms`)
    console.log('   🌐 HTTP状态:', response.status)
    console.log('   📋 业务代码:', response.data.code)
    console.log('   💬 响应消息:', response.data.message)

    if (response.data.code === 200) {
      console.log('✅ 立即执行任务成功:')
      console.log('   📋 任务ID:', task.reservationId)
      console.log('   💬 成功消息:', response.data.message)
      console.log('   ⏱️ 执行耗时:', `${duration}ms`)

      ElMessage.success('任务立即执行成功！请查看控制台日志了解详情')

      // 刷新任务列表
      console.log('🔄 刷新任务列表...')
      await refreshTasksList()
      console.log('✅ 任务列表刷新完成')

    } else {
      console.error('❌ 立即执行任务失败:')
      console.error('   📋 任务ID:', task.reservationId)
      console.error('   🚫 错误代码:', response.data.code)
      console.error('   💬 错误消息:', response.data.message)
      ElMessage.error('任务立即执行失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('💥 立即执行任务异常:')
    console.error('   📋 任务ID:', task.reservationId)
    console.error('   🚫 异常信息:', error.message)
    console.error('   📚 错误堆栈:', error.stack)

    // 根据错误类型提供不同的提示
    if (error.message && error.message.includes('timeout')) {
      ElMessage.warning({
        message: '任务执行时间较长，请稍后查看任务状态。学习通预约可能需要10-30秒完成。',
        duration: 5000
      })
      console.log('⏰ 超时提示: 任务可能仍在后台执行，建议稍后刷新查看结果')
    } else if (error.message && error.message.includes('Network Error')) {
      ElMessage.error('网络连接失败，请检查服务器状态')
    } else {
      ElMessage.error('任务立即执行失败: ' + (error.response?.data?.message || error.message))
    }
  } finally {
    // 清除执行状态
    task.executing = false
    console.log('🏁 立即执行任务流程结束:')
    console.log('   📋 任务ID:', task.reservationId)
    console.log('   ⏰ 结束时间:', new Date().toISOString())
    console.log('   ✅ 执行状态已重置为: false')
  }
}





// 修改服务器分配相关方法
const openServerChangeDialog = async (task) => {
  selectedTask.value = task
  changeServerForm.workerId = task.workerId || ''

  try {
    const response = await distributedTaskApi.getAvailableWorkers()
    if (response.data.code === 200) {
      availableWorkers.value = response.data.data || []
      changeServerDialogVisible.value = true
    } else {
      ElMessage.error('获取可用服务器列表失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('获取可用服务器列表失败:', error)
    ElMessage.error('获取可用服务器列表失败，请检查网络连接')
  }
}

const handleChangeServer = async () => {
  changeServerLoading.value = true
  try {
    const response = await distributedTaskApi.assignTaskToWorker(
      selectedTask.value.reservationId,
      changeServerForm.workerId
    )
    if (response.data.code === 200) {
      ElMessage.success('服务器分配修改成功')
      changeServerDialogVisible.value = false
      await refreshTasksList()
    } else {
      ElMessage.error('服务器分配修改失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('服务器分配修改失败:', error)
    ElMessage.error('服务器分配修改失败，请检查网络连接')
  } finally {
    changeServerLoading.value = false
  }
}

// 工具方法
const formatDateTime = (dateTime) => {
  if (!dateTime) return 'N/A'
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatTimeRange = (startTime, endTime) => {
  if (!startTime || !endTime) return 'N/A'
  return `${startTime} - ${endTime}`
}



const getServerType = (index) => {
  const types = ['master', 'backup', 'test']
  return types[index] || 'worker'
}

// 服务器点击处理
const handleServerClick = async (serverData) => {
  selectedServer.value = serverData
  await fetchServerTasks(serverData.workerId)
  generateTimeSlots()
  serverScheduleDialogVisible.value = true
}

// 获取指定服务器的任务
const fetchServerTasks = async (workerId) => {
  scheduleLoading.value = true
  try {
    const params = {
      page: 1,
      size: 1000, // 获取所有任务
      workerId: workerId
    }

    const response = await distributedTaskApi.getReservationTasks(params)
    if (response.data.code === 200) {
      const pageData = response.data.data
      serverTasks.value = pageData.records || []
    } else {
      console.error('获取服务器任务失败:', response.data.message)
      ElMessage.error('获取服务器任务失败: ' + response.data.message)
      serverTasks.value = []
    }
  } catch (error) {
    console.error('获取服务器任务失败:', error)
    ElMessage.error('获取服务器任务失败，请检查网络连接')
    serverTasks.value = []
  } finally {
    scheduleLoading.value = false
  }
}

// 生成24小时时间段
const generateTimeSlots = () => {
  const slots = []
  for (let hour = 0; hour < 24; hour++) {
    const startTime = `${hour.toString().padStart(2, '0')}:00:00`
    const endTime = `${((hour + 1) % 24).toString().padStart(2, '0')}:00:00`
    const timeSlotKey = `${hour.toString().padStart(2, '0')}:00-${((hour + 1) % 24).toString().padStart(2, '0')}:00`

    // 筛选该时间段的任务
    const tasksInSlot = serverTasks.value.filter(task => {
      if (!task.reservationOpenTime) return false
      const openTime = task.reservationOpenTime
      return openTime >= startTime && openTime < endTime
    })

    // 只添加有任务的时间段
    if (tasksInSlot.length > 0) {
      slots.push({
        timeRange: timeSlotKey,
        startTime,
        endTime,
        tasks: tasksInSlot,
        taskCount: tasksInSlot.length,
        expanded: false // 默认不展开，需要手动点击
      })
    }
  }
  timeSlots.value = slots
}

// 切换时间段展开状态
const toggleTimeSlot = (index) => {
  timeSlots.value[index].expanded = !timeSlots.value[index].expanded
}

// 生命周期
onMounted(async () => {
  await refreshAllData()

  // 设置自动刷新（每30秒）
  refreshTimer = setInterval(() => {
    fetchStatistics()
    fetchWorkerStatus()
  }, 30000)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.distributed-task-management-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-left h2 {
  margin: 0;
  color: #333;
  font-weight: 600;
  font-size: 24px;
}

.header-subtitle {
  margin: 0;
  color: #666;
  font-size: 14px;
  font-weight: 400;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-main {
  padding: 20px;
}

.main-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

/* 系统状态概览 */
.overview-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.status-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

/* 任务列表 */
.tasks-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.tasks-card {
  border: none;
  box-shadow: none;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header .header-left {
  gap: 0;
}

.card-header .header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name {
  font-weight: 500;
  color: #303133;
}

.username {
  font-size: 12px;
  color: #909399;
}

.action-buttons {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 4px;
  flex-wrap: nowrap !important;
  white-space: nowrap;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 16px 0;
}

/* 副服务器状态 */
.servers-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.servers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

/* 可点击的服务器标签 */
.clickable-server-tag {
  cursor: pointer;
  transition: all 0.2s ease;
}

.clickable-server-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 任务信息样式 */
.task-info {
  background: #f5f7fa;
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
}

.task-info p {
  margin: 4px 0;
  font-size: 14px;
  color: #606266;
}

/* 弹窗样式 */
.task-info {
  background: #f5f7fa;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
}

.task-info p {
  margin: 4px 0;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .status-cards-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .servers-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }

  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .header-left h2 {
    font-size: 20px;
  }

  .header-subtitle {
    font-size: 12px;
  }

  .page-main {
    padding: 12px;
  }

  .status-cards-grid {
    grid-template-columns: 1fr;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .card-header .header-right {
    width: 100%;
    justify-content: space-between;
  }

  .action-buttons {
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    width: 100%;
    justify-content: flex-start;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #e9ecef;
}

:deep(.el-table tr:hover > td) {
  background-color: #f8f9fa;
}

/* 卡片样式 */
:deep(.el-card) {
  border-radius: 12px;
}

:deep(.el-card__header) {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 12px;
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  padding: 20px;
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
}

:deep(.el-dialog__body) {
  padding: 30px 20px;
}

/* 服务器时间段分布弹窗样式 */
.server-schedule-content {
  max-height: 70vh;
  overflow-y: auto;
}

.schedule-header {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.server-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.server-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.task-summary {
  font-size: 14px;
  color: #606266;
}

.time-slots-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.time-slot-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.time-slot-item.has-tasks {
  border-color: #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

.time-slot-header {
  padding: 12px 16px;
  background: #fafafa;
  cursor: pointer;
  transition: background-color 0.3s ease;
  user-select: none;
}

.time-slot-header:hover {
  background: #f0f0f0;
}

.time-slot-item.has-tasks .time-slot-header {
  background: #ecf5ff;
}

.time-slot-item.has-tasks .time-slot-header:hover {
  background: #d9ecff;
}

.time-slot-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.expand-icon {
  transition: transform 0.3s ease;
  color: #909399;
}

.time-range {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  font-family: 'Courier New', monospace;
}

.time-slot-content {
  border-top: 1px solid #e4e7ed;
  background: #fff;
}

.no-tasks {
  padding: 20px;
  text-align: center;
}

.tasks-list {
  padding: 12px;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
  transition: all 0.3s ease;
}

.task-item:hover {
  background: #ecf5ff;
  transform: translateX(2px);
}

.task-item:last-child {
  margin-bottom: 0;
}

.task-info {
  flex: 1;
}

.task-main {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
}

.task-user {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.task-school {
  font-size: 13px;
  color: #606266;
  background: #e1f3d8;
  padding: 2px 8px;
  border-radius: 12px;
}

.task-time {
  font-size: 12px;
  color: #409eff;
  font-family: 'Courier New', monospace;
  background: #ecf5ff;
  padding: 2px 6px;
  border-radius: 4px;
}

.task-details {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.task-room {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-seat {
  background: #fff2e8;
  color: #e6a23c;
  padding: 1px 6px;
  border-radius: 4px;
}

.task-actions {
  flex-shrink: 0;
  display: flex;
  gap: 8px;
  align-items: center;
}

.no-tasks-message {
  padding: 40px 20px;
  text-align: center;
}
</style>
