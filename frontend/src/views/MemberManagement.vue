<template>
  <div class="member-management-container">
    <el-container>
      <el-header class="member-header">
        <div class="header-left">
          <el-button type="info" plain @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回主页
          </el-button>
          <h2>成员管理</h2>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新数据
          </el-button>
        </div>
      </el-header>
      
      <el-main class="member-main">
        <div class="member-content">
          <!-- 筛选条件 -->
          <el-card class="filter-card">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-input
                  v-model="searchKeyword"
                  placeholder="搜索用户名或姓名"
                  @input="handleSearch"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
              </el-col>
              <el-col :span="4">
                <el-select v-model="schoolFilter" placeholder="选择学校" @change="handleFilter" clearable>
                  <el-option label="全部学校" value="" />
                  <el-option label="未预约" value="no-reservation" />
                  <el-option
                    v-for="school in schools"
                    :key="school.id"
                    :label="school.name"
                    :value="school.name"
                  />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select v-model="statusFilter" placeholder="预约状态" @change="handleFilter" clearable>
                  <el-option label="全部" value="" />
                  <el-option label="活跃预约" value="ACTIVE" />
                  <el-option label="暂停预约" value="PAUSED" />
                  <el-option label="无预约" value="NONE" />
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select v-model="daysFilter" placeholder="剩余天数" @change="handleFilter" clearable>
                  <el-option label="全部" value="" />
                  <el-option label="即将到期(≤1天)" value="expiring" />
                  <el-option label="正常(>1天)" value="normal" />
                </el-select>
              </el-col>
              <el-col :span="6">
                <div class="filter-actions">
                  <el-button @click="resetFilters" type="info" plain>
                    重置筛选
                  </el-button>
                </div>
              </el-col>
            </el-row>
          </el-card>
          
          <!-- 用户列表 -->
          <el-card class="users-card">
            <template #header>
              <div class="card-header">
                <span>用户列表 ({{ filteredUsers.length }})</span>
                <el-tag type="info">点击行查看详细信息</el-tag>
              </div>
            </template>
            
            <el-table
              :data="filteredUsers"
              style="width: 100%"
              @row-click="showUserDetail"
              highlight-current-row
              v-loading="loading"
            >
              <el-table-column prop="id" label="用户ID" width="80" />
              <el-table-column prop="username" label="用户名" width="120" />
              <el-table-column prop="name" label="姓名" width="120" />
              <el-table-column label="预约状态" width="120">
                <template #default="{ row }">
                  <el-tag
                    v-if="row.currentReservation"
                    :type="getReservationStatusType(row.currentReservation.status)"
                  >
                    {{ getReservationStatusText(row.currentReservation.status) }}
                  </el-tag>
                  <el-tag v-else type="info">无预约</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="座位信息" width="150">
                <template #default="{ row }">
                  <span v-if="row.currentReservation">
                    {{ row.currentReservation.seatId }}
                  </span>
                  <span v-else class="no-data">-</span>
                </template>
              </el-table-column>
              <el-table-column label="房间" width="150">
                <template #default="{ row }">
                  <span v-if="row.currentReservation">
                    {{ row.currentReservation.roomName }}
                  </span>
                  <span v-else class="no-data">-</span>
                </template>
              </el-table-column>
              <el-table-column label="学校" width="150">
                <template #default="{ row }">
                  <span v-if="row.currentReservation">
                    {{ row.currentReservation.schoolName }}
                  </span>
                  <span v-else class="no-data">未预约</span>
                </template>
              </el-table-column>
              <el-table-column label="剩余天数" width="120">
                <template #default="{ row }">
                  <el-tag
                    :type="getRemainingDaysType(row.remainingDays)"
                  >
                    {{ row.remainingDays !== undefined ? row.remainingDays : 0 }} 天
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="创建时间" width="180">
                <template #default="{ row }">
                  {{ formatDateTime(row.createdTime) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" fixed="right" width="200">
                <template #default="{ row }">
                  <el-button
                    type="primary"
                    size="small"
                    @click.stop="editRemainingDays(row)"
                  >
                    修改天数
                  </el-button>
                  <el-button
                    v-if="row.currentReservation"
                    type="danger"
                    size="small"
                    @click.stop="cancelUserReservation(row)"
                  >
                    取消预约
                  </el-button>
                  <span v-if="!row.currentReservation" class="no-reservation-text">无预约</span>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-main>
    </el-container>
    
    <!-- 用户详情弹窗 -->
    <el-dialog
      v-model="userDetailVisible"
      title="用户详细信息"
      width="800px"
      :before-close="handleDetailClose"
    >
      <div v-if="selectedUser" class="user-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ selectedUser.id }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ selectedUser.username }}</el-descriptions-item>
          <el-descriptions-item label="姓名">{{ selectedUser.name }}</el-descriptions-item>
          <el-descriptions-item label="角色">
            <el-tag :type="selectedUser.role === 'ADMIN' ? 'danger' : 'primary'">
              {{ selectedUser.role === 'ADMIN' ? '管理员' : '普通用户' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">
            {{ formatDateTime(selectedUser.createdTime) }}
          </el-descriptions-item>
        </el-descriptions>
        
        <el-divider>预约信息</el-divider>
        
        <div v-if="selectedUser.currentReservation" class="reservation-detail">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="学校">{{ selectedUser.currentReservation.schoolName }}</el-descriptions-item>
            <el-descriptions-item label="房间">{{ selectedUser.currentReservation.roomName }}</el-descriptions-item>
            <el-descriptions-item label="座位号">{{ selectedUser.currentReservation.seatId }}</el-descriptions-item>
            <el-descriptions-item label="剩余天数">
              <el-tag :type="getRemainingDaysType(selectedUser.remainingDays)">
                {{ selectedUser.remainingDays !== undefined ? selectedUser.remainingDays : 0 }} 天
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="开始时间">
              {{ formatTime(selectedUser.currentReservation.startTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="结束时间">
              {{ formatTime(selectedUser.currentReservation.endTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="预约类型" :span="2">
              {{ selectedUser.currentReservation.reservationType === 'SAME_DAY' ? '当天预约' : '提前一天预约' }}
            </el-descriptions-item>
          </el-descriptions>
          
          <div class="detail-actions">
            <el-button type="primary" @click="editRemainingDays(selectedUser)">
              <el-icon><Edit /></el-icon>
              修改剩余天数
            </el-button>
            <el-button type="danger" @click="cancelUserReservation(selectedUser)">
              <el-icon><Delete /></el-icon>
              取消此预约
            </el-button>
          </div>
        </div>
        
        <div v-else class="no-reservation">
          <el-empty description="该用户暂无预约" />
          <div class="detail-actions">
            <el-button type="primary" @click="editRemainingDays(selectedUser)">
              <el-icon><Edit /></el-icon>
              修改剩余天数
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
    
    <!-- 修改剩余天数弹窗 -->
    <el-dialog
      v-model="editDaysVisible"
      title="修改剩余天数"
      width="400px"
      :before-close="handleEditClose"
    >
      <el-form ref="editFormRef" :model="editForm" :rules="editRules" label-width="100px">
        <el-form-item label="用户" prop="username">
          <el-input v-model="editForm.username" readonly />
        </el-form-item>
        <el-form-item label="座位号" prop="seatId">
          <el-input v-model="editForm.seatId" readonly />
        </el-form-item>
        <el-form-item label="当前天数" prop="currentDays">
          <el-input v-model="editForm.currentDays" readonly />
        </el-form-item>
        <el-form-item label="新天数" prop="newDays">
          <el-input-number
            v-model="editForm.newDays"
            :min="0"
            :max="365"
            controls-position="right"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDaysVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRemainingDays" :loading="saving">
            确定修改
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Refresh, Search, Edit, Delete } from '@element-plus/icons-vue'
import api from '@/utils/api'

const router = useRouter()

// 响应式数据
const users = ref([])
const loading = ref(false)
const searchKeyword = ref('')
const schoolFilter = ref('')
const statusFilter = ref('')
const daysFilter = ref('')
const schools = ref([])

// 用户详情弹窗
const userDetailVisible = ref(false)
const selectedUser = ref(null)

// 修改天数弹窗
const editDaysVisible = ref(false)
const editFormRef = ref(null)
const saving = ref(false)
const editForm = reactive({
  userId: null,
  reservationId: null,
  username: '',
  seatId: '',
  currentDays: 0,
  newDays: 1
})

// 表单验证规则
const editRules = reactive({
  newDays: [
    { required: true, message: '请输入新的天数', trigger: 'blur' },
    { type: 'number', min: 0, max: 365, message: '天数范围为0-365天', trigger: 'blur' }
  ]
})

// 计算属性：筛选后的用户列表
const filteredUsers = computed(() => {
  let filtered = users.value

  // 搜索筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(user =>
      user.username.toLowerCase().includes(keyword) ||
      user.name.toLowerCase().includes(keyword)
    )
  }

  // 学校筛选
  if (schoolFilter.value) {
    if (schoolFilter.value === 'no-reservation') {
      // 筛选无预约的用户
      filtered = filtered.filter(user => !user.currentReservation)
    } else {
      // 筛选指定学校的用户
      filtered = filtered.filter(user =>
        user.currentReservation && user.currentReservation.schoolName === schoolFilter.value
      )
    }
  }

  // 预约状态筛选
  if (statusFilter.value) {
    if (statusFilter.value === 'ACTIVE') {
      filtered = filtered.filter(user =>
        user.currentReservation && user.currentReservation.status === 'ACTIVE'
      )
    } else if (statusFilter.value === 'PAUSED') {
      filtered = filtered.filter(user =>
        user.currentReservation && user.currentReservation.status === 'PAUSED'
      )
    } else if (statusFilter.value === 'NONE') {
      filtered = filtered.filter(user => !user.currentReservation)
    }
  }

  // 剩余天数筛选
  if (daysFilter.value) {
    if (daysFilter.value === 'expiring') {
      filtered = filtered.filter(user =>
        (user.remainingDays !== undefined ? user.remainingDays : 0) <= 1
      )
    } else if (daysFilter.value === 'normal') {
      filtered = filtered.filter(user =>
        (user.remainingDays !== undefined ? user.remainingDays : 0) > 1
      )
    }
  }

  return filtered
})

// 获取所有用户数据
const fetchUsers = async () => {
  loading.value = true
  try {
    const response = await api.get('/admin/users')
    if (response.data.code === 200) {
      console.log('API响应数据:', response.data.data)
      console.log('数据类型:', typeof response.data.data)
      console.log('是否为数组:', Array.isArray(response.data.data))

      // 确保数据是数组格式
      let userData = response.data.data
      if (!Array.isArray(userData)) {
        console.warn('API返回的数据不是数组，尝试转换:', userData)
        // 如果是对象，尝试提取数组
        if (userData && typeof userData === 'object') {
          // 检查是否是 {0: {...}, 1: {...}} 格式
          const keys = Object.keys(userData)
          if (keys.every(key => /^\d+$/.test(key))) {
            console.log('检测到对象格式，转换为数组')
            userData = Object.values(userData)
          } else {
            console.error('无法识别的数据格式')
            userData = []
          }
        } else {
          userData = []
        }
      }

      users.value = userData
      console.log('最终用户数据:', users.value)
    } else {
      ElMessage.error(response.data.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败，请稍后再试')
  } finally {
    loading.value = false
  }
}

// 获取学校列表数据
const fetchSchools = async () => {
  try {
    const response = await api.get('/admin/schools')
    if (response.data.code === 200) {
      schools.value = response.data.data || []
      console.log('获取学校列表成功:', schools.value)
    } else {
      console.error('获取学校列表失败:', response.data.message)
      ElMessage.error('获取学校列表失败')
    }
  } catch (error) {
    console.error('获取学校列表失败:', error)
    ElMessage.error('获取学校列表失败，请稍后再试')
  }
}

// 格式化日期时间
const formatDateTime = (dateTimeString) => {
  if (!dateTimeString) return ''
  try {
    const date = new Date(dateTimeString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (e) {
    return dateTimeString
  }
}

// 格式化时间（处理HH:MM:SS格式）
const formatTime = (timeString) => {
  if (!timeString) return ''
  try {
    // 处理 HH:MM:SS 格式的时间字符串
    if (typeof timeString === 'string' && timeString.match(/^\d{2}:\d{2}:\d{2}$/)) {
      // 提取小时和分钟部分
      const [hours, minutes] = timeString.split(':')
      return `${hours}:${minutes}`
    }
    // 如果是完整的日期时间字符串，使用原有逻辑
    const date = new Date(timeString)
    if (!isNaN(date.getTime())) {
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    return timeString
  } catch (e) {
    return timeString || ''
  }
}

// 根据剩余天数返回标签类型
const getRemainingDaysType = (days) => {
  const actualDays = days !== undefined ? days : 0
  if (actualDays <= 0) return 'danger'
  if (actualDays === 1) return 'warning'
  if (actualDays <= 3) return 'success'
  return 'primary'
}

// 根据预约状态返回标签类型
const getReservationStatusType = (status) => {
  switch (status) {
    case 'ACTIVE':
      return 'success'
    case 'PAUSED':
      return 'warning'
    case 'CANCELLED':
      return 'danger'
    default:
      return 'info'
  }
}

// 根据预约状态返回显示文本
const getReservationStatusText = (status) => {
  switch (status) {
    case 'ACTIVE':
      return '活跃'
    case 'PAUSED':
      return '暂停'
    case 'CANCELLED':
      return '已取消'
    default:
      return '未知'
  }
}

// 显示用户详情
const showUserDetail = (user) => {
  selectedUser.value = user
  userDetailVisible.value = true
}

// 修改剩余天数
const editRemainingDays = (user) => {
  editForm.userId = user.id
  editForm.username = user.username
  editForm.currentDays = user.remainingDays || 0
  editForm.newDays = user.remainingDays !== undefined ? user.remainingDays : 0

  // 如果用户有预约，记录预约信息（用于显示）
  if (user.currentReservation) {
    editForm.reservationId = user.currentReservation.reservationId
    editForm.seatId = user.currentReservation.seatId
  } else {
    editForm.reservationId = null
    editForm.seatId = '无预约'
  }

  editDaysVisible.value = true
  userDetailVisible.value = false
}

// 保存剩余天数修改
const saveRemainingDays = async () => {
  if (!editFormRef.value) return

  try {
    const valid = await editFormRef.value.validate()
    if (!valid) return

    saving.value = true

    const response = await api.put(`/admin/users/${editForm.userId}/remaining-days`, {
      remainingDays: editForm.newDays
    })

    if (response.data.code === 200) {
      ElMessage.success('修改成功')
      editDaysVisible.value = false
      console.log('修改成功，准备刷新数据...')
      await refreshData()
      console.log('数据刷新完成，当前用户列表:', users.value)
    } else {
      ElMessage.error(response.data.message || '修改失败')
    }
  } catch (error) {
    console.error('修改天数失败:', error)
    ElMessage.error('修改失败，请稍后再试')
  } finally {
    saving.value = false
  }
}

// 取消用户预约
const cancelUserReservation = async (user) => {
  if (!user.currentReservation) {
    ElMessage.warning('该用户没有预约')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要取消用户 ${user.username} 的预约吗？\n\n座位：${user.currentReservation.seatId}\n房间：${user.currentReservation.roomName}`,
      '确认取消预约',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '保留预约',
        type: 'warning'
      }
    )

    const response = await api.post(`/admin/reservations/cancel/${user.currentReservation.reservationId}`)

    if (response.data.code === 200) {
      ElMessage.success('预约已取消')
      userDetailVisible.value = false
      await refreshData()
    } else {
      ElMessage.error(response.data.message || '取消预约失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消预约失败:', error)
      ElMessage.error('取消预约失败，请稍后再试')
    }
  }
}

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑由计算属性自动处理
}

// 筛选处理
const handleFilter = () => {
  // 筛选逻辑由计算属性自动处理
}

// 重置所有筛选条件
const resetFilters = () => {
  searchKeyword.value = ''
  schoolFilter.value = ''
  statusFilter.value = ''
  daysFilter.value = ''
  ElMessage.success('筛选条件已重置')
}

// 刷新数据
const refreshData = async () => {
  await fetchUsers()
}

// 关闭弹窗
const handleDetailClose = () => {
  userDetailVisible.value = false
  selectedUser.value = null
}

const handleEditClose = () => {
  editDaysVisible.value = false
  Object.assign(editForm, {
    userId: null,
    reservationId: null,
    username: '',
    seatId: '',
    currentDays: 0,
    newDays: 1
  })
}

const goBack = () => {
  router.push('/dashboard')
}

// 页面加载时获取数据
onMounted(() => {
  fetchUsers()
  fetchSchools()
})
</script>

<style scoped>
.member-management-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.member-header {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h2 {
  color: #333;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.member-main {
  padding: 20px;
}

.member-content {
  max-width: 1400px;
  margin: 0 auto;
}

.filter-card {
  margin-bottom: 20px;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 32px;
}

.users-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.no-data {
  color: #909399;
  font-style: italic;
}

.no-reservation-text {
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

.user-detail {
  margin: 20px 0;
}

.reservation-detail {
  margin-top: 20px;
}

.detail-actions {
  margin-top: 20px;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.no-reservation {
  margin-top: 20px;
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .member-header {
    padding: 0 10px;
  }
  
  .member-main {
    padding: 10px;
  }
  
  .header-left h2 {
    font-size: 18px;
  }
  
  .detail-actions {
    flex-direction: column;
  }
}
</style> 