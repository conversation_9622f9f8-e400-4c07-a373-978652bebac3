<template>
  <div class="redemption-management">
    <!-- 页面标题 -->
    <el-card class="header-card" shadow="hover">
      <div class="header-wrapper">
        <el-button class="back-button" @click="goToHome">
          <el-icon><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path fill="currentColor" d="M609.408 149.376 277.76 489.6a32 32 0 0 0 0 44.672l331.648 340.352a29.12 29.12 0 0 0 41.728 0 30.592 30.592 0 0 0 0-42.752L339.264 511.936l311.872-319.872a30.592 30.592 0 0 0 0-42.688 29.12 29.12 0 0 0-41.728 0z"></path></svg></el-icon>
          返回首页
        </el-button>
        <div class="header-content">
          <h2><el-icon><Setting /></el-icon> 兑换码管理</h2>
          <p>管理系统中的所有兑换码</p>
        </div>
      </div>
    </el-card>

    <!-- 操作栏 -->
    <el-card class="action-bar" shadow="hover">
      <div class="action-buttons">
        <el-button type="primary" size="large" @click="showGenerateDialog">
          <el-icon><Plus /></el-icon>
          生成兑换码
        </el-button>
        <el-button @click="loadCodes">
          <el-icon><Refresh /></el-icon>
          刷新列表
        </el-button>
      </div>
    </el-card>

    <!-- 筛选栏 -->
    <el-card class="filter-bar" shadow="hover">
      <el-form inline>
        <el-form-item label="状态筛选">
          <el-select v-model="filterStatus" placeholder="选择状态" style="width: 150px;">
            <el-option label="全部" :value="null" />
            <el-option label="未使用" :value="false" />
            <el-option label="已使用" :value="true" />
          </el-select>
        </el-form-item>
        <el-form-item label="批次ID">
          <el-input 
            v-model="filterBatchId" 
            placeholder="输入批次ID"
            clearable
            style="width: 200px;"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadCodes">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="resetFilters">
            <el-icon><RefreshLeft /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 兑换码列表 -->
    <el-card class="codes-table" shadow="hover">
      <el-table 
        :data="codes" 
        stripe 
        v-loading="loading"
        empty-text="暂无兑换码">
        <el-table-column prop="code" label="兑换码" width="180" />
        <el-table-column prop="daysToAdd" label="天数" width="80">
          <template #default="scope">
            <el-tag type="info">{{ scope.row.daysToAdd }}天</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isUsed" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row)">
              {{ getStatusText(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="usedByUserId" label="使用者" width="120">
          <template #default="scope">
            {{ scope.row.usedByUserId || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.createdTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="expireTime" label="过期时间" width="180">
          <template #default="scope">
            {{ scope.row.expireTime ? formatDateTime(scope.row.expireTime) : '永不过期' }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="150" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button 
              v-if="!scope.row.isUsed" 
              type="danger" 
              size="small"
              @click="deleteCode(scope.row)"
            >
              删除
            </el-button>
            <span v-else class="disabled-text">已使用</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadCodes"
          @current-change="loadCodes"
        />
      </div>
    </el-card>

    <!-- 生成兑换码对话框 -->
    <GenerateCodeDialog 
      v-model="showGenerate" 
      @success="handleGenerateSuccess" 
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Setting, Plus, Refresh, Search, RefreshLeft } from '@element-plus/icons-vue'
import api from '@/utils/api'
import GenerateCodeDialog from '@/components/GenerateCodeDialog.vue'

// 路由
const router = useRouter()

// 数据
const codes = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 筛选条件
const filterStatus = ref(null)
const filterBatchId = ref('')

// 对话框
const showGenerate = ref(false)

// 加载兑换码列表
const loadCodes = async () => {
  try {
    loading.value = true
    
    const params = {
      page: currentPage.value,
      size: pageSize.value
    }
    
    if (filterStatus.value !== null) {
      params.isUsed = filterStatus.value
    }
    
    if (filterBatchId.value) {
      params.batchId = filterBatchId.value
    }
    
    const response = await api.get('/admin/redemption/codes', { params })
    
    if (response.data.code === 200) {
      const data = response.data.data
      codes.value = data.records || []
      total.value = data.total || 0
    } else {
      ElMessage.error('获取兑换码列表失败')
    }
  } catch (error) {
    console.error('获取兑换码列表失败:', error)
    ElMessage.error('获取兑换码列表失败')
  } finally {
    loading.value = false
  }
}

// 删除兑换码
const deleteCode = async (code) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除兑换码 "${code.code}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await api.delete(`/admin/redemption/codes/${code.id}`)
    
    if (response.data.code === 200) {
      ElMessage.success('兑换码删除成功')
      await loadCodes()
    } else {
      ElMessage.error(response.data.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除兑换码失败:', error)
      ElMessage.error(error.response?.data?.message || '删除失败')
    }
  }
}

// 重置筛选条件
const resetFilters = () => {
  filterStatus.value = null
  filterBatchId.value = ''
  currentPage.value = 1
  loadCodes()
}

// 显示生成对话框
const showGenerateDialog = () => {
  showGenerate.value = true
}

// 生成成功回调
const handleGenerateSuccess = () => {
  loadCodes()
}

// 返回首页
const goToHome = () => {
  router.push('/dashboard')
}

// 获取状态类型
const getStatusType = (row) => {
  if (row.isUsed) return 'success'
  if (row.expireTime && new Date(row.expireTime) < new Date()) return 'danger'
  return 'warning'
}

// 获取状态文本
const getStatusText = (row) => {
  if (row.isUsed) return '已使用'
  if (row.expireTime && new Date(row.expireTime) < new Date()) return '已过期'
  return '未使用'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 页面加载时获取数据
onMounted(() => {
  loadCodes()
})
</script>

<style scoped>
.redemption-management {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.header-card {
  margin-bottom: 20px;
  position: relative;
}

.header-wrapper {
  position: relative;
}

.back-button {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
}

.header-content {
  text-align: center;
}

.header-content h2 {
  margin: 0 0 10px 0;
  color: #409EFF;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.header-content p {
  margin: 0;
  color: #666;
}

.action-bar {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.filter-bar {
  margin-bottom: 20px;
}

.codes-table {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.disabled-text {
  color: #999;
  font-size: 12px;
}

.el-card {
  border-radius: 8px;
}

.el-table {
  border-radius: 8px;
}
</style>
