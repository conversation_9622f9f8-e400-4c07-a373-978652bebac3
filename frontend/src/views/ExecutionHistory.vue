<template>
  <div class="execution-history">
    <div class="page-header">
      <h1>我的预约执行历史</h1>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button @click="exportLogs" type="primary">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card">
      <div class="filter-row">
        <div class="filter-item">
          <label>状态筛选：</label>
          <el-select v-model="filters.status" placeholder="全部状态" clearable @change="loadData">
            <el-option label="全部" value="" />
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
            <el-option label="异常" value="error" />
          </el-select>
        </div>
        <div class="filter-item">
          <label>日期范围：</label>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="onDateRangeChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 执行历史列表 -->
    <div class="history-list">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>
      
      <div v-else-if="logs.length === 0" class="empty-container">
        <el-empty description="暂无执行历史" />
      </div>
      
      <div v-else>
        <div v-for="log in logs" :key="log.id" class="log-item">
          <!-- 预约记录 -->
          <el-card class="normal-log">
            <div class="log-header">
              <div class="log-time">
                <el-icon><Clock /></el-icon>
                {{ formatDateTime(log.apiResponseTime || log.createdAt) }}
              </div>
              <div class="log-status" :class="getStatusClass(log.status)">
                {{ log.statusIcon }} {{ log.statusText }}
              </div>
              <div class="log-duration">{{ log.durationText }}</div>
            </div>
            
            <div class="log-content">
              <div class="basic-info">
                <span class="info-item">
                  <el-icon><User /></el-icon>
                  {{ log.userInfo || log.username }}
                </span>
                <span class="info-item">
                  <el-icon><Location /></el-icon>
                  {{ log.seatInfo || ('座位' + log.seatid) }}
                </span>
                <span class="info-item">
                  <el-icon><House /></el-icon>
                  {{ log.roomInfo || log.roomName || ('房间' + log.roomid) }}
                </span>
                <span class="info-item">
                  <el-icon><Clock /></el-icon>
                  {{ log.timeRange || (log.startTime + ' - ' + log.endTime) }}
                </span>
              </div>
              
              <div class="api-response">
                <div class="response-label">📱 执行结果:</div>
                <div class="response-content">{{ formatExecutionResult(log) }}</div>
              </div>

              <div class="execution-details">
                <div class="details-label">📊 执行详情:</div>
                <div class="details-content">{{ log.executionDetails || formatExecutionDetails(log) }}</div>
              </div>

              <div v-if="log.errorMessage" class="error-details">
                <div class="error-label">⚠️ 错误详情:</div>
                <div class="error-content">{{ log.errorMessage }}</div>
              </div>
            </div>
          </el-card>


        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="onPageSizeChange"
        @current-change="onPageChange"
      />
    </div>


  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Clock, Refresh, Download, User, Location, House } from '@element-plus/icons-vue'
import api from '@/utils/api'


// 响应式数据
const loading = ref(false)
const logs = ref([])

const filters = reactive({
  status: '',
})

const dateRange = ref([])

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})



// 计算属性
const currentUser = computed(() => {
  const userStr = localStorage.getItem('user')
  console.log('localStorage user:', userStr)

  if (!userStr) {
    console.log('No user in localStorage')
    return null
  }

  try {
    const user = JSON.parse(userStr)
    console.log('Parsed user:', user)
    return user
  } catch (error) {
    console.error('Error parsing user from localStorage:', error)
    return null
  }
})

const userId = computed(() => {
  // 如果有id字段就用id，否则用username
  return currentUser.value?.id || currentUser.value?.username
})

// 方法 - 优化后的版本

/**
 * 加载执行历史数据
 */
const loadData = async () => {
  // 用户验证
  if (!userId.value) {
    ElMessage.error('请先登录')
    return
  }

  loading.value = true

  try {
    const params = buildQueryParams()
    console.log('Loading execution logs:', { userId: userId.value, params })

    const response = await api.get(`/execution-logs/user/${userId.value}`, {
      params,
      timeout: 10000 // 10秒超时
    })

    // 数据验证和处理
    if (response?.data) {
      logs.value = Array.isArray(response.data.logs) ? response.data.logs : []
      pagination.total = Number(response.data.totalCount) || 0

      console.log(`Successfully loaded ${logs.value.length} logs, total: ${pagination.total}`)
    } else {
      throw new Error('响应数据格式错误')
    }

  } catch (error) {
    handleLoadError(error)
  } finally {
    loading.value = false
  }
}

/**
 * 构建查询参数
 */
const buildQueryParams = () => {
  return {
    page: pagination.page,
    size: pagination.size,
    status: filters.status || undefined,
    startDate: dateRange.value?.[0] || undefined,
    endDate: dateRange.value?.[1] || undefined
  }
}

/**
 * 处理加载错误
 */
const handleLoadError = (error) => {
  console.error('加载执行历史失败:', error)

  let errorMessage = '加载执行历史失败'

  if (error.code === 'ECONNABORTED') {
    errorMessage = '请求超时，请检查网络连接'
  } else if (error.response) {
    const status = error.response.status
    const data = error.response.data

    switch (status) {
      case 401:
        errorMessage = '登录已过期，请重新登录'
        break
      case 403:
        errorMessage = '没有权限访问该数据'
        break
      case 404:
        errorMessage = '请求的资源不存在'
        break
      case 500:
        errorMessage = '服务器内部错误'
        break
      default:
        errorMessage = data?.message || `请求失败 (${status})`
    }
  } else if (error.request) {
    errorMessage = '网络连接失败，请检查网络'
  }

  ElMessage.error(errorMessage)
}

const refreshData = () => {
  loadData()
}

const exportLogs = async () => {
  try {
    const params = {
      startDate: dateRange.value?.[0],
      endDate: dateRange.value?.[1]
    }
    
    const response = await api.get(`/execution-logs/export/${userId.value}`, { params })
    ElMessage.success('日志导出成功: ' + response.data.filePath)
  } catch (error) {
    console.error('导出日志失败:', error)
    ElMessage.error('导出日志失败')
  }
}

const onDateRangeChange = () => {
  pagination.page = 1
  loadData()
}

const onPageSizeChange = () => {
  pagination.page = 1
  loadData()
}

const onPageChange = () => {
  loadData()
}



// 工具方法 - 优化后的版本

/**
 * 安全的日期时间格式化
 */
const formatDateTime = (dateTime) => {
  try {
    if (!dateTime) return '未知'
    const date = new Date(dateTime)
    if (isNaN(date.getTime())) return '无效时间'
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    console.warn('时间格式化失败:', error)
    return '格式错误'
  }
}

/**
 * 安全的时长格式化
 */
const formatDuration = (ms) => {
  try {
    if (!ms || isNaN(ms)) return '未知'
    const num = Number(ms)
    if (num < 1000) return `${Math.round(num)}ms`
    return `${(num / 1000).toFixed(1)}秒`
  } catch (error) {
    console.warn('时长格式化失败:', error)
    return '格式错误'
  }
}

/**
 * 状态样式类映射
 */
const STATUS_CLASS_MAP = {
  success: 'status-success',
  failed: 'status-failed',
  error: 'status-error'
}

const getStatusClass = (status) => {
  return STATUS_CLASS_MAP[status?.toLowerCase()] || 'status-unknown'
}

/**
 * 格式化执行结果 - 用户友好的显示
 */
const formatExecutionResult = (log) => {
  try {
    // 如果执行成功，显示用户友好的信息
    if (log.status === 'success') {
      return formatSuccessResult(log)
    }

    // 如果执行失败，显示错误信息
    if (log.status === 'failed' || log.status === 'error') {
      return formatFailureResult(log)
    }

    // 其他状态，显示原始响应
    return formatApiResponse(log.apiResponse)
  } catch (error) {
    console.warn('执行结果格式化失败:', error)
    return '格式化失败'
  }
}

/**
 * 格式化成功结果
 */
const formatSuccessResult = (log) => {
  const parts = []

  // 预约成功信息
  parts.push('🎉 预约成功！')

  // 时间信息
  if (log.startTime && log.endTime) {
    parts.push(`⏰ 时间：${log.startTime} - ${log.endTime}`)
  }

  // 房间信息
  if (log.roomName || log.roomid) {
    const roomInfo = log.roomName || `房间${log.roomid}`
    parts.push(`🏠 房间：${roomInfo}`)
  }

  // 座位信息
  if (log.seatid) {
    parts.push(`💺 座位：${log.seatid}号`)
  }

  // 尝试从API响应中提取更多信息
  const apiData = parseApiResponse(log.apiResponse)
  if (apiData?.seatReserve) {
    const reserve = apiData.seatReserve
    if (reserve.duration) {
      parts.push(`⏱️ 时长：${reserve.duration}小时`)
    }
    if (reserve.firstLevelName) {
      parts.push(`📍 位置：${reserve.firstLevelName}`)
    }
  }

  return parts.join('\n')
}

/**
 * 格式化失败结果
 */
const formatFailureResult = (log) => {
  const parts = []

  // 失败信息
  parts.push('❌ 预约失败')

  // 错误信息
  if (log.errorMessage) {
    parts.push(`原因：${log.errorMessage}`)
  }

  // 尝试从API响应中提取错误信息
  const apiData = parseApiResponse(log.apiResponse)
  if (apiData?.message) {
    parts.push(`详情：${apiData.message}`)
  }

  return parts.join('\n')
}

/**
 * 安全解析API响应
 */
const parseApiResponse = (apiResponse) => {
  try {
    if (!apiResponse) return null

    if (typeof apiResponse === 'string') {
      return JSON.parse(apiResponse)
    }

    return apiResponse
  } catch {
    return null
  }
}

/**
 * 原始API响应格式化（备用）
 */
const formatApiResponse = (apiResponse) => {
  try {
    if (!apiResponse) return '无响应数据'

    if (typeof apiResponse === 'string') {
      // 尝试解析JSON，失败则返回原字符串
      try {
        const parsed = JSON.parse(apiResponse)
        return JSON.stringify(parsed, null, 2)
      } catch {
        return apiResponse.length > 200 ? apiResponse.substring(0, 200) + '...' : apiResponse
      }
    }

    return JSON.stringify(apiResponse, null, 2)
  } catch (error) {
    console.warn('API响应格式化失败:', error)
    return '格式化失败'
  }
}

/**
 * 简化的执行详情格式化
 */
const formatExecutionDetails = (log) => {
  try {
    const details = []

    // 重试次数
    if (log?.attemptCount > 1) {
      details.push(`重试${log.attemptCount}次`)
    }

    // 执行时间
    if (log?.executionTime) {
      const seconds = parseFloat(log.executionTime)
      if (!isNaN(seconds)) {
        const timeText = seconds < 1
          ? `${Math.round(seconds * 1000)}ms`
          : `${seconds.toFixed(1)}秒`
        details.push(`耗时${timeText}`)
      }
    }

    // 响应时间
    if (log?.apiResponseTime) {
      details.push(`响应时间${formatDateTime(log.apiResponseTime)}`)
    }

    return details.length > 0 ? details.join(' | ') : '无详细信息'
  } catch (error) {
    console.warn('执行详情格式化失败:', error)
    return '格式化失败'
  }
}

// 生命周期
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.execution-history {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}



.filter-card {
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-item label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.history-list {
  margin-bottom: 20px;
}

.loading-container {
  padding: 40px;
}

.empty-container {
  padding: 40px;
  text-align: center;
}

.log-item {
  margin-bottom: 15px;
}

.normal-log {
  transition: all 0.3s;
}

.normal-log:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}



.log-time {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #606266;
}

.log-status {
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.status-success {
  background-color: #f0f9ff;
  color: #67c23a;
}

.status-failed {
  background-color: #fef0f0;
  color: #f56c6c;
}

.status-error {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.status-unknown {
  background-color: #f4f4f5;
  color: #909399;
}

.log-duration {
  font-size: 14px;
  color: #909399;
}



.log-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.basic-info {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  color: #606266;
}

.api-response, .execution-details, .error-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.response-label, .details-label, .error-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.response-content {
  font-size: 13px;
  color: #303133;
  background-color: #f5f7fa;
  padding: 12px 16px;
  border-radius: 6px;
  border-left: 3px solid #409eff;
  word-break: break-word;
  line-height: 1.6;
  white-space: pre-line;
}

/* 成功状态的特殊样式 */
.status-success .response-content {
  background-color: #f0f9ff;
  border-left-color: #67c23a;
  color: #529b2e;
}

/* 失败状态的特殊样式 */
.status-failed .response-content,
.status-error .response-content {
  background-color: #fef0f0;
  border-left-color: #f56c6c;
  color: #c45656;
}

.details-content {
  font-size: 13px;
  color: #606266;
  font-family: 'Courier New', monospace;
}

.error-content {
  font-size: 13px;
  color: #f56c6c;
  background-color: #fef0f0;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #f56c6c;
}



.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .execution-history {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }



  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .log-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .basic-info {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
