<template>
  <div class="dashboard-container">
    <el-container>
      <el-header class="dashboard-header">
        <div class="header-left">
          <h2>座位预约系统</h2>
        </div>
        <div class="header-right">
          <span class="welcome-text">欢迎，{{ user?.name }}</span>
          <el-button type="info" plain @click="goToProfile" v-if="user?.role !== 'ADMIN'">
            <el-icon><User /></el-icon>
            个人信息
          </el-button>
          <el-button type="primary" plain @click="goToExecutionHistory" v-if="user?.role !== 'ADMIN'">
            <el-icon><Document /></el-icon>
            执行历史
          </el-button>
          <el-button type="warning" plain @click="goToRedemption" v-if="user?.role !== 'ADMIN'">
            <el-icon><Present /></el-icon>
            兑换码
          </el-button>
          <el-button type="success" plain @click="goToMemberManagement" v-if="user?.role === 'ADMIN'">
            <el-icon><UserFilled /></el-icon>
            成员管理
          </el-button>
          <el-button type="warning" plain @click="goToRoomManagement" v-if="user?.role === 'ADMIN'">
            <el-icon><House /></el-icon>
            房间管理
          </el-button>
          <el-button type="danger" plain @click="goToWorkerManagement" v-if="user?.role === 'ADMIN'">
            <el-icon><Monitor /></el-icon>
            副服务器管理
          </el-button>
          <el-button type="info" plain @click="goToRedemptionManagement" v-if="user?.role === 'ADMIN'">
            <el-icon><Present /></el-icon>
            兑换码管理
          </el-button>
          <el-button type="success" plain @click="goToAdminExecutionLogs" v-if="user?.role === 'ADMIN'">
            <el-icon><Document /></el-icon>
            执行日志管理
          </el-button>
          <el-button type="warning" plain @click="goToAnnouncementManagement" v-if="user?.role === 'ADMIN'">
            <el-icon><Warning /></el-icon>
            公告管理
          </el-button>
          <el-button type="info" plain @click="showUserAnnouncements">
            <el-icon><Document /></el-icon>
            系统公告
          </el-button>
          <el-button type="primary" @click="handleLogout">退出登录</el-button>
        </div>
      </el-header>
      
      <el-main class="dashboard-main">
        <div class="dashboard-content">
          <!-- 管理员界面 -->
          <template v-if="user?.role === 'ADMIN'">
            <el-card class="admin-welcome-card">
              <h3>管理员控制台</h3>
              <p>您可以管理系统中的所有用户预约信息和设置。</p>
              <div class="admin-stats">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <div class="stat-item">
                      <el-icon class="stat-icon"><User /></el-icon>
                      <div class="stat-info">
                        <span class="stat-number">{{ totalUsers }}</span>
                        <span class="stat-label">总用户数</span>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="stat-item">
                      <el-icon class="stat-icon"><Calendar /></el-icon>
                      <div class="stat-info">
                        <span class="stat-number">{{ activeReservations }}</span>
                        <span class="stat-label">活跃预约</span>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="stat-item">
                      <el-icon class="stat-icon"><House /></el-icon>
                      <div class="stat-info">
                        <span class="stat-number">{{ totalRooms }}</span>
                        <span class="stat-label">总房间数</span>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="stat-item">
                      <el-icon class="stat-icon"><Warning /></el-icon>
                      <div class="stat-info">
                        <span class="stat-number">{{ expiringSoon }}</span>
                        <span class="stat-label">即将到期</span>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-card>
            
            <!-- 管理员功能卡片 -->
            <div class="admin-function-cards">
              <el-row :gutter="20">
                <el-col :xs="24" :sm="12" :md="6">
                  <el-card class="function-card admin-card" @click="goToMemberManagement">
                    <div class="card-content">
                      <el-icon class="card-icon admin-icon"><UserFilled /></el-icon>
                      <h4>成员管理</h4>
                      <p>管理用户预约信息和剩余天数</p>
                    </div>
                  </el-card>
                </el-col>

                <el-col :xs="24" :sm="12" :md="6">
                  <el-card class="function-card admin-card" @click="goToRoomManagement">
                    <div class="card-content">
                      <el-icon class="card-icon admin-icon"><House /></el-icon>
                      <h4>房间管理</h4>
                      <p>管理学校和房间信息</p>
                    </div>
                  </el-card>
                </el-col>

                <el-col :xs="24" :sm="12" :md="6">
                  <el-card class="function-card admin-card" @click="goToWorkerManagement">
                    <div class="card-content">
                      <el-icon class="card-icon admin-icon"><Monitor /></el-icon>
                      <h4>副服务器管理</h4>
                      <p>管理和监控副服务器状态</p>
                    </div>
                  </el-card>
                </el-col>

                <el-col :xs="24" :sm="12" :md="6">
                  <el-card class="function-card admin-card" @click="goToDistributedTaskManagement">
                    <div class="card-content">
                      <el-icon class="card-icon admin-icon"><Setting /></el-icon>
                      <h4>分布式任务管理</h4>
                      <p>智能分配预约任务，监控执行状态</p>
                    </div>
                  </el-card>
                </el-col>

                <el-col :xs="24" :sm="12" :md="6">
                  <el-card class="function-card admin-card" @click="goToRedemptionManagement">
                    <div class="card-content">
                      <el-icon class="card-icon admin-icon"><Present /></el-icon>
                      <h4>兑换码管理</h4>
                      <p>生成和管理兑换码</p>
                    </div>
                  </el-card>
                </el-col>

                <el-col :xs="24" :sm="12" :md="6">
                  <el-card class="function-card admin-card" @click="goToAdminExecutionLogs">
                    <div class="card-content">
                      <el-icon class="card-icon admin-icon"><Document /></el-icon>
                      <h4>执行日志管理</h4>
                      <p>查看所有用户的预约执行记录和技术调试信息</p>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </template>
          
          <!-- 普通用户界面 -->
          <template v-else>
            <!-- 当前预约信息卡片 -->
            <el-card v-if="currentReservation" class="current-reservation-card">
              <template #header>
                <div class="reservation-header">
                  <span>当前预约</span>
                  <el-tag :type="getReservationStatusType(currentReservation.status)">
                    {{ getReservationStatusText(currentReservation.status) }}
                  </el-tag>
                </div>
              </template>
              
              <div class="reservation-info">
                <el-row :gutter="20">
                  <el-col :xs="24" :sm="12" :md="6">
                    <div class="info-item">
                      <span class="label">学校</span>
                      <span class="value">{{ currentReservation.schoolName }}</span>
                    </div>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="6">
                    <div class="info-item">
                      <span class="label">房间</span>
                      <span class="value">{{ currentReservation.roomName }}</span>
                    </div>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="6">
                    <div class="info-item">
                      <span class="label">座位号</span>
                      <span class="value">{{ currentReservation.seatId }}</span>
                    </div>
                  </el-col>
                  <el-col :xs="24" :sm="12" :md="6">
                    <div class="info-item">
                      <span class="label">剩余天数</span>
                      <span class="value remaining-days">
                        <el-tag
                          :type="getRemainingDaysType(userProfile?.remainingDays)"
                          size="large"
                        >
                          {{ userProfile?.remainingDays !== undefined ? userProfile.remainingDays : 0 }} 天
                        </el-tag>
                      </span>
                    </div>
                  </el-col>
                </el-row>
                
                <el-divider />
                
                <el-row :gutter="20">
                  <el-col :xs="24" :sm="12">
                    <div class="info-item">
                      <span class="label">开始时间</span>
                      <span class="value">{{ formatDateTime(currentReservation.startTime) }}</span>
                    </div>
                  </el-col>
                  <el-col :xs="24" :sm="12">
                    <div class="info-item">
                      <span class="label">结束时间</span>
                      <span class="value">{{ formatDateTime(currentReservation.endTime) }}</span>
                    </div>
                  </el-col>
                </el-row>
                
                <div class="reservation-actions">
                  <!-- 活跃状态的预约显示正常操作 -->
                  <template v-if="currentReservation.status === 'ACTIVE'">
                    <el-button type="primary" @click="goToReservation">
                      <el-icon><Edit /></el-icon>
                      修改预约
                    </el-button>
                    <el-button type="danger" @click="cancelReservation">
                      <el-icon><Delete /></el-icon>
                      取消预约
                    </el-button>
                  </template>

                  <!-- 暂停状态的预约显示提示信息 -->
                  <template v-else-if="currentReservation.status === 'PAUSED'">
                    <el-alert
                      title="预约已暂停"
                      type="warning"
                      description="您的剩余天数不足，预约已被暂停。请联系管理员充值后预约将自动恢复。"
                      show-icon
                      :closable="false"
                    />
                    <el-button type="danger" @click="cancelReservation" style="margin-top: 10px;">
                      <el-icon><Delete /></el-icon>
                      取消预约
                    </el-button>
                  </template>
                </div>
              </div>
            </el-card>
            
            <el-card class="welcome-card">
              <h3>欢迎使用座位预约系统</h3>
              <p v-if="!currentReservation">您目前没有预约，可以点击下方"座位预约"开始预约。</p>
              <p v-else>您已成功预约座位，预约信息如上所示。</p>
              <div class="user-info">
                <p><strong>用户名：</strong>{{ user?.username }}</p>
                <p><strong>姓名：</strong>{{ user?.name }}</p>
                <p><strong>角色：</strong>{{ user?.role === 'ADMIN' ? '管理员' : '普通用户' }}</p>
              </div>
            </el-card>
            
            <!-- 普通用户功能快捷入口 -->
            <div class="function-cards">
              <el-row :gutter="20">
                <el-col :xs="24" :sm="12" :md="8" :lg="6">
                  <el-card class="function-card" @click="goToProfile">
                    <div class="card-content">
                      <el-icon class="card-icon"><User /></el-icon>
                      <h4>个人信息</h4>
                      <p>查看和修改个人信息</p>
                    </div>
                  </el-card>
                </el-col>
                
                <el-col :xs="24" :sm="12" :md="8" :lg="6">
                  <el-card class="function-card" @click="goToReservation">
                    <div class="card-content">
                      <el-icon class="card-icon"><Calendar /></el-icon>
                      <h4>座位预约</h4>
                      <p>预约座位位置</p>
                    </div>
                  </el-card>
                </el-col>
                
                <el-col :xs="24" :sm="12" :md="8" :lg="6">
                  <el-card class="function-card" @click="goToExecutionHistory">
                    <div class="card-content">
                      <el-icon class="card-icon"><Document /></el-icon>
                      <h4>执行历史</h4>
                      <p>查看预约执行记录和详细统计</p>
                    </div>
                  </el-card>
                </el-col>
                
                <el-col :xs="24" :sm="12" :md="8" :lg="6">
                  <el-card class="function-card" @click="goToRedemption">
                    <div class="card-content">
                      <el-icon class="card-icon"><Present /></el-icon>
                      <h4>兑换码</h4>
                      <p>输入兑换码增加使用天数</p>
                    </div>
                  </el-card>
                </el-col>

                <el-col :xs="24" :sm="12" :md="8" :lg="6">
                  <el-card class="function-card more-features-card">
                    <div class="card-content">
                      <el-icon class="card-icon more-features-icon"><Plus /></el-icon>
                      <h4>更多功能</h4>
                      <p class="coming-soon">更多功能敬请期待</p>
                    </div>
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </template>
        </div>
      </el-main>
    </el-container>

    <!-- 公告弹窗 -->
    <AnnouncementPopup
      v-model:visible="showAnnouncementPopup"
      :user-id="user?.id"
      @closed="onAnnouncementPopupClosed"
    />

    <!-- 用户公告查看对话框 -->
    <el-dialog
      v-model="userAnnouncementDialogVisible"
      title="系统公告"
      width="80%"
      :before-close="handleUserAnnouncementDialogClose"
    >
      <div v-loading="userAnnouncementsLoading" class="user-announcements-container">
        <div v-if="userAnnouncements.length === 0" class="no-announcements">
          <el-empty description="暂无公告" />
        </div>
        <div v-else class="announcements-list">
          <div
            v-for="announcement in userAnnouncements"
            :key="announcement.id"
            class="announcement-item"
            @click="viewUserAnnouncement(announcement)"
          >
            <div class="announcement-header">
              <h3 class="announcement-title">{{ announcement.title }}</h3>
              <div class="announcement-meta">
                <el-tag :type="getAnnouncementPriorityType(announcement.priority)" size="small">
                  优先级: {{ announcement.priority }}
                </el-tag>
                <span class="announcement-time">
                  {{ formatDateTime(announcement.createdTime) }}
                </span>
              </div>
            </div>
            <div class="announcement-summary">
              {{ announcement.summary || '点击查看详情...' }}
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleUserAnnouncementDialogClose">关闭</el-button>
          <el-button type="primary" @click="markAllAnnouncementsAsRead" :loading="markingAllRead">
            全部标记为已读
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 公告详情查看对话框 -->
    <el-dialog
      v-model="announcementDetailDialogVisible"
      :title="currentViewAnnouncement?.title || '公告详情'"
      width="70%"
    >
      <div v-if="currentViewAnnouncement" class="announcement-detail-content">
        <div class="announcement-detail-meta">
          <el-tag :type="getTargetUsersTagType(currentViewAnnouncement.targetUsers)" size="small">
            {{ getTargetUsersText(currentViewAnnouncement.targetUsers) }}
          </el-tag>
          <span class="meta-item">
            发布时间: {{ formatDateTime(currentViewAnnouncement.createdTime) }}
          </span>
          <span class="meta-item">
            创建者: {{ currentViewAnnouncement.createdByName }}
          </span>
          <span v-if="currentViewAnnouncement.priority > 50" class="meta-item">
            <el-tag type="danger" size="small">高优先级</el-tag>
          </span>
        </div>
        <div class="announcement-detail-body" v-html="currentViewAnnouncement.content"></div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="announcementDetailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="markCurrentAnnouncementAsRead" :loading="markingCurrentRead">
            标记为已读
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Calendar, Document, Setting, Plus, Edit, Delete, UserFilled, House, Warning, Monitor, Present } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import AnnouncementPopup from '@/components/AnnouncementPopup.vue'
import announcementApi from '@/api/announcement'
import api from '@/utils/api'

const router = useRouter()
const authStore = useAuthStore()

const user = computed(() => authStore.user)
const currentReservation = ref(null)
const userProfile = ref(null) // 存储完整的用户信息

// 公告弹窗相关
const showAnnouncementPopup = ref(false)

// 用户公告查看相关
const userAnnouncementDialogVisible = ref(false)
const announcementDetailDialogVisible = ref(false)
const userAnnouncements = ref([])
const currentViewAnnouncement = ref(null)
const userAnnouncementsLoading = ref(false)
const markingAllRead = ref(false)
const markingCurrentRead = ref(false)

// 管理员统计数据
const totalUsers = ref(0)
const activeReservations = ref(0)
const totalRooms = ref(0)
const expiringSoon = ref(0)

// 获取管理员统计数据
const fetchAdminStats = async () => {
  if (user.value?.role !== 'ADMIN') return
  
  try {
    const response = await api.get('/admin/stats')
    if (response.data.code === 200) {
      const stats = response.data.data
      totalUsers.value = stats.totalUsers || 0
      activeReservations.value = stats.activeReservations || 0
      totalRooms.value = stats.totalRooms || 0
      expiringSoon.value = stats.expiringSoon || 0
    }
  } catch (error) {
    console.error('获取管理员统计数据失败:', error)
  }
}

// 获取当前预约信息
const fetchCurrentReservation = async () => {
  try {
    const response = await api.get('/user/profile')
    if (response.data.code === 200) {
      userProfile.value = response.data.data // 存储完整的用户信息
      currentReservation.value = response.data.data.currentReservation || null
    } else {
      userProfile.value = null
      currentReservation.value = null
    }
  } catch (error) {
    console.error('获取当前预约失败:', error)
    userProfile.value = null
    currentReservation.value = null
  }
}

// 格式化日期时间
const formatDateTime = (dateTimeString) => {
  if (!dateTimeString) return ''
  try {
    // 检查是否是时间格式（HH:MM:SS）
    if (typeof dateTimeString === 'string' && /^\d{2}:\d{2}:\d{2}$/.test(dateTimeString)) {
      // 如果是纯时间格式，直接返回时间部分
      return dateTimeString.substring(0, 5) // 只显示 HH:MM
    }

    // 尝试解析为完整的日期时间
    const date = new Date(dateTimeString)
    if (isNaN(date.getTime())) {
      // 如果解析失败，尝试作为时间处理
      return dateTimeString.toString()
    }

    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (e) {
    return dateTimeString
  }
}

// 根据剩余天数返回标签类型
const getRemainingDaysType = (days) => {
  if (!days) return 'info'
  if (days <= 0) return 'danger'
  if (days === 1) return 'warning'
  if (days <= 3) return 'success'
  return 'primary'
}

// 根据预约状态返回标签类型
const getReservationStatusType = (status) => {
  switch (status) {
    case 'ACTIVE':
      return 'success'
    case 'PAUSED':
      return 'warning'
    case 'CANCELLED':
      return 'danger'
    default:
      return 'info'
  }
}

// 根据预约状态返回显示文本
const getReservationStatusText = (status) => {
  switch (status) {
    case 'ACTIVE':
      return '进行中'
    case 'PAUSED':
      return '已暂停'
    case 'CANCELLED':
      return '已取消'
    default:
      return '未知'
  }
}

// 取消预约
const cancelReservation = async () => {
  if (!currentReservation.value) return
  
  try {
    await ElMessageBox.confirm(
      `确定要取消当前预约吗？\n\n座位：${currentReservation.value.seatId}\n房间：${currentReservation.value.roomName}`,
      '确认取消预约',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '保留预约',
        type: 'warning'
      }
    )
    
    const response = await api.post(`/reservations/cancel/${currentReservation.value.reservationId}`)
    
    if (response.data.code === 200) {
      ElMessage.success('预约已取消')
      currentReservation.value = null
      // 重新获取用户信息
      await fetchCurrentReservation()
    } else {
      ElMessage.error(response.data.message || '取消预约失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消预约失败:', error)
      ElMessage.error('取消预约失败，请稍后再试')
    }
  }
}

const goToProfile = () => {
  router.push('/profile')
}

const goToReservation = () => {
  router.push('/reservation')
}

const goToExecutionHistory = () => {
  router.push('/execution-history')
}

const goToMemberManagement = () => {
  router.push('/member-management')
}

const goToRoomManagement = () => {
  router.push('/room-management')
}

const goToWorkerManagement = () => {
  router.push('/worker-management')
}

const goToDistributedTaskManagement = () => {
  router.push('/distributed-task-management')
}

const goToRedemption = () => {
  router.push('/redemption')
}

const goToRedemptionManagement = () => {
  router.push('/redemption-management')
}

const goToAdminExecutionLogs = () => {
  router.push('/admin-execution-logs')
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    authStore.logout()
    ElMessage.success('已退出登录')
    router.push('/login')
  } catch {
    // 用户取消操作
  }
}

// 公告弹窗相关方法
const checkAndShowAnnouncements = () => {
  // 延迟显示公告弹窗，确保页面加载完成
  setTimeout(() => {
    if (user.value?.id) {
      showAnnouncementPopup.value = true
    }
  }, 1000)
}

const onAnnouncementPopupClosed = () => {
  showAnnouncementPopup.value = false
}

// 添加公告管理按钮的导航方法
const goToAnnouncementManagement = () => {
  router.push('/announcement-management')
}

// 显示用户公告列表
const showUserAnnouncements = () => {
  userAnnouncementDialogVisible.value = true
  loadUserAnnouncements()
}

// 加载用户公告列表
const loadUserAnnouncements = async () => {
  try {
    userAnnouncementsLoading.value = true
    const response = await announcementApi.getActiveAnnouncements()
    if (response.data.code === 200) {
      userAnnouncements.value = response.data.data
    } else {
      ElMessage.error(response.data.message || '获取公告列表失败')
    }
  } catch (error) {
    console.error('获取公告列表失败:', error)
    ElMessage.error('获取公告列表失败')
  } finally {
    userAnnouncementsLoading.value = false
  }
}

// 查看公告详情
const viewUserAnnouncement = async (announcement) => {
  try {
    const response = await announcementApi.getUserAnnouncementById(announcement.id)
    if (response.data.code === 200) {
      currentViewAnnouncement.value = response.data.data
      announcementDetailDialogVisible.value = true
    } else {
      ElMessage.error(response.data.message || '获取公告详情失败')
    }
  } catch (error) {
    console.error('获取公告详情失败:', error)
    ElMessage.error('获取公告详情失败')
  }
}

// 标记当前公告为已读
const markCurrentAnnouncementAsRead = async () => {
  if (!currentViewAnnouncement.value) return

  try {
    markingCurrentRead.value = true
    const response = await announcementApi.markAnnouncementAsRead(currentViewAnnouncement.value.id)
    if (response.data.code === 200) {
      ElMessage.success('已标记为已读')
      announcementDetailDialogVisible.value = false
    } else {
      ElMessage.error(response.data.message || '标记失败')
    }
  } catch (error) {
    console.error('标记公告已读失败:', error)
    ElMessage.error('标记失败')
  } finally {
    markingCurrentRead.value = false
  }
}

// 标记所有公告为已读
const markAllAnnouncementsAsRead = async () => {
  if (userAnnouncements.value.length === 0) return

  try {
    markingAllRead.value = true
    const announcementIds = userAnnouncements.value.map(ann => ann.id)
    const response = await announcementApi.batchMarkAnnouncementsAsRead(announcementIds)
    if (response.data.code === 200) {
      ElMessage.success('已全部标记为已读')
      userAnnouncementDialogVisible.value = false
    } else {
      ElMessage.error(response.data.message || '批量标记失败')
    }
  } catch (error) {
    console.error('批量标记公告已读失败:', error)
    ElMessage.error('批量标记失败')
  } finally {
    markingAllRead.value = false
  }
}

// 关闭用户公告对话框
const handleUserAnnouncementDialogClose = () => {
  userAnnouncementDialogVisible.value = false
  userAnnouncements.value = []
}

// 获取公告优先级标签类型
const getAnnouncementPriorityType = (priority) => {
  if (priority >= 80) return 'danger'
  if (priority >= 50) return 'warning'
  if (priority >= 20) return 'success'
  return ''
}

// 获取目标用户标签类型
const getTargetUsersTagType = (targetUsers) => {
  const typeMap = {
    'ALL': '',
    'USER': 'success',
    'ADMIN': 'warning'
  }
  return typeMap[targetUsers] || ''
}

// 获取目标用户文本
const getTargetUsersText = (targetUsers) => {
  const textMap = {
    'ALL': '所有用户',
    'USER': '普通用户',
    'ADMIN': '管理员'
  }
  return textMap[targetUsers] || targetUsers
}

// 页面加载时获取当前预约信息和显示公告
onMounted(() => {
  fetchCurrentReservation()
  fetchAdminStats()
  // 检查并显示公告弹窗
  checkAndShowAnnouncements()
})
</script>

<style scoped>
.dashboard-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.dashboard-header {
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left h2 {
  color: #333;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.welcome-text {
  color: #666;
  font-size: 14px;
}

.dashboard-main {
  padding: 20px;
}

.dashboard-content {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-card {
  text-align: center;
  padding: 40px 20px;
  margin-bottom: 30px;
}

.welcome-card h3 {
  color: #333;
  margin-bottom: 16px;
  font-size: 24px;
}

.welcome-card p {
  color: #666;
  margin-bottom: 24px;
  font-size: 16px;
}

.user-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.user-info p {
  margin: 8px 0;
  color: #333;
}

.function-cards {
  margin-top: 20px;
}

/* 当前预约信息卡片样式 */
.current-reservation-card {
  margin-bottom: 30px;
  border: 2px solid #409EFF;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
}

.reservation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.reservation-info {
  padding: 10px 0;
}

.info-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}

.info-item .label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
  font-weight: 500;
}

.info-item .value {
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

.remaining-days {
  display: flex;
  align-items: center;
}

.reservation-actions {
  margin-top: 20px;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.reservation-actions .el-button {
  flex: 1;
  max-width: 200px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .reservation-actions {
    flex-direction: column;
  }
  
  .reservation-actions .el-button {
    max-width: none;
  }
  
  .info-item {
    margin-bottom: 12px;
  }
  
  .info-item .value {
    font-size: 14px;
  }
}

.function-card {
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.function-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-content {
  text-align: center;
  padding: 20px;
}

.card-icon {
  font-size: 48px;
  color: #409EFF;
  margin-bottom: 16px;
}

.card-content h4 {
  color: #333;
  margin: 16px 0 8px 0;
  font-size: 18px;
}

.card-content p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.more-features-card {
  background-color: #f8f9fa;
  border-color: #e9e9e9;
  cursor: default;
}

.more-features-card:hover {
  transform: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.more-features-icon {
  color: #909399;
}

.coming-soon {
  color: #909399;
  font-style: italic;
}

/* 管理员界面样式 */
.admin-welcome-card {
  margin-bottom: 30px;
  border: 2px solid #409EFF;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
}

.admin-stats {
  padding: 10px 0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16px;
}

.stat-icon {
  font-size: 48px;
  color: #409EFF;
  margin-bottom: 8px;
}

.stat-info {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

/* 管理员功能卡片样式 */
.admin-function-cards {
  margin-top: 20px;
}

.admin-card {
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
  min-height: 200px;
  display: flex;
  flex-direction: column;
}

.admin-card .el-card__body {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 20px;
}

.admin-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.admin-card .card-content {
  text-align: center;
  padding: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex: 1;
  min-height: 160px;
}

.admin-card .card-icon {
  font-size: 48px;
  color: #409EFF;
  margin-bottom: 12px;
  flex-shrink: 0;
}

.admin-card .card-content h4 {
  color: #333;
  margin: 12px 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.admin-card .card-content p {
  color: #666;
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  max-width: 180px;
  word-wrap: break-word;
  text-align: center;
  padding: 0 8px;
}

.admin-icon {
  color: #409EFF;
}

/* 用户公告查看样式 */
.user-announcements-container {
  max-height: 500px;
  overflow-y: auto;
}

.no-announcements {
  text-align: center;
  padding: 40px 0;
}

.announcements-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.announcement-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.announcement-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.announcement-title {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  flex: 1;
  margin-right: 16px;
}

.announcement-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.announcement-time {
  color: #909399;
  font-size: 12px;
}

.announcement-summary {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  margin-top: 8px;
}

.announcement-detail-content {
  max-height: 400px;
  overflow-y: auto;
}

.announcement-detail-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.meta-item {
  color: #606266;
  font-size: 14px;
}

.announcement-detail-body {
  line-height: 1.8;
  color: #303133;
}

.announcement-detail-body :deep(h1),
.announcement-detail-body :deep(h2),
.announcement-detail-body :deep(h3) {
  color: #303133;
  margin-top: 20px;
  margin-bottom: 12px;
}

.announcement-detail-body :deep(p) {
  margin-bottom: 12px;
}

.announcement-detail-body :deep(ul),
.announcement-detail-body :deep(ol) {
  margin-bottom: 12px;
  padding-left: 20px;
}

.announcement-detail-body :deep(li) {
  margin-bottom: 6px;
}

@media (max-width: 768px) {
  .announcement-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .announcement-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .announcement-detail-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>