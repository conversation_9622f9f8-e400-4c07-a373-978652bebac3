<template>
  <div class="worker-management-container">
    <el-container>
      <el-header class="worker-management-header">
        <div class="header-left">
          <el-button type="info" plain @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回首页
          </el-button>
          <h2>副服务器管理</h2>
        </div>
        <div class="header-right">
          <el-button type="success" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            添加服务器
          </el-button>
          <el-button @click="refreshServers">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </el-header>
      
      <el-main class="worker-management-main">
        <!-- 统计卡片 -->
        <div class="stats-cards">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-card class="stat-card">
                <div class="stat-content">
                  <div class="stat-icon online">
                    <el-icon><Monitor /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-number">{{ statistics.onlineServers || 0 }}</div>
                    <div class="stat-label">在线服务器</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stat-card">
                <div class="stat-content">
                  <div class="stat-icon total">
                    <el-icon><Setting /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-number">{{ statistics.totalServers || 0 }}</div>
                    <div class="stat-label">总服务器数</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stat-card">
                <div class="stat-content">
                  <div class="stat-icon load">
                    <el-icon><Loading /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-number">{{ statistics.totalCurrentLoad || 0 }}</div>
                    <div class="stat-label">当前负载</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="6">
              <el-card class="stat-card">
                <div class="stat-content">
                  <div class="stat-icon tasks">
                    <el-icon><Document /></el-icon>
                  </div>
                  <div class="stat-info">
                    <div class="stat-number">{{ statistics.totalCompleted || 0 }}</div>
                    <div class="stat-label">完成任务</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 操作栏 -->
        <div class="action-bar">
          <div class="action-left">
            <el-button
              type="info"
              :disabled="selectedServers.length === 0"
              @click="batchHealthCheck"
            >
              <el-icon><Monitor /></el-icon>
              批量检查
            </el-button>
            <el-button
              type="danger"
              :disabled="selectedServers.length === 0"
              @click="batchDelete"
            >
              <el-icon><Delete /></el-icon>
              批量删除
            </el-button>
          </div>
          <div class="action-right">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索服务器名称或ID"
              style="width: 200px"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div>

        <!-- 服务器列表表格 -->
        <el-card class="table-card">
          <el-table
            v-loading="loading"
            :data="filteredServers"
            @selection-change="handleSelectionChange"
            stripe
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" />
            
            <el-table-column prop="workerId" label="服务器ID" width="120" />
            
            <el-table-column prop="name" label="服务器名称" width="150" />
            
            <el-table-column label="地址" width="180">
              <template #default="scope">
                {{ scope.row.host }}:{{ scope.row.port }}
              </template>
            </el-table-column>
            
            <el-table-column label="状态" width="120">
              <template #default="scope">
                <el-tag 
                  :type="getStatusType(scope.row.status)"
                  size="small"
                >
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <el-table-column label="负载" width="120">
              <template #default="scope">
                <div class="load-info">
                  <span>{{ scope.row.currentLoad || 0 }}/{{ scope.row.maxConcurrentTasks || 0 }}</span>
                  <el-progress 
                    :percentage="getLoadPercentage(scope.row)" 
                    :stroke-width="6"
                    :show-text="false"
                    style="margin-top: 4px"
                  />
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="任务统计" width="120">
              <template #default="scope">
                <div class="task-stats">
                  <div>完成: {{ scope.row.totalTasksCompleted || 0 }}</div>
                  <div>失败: {{ scope.row.totalTasksFailed || 0 }}</div>
                </div>
              </template>
            </el-table-column>
            
            <el-table-column label="最后心跳" width="150">
              <template #default="scope">
                <span v-if="scope.row.lastHeartbeat">
                  {{ formatTime(scope.row.lastHeartbeat) }}
                </span>
                <span v-else class="text-muted">从未连接</span>
              </template>
            </el-table-column>
            
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="scope">
                <el-button-group>
                  <el-button
                    type="primary"
                    size="small"
                    @click="healthCheck(scope.row)"
                  >
                    健康检查
                  </el-button>
                  <el-button
                    type="default"
                    size="small"
                    @click="editServer(scope.row)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="deleteServer(scope.row)"
                  >
                    删除
                  </el-button>
                </el-button-group>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-main>
    </el-container>

    <!-- 创建/编辑服务器弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogMode === 'create' ? '添加服务器' : '编辑服务器'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="服务器ID" prop="workerId">
          <el-input 
            v-model="form.workerId" 
            :disabled="dialogMode === 'edit'"
            placeholder="例如: worker-001"
          />
          <div class="form-tip">服务器的唯一标识，只能包含字母、数字、下划线和横线</div>
        </el-form-item>
        
        <el-form-item label="服务器名称" prop="name">
          <el-input v-model="form.name" placeholder="例如: 副服务器1" />
        </el-form-item>
        
        <el-form-item label="主机地址" prop="host">
          <el-input v-model="form.host" placeholder="例如: localhost 或 *************" />
        </el-form-item>
        
        <el-form-item label="端口" prop="port">
          <el-input-number 
            v-model="form.port" 
            :min="1024" 
            :max="65535"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="最大并发任务" prop="maxConcurrentTasks">
          <el-input-number 
            v-model="form.maxConcurrentTasks" 
            :min="1" 
            :max="1000"
            style="width: 100%"
          />
          <div class="form-tip">服务器同时处理的最大任务数量</div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="saving">
            {{ dialogMode === 'create' ? '创建' : '更新' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import {
  ArrowLeft, Plus, Refresh, Monitor, Setting, Loading, Document,
  Delete, Search
} from '@element-plus/icons-vue'
import workerApi from '@/api/worker'

const router = useRouter()

// 响应式数据
const servers = ref([])
const statistics = ref({})
const loading = ref(false)
const searchKeyword = ref('')
const selectedServers = ref([])

// 弹窗相关
const dialogVisible = ref(false)
const dialogMode = ref('create') // 'create' | 'edit'
const saving = ref(false)
const formRef = ref(null)

// 表单数据
const form = reactive({
  id: null,
  workerId: '',
  name: '',
  host: 'localhost',
  port: 8082,
  maxConcurrentTasks: 10
})

// 表单验证规则
const formRules = {
  workerId: [
    { required: true, message: '请输入服务器ID', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '只能包含字母、数字、下划线和横线', trigger: 'blur' },
    { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入服务器名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  host: [
    { required: true, message: '请输入主机地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口', trigger: 'blur' },
    { type: 'number', min: 1024, max: 65535, message: '端口范围 1024-65535', trigger: 'blur' }
  ],
  maxConcurrentTasks: [
    { required: true, message: '请输入最大并发任务数', trigger: 'blur' },
    { type: 'number', min: 1, max: 1000, message: '范围 1-1000', trigger: 'blur' }
  ]
}

// 计算属性
const filteredServers = computed(() => {
  if (!searchKeyword.value) {
    return servers.value
  }

  const keyword = searchKeyword.value.toLowerCase()
  return servers.value.filter(server =>
    server.workerId.toLowerCase().includes(keyword) ||
    server.name.toLowerCase().includes(keyword)
  )
})

// 自动刷新定时器
let refreshTimer = null

// 页面方法
const goBack = () => {
  router.push('/dashboard')
}

const showCreateDialog = () => {
  dialogMode.value = 'create'
  resetForm()
  dialogVisible.value = true
}

const resetForm = () => {
  Object.assign(form, {
    id: null,
    workerId: '',
    name: '',
    host: 'localhost',
    port: 8082,
    maxConcurrentTasks: 10
  })

  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 获取服务器列表
const fetchServers = async () => {
  loading.value = true
  try {
    const response = await workerApi.getAllServers()
    if (response.data.code === 200) {
      servers.value = response.data.data || []
    } else {
      ElMessage.error('获取服务器列表失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('获取服务器列表失败:', error)
    ElMessage.error('获取服务器列表失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 获取统计信息
const fetchStatistics = async () => {
  try {
    const response = await workerApi.getStatistics()
    if (response.data.code === 200) {
      statistics.value = response.data.data || {}
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}

// 刷新数据
const refreshServers = async () => {
  await Promise.all([
    fetchServers(),
    fetchStatistics()
  ])
  ElMessage.success('刷新成功')
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedServers.value = selection
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    saving.value = true

    let response
    if (dialogMode.value === 'create') {
      response = await workerApi.createServer({
        workerId: form.workerId,
        name: form.name,
        host: form.host,
        port: form.port,
        maxConcurrentTasks: form.maxConcurrentTasks
      })
    } else {
      response = await workerApi.updateServer(form.id, {
        name: form.name,
        host: form.host,
        port: form.port,
        maxConcurrentTasks: form.maxConcurrentTasks
      })
    }

    if (response.data.code === 200) {
      ElMessage.success(dialogMode.value === 'create' ? '创建成功' : '更新成功')
      dialogVisible.value = false
      await fetchServers()
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    console.error('提交表单失败:', error)
    ElMessage.error('操作失败: ' + (error.response?.data?.message || error.message))
  } finally {
    saving.value = false
  }
}

// 编辑服务器
const editServer = (server) => {
  dialogMode.value = 'edit'
  Object.assign(form, {
    id: server.id,
    workerId: server.workerId,
    name: server.name,
    host: server.host,
    port: server.port,
    maxConcurrentTasks: server.maxConcurrentTasks
  })
  dialogVisible.value = true
}

// 删除服务器
const deleteServer = async (server) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除服务器 "${server.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await workerApi.deleteServer(server.id)
    if (response.data.code === 200) {
      ElMessage.success('删除成功')
      await fetchServers()
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除服务器失败:', error)
      ElMessage.error('删除失败: ' + (error.response?.data?.message || error.message))
    }
  }
}



// 健康检查
const healthCheck = async (server) => {
  try {
    const response = await workerApi.healthCheck(server.id)
    if (response.data.code === 200) {
      const result = response.data.data
      if (result.success) {
        ElNotification.success({
          title: '健康检查通过',
          message: `服务器 "${server.name}" 状态正常\n响应时间: ${result.responseTime}ms`,
          duration: 3000
        })
      } else {
        ElNotification.warning({
          title: '健康检查失败',
          message: `服务器 "${server.name}" 状态异常\n错误: ${result.message}`,
          duration: 5000
        })
      }
      await fetchServers()
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    console.error('健康检查失败:', error)
    ElMessage.error('健康检查失败: ' + (error.response?.data?.message || error.message))
  }
}

// 批量操作
const performBatchOperation = async (operation, operationName) => {
  if (selectedServers.value.length === 0) {
    ElMessage.warning('请先选择要操作的服务器')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要${operationName} ${selectedServers.value.length} 个服务器吗？`,
      `确认${operationName}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = selectedServers.value.map(server => server.id)
    const response = await workerApi.batchOperation({ ids, operation })

    if (response.data.code === 200) {
      const result = response.data.data
      ElNotification.success({
        title: `${operationName}完成`,
        message: `成功: ${result.successCount}个, 失败: ${result.errorCount}个`,
        duration: 3000
      })

      if (result.errors && result.errors.length > 0) {
        console.warn('批量操作错误:', result.errors)
      }

      await fetchServers()
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`批量${operationName}失败:`, error)
      ElMessage.error(`批量${operationName}失败: ` + (error.response?.data?.message || error.message))
    }
  }
}

const batchDelete = () => performBatchOperation('delete', '删除')

// 批量健康检查
const batchHealthCheck = async () => {
  if (selectedServers.value.length === 0) {
    ElMessage.warning('请先选择要检查的服务器')
    return
  }

  try {
    const ids = selectedServers.value.map(server => server.id)
    const response = await workerApi.batchHealthCheck(ids)

    if (response.data.code === 200) {
      const results = response.data.data
      const successCount = results.filter(r => r.success).length
      const failCount = results.length - successCount

      ElNotification.info({
        title: '批量健康检查完成',
        message: `检查 ${results.length} 个服务器\n正常: ${successCount}个, 异常: ${failCount}个`,
        duration: 3000
      })

      await fetchServers()
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    console.error('批量健康检查失败:', error)
    ElMessage.error('批量健康检查失败: ' + (error.response?.data?.message || error.message))
  }
}

// 工具方法
const getStatusType = (status) => {
  switch (status) {
    case 'ONLINE': return 'success'
    case 'OFFLINE': return 'info'
    case 'BUSY': return 'warning'
    case 'ERROR': return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'ONLINE': return '在线'
    case 'OFFLINE': return '离线'
    case 'BUSY': return '繁忙'
    case 'ERROR': return '错误'
    default: return '未知'
  }
}

const getLoadPercentage = (server) => {
  if (!server.currentLoad || !server.maxConcurrentTasks) return 0
  return Math.round((server.currentLoad / server.maxConcurrentTasks) * 100)
}

const formatTime = (timeString) => {
  if (!timeString) return ''
  const date = new Date(timeString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 生命周期
onMounted(async () => {
  await Promise.all([
    fetchServers(),
    fetchStatistics()
  ])

  // 设置自动刷新（每30秒）
  refreshTimer = setInterval(() => {
    fetchStatistics()
  }, 30000)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>

<style scoped>
.worker-management-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.worker-management-header {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.header-right {
  display: flex;
  gap: 12px;
}

.worker-management-main {
  padding: 20px;
}

/* 统计卡片样式 */
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stat-icon.online {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.stat-icon.total {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.stat-icon.load {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.stat-icon.tasks {
  background: linear-gradient(135deg, #909399, #a6a9ad);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

/* 操作栏样式 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.action-left {
  display: flex;
  gap: 12px;
}

.action-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 表格卡片样式 */
.table-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 表格内容样式 */
.load-info {
  min-width: 80px;
}

.task-stats {
  font-size: 12px;
  line-height: 1.4;
}

.text-muted {
  color: #909399;
  font-style: italic;
}

/* 表单样式 */
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .worker-management-header {
    flex-direction: column;
    gap: 16px;
    padding: 16px 20px;
  }

  .header-left {
    width: 100%;
    justify-content: center;
  }

  .header-right {
    width: 100%;
    justify-content: center;
  }

  .action-bar {
    flex-direction: column;
    gap: 16px;
  }

  .action-left {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }

  .action-right {
    width: 100%;
    justify-content: center;
  }

  .stats-cards .el-col {
    margin-bottom: 16px;
  }
}

/* 动画效果 */
.stat-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.el-button {
  transition: all 0.2s ease;
}

.el-table {
  border-radius: 8px;
  overflow: hidden;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
