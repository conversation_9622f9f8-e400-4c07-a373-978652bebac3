<template>
  <div class="announcement-popup-container">
    <!-- 公告弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="currentAnnouncement?.title || '系统公告'"
      width="70%"
      :before-close="handleClose"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="announcement-dialog"
    >
      <div v-if="currentAnnouncement" class="announcement-content">
        <!-- 公告元信息 -->
        <div class="announcement-meta">
          <el-tag type="info" size="small">
            {{ getTargetUsersText(currentAnnouncement.targetUsers) }}
          </el-tag>
          <span class="meta-item">
            发布时间: {{ formatDateTime(currentAnnouncement.createdTime) }}
          </span>
          <span v-if="currentAnnouncement.priority > 50" class="meta-item">
            <el-tag type="danger" size="small">高优先级</el-tag>
          </span>
        </div>

        <!-- 公告内容 -->
        <div class="content-body" v-html="currentAnnouncement.content"></div>

        <!-- 多公告导航 -->
        <div v-if="announcements.length > 1" class="announcement-navigation">
          <div class="nav-info">
            <span>{{ currentIndex + 1 }} / {{ announcements.length }}</span>
            <span class="nav-title">还有 {{ announcements.length - currentIndex - 1 }} 条公告</span>
          </div>
          <div class="nav-buttons">
            <el-button 
              v-if="currentIndex > 0" 
              size="small" 
              @click="previousAnnouncement"
            >
              上一条
            </el-button>
            <el-button 
              v-if="currentIndex < announcements.length - 1" 
              size="small" 
              type="primary"
              @click="nextAnnouncement"
            >
              下一条
            </el-button>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div class="footer-left">
            <el-checkbox v-model="dontShowAgain" size="small">
              不再显示此类公告
            </el-checkbox>
          </div>
          <div class="footer-right">
            <el-button @click="handleClose">
              {{ currentIndex < announcements.length - 1 ? '跳过' : '关闭' }}
            </el-button>
            <el-button 
              v-if="currentIndex < announcements.length - 1" 
              type="primary" 
              @click="nextAnnouncement"
            >
              下一条
            </el-button>
            <el-button 
              v-else 
              type="primary" 
              @click="handleClose"
            >
              我知道了
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import announcementApi from '@/api/announcement'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  userId: {
    type: Number,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:visible', 'closed'])

// 响应式数据
const dialogVisible = ref(false)
const announcements = ref([])
const currentIndex = ref(0)
const dontShowAgain = ref(false)
const loading = ref(false)

// 计算属性
const currentAnnouncement = computed(() => {
  return announcements.value[currentIndex.value] || null
})

// 监听 visible 变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadUnreadAnnouncements()
  } else {
    dialogVisible.value = false
  }
})

// 监听 dialogVisible 变化
watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
    emit('closed')
  }
})

// 方法
const loadUnreadAnnouncements = async () => {
  try {
    loading.value = true
    const response = await announcementApi.getUnreadPopupAnnouncements()
    const responseData = response.data

    if (responseData.code === 200) {
      announcements.value = responseData.data || []
      currentIndex.value = 0
      
      if (announcements.value.length > 0) {
        dialogVisible.value = true
        // 自动标记第一条公告为已读
        markCurrentAsRead()
      } else {
        // 没有未读公告，直接关闭
        emit('update:visible', false)
        emit('closed')
      }
    } else {
      ElMessage.error(responseData.message || '获取公告失败')
      emit('update:visible', false)
      emit('closed')
    }
  } catch (error) {
    console.error('获取未读公告失败:', error)
    ElMessage.error('获取公告失败')
    emit('update:visible', false)
    emit('closed')
  } finally {
    loading.value = false
  }
}

const markCurrentAsRead = async () => {
  if (!currentAnnouncement.value) return
  
  try {
    await announcementApi.markAnnouncementAsRead(currentAnnouncement.value.id)
  } catch (error) {
    console.error('标记公告已读失败:', error)
  }
}

const nextAnnouncement = () => {
  if (currentIndex.value < announcements.value.length - 1) {
    currentIndex.value++
    markCurrentAsRead()
  }
}

const previousAnnouncement = () => {
  if (currentIndex.value > 0) {
    currentIndex.value--
  }
}

const handleClose = async () => {
  if (currentAnnouncement.value) {
    try {
      // 关闭弹窗并标记为已读
      await announcementApi.dismissAnnouncementPopup(currentAnnouncement.value.id)
      
      // 如果选择了"不再显示"，则标记所有同类公告为已关闭
      if (dontShowAgain.value) {
        const remainingIds = announcements.value
          .slice(currentIndex.value + 1)
          .map(ann => ann.id)
        
        if (remainingIds.length > 0) {
          await announcementApi.batchMarkAnnouncementsAsRead(remainingIds)
        }
      }
    } catch (error) {
      console.error('关闭公告弹窗失败:', error)
    }
  }
  
  dialogVisible.value = false
}

const getTargetUsersText = (targetUsers) => {
  const textMap = {
    'ALL': '所有用户',
    'USER': '普通用户',
    'ADMIN': '管理员'
  }
  return textMap[targetUsers] || targetUsers
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 暴露方法给父组件
defineExpose({
  loadUnreadAnnouncements
})
</script>

<style scoped>
.announcement-popup-container {
  /* 容器样式 */
}

.announcement-dialog :deep(.el-dialog) {
  border-radius: 8px;
}

.announcement-dialog :deep(.el-dialog__header) {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  border-radius: 8px 8px 0 0;
}

.announcement-dialog :deep(.el-dialog__title) {
  font-weight: 600;
  color: #303133;
}

.announcement-content {
  padding: 0;
}

.announcement-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.meta-item {
  color: #606266;
  font-size: 14px;
}

.content-body {
  line-height: 1.8;
  color: #303133;
  margin-bottom: 20px;
  max-height: 400px;
  overflow-y: auto;
  padding: 0 4px;
}

.content-body :deep(h1),
.content-body :deep(h2),
.content-body :deep(h3) {
  color: #303133;
  margin-top: 20px;
  margin-bottom: 12px;
}

.content-body :deep(p) {
  margin-bottom: 12px;
}

.content-body :deep(ul),
.content-body :deep(ol) {
  margin-bottom: 12px;
  padding-left: 20px;
}

.content-body :deep(li) {
  margin-bottom: 6px;
}

.announcement-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #f0f9ff;
  border-radius: 4px;
  border: 1px solid #e1f5fe;
}

.nav-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #606266;
  font-size: 14px;
}

.nav-title {
  color: #409eff;
  font-weight: 500;
}

.nav-buttons {
  display: flex;
  gap: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #e9ecef;
}

.footer-left {
  color: #606266;
}

.footer-right {
  display: flex;
  gap: 8px;
}

@media (max-width: 768px) {
  .announcement-dialog {
    width: 95% !important;
  }
  
  .announcement-navigation {
    flex-direction: column;
    gap: 12px;
  }
  
  .dialog-footer {
    flex-direction: column;
    gap: 12px;
  }
  
  .footer-left,
  .footer-right {
    width: 100%;
    justify-content: center;
  }
}
</style>
