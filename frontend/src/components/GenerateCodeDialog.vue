<template>
  <el-dialog
    v-model="dialogVisible"
    title="生成兑换码"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="增加天数" prop="daysToAdd">
        <el-input-number
          v-model="form.daysToAdd"
          :min="1"
          :max="365"
          placeholder="请输入天数"
          style="width: 200px;"
        />
        <span class="form-tip">每个兑换码可增加的天数（1-365天）</span>
      </el-form-item>

      <el-form-item label="生成数量" prop="quantity">
        <el-input-number
          v-model="form.quantity"
          :min="1"
          :max="1000"
          placeholder="请输入数量"
          style="width: 200px;"
        />
        <span class="form-tip">一次生成的兑换码数量（1-1000个）</span>
      </el-form-item>

      <el-form-item label="过期时间">
        <el-date-picker
          v-model="form.expireTime"
          type="datetime"
          placeholder="选择过期时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 300px;"
        />
        <span class="form-tip">留空表示永不过期</span>
      </el-form-item>

      <el-form-item label="描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入兑换码描述（可选）"
          maxlength="255"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleGenerate" :loading="loading">
          生成兑换码
        </el-button>
      </div>
    </template>

    <!-- 生成结果对话框 -->
    <el-dialog
      v-model="showResult"
      title="生成结果"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="generateResult">
        <el-alert
          :title="`成功生成 ${generateResult.totalGenerated} 个兑换码`"
          type="success"
          :closable="false"
          style="margin-bottom: 20px;"
        />
        
        <div class="result-info">
          <p><strong>批次ID：</strong>{{ generateResult.batchId }}</p>
          <p><strong>每个兑换码天数：</strong>{{ generateResult.daysToAdd }}天</p>
          <p><strong>描述：</strong>{{ generateResult.description || '无' }}</p>
        </div>

        <div class="codes-container">
          <div class="codes-header">
            <h4>生成的兑换码：</h4>
            <el-button size="small" @click="copyAllCodes">
              <el-icon><CopyDocument /></el-icon>
              复制全部
            </el-button>
          </div>
          
          <el-scrollbar height="300px">
            <div class="codes-list">
              <div 
                v-for="(code, index) in generateResult.codes" 
                :key="index"
                class="code-item"
              >
                <span class="code-text">{{ code }}</span>
                <el-button 
                  size="small" 
                  text 
                  @click="copyCode(code)"
                >
                  复制
                </el-button>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="closeResult">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { CopyDocument } from '@element-plus/icons-vue'
import api from '@/utils/api'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 数据
const dialogVisible = ref(false)
const loading = ref(false)
const showResult = ref(false)
const generateResult = ref(null)

// 表单
const formRef = ref()
const form = reactive({
  daysToAdd: 7,
  quantity: 10,
  expireTime: '',
  description: ''
})

// 表单验证规则
const rules = {
  daysToAdd: [
    { required: true, message: '请输入天数', trigger: 'blur' },
    { type: 'number', min: 1, max: 365, message: '天数必须在1-365之间', trigger: 'blur' }
  ],
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 1000, message: '数量必须在1-1000之间', trigger: 'blur' }
  ]
}

// 监听 modelValue 变化
watch(() => props.modelValue, (val) => {
  dialogVisible.value = val
})

watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
})

// 生成兑换码
const handleGenerate = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    const requestData = {
      daysToAdd: form.daysToAdd,
      quantity: form.quantity,
      description: form.description
    }
    
    if (form.expireTime) {
      requestData.expireTime = form.expireTime
    }
    
    const response = await api.post('/admin/redemption/generate', requestData)
    
    if (response.data.code === 200) {
      generateResult.value = response.data.data
      showResult.value = true
      dialogVisible.value = false
      
      ElMessage.success(response.data.message)
      emit('success')
    } else {
      ElMessage.error(response.data.message || '生成失败')
    }
  } catch (error) {
    console.error('生成兑换码失败:', error)
    ElMessage.error(error.response?.data?.message || '生成失败，请重试')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 关闭结果对话框
const closeResult = () => {
  showResult.value = false
  generateResult.value = null
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.daysToAdd = 7
  form.quantity = 10
  form.expireTime = ''
  form.description = ''
}

// 复制单个兑换码
const copyCode = async (code) => {
  try {
    await navigator.clipboard.writeText(code)
    ElMessage.success('兑换码已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

// 复制全部兑换码
const copyAllCodes = async () => {
  if (!generateResult.value || !generateResult.value.codes) return
  
  try {
    const allCodes = generateResult.value.codes.join('\n')
    await navigator.clipboard.writeText(allCodes)
    ElMessage.success(`已复制 ${generateResult.value.codes.length} 个兑换码到剪贴板`)
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}
</script>

<style scoped>
.form-tip {
  margin-left: 10px;
  color: #999;
  font-size: 12px;
}

.result-info {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.result-info p {
  margin: 5px 0;
}

.codes-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
}

.codes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.codes-header h4 {
  margin: 0;
}

.codes-list {
  background: #fafafa;
  border-radius: 4px;
  padding: 10px;
}

.code-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 5px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.code-text {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #409EFF;
}

.dialog-footer {
  text-align: right;
}
</style>
