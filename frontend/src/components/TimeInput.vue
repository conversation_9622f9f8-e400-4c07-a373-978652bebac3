<template>
  <div class="time-input-container">
    <div class="time-input-wrapper">
      <!-- 小时输入 -->
      <div class="time-segment">
        <input
          ref="hourInput"
          v-model="inputHours"
          type="text"
          class="time-input hour-input"
          placeholder="HH"
          maxlength="2"
          @input="handleHourInput"
          @keydown="handleKeydown($event, 'hour')"
          @focus="handleFocus"
          @blur="handleBlur"
        />
        <label class="time-label">时</label>
      </div>
      
      <span class="time-separator">:</span>
      
      <!-- 分钟输入 -->
      <div class="time-segment">
        <input
          ref="minuteInput"
          v-model="inputMinutes"
          type="text"
          class="time-input minute-input"
          placeholder="MM"
          maxlength="2"
          @input="handleMinuteInput"
          @keydown="handleKeydown($event, 'minute')"
          @focus="handleFocus"
          @blur="handleBlur"
        />
        <label class="time-label">分</label>
      </div>
      
      <span class="time-separator">:</span>
      
      <!-- 秒输入 -->
      <div class="time-segment">
        <input
          ref="secondInput"
          v-model="inputSeconds"
          type="text"
          class="time-input second-input"
          placeholder="SS"
          maxlength="2"
          @input="handleSecondInput"
          @keydown="handleKeydown($event, 'second')"
          @focus="handleFocus"
          @blur="handleBlur"
        />
        <label class="time-label">秒</label>
      </div>
      
      <!-- 清除按钮 -->
      <button
        v-if="clearable && hasValue"
        class="clear-button"
        @click="clearTime"
        type="button"
      >
        ×
      </button>
    </div>
    
    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
    
    <!-- 帮助提示 -->
    <div v-if="showHelp" class="help-message">
      请输入24小时制时间，小时(00-23)，分钟和秒(00-59)
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入时间'
  },
  clearable: {
    type: Boolean,
    default: true
  },
  showHelp: {
    type: Boolean,
    default: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change', 'blur', 'focus', 'complete'])

// Refs
const hourInput = ref(null)
const minuteInput = ref(null)
const secondInput = ref(null)

// Data
const errorMessage = ref('')

// Internal state for each input field
const inputHours = ref('')
const inputMinutes = ref('')
const inputSeconds = ref('')

// Computed property to check if any input has a value
const hasValue = computed(() => {
  return inputHours.value || inputMinutes.value || inputSeconds.value
})

// Computed property for formatted time
const formattedTime = computed(() => {
  if (inputHours.value && inputMinutes.value && inputSeconds.value) {
    const h = inputHours.value.padStart(2, '0')
    const m = inputMinutes.value.padStart(2, '0')
    const s = inputSeconds.value.padStart(2, '0')
    return `${h}:${m}:${s}`
  }
  return ''
})

// --- Methods ---

// Function to parse the modelValue string and update internal state
const parseTimeValue = (timeStr) => {
  if (typeof timeStr === 'string' && timeStr) {
    const parts = timeStr.split(':')
    inputHours.value = parts[0] || ''
    inputMinutes.value = parts[1] || ''
    inputSeconds.value = parts[2] || ''
  } else {
    inputHours.value = ''
    inputMinutes.value = ''
    inputSeconds.value = ''
  }
}

// Watch for changes in the modelValue prop from the parent
watch(() => props.modelValue, (newValue, oldValue) => {
  console.log('TimeInput watch - modelValue变化:', { newValue, oldValue })

  // Always parse the new value from the prop to stay in sync
  parseTimeValue(newValue)
}, { immediate: true }) // immediate: true ensures the initial value is parsed

const validateNumber = (value, min, max) => {
  if (value === '') return true
  const num = parseInt(value, 10)
  return !isNaN(num) && num >= min && num <= max
}

const formatInput = (value, maxLength = 2) => {
  // 只允许数字
  return value.replace(/\D/g, '').slice(0, maxLength)
}

const handleHourInput = (event) => {
  const value = formatInput(event.target.value)
  inputHours.value = value

  // 清除错误信息
  errorMessage.value = ''

  // 验证范围 - 只在输入完成时验证，避免输入过程中的错误提示
  if (value && value.length === 2 && !validateNumber(value, 0, 23)) {
    errorMessage.value = '小时必须在00-23之间'
    return
  }

  // 移除自动跳转逻辑，让用户完全手动控制
  // 用户可以使用Tab键或方向键在输入框间导航
}

const handleMinuteInput = (event) => {
  const value = formatInput(event.target.value)
  inputMinutes.value = value

  // 清除错误信息
  errorMessage.value = ''

  // 验证范围 - 只在输入完成时验证，避免输入过程中的错误提示
  if (value && value.length === 2 && !validateNumber(value, 0, 59)) {
    errorMessage.value = '分钟必须在00-59之间'
    return
  }

  // 移除自动跳转逻辑，让用户完全手动控制
  // 用户可以使用Tab键或方向键在输入框间导航
}

const handleSecondInput = (event) => {
  const value = formatInput(event.target.value)
  inputSeconds.value = value

  // 清除错误信息
  errorMessage.value = ''

  // 验证范围 - 只在输入完成时验证，避免输入过程中的错误提示
  if (value && value.length === 2 && !validateNumber(value, 0, 59)) {
    errorMessage.value = '秒必须在00-59之间'
    return
  }

  // 秒输入完成后可以触发完成事件
  if (value.length === 2 && validateNumber(value, 0, 59)) {
    emit('complete', formattedTime.value)
  }
}

const handleKeydown = (event, field) => {
  const input = event.target
  const cursorPosition = input.selectionStart
  const inputValue = input.value

  // Tab键保持默认行为
  if (event.key === 'Tab') {
    return
  }

  // 改进的方向键导航
  if (event.key === 'ArrowRight') {
    // 只有当光标在末尾时才跳转到下一个输入框
    if (cursorPosition === inputValue.length) {
      if (field === 'hour') {
        event.preventDefault()
        minuteInput.value?.focus()
      } else if (field === 'minute') {
        event.preventDefault()
        secondInput.value?.focus()
      }
    }
  } else if (event.key === 'ArrowLeft') {
    // 只有当光标在开头时才跳转到上一个输入框
    if (cursorPosition === 0) {
      if (field === 'minute') {
        event.preventDefault()
        hourInput.value?.focus()
        // 将光标移到末尾
        setTimeout(() => {
          if (hourInput.value) {
            hourInput.value.setSelectionRange(hourInput.value.value.length, hourInput.value.value.length)
          }
        }, 0)
      } else if (field === 'second') {
        event.preventDefault()
        minuteInput.value?.focus()
        // 将光标移到末尾
        setTimeout(() => {
          if (minuteInput.value) {
            minuteInput.value.setSelectionRange(minuteInput.value.value.length, minuteInput.value.value.length)
          }
        }, 0)
      }
    }
  }

  // 改进的退格键处理
  if (event.key === 'Backspace') {
    // 如果当前输入框为空且光标在开头，跳转到上一个输入框
    if (inputValue === '' || (inputValue.length === 1 && cursorPosition === 0)) {
      if (field === 'minute') {
        event.preventDefault()
        hourInput.value?.focus()
        setTimeout(() => {
          if (hourInput.value) {
            hourInput.value.setSelectionRange(hourInput.value.value.length, hourInput.value.value.length)
          }
        }, 0)
      } else if (field === 'second') {
        event.preventDefault()
        minuteInput.value?.focus()
        setTimeout(() => {
          if (minuteInput.value) {
            minuteInput.value.setSelectionRange(minuteInput.value.value.length, minuteInput.value.value.length)
          }
        }, 0)
      }
    }
  }

  // 移除连续数字输入的自动跳转功能
  // 用户需要手动使用Tab键或方向键在输入框间导航
}

const handleFocus = (event) => {
  // Select all text on focus for easier editing
  event.target.select()
  emit('focus', event)
}

const handleBlur = () => {
  // Use nextTick to allow focus to shift before we check the activeElement
  nextTick(() => {
    const wrapper = hourInput.value?.closest('.time-input-wrapper')
    // If the new focused element is not inside our component, then we process the blur
    if (!wrapper || !wrapper.contains(document.activeElement)) {
      // Auto-complete fields if they are empty and at least one field has a value
      if (hasValue.value) {
        const h_val = inputHours.value || '00'
        const m_val = inputMinutes.value || '00'
        const s_val = inputSeconds.value || '00'

        // Validate and format
        const h = parseInt(h_val, 10)
        const m = parseInt(m_val, 10)
        const s = parseInt(s_val, 10)

        if (h >= 0 && h <= 23 && m >= 0 && m <= 59 && s >= 0 && s <= 59) {
          const finalTime = `${String(h).padStart(2, '0')}:${String(m).padStart(2, '0')}:${String(s).padStart(2, '0')}`
          
          // Update internal state to reflect formatted value
          inputHours.value = String(h).padStart(2, '0')
          inputMinutes.value = String(m).padStart(2, '0')
          inputSeconds.value = String(s).padStart(2, '0')
          errorMessage.value = ''

          if (finalTime !== props.modelValue) {
            emit('update:modelValue', finalTime)
            emit('change', finalTime)
          }
          emit('blur', finalTime)
        } else {
          errorMessage.value = '时间值无效'
        }
      } else {
        // If all fields are empty, emit an empty string
        if (props.modelValue !== '') {
          emit('update:modelValue', '')
          emit('change', '')
        }
        emit('blur', '')
      }
    }
  })
}

const clearTime = () => {
  inputHours.value = ''
  inputMinutes.value = ''
  inputSeconds.value = ''
  errorMessage.value = ''
  emit('update:modelValue', '')
  emit('change', '')
}

// 暴露方法给父组件
defineExpose({
  focus: () => hourInput.value?.focus(),
  clear: clearTime,
  validate: () => {
    const h = parseInt(inputHours.value || '0', 10)
    const m = parseInt(inputMinutes.value || '0', 10)
    const s = parseInt(inputSeconds.value || '0', 10)

    if (!inputHours.value || !inputMinutes.value || !inputSeconds.value) {
      return { valid: false, message: '所有时间字段均为必填项' }
    }
    if (h < 0 || h > 23) return { valid: false, message: '小时必须在00-23之间' }
    if (m < 0 || m > 59) return { valid: false, message: '分钟必须在00-59之间' }
    if (s < 0 || s > 59) return { valid: false, message: '秒必须在00-59之间' }

    const finalTime = `${String(h).padStart(2, '0')}:${String(m).padStart(2, '0')}:${String(s).padStart(2, '0')}`
    return { valid: true, value: finalTime }
  }
})
</script>

<style scoped>
.time-input-container {
  width: 100%;
}

.time-input-wrapper {
  display: flex;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px 12px;
  background-color: #fff;
  transition: border-color 0.2s;
  position: relative;
}

.time-input-wrapper:hover {
  border-color: #c0c4cc;
}

.time-input-wrapper:focus-within {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.time-segment {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.time-input {
  width: 32px;
  height: 24px;
  border: none;
  outline: none;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  background: transparent;
  color: #606266;
}

.time-input:focus {
  background-color: #e6f7ff;
  border-radius: 2px;
  box-shadow: 0 0 0 1px #409eff;
  transform: scale(1.05);
  transition: all 0.2s ease;
}

.time-input::placeholder {
  color: #c0c4cc;
  font-size: 14px;
}

.time-label {
  font-size: 10px;
  color: #909399;
  margin-top: 2px;
  line-height: 1;
}

.time-separator {
  font-size: 16px;
  font-weight: bold;
  color: #909399;
  margin: 0 8px;
  user-select: none;
}

.clear-button {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  border: none;
  background: #c0c4cc;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.clear-button:hover {
  background: #909399;
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.4;
}

.help-message {
  color: #909399;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.4;
}

/* 禁用状态 */
.time-input-wrapper.disabled {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  cursor: not-allowed;
}

.time-input-wrapper.disabled .time-input {
  color: #c0c4cc;
  cursor: not-allowed;
}

/* 输入完成状态 */
.time-input.completed {
  background-color: #f0f9ff;
  color: #1d4ed8;
}

/* 错误状态 */
.time-input-wrapper.error {
  border-color: #f56c6c;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
}

</style>
