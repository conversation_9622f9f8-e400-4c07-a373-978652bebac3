<template>
  <el-card
    class="server-status-card clickable-server-card"
    :class="statusClass"
    shadow="hover"
    @click="handleCardClick"
  >
    <div class="card-header">
      <div class="server-info">
        <div class="server-name">{{ serverData.name || '未知服务器' }}</div>
        <div class="server-id">{{ serverData.workerId || 'N/A' }}</div>
      </div>
      <div class="status-indicator">
        <el-tag :type="statusTagType" size="small">
          {{ statusText }}
        </el-tag>
        <!-- 心跳状态指示器 -->
        <div class="heartbeat-indicator" v-if="serverData.lastHeartbeat">
          <div
            class="heartbeat-dot"
            :class="heartbeatStatusClass"
            :title="heartbeatTooltip"
          ></div>
        </div>
      </div>
    </div>
    
    <div class="card-body">
      <div class="metrics-grid">
        <div class="metric-item">
          <div class="metric-label">活跃任务</div>
          <div class="metric-value">{{ serverData.currentLoad || 0 }}</div>
        </div>
        <div class="metric-item">
          <div class="metric-label">最大任务</div>
          <div class="metric-value">{{ serverData.maxConcurrentTasks || 0 }}</div>
        </div>
        <div class="metric-item">
          <div class="metric-label">已完成</div>
          <div class="metric-value">{{ serverData.totalTasksCompleted || 0 }}</div>
        </div>
        <div class="metric-item">
          <div class="metric-label">失败数</div>
          <div class="metric-value">{{ serverData.totalTasksFailed || 0 }}</div>
        </div>
      </div>
      
      <div class="progress-section">
        <div class="progress-header">
          <span class="progress-label">负载率</span>
          <span class="progress-value">{{ loadRateText }}</span>
        </div>
        <el-progress 
          :percentage="loadRate" 
          :color="progressColor"
          :stroke-width="8"
          :show-text="false"
        />
      </div>
      
      <div class="success-rate-section" v-if="successRate !== null">
        <div class="success-rate-header">
          <span class="success-rate-label">成功率</span>
          <span class="success-rate-value">{{ successRateText }}</span>
        </div>
        <el-progress 
          :percentage="successRate" 
          color="#67C23A"
          :stroke-width="6"
          :show-text="false"
        />
      </div>
    </div>
    
    <div class="card-footer">
      <div class="heartbeat-info" v-if="serverData.lastHeartbeat">
        <el-icon :size="14" :color="heartbeatIconColor">
          <Clock />
        </el-icon>
        <span class="heartbeat-text" :class="heartbeatTextClass">{{ heartbeatText }}</span>
        <span class="heartbeat-time">{{ formattedHeartbeatTime }}</span>
      </div>
      <div class="heartbeat-info no-heartbeat" v-else>
        <el-icon :size="14" color="#F56C6C">
          <Clock />
        </el-icon>
        <span class="heartbeat-text error">无心跳数据</span>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import { Clock } from '@element-plus/icons-vue'

const props = defineProps({
  serverData: {
    type: Object,
    required: true,
    default: () => ({})
  },
  type: {
    type: String,
    default: 'worker',
    validator: (value) => ['master', 'backup', 'test', 'worker'].includes(value)
  }
})

const emit = defineEmits(['click'])

// 处理卡片点击事件
const handleCardClick = () => {
  emit('click', props.serverData)
}

// 服务器状态
const status = computed(() => {
  return props.serverData.status || 'OFFLINE'
})

// 状态样式类
const statusClass = computed(() => {
  return `server-status-card--${status.value.toLowerCase()}`
})

// 状态标签类型
const statusTagType = computed(() => {
  const typeMap = {
    'ONLINE': 'success',
    'OFFLINE': 'danger',
    'BUSY': 'warning',
    'ERROR': 'danger'
  }
  return typeMap[status.value] || 'info'
})

// 状态文本
const statusText = computed(() => {
  const textMap = {
    'ONLINE': '在线',
    'OFFLINE': '离线',
    'BUSY': '繁忙',
    'ERROR': '错误'
  }
  return textMap[status.value] || '未知'
})

// 负载率计算
const loadRate = computed(() => {
  const current = props.serverData.currentLoad || 0
  const max = props.serverData.maxConcurrentTasks || 1
  return Math.round((current / max) * 100)
})

const loadRateText = computed(() => {
  return `${loadRate.value}%`
})

// 进度条颜色
const progressColor = computed(() => {
  const rate = loadRate.value
  if (rate >= 90) return '#F56C6C'
  if (rate >= 70) return '#E6A23C'
  return '#409EFF'
})

// 成功率计算
const successRate = computed(() => {
  const completed = props.serverData.totalTasksCompleted || 0
  const failed = props.serverData.totalTasksFailed || 0
  const total = completed + failed
  
  if (total === 0) return null
  return Math.round((completed / total) * 100)
})

const successRateText = computed(() => {
  return successRate.value !== null ? `${successRate.value}%` : 'N/A'
})

// 心跳时间格式化
const heartbeatText = computed(() => {
  if (!props.serverData.lastHeartbeat) return '无心跳数据'

  const heartbeatTime = new Date(props.serverData.lastHeartbeat)
  const now = new Date()
  const diffMs = now - heartbeatTime
  const diffMinutes = Math.floor(diffMs / (1000 * 60))

  if (diffMinutes < 1) return '刚刚'
  if (diffMinutes < 60) return `${diffMinutes}分钟前`

  const diffHours = Math.floor(diffMinutes / 60)
  if (diffHours < 24) return `${diffHours}小时前`

  const diffDays = Math.floor(diffHours / 24)
  return `${diffDays}天前`
})

// 格式化的心跳时间（具体时间）
const formattedHeartbeatTime = computed(() => {
  if (!props.serverData.lastHeartbeat) return ''

  const heartbeatTime = new Date(props.serverData.lastHeartbeat)
  return heartbeatTime.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
})

// 心跳状态类
const heartbeatStatusClass = computed(() => {
  if (!props.serverData.lastHeartbeat) return 'heartbeat-dot--error'

  const heartbeatTime = new Date(props.serverData.lastHeartbeat)
  const now = new Date()
  const diffMinutes = Math.floor((now - heartbeatTime) / (1000 * 60))

  if (diffMinutes < 2) return 'heartbeat-dot--active'
  if (diffMinutes < 10) return 'heartbeat-dot--warning'
  return 'heartbeat-dot--error'
})

// 心跳提示文本
const heartbeatTooltip = computed(() => {
  if (!props.serverData.lastHeartbeat) return '无心跳数据'
  return `最后心跳: ${formattedHeartbeatTime.value}`
})

// 心跳图标颜色
const heartbeatIconColor = computed(() => {
  if (!props.serverData.lastHeartbeat) return '#F56C6C'

  const heartbeatTime = new Date(props.serverData.lastHeartbeat)
  const now = new Date()
  const diffMinutes = Math.floor((now - heartbeatTime) / (1000 * 60))

  if (diffMinutes < 2) return '#67C23A'
  if (diffMinutes < 10) return '#E6A23C'
  return '#F56C6C'
})

// 心跳文本样式类
const heartbeatTextClass = computed(() => {
  if (!props.serverData.lastHeartbeat) return 'error'

  const heartbeatTime = new Date(props.serverData.lastHeartbeat)
  const now = new Date()
  const diffMinutes = Math.floor((now - heartbeatTime) / (1000 * 60))

  if (diffMinutes < 2) return 'success'
  if (diffMinutes < 10) return 'warning'
  return 'error'
})
</script>

<style scoped>
.server-status-card {
  border-radius: 12px;
  border: none;
  transition: all 0.3s ease;
  height: 100%;
}

.clickable-server-card {
  cursor: pointer;
}

.server-status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.clickable-server-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.server-info {
  flex: 1;
}

.server-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.server-id {
  font-size: 12px;
  color: #909399;
  font-family: 'Courier New', monospace;
}

.status-indicator {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 心跳指示器 */
.heartbeat-indicator {
  display: flex;
  align-items: center;
}

.heartbeat-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  position: relative;
}

.heartbeat-dot--active {
  background-color: #67C23A;
  animation: heartbeat-pulse 2s infinite;
}

.heartbeat-dot--warning {
  background-color: #E6A23C;
}

.heartbeat-dot--error {
  background-color: #F56C6C;
}

@keyframes heartbeat-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(103, 194, 58, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
  }
}

.card-body {
  margin-bottom: 16px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.metric-item {
  text-align: center;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 8px;
}

.metric-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.progress-section {
  margin-bottom: 12px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.progress-value {
  font-size: 13px;
  color: #303133;
  font-weight: 600;
}

.success-rate-section {
  margin-bottom: 8px;
}

.success-rate-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.success-rate-label {
  font-size: 12px;
  color: #606266;
}

.success-rate-value {
  font-size: 12px;
  color: #303133;
  font-weight: 600;
}

.card-footer {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.heartbeat-info {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-wrap: wrap;
}

.heartbeat-info.no-heartbeat {
  opacity: 0.7;
}

.heartbeat-text {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
}

.heartbeat-text.success {
  color: #67C23A;
}

.heartbeat-text.warning {
  color: #E6A23C;
}

.heartbeat-text.error {
  color: #F56C6C;
}

.heartbeat-time {
  font-size: 11px;
  color: #C0C4CC;
  font-family: 'Courier New', monospace;
  margin-left: auto;
  white-space: nowrap;
}

/* 不同状态的卡片样式 */
.server-status-card--online {
  border-left: 4px solid #67C23A;
}

.server-status-card--offline {
  border-left: 4px solid #F56C6C;
}

.server-status-card--busy {
  border-left: 4px solid #E6A23C;
}

.server-status-card--error {
  border-left: 4px solid #F56C6C;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .metric-item {
    padding: 6px;
  }
  
  .metric-value {
    font-size: 16px;
  }
  
  .server-name {
    font-size: 14px;
  }
}
</style>
