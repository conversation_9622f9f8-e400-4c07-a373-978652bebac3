<template>
  <el-card class="task-status-card" :class="cardClass" shadow="hover">
    <div class="card-content">
      <div class="card-icon">
        <el-icon :size="32" :color="iconColor">
          <component :is="iconComponent" />
        </el-icon>
      </div>
      <div class="card-info">
        <div class="card-value">{{ displayValue }}</div>
        <div class="card-title">{{ title }}</div>
        <div class="card-subtitle" v-if="subtitle">{{ subtitle }}</div>
      </div>
    </div>
    <div class="card-trend" v-if="showTrend">
      <el-icon :size="14" :color="trendColor">
        <ArrowUp v-if="trendDirection === 'up'" />
        <ArrowDown v-if="trendDirection === 'down'" />
        <Minus v-if="trendDirection === 'stable'" />
      </el-icon>
      <span class="trend-text" :style="{ color: trendColor }">{{ trendText }}</span>
    </div>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import {
  CircleCheck, Clock, Loading, WarningFilled, Timer,
  ArrowUp, ArrowDown, Minus
} from '@element-plus/icons-vue'

const props = defineProps({
  type: {
    type: String,
    required: true,
    validator: (value) => ['completed', 'pending', 'running', 'failed', 'success-rate', 'average-time'].includes(value)
  },
  value: {
    type: [Number, String],
    required: true
  },
  title: {
    type: String,
    required: true
  },
  subtitle: {
    type: String,
    default: ''
  },
  trend: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 计算显示值
const displayValue = computed(() => {
  if (props.loading) return '--'

  if (props.type === 'success-rate') {
    return `${Number(props.value).toFixed(1)}%`
  }

  if (props.type === 'average-time') {
    const minutes = Number(props.value)
    if (minutes < 1) {
      return `${(minutes * 60).toFixed(2)}秒`
    }
    return `${minutes.toFixed(2)}分钟`
  }

  return props.value
})

// 卡片样式类
const cardClass = computed(() => {
  return `task-status-card--${props.type}`
})

// 图标组件
const iconComponent = computed(() => {
  const iconMap = {
    completed: CircleCheck,
    pending: Clock,
    running: Loading,
    failed: WarningFilled,
    'success-rate': CircleCheck,
    'average-time': Timer
  }
  return iconMap[props.type]
})

// 图标颜色
const iconColor = computed(() => {
  const colorMap = {
    completed: '#67C23A',
    pending: '#E6A23C',
    running: '#409EFF',
    failed: '#F56C6C',
    'success-rate': '#67C23A',
    'average-time': '#909399'
  }
  return colorMap[props.type]
})

// 趋势相关计算
const showTrend = computed(() => {
  return props.trend && props.trend.value !== undefined
})

const trendDirection = computed(() => {
  if (!props.trend) return 'stable'
  const value = props.trend.value
  if (value > 0) return 'up'
  if (value < 0) return 'down'
  return 'stable'
})

const trendColor = computed(() => {
  const direction = trendDirection.value
  if (direction === 'up') return '#67C23A'
  if (direction === 'down') return '#F56C6C'
  return '#909399'
})

const trendText = computed(() => {
  if (!props.trend) return ''
  const value = Math.abs(props.trend.value)
  const period = props.trend.period || '较昨日'
  return `${period} ${value}${props.trend.unit || ''}`
})
</script>

<style scoped>
.task-status-card {
  border-radius: 12px;
  border: none;
  transition: all 0.3s ease;
  cursor: pointer;
}

.task-status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.card-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(64, 158, 255, 0.1);
}

.card-info {
  flex: 1;
  min-width: 0;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.card-title {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  margin-bottom: 2px;
}

.card-subtitle {
  font-size: 12px;
  color: #909399;
}

.card-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.trend-text {
  font-size: 12px;
  font-weight: 500;
}

/* 不同类型的卡片样式 */
.task-status-card--completed .card-icon {
  background: rgba(103, 194, 58, 0.1);
}

.task-status-card--pending .card-icon {
  background: rgba(230, 162, 60, 0.1);
}

.task-status-card--running .card-icon {
  background: rgba(64, 158, 255, 0.1);
}

.task-status-card--failed .card-icon {
  background: rgba(245, 108, 108, 0.1);
}

.task-status-card--success-rate .card-icon {
  background: rgba(103, 194, 58, 0.1);
}

.task-status-card--average-time .card-icon {
  background: rgba(144, 147, 153, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-content {
    gap: 12px;
  }
  
  .card-icon {
    width: 40px;
    height: 40px;
  }
  
  .card-value {
    font-size: 24px;
  }
  
  .card-title {
    font-size: 13px;
  }
}
</style>
