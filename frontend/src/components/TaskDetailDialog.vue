<template>
  <el-dialog
    v-model="dialogVisible"
    title="任务详情"
    width="800px"
    :before-close="handleClose"
    destroy-on-close
  >
    <div v-loading="loading" class="task-detail-content">
      <div v-if="taskDetail" class="detail-sections">
        <!-- 基本信息 -->
        <el-card class="detail-section" shadow="never">
          <template #header>
            <div class="section-header">
              <el-icon><InfoFilled /></el-icon>
              <span>基本信息</span>
            </div>
          </template>
          
          <div class="info-grid">
            <div class="info-item">
              <label>任务ID:</label>
              <span>{{ taskDetail.reservationId }}</span>
            </div>
            <div class="info-item">
              <label>用户:</label>
              <span>{{ taskDetail.userName }}（{{ taskDetail.username }}）</span>
            </div>
            <div class="info-item">
              <label>学校:</label>
              <span>{{ taskDetail.schoolName }}</span>
            </div>
            <div class="info-item">
              <label>房间:</label>
              <span>{{ taskDetail.roomName }}</span>
            </div>
            <div class="info-item">
              <label>座位号:</label>
              <span>{{ taskDetail.seatId }}</span>
            </div>
            <div class="info-item">
              <label>预约时间:</label>
              <span>{{ formatTimeRange(taskDetail.startTime, taskDetail.endTime) }}</span>
            </div>
            <div class="info-item">
              <label>预约类型:</label>
              <span>{{ getReservationTypeText(taskDetail.reservationType) }}</span>
            </div>
            <div class="info-item">
              <label>创建时间:</label>
              <span>{{ formatDateTime(taskDetail.createdTime) }}</span>
            </div>
          </div>
        </el-card>
        
        <!-- 执行状态 -->
        <el-card class="detail-section" shadow="never">
          <template #header>
            <div class="section-header">
              <el-icon><Setting /></el-icon>
              <span>执行状态</span>
            </div>
          </template>
          
          <div class="status-info">
            <div class="status-item">
              <label>执行状态:</label>
              <el-tag :type="getStatusTagType(taskDetail.executionStatus)" size="large">
                {{ getExecutionStatusText(taskDetail.executionStatus) }}
              </el-tag>
            </div>
            <div class="status-item">
              <label>分配服务器:</label>
              <span>{{ taskDetail.workerName || taskDetail.workerId || '未分配' }}</span>
            </div>
            <div class="status-item">
              <label>重试次数:</label>
              <span>{{ taskDetail.retryCount || 0 }} / 3</span>
            </div>
            <div class="status-item" v-if="taskDetail.lastExecutionTime">
              <label>最后执行时间:</label>
              <span>{{ formatDateTime(taskDetail.lastExecutionTime) }}</span>
            </div>
            <div class="status-item" v-if="taskDetail.executionResult">
              <label>执行结果:</label>
              <div class="execution-result">
                <pre>{{ taskDetail.executionResult }}</pre>
              </div>
            </div>
          </div>
        </el-card>
        
        <!-- 执行日志 -->
        <el-card class="detail-section" shadow="never">
          <template #header>
            <div class="section-header">
              <el-icon><Document /></el-icon>
              <span>执行日志</span>
              <el-button 
                type="primary" 
                size="small" 
                @click="refreshLogs"
                :loading="logsLoading"
              >
                刷新日志
              </el-button>
            </div>
          </template>
          
          <div class="logs-container">
            <div v-if="executionLogs.length === 0" class="no-logs">
              暂无执行日志
            </div>
            <div v-else class="logs-content">
              <div 
                v-for="(log, index) in executionLogs" 
                :key="index" 
                class="log-line"
              >
                {{ log }}
              </div>
            </div>
          </div>
        </el-card>
      </div>
      
      <div v-else class="no-data">
        <el-empty description="任务详情加载失败" />
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          v-if="canRetry" 
          type="warning" 
          @click="handleRetry"
          :loading="retryLoading"
        >
          重新执行
        </el-button>
        <el-button 
          v-if="canAssign" 
          type="primary" 
          @click="showAssignDialog"
        >
          手动分配
        </el-button>
      </div>
    </template>
  </el-dialog>
  
  <!-- 手动分配弹窗 -->
  <el-dialog
    v-model="assignDialogVisible"
    title="手动分配任务"
    width="400px"
    append-to-body
  >
    <el-form :model="assignForm" label-width="100px">
      <el-form-item label="选择服务器:">
        <el-select 
          v-model="assignForm.workerId" 
          placeholder="请选择副服务器"
          style="width: 100%"
        >
          <el-option
            v-for="worker in availableWorkers"
            :key="worker.workerId"
            :label="`${worker.name} (${worker.workerId})`"
            :value="worker.workerId"
          />
        </el-select>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="assignDialogVisible = false">取消</el-button>
      <el-button 
        type="primary" 
        @click="handleAssign"
        :loading="assignLoading"
      >
        确认分配
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled, Setting, Document } from '@element-plus/icons-vue'
import distributedTaskApi from '@/api/distributedTask'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  taskId: {
    type: Number,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'refresh'])

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const logsLoading = ref(false)
const retryLoading = ref(false)
const assignLoading = ref(false)
const taskDetail = ref(null)
const executionLogs = ref([])

// 手动分配相关
const assignDialogVisible = ref(false)
const availableWorkers = ref([])
const assignForm = reactive({
  workerId: ''
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.taskId) {
    fetchTaskDetail()
    fetchExecutionLogs()
  }
})

watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})

// 计算属性
const canRetry = computed(() => {
  return taskDetail.value && 
         taskDetail.value.executionStatus === 'FAILED' && 
         (taskDetail.value.retryCount || 0) < 3
})

const canAssign = computed(() => {
  return taskDetail.value && 
         ['PENDING', 'FAILED'].includes(taskDetail.value.executionStatus)
})

// 方法
const fetchTaskDetail = async () => {
  if (!props.taskId) return
  
  loading.value = true
  try {
    const response = await distributedTaskApi.getTaskDetail(props.taskId)
    if (response.data.code === 200) {
      taskDetail.value = response.data.data
    } else {
      ElMessage.error('获取任务详情失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    ElMessage.error('获取任务详情失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

const fetchExecutionLogs = async () => {
  if (!props.taskId) return
  
  logsLoading.value = true
  try {
    const response = await distributedTaskApi.getTaskExecutionLogs(props.taskId)
    if (response.data.code === 200) {
      executionLogs.value = response.data.data || []
    }
  } catch (error) {
    console.error('获取执行日志失败:', error)
  } finally {
    logsLoading.value = false
  }
}

const refreshLogs = () => {
  fetchExecutionLogs()
}

const handleRetry = async () => {
  if (!props.taskId) return
  
  retryLoading.value = true
  try {
    const response = await distributedTaskApi.retryFailedTask(props.taskId)
    if (response.data.code === 200) {
      ElMessage.success('任务重新执行设置成功')
      await fetchTaskDetail()
      emit('refresh')
    } else {
      ElMessage.error('任务重新执行失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('任务重新执行失败:', error)
    ElMessage.error('任务重新执行失败，请检查网络连接')
  } finally {
    retryLoading.value = false
  }
}

const showAssignDialog = async () => {
  try {
    const response = await distributedTaskApi.getAvailableWorkers()
    if (response.data.code === 200) {
      availableWorkers.value = response.data.data || []
      assignForm.workerId = ''
      assignDialogVisible.value = true
    } else {
      ElMessage.error('获取可用服务器列表失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('获取可用服务器列表失败:', error)
    ElMessage.error('获取可用服务器列表失败，请检查网络连接')
  }
}

const handleAssign = async () => {
  if (!assignForm.workerId) {
    ElMessage.warning('请选择副服务器')
    return
  }
  
  assignLoading.value = true
  try {
    const response = await distributedTaskApi.assignTaskToWorker(props.taskId, assignForm.workerId)
    if (response.data.code === 200) {
      ElMessage.success('任务分配成功')
      assignDialogVisible.value = false
      await fetchTaskDetail()
      emit('refresh')
    } else {
      ElMessage.error('任务分配失败: ' + response.data.message)
    }
  } catch (error) {
    console.error('任务分配失败:', error)
    ElMessage.error('任务分配失败，请检查网络连接')
  } finally {
    assignLoading.value = false
  }
}

const handleClose = () => {
  dialogVisible.value = false
}

// 工具方法
const formatDateTime = (dateTime) => {
  if (!dateTime) return 'N/A'
  return new Date(dateTime).toLocaleString('zh-CN')
}

const formatTimeRange = (startTime, endTime) => {
  if (!startTime || !endTime) return 'N/A'
  return `${startTime} - ${endTime}`
}

const getReservationTypeText = (type) => {
  const typeMap = {
    'SAME_DAY': '当天预约',
    'ADVANCE_ONE_DAY': '提前一天预约'
  }
  return typeMap[type] || '未知'
}

const getExecutionStatusText = (status) => {
  const statusMap = {
    'PENDING': '待执行',
    'RUNNING': '执行中',
    'SUCCESS': '成功',
    'FAILED': '失败'
  }
  return statusMap[status] || '未知'
}

const getStatusTagType = (status) => {
  const typeMap = {
    'PENDING': 'warning',
    'RUNNING': 'primary',
    'SUCCESS': 'success',
    'FAILED': 'danger'
  }
  return typeMap[status] || 'info'
}
</script>

<style scoped>
.task-detail-content {
  max-height: 600px;
  overflow-y: auto;
}

.detail-sections {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-section {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.info-item label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  flex-shrink: 0;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.status-item label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
  flex-shrink: 0;
}

.execution-result {
  flex: 1;
  background: #f5f7fa;
  border-radius: 4px;
  padding: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.execution-result pre {
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-word;
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  background: #f5f7fa;
  border-radius: 4px;
  padding: 12px;
}

.no-logs {
  text-align: center;
  color: #909399;
  font-style: italic;
}

.logs-content {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-line {
  margin-bottom: 4px;
  padding: 2px 0;
  border-bottom: 1px solid #e4e7ed;
}

.log-line:last-child {
  border-bottom: none;
}

.no-data {
  text-align: center;
  padding: 40px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .info-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .info-item,
  .status-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .info-item label,
  .status-item label {
    min-width: auto;
  }
}
</style>
