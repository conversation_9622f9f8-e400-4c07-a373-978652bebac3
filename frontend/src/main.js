import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 导入 ResizeObserver 错误修复
import './utils/resizeObserverFix.js'

// 导入 Webpack Overlay 错误拦截器
import './utils/webpackOverlayFix.js'

// 导入自定义错误覆盖层（替代webpack-dev-server overlay）
import './utils/customErrorOverlay.js'

// 🔧 早期错误拦截 - 在webpack-dev-server overlay之前处理
const originalConsoleError = console.error
console.error = (...args) => {
  // 检查是否是时间验证相关错误
  if (args.length > 0) {
    const firstArg = args[0]

    // 拦截包含endTime字段的错误对象
    if (typeof firstArg === 'object' && firstArg && firstArg.endTime) {
      console.warn('时间验证错误已被拦截（避免webpack overlay）:', firstArg)
      return
    }

    // 拦截ResizeObserver错误
    if (typeof firstArg === 'string' &&
        (firstArg.includes('ResizeObserver') ||
         firstArg.includes('loop completed'))) {
      return
    }
  }

  // 其他错误正常处理
  originalConsoleError.apply(console, args)
}

// 🔧 全局错误事件拦截
window.addEventListener('error', (event) => {
  // 拦截时间验证相关错误
  if (event.error && typeof event.error === 'object' && event.error.endTime) {
    console.warn('全局错误事件：时间验证错误已被拦截')
    event.preventDefault()
    event.stopPropagation()
    return false
  }

  // 拦截ResizeObserver错误
  if (event.message &&
      (event.message.includes('ResizeObserver') ||
       event.message.includes('loop completed'))) {
    event.preventDefault()
    event.stopPropagation()
    return false
  }
}, true) // 使用捕获阶段，确保在其他监听器之前执行

// 开发环境下导入错误测试工具
if (process.env.NODE_ENV === 'development') {
  import('./utils/errorTest.js')
}

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// Vue 应用级错误处理
app.config.errorHandler = (err, vm, info) => {
  // 拦截时间验证相关错误
  if (err && typeof err === 'object' && err.endTime) {
    console.warn('Vue错误处理器：时间验证错误已被拦截')
    return
  }

  // 拦截ResizeObserver相关错误
  if (
    err.message &&
    (err.message.includes('ResizeObserver loop completed') ||
     err.message.includes('ResizeObserver loop limit exceeded'))
  ) {
    return
  }

  console.error('Vue Error:', err, info)
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus)

app.mount('#app')