/**
 * ResizeObserver 错误修复工具
 * 解决 "ResizeObserver loop completed with undelivered notifications" 错误
 */

// 存储原始的 ResizeObserver
const OriginalResizeObserver = window.ResizeObserver

// 创建一个包装的 ResizeObserver
class SafeResizeObserver extends OriginalResizeObserver {
  constructor(callback) {
    // 包装回调函数，添加错误处理
    const wrappedCallback = (entries, observer) => {
      try {
        // 使用 requestAnimationFrame 来避免循环
        requestAnimationFrame(() => {
          callback(entries, observer)
        })
      } catch (error) {
        if (!error.message.includes('ResizeObserver loop completed')) {
          console.error('ResizeObserver callback error:', error)
        }
      }
    }
    
    super(wrappedCallback)
  }
}

// 替换全局的 ResizeObserver
window.ResizeObserver = SafeResizeObserver

// 导出修复函数
export function fixResizeObserverError() {
  // 捕获并忽略 ResizeObserver 错误
  const originalError = console.error
  console.error = (...args) => {
    if (
      args.length > 0 &&
      typeof args[0] === 'string' &&
      (args[0].includes('ResizeObserver loop completed') ||
       args[0].includes('ResizeObserver loop limit exceeded'))
    ) {
      // 静默忽略这些错误
      return
    }
    originalError.apply(console, args)
  }

  // 全局错误事件处理
  window.addEventListener('error', (event) => {
    if (
      event.message &&
      (event.message.includes('ResizeObserver loop completed') ||
       event.message.includes('ResizeObserver loop limit exceeded'))
    ) {
      event.preventDefault()
      event.stopPropagation()
      return false
    }
  }, true)

  // Promise 错误处理
  window.addEventListener('unhandledrejection', (event) => {
    const reason = event.reason

    // ResizeObserver 相关错误
    if (
      reason &&
      reason.message &&
      (reason.message.includes('ResizeObserver loop completed') ||
       reason.message.includes('ResizeObserver loop limit exceeded'))
    ) {
      event.preventDefault()
      return false
    }

    // 时间验证相关错误（包含 endTime 字段的对象）
    if (reason && typeof reason === 'object' && reason.endTime) {
      console.warn('时间验证相关Promise错误已被静默处理')
      event.preventDefault()
      return false
    }

    // Element Plus 相关的循环错误
    if (
      reason &&
      reason.message &&
      (reason.message.includes('Maximum call stack') ||
       reason.message.includes('循环引用'))
    ) {
      event.preventDefault()
      return false
    }
  })
}

// 自动执行修复
fixResizeObserverError()

export default SafeResizeObserver
