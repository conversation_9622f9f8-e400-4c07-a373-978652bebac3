/**
 * Webpack Dev Server Overlay 错误拦截器
 * 专门处理webpack-dev-server的错误覆盖层显示问题
 */

// 拦截webpack-dev-server的错误处理
if (process.env.NODE_ENV === 'development') {
  
  // 1. 拦截webpack的错误收集
  const originalAddEventListener = EventTarget.prototype.addEventListener
  EventTarget.prototype.addEventListener = function(type, listener, options) {
    if (type === 'error' || type === 'unhandledrejection') {
      // 包装监听器，过滤特定错误
      const wrappedListener = function(event) {
        // 检查是否是时间验证错误
        if (event.type === 'unhandledrejection' && 
            event.reason && 
            typeof event.reason === 'object' && 
            event.reason.endTime) {
          console.warn('Webpack overlay: 时间验证错误已被拦截')
          event.preventDefault()
          event.stopImmediatePropagation()
          return false
        }
        
        // 检查是否是ResizeObserver错误
        if ((event.type === 'error' || event.type === 'unhandledrejection') &&
            event.reason && 
            event.reason.message &&
            (event.reason.message.includes('ResizeObserver') ||
             event.reason.message.includes('loop completed'))) {
          event.preventDefault()
          event.stopImmediatePropagation()
          return false
        }
        
        // 其他错误正常处理
        return listener.call(this, event)
      }
      
      return originalAddEventListener.call(this, type, wrappedListener, options)
    }
    
    return originalAddEventListener.call(this, type, listener, options)
  }
  
  // 2. 拦截webpack-dev-server的overlay模块
  const originalCreateOverlay = window.__webpack_dev_server_overlay_create_overlay__
  if (originalCreateOverlay) {
    window.__webpack_dev_server_overlay_create_overlay__ = function(options) {
      // 过滤错误信息
      if (options && options.errors) {
        options.errors = options.errors.filter(error => {
          // 过滤时间验证错误
          if (typeof error === 'object' && error.endTime) {
            console.warn('Webpack overlay: 过滤时间验证错误')
            return false
          }
          
          // 过滤ResizeObserver错误
          if (typeof error === 'string' && 
              (error.includes('ResizeObserver') || 
               error.includes('loop completed'))) {
            return false
          }
          
          return true
        })
      }
      
      return originalCreateOverlay.call(this, options)
    }
  }
  
  // 3. 拦截webpack的错误报告
  const originalWebpackHotUpdate = window.__webpack_require__?.hmr?.check
  if (originalWebpackHotUpdate) {
    window.__webpack_require__.hmr.check = function(...args) {
      try {
        return originalWebpackHotUpdate.apply(this, args)
      } catch (error) {
        // 过滤特定错误
        if (error && typeof error === 'object' && error.endTime) {
          console.warn('Webpack HMR: 时间验证错误已被拦截')
          return Promise.resolve()
        }
        throw error
      }
    }
  }
  
  // 4. 直接拦截webpack-dev-server的overlay显示
  const originalShowOverlay = window.__webpack_dev_server_overlay_show__
  if (originalShowOverlay) {
    window.__webpack_dev_server_overlay_show__ = function(options) {
      // 检查错误内容
      if (options && (options.errors || options.warnings)) {
        const hasTimeValidationError = (options.errors || []).some(error => 
          typeof error === 'object' && error.endTime
        )
        
        const hasResizeObserverError = (options.errors || []).some(error => 
          typeof error === 'string' && 
          (error.includes('ResizeObserver') || error.includes('loop completed'))
        )
        
        if (hasTimeValidationError || hasResizeObserverError) {
          console.warn('Webpack overlay: 阻止显示已过滤的错误')
          return
        }
      }
      
      return originalShowOverlay.call(this, options)
    }
  }
  
  console.log('🔧 Webpack Dev Server Overlay 错误拦截器已启用')
}

// 导出拦截器状态
export const webpackOverlayFixEnabled = process.env.NODE_ENV === 'development'

export default {
  enabled: webpackOverlayFixEnabled
}
