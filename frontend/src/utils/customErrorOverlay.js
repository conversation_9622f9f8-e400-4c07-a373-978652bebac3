/**
 * 自定义错误覆盖层
 * 替代webpack-dev-server的overlay，提供更精确的错误过滤
 */

class CustomErrorOverlay {
  constructor() {
    this.overlay = null
    this.errors = []
    this.isVisible = false
    
    // 初始化样式
    this.initStyles()
    
    // 监听错误
    this.setupErrorListeners()
  }
  
  initStyles() {
    const style = document.createElement('style')
    style.textContent = `
      .custom-error-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.85);
        color: #fff;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 14px;
        z-index: 9999;
        padding: 20px;
        box-sizing: border-box;
        overflow-y: auto;
      }
      
      .custom-error-header {
        background: #ff4757;
        color: white;
        padding: 15px 20px;
        margin: -20px -20px 20px -20px;
        font-weight: bold;
        font-size: 16px;
      }
      
      .custom-error-content {
        background: #2f3542;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        white-space: pre-wrap;
        word-break: break-word;
      }
      
      .custom-error-close {
        position: absolute;
        top: 20px;
        right: 30px;
        background: none;
        border: none;
        color: white;
        font-size: 24px;
        cursor: pointer;
        padding: 5px 10px;
      }
      
      .custom-error-close:hover {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
      }
    `
    document.head.appendChild(style)
  }
  
  setupErrorListeners() {
    // 监听未处理的Promise错误
    window.addEventListener('unhandledrejection', (event) => {
      const error = event.reason
      
      // 过滤特定错误
      if (this.shouldFilterError(error)) {
        event.preventDefault()
        return
      }
      
      // 显示其他错误
      this.addError(error)
      event.preventDefault()
    })
    
    // 监听JavaScript错误
    window.addEventListener('error', (event) => {
      const error = event.error || new Error(event.message)
      
      // 过滤特定错误
      if (this.shouldFilterError(error)) {
        event.preventDefault()
        return
      }
      
      // 显示其他错误
      this.addError(error)
      event.preventDefault()
    })
  }
  
  shouldFilterError(error) {
    // 过滤时间验证错误
    if (error && typeof error === 'object' && error.endTime) {
      console.warn('🔧 自定义overlay: 过滤时间验证错误')
      return true
    }
    
    // 过滤ResizeObserver错误
    if (error && error.message && 
        (error.message.includes('ResizeObserver') || 
         error.message.includes('loop completed'))) {
      console.warn('🔧 自定义overlay: 过滤ResizeObserver错误')
      return true
    }
    
    // 过滤webpack HMR相关错误
    if (error && error.message && 
        error.message.includes('Cannot read properties of undefined')) {
      return true
    }
    
    return false
  }
  
  addError(error) {
    this.errors.push(error)
    this.show()
  }
  
  show() {
    if (this.isVisible || this.errors.length === 0) return
    
    this.isVisible = true
    
    // 创建覆盖层
    this.overlay = document.createElement('div')
    this.overlay.className = 'custom-error-overlay'
    
    // 创建内容
    const header = document.createElement('div')
    header.className = 'custom-error-header'
    header.textContent = `发现 ${this.errors.length} 个错误`
    
    const closeBtn = document.createElement('button')
    closeBtn.className = 'custom-error-close'
    closeBtn.textContent = '×'
    closeBtn.onclick = () => this.hide()
    
    this.overlay.appendChild(header)
    this.overlay.appendChild(closeBtn)
    
    // 添加错误内容
    this.errors.forEach((error, index) => {
      const errorDiv = document.createElement('div')
      errorDiv.className = 'custom-error-content'
      
      let errorText = `错误 ${index + 1}:\n`
      if (error instanceof Error) {
        errorText += `${error.name}: ${error.message}\n`
        if (error.stack) {
          errorText += `\n堆栈跟踪:\n${error.stack}`
        }
      } else {
        errorText += JSON.stringify(error, null, 2)
      }
      
      errorDiv.textContent = errorText
      this.overlay.appendChild(errorDiv)
    })
    
    document.body.appendChild(this.overlay)
    
    // ESC键关闭
    const handleKeydown = (e) => {
      if (e.key === 'Escape') {
        this.hide()
        document.removeEventListener('keydown', handleKeydown)
      }
    }
    document.addEventListener('keydown', handleKeydown)
  }
  
  hide() {
    if (!this.isVisible) return
    
    this.isVisible = false
    if (this.overlay && this.overlay.parentNode) {
      this.overlay.parentNode.removeChild(this.overlay)
    }
    this.overlay = null
    this.errors = []
  }
  
  clear() {
    this.errors = []
    this.hide()
  }
}

// 只在开发环境启用
if (process.env.NODE_ENV === 'development') {
  const customOverlay = new CustomErrorOverlay()
  
  // 导出到全局，方便调试
  window.__customErrorOverlay = customOverlay
  
  console.log('🔧 自定义错误覆盖层已启用')
}

export default CustomErrorOverlay
