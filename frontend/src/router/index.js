import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/Register.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/Profile.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/reservation',
    name: 'Reservation',
    component: () => import('@/views/Reservation.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/execution-history',
    name: 'ExecutionHistory',
    component: () => import('@/views/ExecutionHistory.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/member-management',
    name: 'MemberManagement',
    component: () => import('@/views/MemberManagement.vue'),
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/room-management',
    name: 'RoomManagement',
    component: () => import('@/views/RoomManagement.vue'),
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/worker-management',
    name: 'WorkerManagement',
    component: () => import('@/views/WorkerManagement.vue'),
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/distributed-task-management',
    name: 'DistributedTaskManagement',
    component: () => import('@/views/DistributedTaskManagement.vue'),
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/redemption',
    name: 'Redemption',
    component: () => import('@/views/Redemption.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/redemption-management',
    name: 'RedemptionManagement',
    component: () => import('@/views/RedemptionManagement.vue'),
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/admin-execution-logs',
    name: 'AdminExecutionLogs',
    component: () => import('@/views/AdminExecutionLogs.vue'),
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/announcement-management',
    name: 'AnnouncementManagement',
    component: () => import('@/views/AnnouncementManagement.vue'),
    meta: { requiresAuth: true, requiresAdmin: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else if (to.meta.requiresGuest && authStore.isAuthenticated) {
    next('/dashboard')
  } else if (to.meta.requiresAdmin && authStore.user?.role !== 'ADMIN') {
    // 需要管理员权限但用户不是管理员
    next('/dashboard')
  } else {
    next()
  }
})

export default router 