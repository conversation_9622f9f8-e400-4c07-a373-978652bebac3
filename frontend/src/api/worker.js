import api from '@/utils/api'

/**
 * 副服务器管理API
 */
export const workerApi = {
  
  // ==================== 服务器列表管理 ====================
  
  /**
   * 获取所有副服务器列表
   */
  getAllServers() {
    return api.get('/admin/worker-management/servers')
  },

  /**
   * 根据ID获取副服务器详情
   */
  getServerById(id) {
    return api.get(`/admin/worker-management/servers/${id}`)
  },

  /**
   * 创建新的副服务器
   */
  createServer(data) {
    return api.post('/admin/worker-management/servers', data)
  },

  /**
   * 更新副服务器配置
   */
  updateServer(id, data) {
    return api.put(`/admin/worker-management/servers/${id}`, data)
  },

  /**
   * 删除副服务器
   */
  deleteServer(id) {
    return api.delete(`/admin/worker-management/servers/${id}`)
  },

  // ==================== 服务器操作控制 ====================



  /**
   * 健康检查
   */
  healthCheck(id) {
    return api.post(`/admin/worker-management/servers/${id}/health-check`)
  },

  // ==================== 批量操作 ====================

  /**
   * 批量操作副服务器
   * @param {Object} data - { ids: [1,2,3], operation: 'start|stop|restart|delete' }
   */
  batchOperation(data) {
    return api.post('/admin/worker-management/servers/batch-operation', data)
  },

  /**
   * 批量健康检查
   * @param {Array} ids - 服务器ID数组
   */
  batchHealthCheck(ids) {
    return api.post('/admin/worker-management/servers/batch-health-check', ids)
  },

  // ==================== 统计和监控 ====================

  /**
   * 获取副服务器统计信息
   */
  getStatistics() {
    return api.get('/admin/worker-management/statistics')
  },

  /**
   * 获取在线副服务器列表
   */
  getOnlineServers() {
    return api.get('/admin/worker-management/servers/online')
  },

  /**
   * 获取可用副服务器列表
   */
  getAvailableServers() {
    return api.get('/admin/worker-management/servers/available')
  },

  /**
   * 更新心跳超时的服务器状态
   */
  updateTimeoutServers(timeoutMinutes = 5) {
    return api.post(`/admin/worker-management/servers/update-timeout-status?timeoutMinutes=${timeoutMinutes}`)
  }
}

export default workerApi
