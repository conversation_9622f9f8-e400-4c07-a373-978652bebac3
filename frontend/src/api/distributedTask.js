import api from '@/utils/api'

/**
 * 分布式任务管理API
 */
const distributedTaskApi = {
  
  // ==================== 统计数据接口 ====================
  
  /**
   * 获取任务统计数据
   */
  getTaskStatistics() {
    return api.get('/admin/distributed-tasks/statistics')
  },
  
  /**
   * 获取副服务器状态概览
   */
  getWorkerStatusOverview() {
    return api.get('/admin/distributed-tasks/worker-status')
  },
  
  // ==================== 任务列表管理 ====================
  
  /**
   * 获取预约任务列表（分页）
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页大小
   * @param {string} params.status - 执行状态筛选
   * @param {string} params.keyword - 搜索关键词
   */
  getReservationTasks(params = {}) {
    return api.get('/admin/distributed-tasks/tasks', { params })
  },
  
  /**
   * 获取任务详情
   * @param {number} taskId - 任务ID
   */
  getTaskDetail(taskId) {
    return api.get(`/admin/distributed-tasks/tasks/${taskId}`)
  },
  
  // ==================== 任务操作接口 ====================
  
  /**
   * 手动分配任务到指定副服务器
   * @param {number} taskId - 任务ID
   * @param {string} workerId - 副服务器ID
   */
  assignTaskToWorker(taskId, workerId) {
    return api.post(`/admin/distributed-tasks/tasks/${taskId}/assign`, null, {
      params: { workerId }
    })
  },
  
  /**
   * 重新执行失败的任务
   * @param {number} taskId - 任务ID
   */
  retryFailedTask(taskId) {
    return api.post(`/admin/distributed-tasks/tasks/${taskId}/retry`)
  },
  
  /**
   * 取消任务执行
   * @param {number} taskId - 任务ID
   */
  cancelTask(taskId) {
    return api.post(`/admin/distributed-tasks/tasks/${taskId}/cancel`)
  },
  
  /**
   * 批量分配任务
   * @param {Array<number>} taskIds - 任务ID列表
   * @param {string} workerId - 副服务器ID
   */
  batchAssignTasks(taskIds, workerId) {
    return api.post('/admin/distributed-tasks/tasks/batch-assign', null, {
      params: { taskIds, workerId }
    })
  },
  
  // ==================== 自动化操作接口 ====================
  
  /**
   * 自动分配待执行任务
   */
  autoAssignPendingTasks() {
    return api.post('/admin/distributed-tasks/auto-assign')
  },
  
  /**
   * 获取可用副服务器列表
   */
  getAvailableWorkers() {
    return api.get('/admin/distributed-tasks/available-workers')
  },
  
  // ==================== 日志和维护接口 ====================
  
  /**
   * 获取任务执行日志
   * @param {number} taskId - 任务ID
   */
  getTaskExecutionLogs(taskId) {
    return api.get(`/admin/distributed-tasks/tasks/${taskId}/logs`)
  },
  
  /**
   * 清理过期任务记录
   * @param {number} daysToKeep - 保留天数
   */
  cleanupExpiredTasks(daysToKeep = 30) {
    return api.post('/admin/distributed-tasks/cleanup', null, {
      params: { daysToKeep }
    })
  }
}

export default distributedTaskApi
