import api from '@/utils/api'

/**
 * 公告管理API
 */
export const announcementApi = {
  
  // ==================== 管理员公告管理 ====================
  
  /**
   * 创建公告
   */
  createAnnouncement(data) {
    return api.post('/admin/announcements', data)
  },

  /**
   * 更新公告
   */
  updateAnnouncement(id, data) {
    return api.put(`/admin/announcements/${id}`, data)
  },

  /**
   * 删除公告
   */
  deleteAnnouncement(id) {
    return api.delete(`/admin/announcements/${id}`)
  },

  /**
   * 获取公告详情
   */
  getAnnouncementById(id) {
    return api.get(`/admin/announcements/${id}`)
  },

  /**
   * 分页查询公告
   */
  getAnnouncementPage(params) {
    return api.get('/admin/announcements', { params })
  },

  /**
   * 切换公告启用状态
   */
  toggleAnnouncementStatus(id, enabled) {
    return api.put(`/admin/announcements/${id}/toggle-status`, { enabled })
  },

  /**
   * 批量删除公告
   */
  batchDeleteAnnouncements(ids) {
    return api.delete('/admin/announcements/batch', { data: ids })
  },

  // ==================== 用户公告查看 ====================

  /**
   * 获取活跃的公告列表
   */
  getActiveAnnouncements() {
    return api.get('/user/announcements')
  },

  /**
   * 获取未读的弹窗公告
   */
  getUnreadPopupAnnouncements() {
    return api.get('/user/announcements/unread-popup')
  },

  /**
   * 获取公告详情（用户视角）
   */
  getUserAnnouncementById(id) {
    return api.get(`/user/announcements/${id}`)
  },

  /**
   * 标记公告为已读
   */
  markAnnouncementAsRead(id) {
    return api.post(`/user/announcements/${id}/mark-read`)
  },

  /**
   * 关闭公告弹窗
   */
  dismissAnnouncementPopup(id) {
    return api.post(`/user/announcements/${id}/dismiss`)
  },

  /**
   * 批量标记公告为已读
   */
  batchMarkAnnouncementsAsRead(announcementIds) {
    return api.post('/user/announcements/batch-mark-read', { announcementIds })
  }
}

export default announcementApi
