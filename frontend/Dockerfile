# SeatMaster Frontend Dockerfile
# 多阶段构建，优化镜像大小

# ================================
# 构建阶段
# ================================
FROM node:16-alpine AS builder

# 设置工作目录
WORKDIR /app

# 设置npm镜像源（加速构建）
RUN npm config set registry https://registry.npmmirror.com

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production --silent

# 复制源代码
COPY . .

# 构建生产版本
RUN npm run build

# ================================
# 运行阶段 - Nginx
# ================================
FROM nginx:1.21-alpine

# 安装必要工具
RUN apk add --no-cache curl

# 删除默认nginx配置
RUN rm -rf /usr/share/nginx/html/*

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf
COPY default.conf /etc/nginx/conf.d/default.conf

# 创建nginx用户目录
RUN mkdir -p /var/cache/nginx/client_temp \
    && mkdir -p /var/cache/nginx/proxy_temp \
    && mkdir -p /var/cache/nginx/fastcgi_temp \
    && mkdir -p /var/cache/nginx/uwsgi_temp \
    && mkdir -p /var/cache/nginx/scgi_temp \
    && chown -R nginx:nginx /var/cache/nginx

# 创建日志目录
RUN mkdir -p /var/log/nginx \
    && chown -R nginx:nginx /var/log/nginx

# 设置权限
RUN chown -R nginx:nginx /usr/share/nginx/html \
    && chmod -R 755 /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
