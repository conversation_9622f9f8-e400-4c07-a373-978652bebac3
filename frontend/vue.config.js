const { defineConfig } = require('@vue/cli-service')
const path = require('path')

const isProduction = process.env.NODE_ENV === 'production'

module.exports = defineConfig({
  transpileDependencies: true,
  lintOnSave: false, // 临时禁用ESLint检查

  // 生产环境配置
  publicPath: isProduction ? '/' : '/',
  outputDir: 'dist',
  assetsDir: 'static',
  indexPath: 'index.html',
  filenameHashing: true,

  // 生产环境关闭source map
  productionSourceMap: !isProduction,

  // 🔧 配置webpack
  configureWebpack: {
    // 过滤特定错误和警告
    stats: {
      warningsFilter: [
        /ResizeObserver/,
        /loop completed/,
        /endTime/
      ]
    },

    // 生产环境优化
    ...(isProduction && {
      // 代码分割优化
      optimization: {
        splitChunks: {
          chunks: 'all',
          minSize: 20000,
          maxSize: 500000,
          minChunks: 1,
          maxAsyncRequests: 30,
          maxInitialRequests: 30,
          cacheGroups: {
            // 第三方库
            vendor: {
              name: 'chunk-vendors',
              test: /[\\/]node_modules[\\/]/,
              priority: 10,
              chunks: 'initial',
              enforce: true
            },
            // Element Plus单独打包
            elementPlus: {
              name: 'chunk-element-plus',
              test: /[\\/]node_modules[\\/]element-plus[\\/]/,
              priority: 20,
              chunks: 'all',
              enforce: true
            },
            // Vue相关库
            vue: {
              name: 'chunk-vue',
              test: /[\\/]node_modules[\\/](vue|vue-router|pinia)[\\/]/,
              priority: 15,
              chunks: 'all',
              enforce: true
            },
            // 工具库
            utils: {
              name: 'chunk-utils',
              test: /[\\/]node_modules[\\/](axios|lodash|dayjs)[\\/]/,
              priority: 12,
              chunks: 'all',
              enforce: true
            },
            // 公共代码
            common: {
              name: 'chunk-common',
              minChunks: 2,
              priority: 5,
              chunks: 'initial',
              reuseExistingChunk: true
            },
            // 异步组件
            async: {
              name: 'chunk-async',
              chunks: 'async',
              minChunks: 2,
              priority: 1
            }
          }
        },
        // 运行时代码单独打包
        runtimeChunk: {
          name: 'runtime'
        },
        // 压缩配置
        minimize: true,
        minimizer: [
          // JS压缩
          new (require('terser-webpack-plugin'))({
            terserOptions: {
              compress: {
                drop_console: true,
                drop_debugger: true,
                pure_funcs: ['console.log']
              },
              format: {
                comments: false
              }
            },
            extractComments: false
          }),
          // CSS压缩
          new (require('css-minimizer-webpack-plugin'))({
            minimizerOptions: {
              preset: [
                'default',
                {
                  discardComments: { removeAll: true }
                }
              ]
            }
          })
        ]
      },

      // 性能提示
      performance: {
        hints: 'warning',
        maxAssetSize: 500000,
        maxEntrypointSize: 500000
      },

      // 解析优化
      resolve: {
        alias: {
          '@': path.resolve(__dirname, 'src')
        },
        extensions: ['.js', '.vue', '.json'],
        modules: ['node_modules']
      },

      // 模块优化
      module: {
        rules: [
          // 图片优化
          {
            test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
            type: 'asset',
            parser: {
              dataUrlCondition: {
                maxSize: 8 * 1024 // 8KB以下转base64
              }
            },
            generator: {
              filename: 'img/[name].[hash:8][ext]'
            }
          }
        ]
      }
    })
  },

  // CSS配置
  css: {
    extract: isProduction ? {
      filename: 'css/[name].[contenthash:8].css',
      chunkFilename: 'css/[name].[contenthash:8].css'
    } : false,
    sourceMap: !isProduction,
    loaderOptions: {
      scss: {
        additionalData: `@import "@/styles/variables.scss";`
      },
      postcss: {
        postcssOptions: {
          plugins: [
            require('autoprefixer'),
            ...(isProduction ? [
              require('cssnano')({
                preset: ['default', {
                  discardComments: { removeAll: true },
                  normalizeWhitespace: true,
                  colormin: true,
                  convertValues: true,
                  discardDuplicates: true,
                  discardEmpty: true,
                  mergeRules: true,
                  minifyFontValues: true,
                  minifyGradients: true,
                  minifyParams: true,
                  minifySelectors: true,
                  reduceIdents: false,
                  reduceInitial: true,
                  reduceTransforms: true,
                  svgo: true,
                  uniqueSelectors: true
                }]
              })
            ] : [])
          ]
        }
      }
    }
  },

  devServer: {
    host: '0.0.0.0', // 允许外部访问
    port: 3000,
    // 🔧 临时禁用错误overlay（避免时间验证错误干扰）
    client: {
      overlay: false
    },
    proxy: {
      '/api': {
        target: process.env.VUE_APP_API_BASE_URL || 'http://localhost:8081',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api'
        }
      }
    }
  },

  // PWA配置
  pwa: {
    name: '座位预约系统',
    themeColor: '#409EFF',
    msTileColor: '#000000',
    appleMobileWebAppCapable: 'yes',
    appleMobileWebAppStatusBarStyle: 'black',
    workboxPluginMode: 'InjectManifest',
    workboxOptions: {
      swSrc: 'src/sw.js'
    }
  }
})