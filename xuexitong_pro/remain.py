import json
import time
import argparse
import os
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
import threading

from utils import reserve, get_user_credentials
get_current_time = lambda action: time.strftime("%H:%M:%S", time.localtime(time.time() + 8*3600)) if action else time.strftime("%H:%M:%S", time.localtime(time.time()))
get_current_dayofweek = lambda action: time.strftime("%A", time.localtime(time.time() + 8*3600)) if action else time.strftime("%A", time.localtime(time.time()))


SLEEPTIME = 0.2 # 每次抢座的间隔
ENDTIME = "07:01:00" # 根据学校的预约座位时间+1min即可

ENABLE_SLIDER = True # 是否有滑块验证
MAX_ATTEMPT = 3 # 最大尝试次数
RESERVE_NEXT_DAY = True # 预约明天而不是今天的

                

def reserve_single_user(user, index, usernames, passwords, action, success_list, results_lock, reserve_results):
    comment = user["_comment"]
    username = user["username"]
    password = user["password"]
    times = user["time"]
    roomid = user["roomid"]
    seatid = user["seatid"]
    daysofweek = user["daysofweek"]
    target_time = user.get('target_time', None)
    
    if action:
        username, password = usernames.split(',')[index], passwords.split(',')[index]
    
    current_dayofweek = get_current_dayofweek(action)
    if current_dayofweek not in daysofweek:
        logging.info(f"{comment} 今天未设置预约")
        return
        
    if not success_list[index]:
        logging.info(f"----------- {comment} -- {times} -- {seatid} 尝试预约 -----------")
        s = reserve(sleep_time=SLEEPTIME, max_attempt=MAX_ATTEMPT, enable_slider=ENABLE_SLIDER, reserve_next_day=RESERVE_NEXT_DAY)
        s.get_login_status()
        s.login(username, password)
        s.requests.headers.update({'Host': 'office.chaoxing.com'})
        suc = s.submit(times, roomid, seatid, action, submit_time=target_time)
        
        with results_lock:
            success_list[index] = suc
            result_message = f"{comment}|房间{roomid}|座位号{seatid}|{'预约成功' if suc else '预约失败'}"
            reserve_results.append(result_message)
        
        if suc:
            logging.info(f"{comment} 预约成功 - 时间: {times}, 房间: {roomid}, 座位: {seatid}")
        else:
            logging.warning(f"{comment} 预约失败")

def login_and_reserve(users, usernames, passwords, action, success_list=None):
    # 保存预约结果的列表
    reserve_results = []
    results_lock = threading.Lock()

    logging.info(f"Global settings: \nSLEEPTIME: {SLEEPTIME}\nENDTIME: {ENDTIME}\nENABLE_SLIDER: {ENABLE_SLIDER}\nRESERVE_NEXT_DAY: {RESERVE_NEXT_DAY}")
    if action and len(usernames.split(",")) != len(users):
        raise Exception("user number should match the number of config")
    if success_list is None:
        success_list = [False] * len(users)
    
    # 创建线程列表
    threads = []
    for index, user in enumerate(users):
        thread = threading.Thread(
            target=reserve_single_user,
            args=(user, index, usernames, passwords, action, success_list, results_lock, reserve_results)
        )
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    # 所有预约操作完成后，统一推送消息
    send_reserve_results(reserve_results)
    return success_list



def main(users, action=False):
    current_time = get_current_time(action)
    logging.info(f"开始时间 {current_time}, action {'开启' if action else '关闭'}")
    attempt_times = 0
    usernames, passwords = None, None
    if action:
        usernames, passwords = get_user_credentials(action)
    success_list = [False] * len(users)
    current_dayofweek = get_current_dayofweek(action)
    today_reservation_num = sum(1 for d in users if current_dayofweek in d.get('daysofweek'))
    
    while current_time < ENDTIME and sum(success_list) < today_reservation_num:
        attempt_times += 1
        try:
            success_list = login_and_reserve(users, usernames, passwords, action, success_list)
        except Exception as e:
            logging.error(f"发生错误: {e}")
        logging.info(f"尝试次数 {attempt_times}, 当前时间 {current_time}, 成功列表 {success_list}")
        current_time = get_current_time(action)
    
    logging.info("预约结束。以下是所有预约成功的用户信息：")
    successful_reservations = []
    for index, (user, success) in enumerate(zip(users, success_list)):
        if success:
            comment = user["_comment"]
            times = user["time"]
            roomid = user["roomid"]
            seatid = user["seatid"]
            successful_reservations.append(f"{comment} - 时间: {times}, 房间: {roomid}, 座位: {seatid}")
    
    if successful_reservations:
        for reservation in successful_reservations:
            logging.info(reservation)
    else:
        logging.info("没有用户成功预约座位。")
    
    logging.info(f"预约总结: 成功预约 {sum(success_list)} 个座位，共 {today_reservation_num} 个座位。")


def debug_single_user(user, index, usernames, passwords, action, success_list, results_lock, reserve_results):
    comment = user["_comment"]
    username = user["username"]
    password = user["password"]
    times = user["time"]
    roomid = user["roomid"]
    seatid = user["seatid"]
    daysofweek = user["daysofweek"]
    target_time = user.get('target_time', None)
    
    if action:
        username, password = usernames.split(',')[index], passwords.split(',')[index]
    
    current_dayofweek = get_current_dayofweek(action)
    if current_dayofweek not in daysofweek:
        logging.info(f"{comment} 今天未设置预约")
        return
    
    if type(seatid) == str:
        seatid = [seatid]
    
    logging.info(f"----------- {comment} -- {times} -- {seatid} 尝试预约 -----------")
    try:
        s = reserve(sleep_time=SLEEPTIME, max_attempt=MAX_ATTEMPT, enable_slider=ENABLE_SLIDER, reserve_next_day=RESERVE_NEXT_DAY)
        s.get_login_status()
        s.login(username, password)
        s.requests.headers.update({'Host': 'office.chaoxing.com'})
        suc = s.submit(times, roomid, seatid, action, submit_time=target_time)
        
        with results_lock:
            success_list[index] = suc
            result_message = f"{comment}|房间{roomid}|座位号{seatid}|{'预约成功' if suc else '预约失败'}"
            reserve_results.append(result_message)
        
        if suc:
            logging.info(f"{comment} 预约成功 - 时间: {times}, 房间: {roomid}, 座位: {seatid}")
        else:
            logging.warning(f"{comment} 预约失败")
    except Exception as e:
        logging.error(f"{comment} 预约时发生错误: {e}")

def debug(users, action=False):
    logging.info(f"Global settings: \nSLEEPTIME: {SLEEPTIME}\nENDTIME: {ENDTIME}\nENABLE_SLIDER: {ENABLE_SLIDER}\nRESERVE_NEXT_DAY: {RESERVE_NEXT_DAY}")
    logging.info(f"Debug Mode start! , action {'on' if action else 'off'}")
    
    # 获取当前线程信息
    main_thread = threading.current_thread()
    logging.info("主线程名称: %s", main_thread.name)
    logging.info("主线程ID: %s", main_thread.ident)
    
    # 初始化线程安全的数据结构
    success_list = [False] * len(users)
    reserve_results = []
    results_lock = threading.Lock()
    
    if action:
        usernames, passwords = get_user_credentials(action)
    else:
        usernames, passwords = None, None
    
    # 创建并启动线程
    threads = []
    for index, user in enumerate(users):
        thread = threading.Thread(
            target=debug_single_user,
            args=(user, index, usernames, passwords, action, success_list, results_lock, reserve_results)
        )
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    # 输出预约结果
    logging.info("\n预约结果汇总:")
    for result in reserve_results:
        logging.info(result)
    
    logging.info("Debug Mode 结束")


def get_sign(users, action=False):
    logging.info(f"Sign Mode start! , action {'on' if action else 'off'}")
    usernames, passwords = None, None
    if action:
        usernames, passwords = get_user_credentials(action)
    
    for index, user in enumerate(users):
        comment = user["_comment"]
        username = user["username"]
        password = user["password"]
        times = user["time"]
        roomid = user["roomid"]
        seatid = user["seatid"]
        daysofweek = user["daysofweek"]
        # 检查是否有target_time参数
        target_time = user.get('target_time', None) 
            
        logging.info(f"----------- Checking reservations for {comment} {username} -----------")
        s = reserve(sleep_time=SLEEPTIME, max_attempt=MAX_ATTEMPT, enable_slider=ENABLE_SLIDER, reserve_next_day=RESERVE_NEXT_DAY)
        s.get_login_status()
        login_success, msg = s.login(username=username, password=password)
        
        if not login_success:
            logging.error(f"Login failed for user {username}: {msg}")
            continue
            
        s.requests.headers.update({'Host': 'office.chaoxing.com'})
        result = s.sign()
        
        if not result:
            logging.info(f"No reservations found for user {username}")
            continue
            
        for reservation in result:
            print(f"预约日期: {reservation['today']}")
            print(f"座位号: {reservation['seatNum']}")
            print(f"开始时间: {reservation['startTime']}")
            print(f"结束时间: {reservation['endTime']}")
            print(f"签到状态: {reservation['sign_status']}")
            print("------------------------")

def get_roomid(args1, args2):
    username = input("请输入用户名：")
    password = input("请输入密码：")
    s = reserve(sleep_time=SLEEPTIME, max_attempt=MAX_ATTEMPT, enable_slider=ENABLE_SLIDER, reserve_next_day=RESERVE_NEXT_DAY)
    s.get_login_status()
    s.login(username=username, password=password)
    s.requests.headers.update({'Host': 'office.chaoxing.com'})
    encode = input("请输入deptldEnc：")
    s.roomid(encode)


if __name__ == "__main__":
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    parser = argparse.ArgumentParser(prog='Chao Xing seat auto reserve')
    parser.add_argument('-u','--user', default=config_path, help='user config file')
    parser.add_argument('-m','--method', default="reserve" ,choices=["reserve", "debug", "room", "sign"], help='for debug')
    parser.add_argument('-a','--action', action="store_true",help='use --action to enable in github action')
    args = parser.parse_args()
    func_dict = {"reserve": main, "debug":debug, "room": get_roomid, "sign": get_sign}
    with open(args.user, "r+", encoding='utf-8') as data:
        usersdata = json.load(data)["reserve"]
    func_dict[args.method](usersdata, args.action)
