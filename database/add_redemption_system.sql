-- 兑换码系统数据库表创建脚本
-- 创建时间: 2025-07-15
-- 描述: 为座位预约系统添加兑换码功能

USE seat_reservation;

-- 1. 创建兑换码表
CREATE TABLE IF NOT EXISTS redemption_codes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    code VARCHAR(20) NOT NULL UNIQUE COMMENT '兑换码（唯一）',
    days_to_add INT NOT NULL COMMENT '增加的天数',
    is_used BOOLEAN DEFAULT FALSE COMMENT '是否已使用',
    used_by_user_id BIGINT NULL COMMENT '使用者用户ID',
    used_time DATETIME NULL COMMENT '使用时间',
    created_by_admin_id BIGINT NOT NULL COMMENT '创建者管理员ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    expire_time DATETIME NULL COMMENT '过期时间（NULL表示永不过期）',
    description VARCHAR(255) NULL COMMENT '兑换码描述',
    batch_id VARCHAR(50) NULL COMMENT '批次ID（用于批量生成）',
    
    -- 外键约束
    FOREIGN KEY (used_by_user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by_admin_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_code (code),
    INDEX idx_used_status (is_used),
    INDEX idx_expire_time (expire_time),
    INDEX idx_batch_id (batch_id),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='兑换码表';

-- 2. 创建兑换记录表（用于审计和历史记录）
CREATE TABLE IF NOT EXISTS redemption_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    code VARCHAR(20) NOT NULL COMMENT '兑换码',
    days_added INT NOT NULL COMMENT '增加的天数',
    days_before INT NOT NULL COMMENT '兑换前剩余天数',
    days_after INT NOT NULL COMMENT '兑换后剩余天数',
    redemption_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '兑换时间',
    user_ip VARCHAR(45) NULL COMMENT '用户IP地址',
    user_agent TEXT NULL COMMENT '用户代理信息',
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_redemption_time (redemption_time),
    INDEX idx_code (code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='兑换记录表';

-- 3. 插入一些测试数据
-- 为管理员用户创建一些测试兑换码
INSERT INTO redemption_codes (code, days_to_add, created_by_admin_id, description, expire_time) VALUES
('TEST7DAYS001', 7, 14, '测试兑换码-7天', DATE_ADD(NOW(), INTERVAL 30 DAY)),
('TEST30DAYS001', 30, 14, '测试兑换码-30天', DATE_ADD(NOW(), INTERVAL 30 DAY)),
('WELCOME2025', 15, 14, '新用户欢迎兑换码', DATE_ADD(NOW(), INTERVAL 60 DAY));

-- 4. 验证表创建
SELECT 'redemption_codes表创建完成' as message;
SELECT COUNT(*) as redemption_codes_count FROM redemption_codes;

SELECT 'redemption_logs表创建完成' as message;
SELECT COUNT(*) as redemption_logs_count FROM redemption_logs;

-- 5. 显示表结构
DESCRIBE redemption_codes;
DESCRIBE redemption_logs;
