-- 诊断wait_time约束问题的SQL脚本
-- 用于检查当前数据库状态和约束配置

-- ================================
-- 1. 检查当前表结构
-- ================================

USE seat_reservation;

-- 查看schools表的详细结构
DESCRIBE schools;

-- 查看schools表的创建语句（包含约束）
SHOW CREATE TABLE schools;

-- ================================
-- 2. 检查CHECK约束状态
-- ================================

-- 查看当前的CHECK约束
SELECT 
    CONSTRAINT_SCHEMA,
    CONSTRAINT_NAME,
    CHECK_CLAUSE,
    TABLE_NAME
FROM INFORMATION_SCHEMA.CHECK_CONSTRAINTS 
WHERE CONSTRAINT_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'schools'
  AND CONSTRAINT_NAME = 'chk_wait_time';

-- 查看所有约束
SELECT 
    CONSTRAINT_SCHEMA,
    CONSTRAINT_NAME,
    CHECK_CLAUSE,
    TABLE_NAME
FROM INFORMATION_SCHEMA.CHECK_CONSTRAINTS 
WHERE CONSTRAINT_SCHEMA = 'seat_reservation';

-- ================================
-- 3. 检查字段类型和精度
-- ================================

SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    NUMERIC_PRECISION,
    NUMERIC_SCALE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'schools' 
  AND COLUMN_NAME = 'wait_time';

-- ================================
-- 4. 测试当前约束范围
-- ================================

-- 查看当前数据
SELECT id, name, wait_time FROM schools;

-- 测试边界值（不会实际插入，只是测试约束）
-- 注意：这些语句可能会失败，这正是我们要检查的

-- 测试最小值 0.10（应该成功）
SELECT '测试 0.10' as test_case;
-- INSERT INTO schools (name, wait_time) VALUES ('测试_最小值', 0.10);

-- 测试新的最大值 10.00（应该成功，但可能失败）
SELECT '测试 10.00' as test_case;
-- INSERT INTO schools (name, wait_time) VALUES ('测试_最大值', 10.00);

-- 测试超出范围的值 10.01（应该失败）
SELECT '测试 10.01' as test_case;
-- INSERT INTO schools (name, wait_time) VALUES ('测试_超出范围', 10.01);

-- ================================
-- 5. 检查MySQL版本和约束支持
-- ================================

SELECT VERSION() as mysql_version;

-- 检查约束相关的系统变量
SHOW VARIABLES LIKE '%check%';

-- ================================
-- 6. 显示诊断结果
-- ================================

SELECT '=== 诊断完成 ===' as status;
SELECT '请检查上述结果，特别是CHECK_CLAUSE的内容' as instruction;
SELECT '如果约束仍然是旧的范围，需要手动修复' as note;
