-- 移除预约拆分相关字段和索引
-- 执行日期: 2025-07-18
-- 说明: 完全移除座位预约系统中的拆分功能，简化数据库结构

USE seat_reservation;

-- 显示清理前的信息
SELECT 'Starting split fields cleanup...' as message;

-- 检查当前 reservations 表结构
SELECT 'Current reservations table structure:' as info;
DESCRIBE reservations;

-- 检查当前 task_execution_logs 表结构
SELECT 'Current task_execution_logs table structure:' as info;
DESCRIBE task_execution_logs;

-- ==================== 清理 reservations 表 ====================

-- 删除拆分相关索引
SELECT 'Dropping split-related indexes from reservations table...' as message;

DROP INDEX IF EXISTS idx_reservations_split_group ON reservations;
DROP INDEX IF EXISTS idx_reservations_split_info ON reservations;

-- 删除拆分相关字段
SELECT 'Dropping split-related columns from reservations table...' as message;

ALTER TABLE reservations DROP COLUMN IF EXISTS is_split_reservation;
ALTER TABLE reservations DROP COLUMN IF EXISTS split_group_id;
ALTER TABLE reservations DROP COLUMN IF EXISTS split_sequence;
ALTER TABLE reservations DROP COLUMN IF EXISTS split_total_count;

-- ==================== 清理 task_execution_logs 表 ====================

-- 删除拆分相关索引
SELECT 'Dropping split-related indexes from task_execution_logs table...' as message;

DROP INDEX IF EXISTS idx_split_group ON task_execution_logs;
DROP INDEX IF EXISTS idx_split_info ON task_execution_logs;

-- 删除拆分相关字段
SELECT 'Dropping split-related columns from task_execution_logs table...' as message;

ALTER TABLE task_execution_logs DROP COLUMN IF EXISTS is_split_reservation;
ALTER TABLE task_execution_logs DROP COLUMN IF EXISTS split_group_id;
ALTER TABLE task_execution_logs DROP COLUMN IF EXISTS split_sequence;
ALTER TABLE task_execution_logs DROP COLUMN IF EXISTS split_total_count;
ALTER TABLE task_execution_logs DROP COLUMN IF EXISTS split_time_range;
ALTER TABLE task_execution_logs DROP COLUMN IF EXISTS original_duration_hours;

-- ==================== 验证清理结果 ====================

-- 验证 reservations 表字段已删除
SELECT 'Verifying reservations table cleanup...' as message;
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'reservations'
  AND COLUMN_NAME IN ('is_split_reservation', 'split_group_id', 'split_sequence', 'split_total_count')
ORDER BY ORDINAL_POSITION;

-- 验证 task_execution_logs 表字段已删除
SELECT 'Verifying task_execution_logs table cleanup...' as message;
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'task_execution_logs'
  AND COLUMN_NAME IN ('is_split_reservation', 'split_group_id', 'split_sequence', 'split_total_count', 'split_time_range', 'original_duration_hours')
ORDER BY ORDINAL_POSITION;

-- 显示清理后的表结构
SELECT 'Final reservations table structure:' as info;
DESCRIBE reservations;

SELECT 'Final task_execution_logs table structure:' as info;
DESCRIBE task_execution_logs;

-- 统计现有数据
SELECT COUNT(*) as total_reservations FROM reservations;
SELECT COUNT(*) as total_execution_logs FROM task_execution_logs;

COMMIT;

-- 显示完成信息
SELECT 'Split fields cleanup completed successfully!' as message;
SELECT 'All split-related fields and indexes have been removed from the database.' as info;
SELECT 'The seat reservation system has been simplified to use single reservation records only.' as info;
