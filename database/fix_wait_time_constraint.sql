-- 修复wait_time约束问题的SQL脚本
-- 强制更新约束到正确的范围 (0.10 - 10.00)

-- ================================
-- 1. 备份当前数据
-- ================================

USE seat_reservation;

-- 显示当前数据（用于备份参考）
SELECT '=== 当前schools表数据 ===' as info;
SELECT id, name, wait_time, created_time FROM schools;

-- ================================
-- 2. 检查并修复约束
-- ================================

-- 方法1：尝试删除现有约束（如果存在）
SET @sql = (SELECT IF(
    EXISTS(
        SELECT 1 FROM INFORMATION_SCHEMA.CHECK_CONSTRAINTS 
        WHERE CONSTRAINT_SCHEMA = 'seat_reservation' 
        AND TABLE_NAME = 'schools' 
        AND CONSTRAINT_NAME = 'chk_wait_time'
    ),
    'ALTER TABLE schools DROP CONSTRAINT chk_wait_time',
    'SELECT "约束不存在，跳过删除" as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 等待一秒确保操作完成
SELECT SLEEP(1);

-- ================================
-- 3. 检查并修复字段类型
-- ================================

-- 检查当前字段类型
SELECT 
    DATA_TYPE,
    NUMERIC_PRECISION,
    NUMERIC_SCALE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'schools' 
  AND COLUMN_NAME = 'wait_time';

-- 修改字段类型以支持10.00（如果需要）
ALTER TABLE schools MODIFY COLUMN wait_time DECIMAL(4, 2) NOT NULL DEFAULT 0.50 COMMENT '学校预约操作等待时间（秒）';

-- ================================
-- 4. 创建新的正确约束
-- ================================

-- 添加新的CHECK约束，支持0.10到10.00的范围
ALTER TABLE schools ADD CONSTRAINT chk_wait_time CHECK (wait_time >= 0.10 AND wait_time <= 10.00);

-- ================================
-- 5. 验证修复结果
-- ================================

-- 检查新约束
SELECT 
    CONSTRAINT_NAME,
    CHECK_CLAUSE,
    TABLE_NAME
FROM INFORMATION_SCHEMA.CHECK_CONSTRAINTS 
WHERE CONSTRAINT_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'schools'
  AND CONSTRAINT_NAME = 'chk_wait_time';

-- 检查字段类型
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    NUMERIC_PRECISION,
    NUMERIC_SCALE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'schools' 
  AND COLUMN_NAME = 'wait_time';

-- ================================
-- 6. 测试新约束
-- ================================

-- 测试边界值
SELECT '=== 测试新约束 ===' as test_phase;

-- 创建临时测试数据
INSERT INTO schools (name, wait_time) VALUES ('测试_最小值_0.10', 0.10);
INSERT INTO schools (name, wait_time) VALUES ('测试_中间值_5.50', 5.50);
INSERT INTO schools (name, wait_time) VALUES ('测试_最大值_10.00', 10.00);

-- 验证插入成功
SELECT id, name, wait_time FROM schools WHERE name LIKE '测试_%';

-- 测试更新操作
UPDATE schools SET wait_time = 8.75 WHERE name = '测试_中间值_5.50';

-- 验证更新成功
SELECT id, name, wait_time FROM schools WHERE name = '测试_中间值_5.50';

-- ================================
-- 7. 清理测试数据
-- ================================

-- 删除测试数据
DELETE FROM schools WHERE name LIKE '测试_%';

-- 确认清理完成
SELECT COUNT(*) as remaining_test_records FROM schools WHERE name LIKE '测试_%';

-- ================================
-- 8. 最终验证
-- ================================

-- 显示表结构
SHOW CREATE TABLE schools;

-- 显示修复完成信息
SELECT '=== 修复完成 ===' as status;
SELECT 'wait_time字段现在支持0.10到10.00秒的范围' as result;
SELECT '请测试您的应用程序更新操作' as next_step;

-- ================================
-- 9. 错误测试（可选）
-- ================================

-- 以下语句应该失败（用于验证约束工作正常）
-- 取消注释来测试：

-- 测试小于最小值（应该失败）
-- INSERT INTO schools (name, wait_time) VALUES ('测试_失败_小值', 0.09);

-- 测试大于最大值（应该失败）  
-- INSERT INTO schools (name, wait_time) VALUES ('测试_失败_大值', 10.01);

SELECT '如需测试约束限制，请取消注释上述INSERT语句' as test_note;
