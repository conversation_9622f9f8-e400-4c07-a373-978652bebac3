-- SeatMaster 生产环境数据库配置脚本
-- 执行前请确保已备份现有数据

-- ================================
-- 1. 创建生产环境数据库用户
-- ================================

-- 创建应用专用用户（替换默认的root用户）
CREATE USER IF NOT EXISTS 'seatmaster_app'@'localhost' IDENTIFIED BY 'your_secure_password_here';
CREATE USER IF NOT EXISTS 'seatmaster_app'@'%' IDENTIFIED BY 'your_secure_password_here';

-- 授予必要权限
GRANT SELECT, INSERT, UPDATE, DELETE ON seat_reservation.* TO 'seatmaster_app'@'localhost';
GRANT SELECT, INSERT, UPDATE, DELETE ON seat_reservation.* TO 'seatmaster_app'@'%';

-- 创建只读用户（用于监控和报表）
CREATE USER IF NOT EXISTS 'seatmaster_readonly'@'localhost' IDENTIFIED BY 'readonly_password_here';
GRANT SELECT ON seat_reservation.* TO 'seatmaster_readonly'@'localhost';

-- ================================
-- 2. 数据库性能优化配置
-- ================================

-- 设置数据库字符集
ALTER DATABASE seat_reservation CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- ================================
-- 3. 表优化和索引创建
-- ================================

USE seat_reservation;

-- ================================
-- 3. 安全索引创建存储过程
-- ================================

DELIMITER $$
CREATE PROCEDURE SafeCreateIndex(
    IN table_name VARCHAR(64),
    IN index_name VARCHAR(64),
    IN column_list VARCHAR(255)
)
BEGIN
    DECLARE index_exists INT DEFAULT 0;

    -- 检查索引是否已存在
    SELECT COUNT(*) INTO index_exists
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = table_name
    AND index_name = index_name;

    -- 如果索引不存在则创建
    IF index_exists = 0 THEN
        SET @sql = CONCAT('CREATE INDEX ', index_name, ' ON ', table_name, ' (', column_list, ')');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END$$
DELIMITER ;

-- 用户表索引优化
-- 注意：只为实际存在的列创建索引
CALL SafeCreateIndex('users', 'idx_username', 'username');
CALL SafeCreateIndex('users', 'idx_created_time', 'created_time');
CALL SafeCreateIndex('users', 'idx_role', 'role');
CALL SafeCreateIndex('users', 'idx_remaining_days', 'remaining_days');

-- 预约表索引优化
-- 注意：只为实际存在的列创建索引
CALL SafeCreateIndex('reservations', 'idx_user_id', 'user_id');
CALL SafeCreateIndex('reservations', 'idx_room_id', 'room_id');
CALL SafeCreateIndex('reservations', 'idx_start_time', 'start_time');
CALL SafeCreateIndex('reservations', 'idx_end_time', 'end_time');
CALL SafeCreateIndex('reservations', 'idx_created_time_reservations', 'created_time');
CALL SafeCreateIndex('reservations', 'idx_worker_id', 'worker_id');
CALL SafeCreateIndex('reservations', 'idx_reservation_type', 'reservation_type');
CALL SafeCreateIndex('reservations', 'idx_user_room', 'user_id, room_id');
CALL SafeCreateIndex('reservations', 'idx_time_range', 'start_time, end_time');

-- 房间表索引优化
-- 注意：只为实际存在的列创建索引
CALL SafeCreateIndex('rooms', 'idx_school_id', 'school_id');
CALL SafeCreateIndex('rooms', 'idx_name_rooms', 'name');
CALL SafeCreateIndex('rooms', 'idx_room_num', 'roomNum');
CALL SafeCreateIndex('rooms', 'idx_created_time_rooms', 'created_time');

-- 学校表索引优化
-- 注意：只为实际存在的列创建索引
CALL SafeCreateIndex('schools', 'idx_name_schools', 'name');
CALL SafeCreateIndex('schools', 'idx_created_time_schools', 'created_time');
CALL SafeCreateIndex('schools', 'idx_wait_time', 'wait_time');

-- Worker服务器表索引优化
CALL SafeCreateIndex('worker_servers', 'idx_status_workers', 'status');
CALL SafeCreateIndex('worker_servers', 'idx_last_heartbeat', 'last_heartbeat');
CALL SafeCreateIndex('worker_servers', 'idx_created_time_workers', 'created_time');
CALL SafeCreateIndex('worker_servers', 'idx_enabled', 'enabled');
CALL SafeCreateIndex('worker_servers', 'idx_current_load', 'current_load');

-- 任务执行日志表索引优化（如果表存在）
-- 使用存储过程来安全地添加索引
DELIMITER $$
CREATE PROCEDURE AddIndexIfTableExists()
BEGIN
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION BEGIN END;

    -- 检查task_execution_logs表是否存在
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'task_execution_logs') THEN
        -- 使用SafeCreateIndex存储过程创建索引
        CALL SafeCreateIndex('task_execution_logs', 'idx_worker_id_tel', 'worker_id');
        CALL SafeCreateIndex('task_execution_logs', 'idx_task_type_tel', 'task_type');
        CALL SafeCreateIndex('task_execution_logs', 'idx_status_tel', 'status');
        CALL SafeCreateIndex('task_execution_logs', 'idx_created_time_tel', 'created_time');
        CALL SafeCreateIndex('task_execution_logs', 'idx_execution_time_tel', 'execution_time');
        CALL SafeCreateIndex('task_execution_logs', 'idx_worker_status_tel', 'worker_id, status');
        CALL SafeCreateIndex('task_execution_logs', 'idx_task_date_tel', 'task_type, created_time');
    END IF;

    -- 检查distributed_tasks表是否存在
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'distributed_tasks') THEN
        -- 使用SafeCreateIndex存储过程创建索引
        CALL SafeCreateIndex('distributed_tasks', 'idx_status_dt', 'status');
        CALL SafeCreateIndex('distributed_tasks', 'idx_assigned_worker_dt', 'assigned_worker_id');
        CALL SafeCreateIndex('distributed_tasks', 'idx_created_time_dt', 'created_time');
        CALL SafeCreateIndex('distributed_tasks', 'idx_scheduled_time_dt', 'scheduled_time');
        CALL SafeCreateIndex('distributed_tasks', 'idx_priority_dt', 'priority');
    END IF;
END$$
DELIMITER ;

-- 执行存储过程
CALL AddIndexIfTableExists();

-- 删除存储过程
DROP PROCEDURE AddIndexIfTableExists;
DROP PROCEDURE SafeCreateIndex;

-- ================================
-- 4. 创建分区表（针对大数据量）
-- ================================

-- 为任务执行日志表创建按月分区（如果数据量大）
-- ALTER TABLE task_execution_logs 
-- PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
--     PARTITION p202401 VALUES LESS THAN (202402),
--     PARTITION p202402 VALUES LESS THAN (202403),
--     PARTITION p202403 VALUES LESS THAN (202404),
--     PARTITION p202404 VALUES LESS THAN (202405),
--     PARTITION p202405 VALUES LESS THAN (202406),
--     PARTITION p202406 VALUES LESS THAN (202407),
--     PARTITION p202407 VALUES LESS THAN (202408),
--     PARTITION p202408 VALUES LESS THAN (202409),
--     PARTITION p202409 VALUES LESS THAN (202410),
--     PARTITION p202410 VALUES LESS THAN (202411),
--     PARTITION p202411 VALUES LESS THAN (202412),
--     PARTITION p202412 VALUES LESS THAN (202501),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- ================================
-- 5. 创建视图（用于常用查询）
-- ================================

-- 活跃预约视图
CREATE OR REPLACE VIEW active_reservations AS
SELECT 
    r.id,
    r.user_id,
    u.username,
    r.room_id,
    rm.name as room_name,
    s.name as school_name,
    r.reservation_date,
    r.start_time,
    r.end_time,
    r.status,
    r.created_at
FROM reservations r
JOIN users u ON r.user_id = u.id
JOIN rooms rm ON r.room_id = rm.id
JOIN schools s ON rm.school_id = s.id
WHERE r.status IN ('confirmed', 'pending')
AND r.reservation_date >= CURDATE();

-- Worker状态视图
CREATE OR REPLACE VIEW worker_status_view AS
SELECT 
    id,
    worker_id,
    name,
    host,
    port,
    status,
    last_heartbeat,
    CASE 
        WHEN last_heartbeat > DATE_SUB(NOW(), INTERVAL 2 MINUTE) THEN 'online'
        WHEN last_heartbeat > DATE_SUB(NOW(), INTERVAL 10 MINUTE) THEN 'warning'
        ELSE 'offline'
    END as health_status,
    created_at
FROM worker_servers;

-- 任务统计视图
CREATE OR REPLACE VIEW task_statistics AS
SELECT 
    DATE(created_at) as date,
    task_type,
    status,
    COUNT(*) as count,
    AVG(execution_time) as avg_execution_time,
    MAX(execution_time) as max_execution_time,
    MIN(execution_time) as min_execution_time
FROM task_execution_logs
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(created_at), task_type, status;

-- ================================
-- 6. 创建存储过程（用于数据清理）
-- ================================

DELIMITER //

-- 清理过期日志的存储过程
CREATE PROCEDURE CleanupOldLogs(IN days_to_keep INT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 删除过期的任务执行日志
    DELETE FROM task_execution_logs 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    -- 删除过期的预约记录（已完成或已取消的）
    DELETE FROM reservations 
    WHERE status IN ('completed', 'cancelled') 
    AND reservation_date < DATE_SUB(CURDATE(), INTERVAL days_to_keep DAY);
    
    COMMIT;
    
    SELECT CONCAT('Cleanup completed. Removed logs older than ', days_to_keep, ' days.') as result;
END //

-- 数据库健康检查存储过程
CREATE PROCEDURE DatabaseHealthCheck()
BEGIN
    SELECT 
        'Table Statistics' as check_type,
        table_name,
        table_rows,
        ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
    FROM information_schema.tables 
    WHERE table_schema = 'seat_reservation'
    ORDER BY (data_length + index_length) DESC;
    
    SELECT 
        'Index Usage' as check_type,
        table_name,
        index_name,
        cardinality
    FROM information_schema.statistics 
    WHERE table_schema = 'seat_reservation'
    AND cardinality > 0
    ORDER BY cardinality DESC;
END //

DELIMITER ;

-- ================================
-- 7. 创建定时任务（需要EVENT调度器开启）
-- ================================

-- 开启事件调度器
SET GLOBAL event_scheduler = ON;

-- 每日清理过期数据
CREATE EVENT IF NOT EXISTS daily_cleanup
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURRENT_DATE + INTERVAL 1 DAY, '02:00:00')
DO
  CALL CleanupOldLogs(90);

-- 每小时更新统计信息
CREATE EVENT IF NOT EXISTS hourly_stats_update
ON SCHEDULE EVERY 1 HOUR
DO
  ANALYZE TABLE reservations, task_execution_logs, worker_servers;

-- ================================
-- 8. 安全配置建议
-- ================================

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示创建的用户
SELECT User, Host FROM mysql.user WHERE User LIKE 'seatmaster%';

-- 显示用户权限
SHOW GRANTS FOR 'seatmaster_app'@'localhost';
SHOW GRANTS FOR 'seatmaster_readonly'@'localhost';
