-- SeatMaster 生产环境数据库安全配置脚本
-- 仅包含必要的优化，不包含自动化数据清理和定时任务
-- 执行前请确保已备份现有数据

-- ================================
-- 1. 创建生产环境数据库用户（可选）
-- ================================

-- 注意：如果您已有数据库用户，可以跳过此部分
-- 取消注释以下行来创建专用用户

/*
-- 创建应用专用用户（替换默认的root用户）
CREATE USER IF NOT EXISTS 'seatmaster_app'@'localhost' IDENTIFIED BY 'your_secure_password_here';
CREATE USER IF NOT EXISTS 'seatmaster_app'@'%' IDENTIFIED BY 'your_secure_password_here';

-- 授予必要权限
GRANT SELECT, INSERT, UPDATE, DELETE ON seat_reservation.* TO 'seatmaster_app'@'localhost';
GRANT SELECT, INSERT, UPDATE, DELETE ON seat_reservation.* TO 'seatmaster_app'@'%';

-- 创建只读用户（用于监控和报表）
CREATE USER IF NOT EXISTS 'seatmaster_readonly'@'localhost' IDENTIFIED BY 'readonly_password_here';
GRANT SELECT ON seat_reservation.* TO 'seatmaster_readonly'@'localhost';
*/

-- ================================
-- 2. 数据库基础配置
-- ================================

-- 设置数据库字符集
ALTER DATABASE seat_reservation CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- ================================
-- 3. 表优化和索引创建（安全）
-- ================================

USE seat_reservation;

-- ================================
-- 3. 安全索引创建存储过程
-- ================================

DELIMITER $$
CREATE PROCEDURE SafeCreateIndex(
    IN table_name VARCHAR(64),
    IN index_name VARCHAR(64),
    IN column_list VARCHAR(255)
)
BEGIN
    DECLARE index_exists INT DEFAULT 0;

    -- 检查索引是否已存在
    SELECT COUNT(*) INTO index_exists
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = table_name
    AND index_name = index_name;

    -- 如果索引不存在则创建
    IF index_exists = 0 THEN
        SET @sql = CONCAT('CREATE INDEX ', index_name, ' ON ', table_name, ' (', column_list, ')');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END$$
DELIMITER ;

-- 用户表索引优化
-- 注意：只为实际存在的列创建索引
CALL SafeCreateIndex('users', 'idx_username', 'username');
CALL SafeCreateIndex('users', 'idx_created_time', 'created_time');
CALL SafeCreateIndex('users', 'idx_role', 'role');
CALL SafeCreateIndex('users', 'idx_remaining_days', 'remaining_days');

-- 预约表索引优化
-- 注意：只为实际存在的列创建索引
CALL SafeCreateIndex('reservations', 'idx_user_id', 'user_id');
CALL SafeCreateIndex('reservations', 'idx_room_id', 'room_id');
CALL SafeCreateIndex('reservations', 'idx_start_time', 'start_time');
CALL SafeCreateIndex('reservations', 'idx_end_time', 'end_time');
CALL SafeCreateIndex('reservations', 'idx_created_time_reservations', 'created_time');
CALL SafeCreateIndex('reservations', 'idx_worker_id', 'worker_id');
CALL SafeCreateIndex('reservations', 'idx_reservation_type', 'reservation_type');
CALL SafeCreateIndex('reservations', 'idx_user_room', 'user_id, room_id');
CALL SafeCreateIndex('reservations', 'idx_time_range', 'start_time, end_time');

-- 房间表索引优化
-- 注意：只为实际存在的列创建索引
CALL SafeCreateIndex('rooms', 'idx_school_id', 'school_id');
CALL SafeCreateIndex('rooms', 'idx_name_rooms', 'name');
CALL SafeCreateIndex('rooms', 'idx_room_num', 'roomNum');
CALL SafeCreateIndex('rooms', 'idx_created_time_rooms', 'created_time');

-- 学校表索引优化
-- 注意：只为实际存在的列创建索引
CALL SafeCreateIndex('schools', 'idx_name_schools', 'name');
CALL SafeCreateIndex('schools', 'idx_created_time_schools', 'created_time');
CALL SafeCreateIndex('schools', 'idx_wait_time', 'wait_time');

-- Worker服务器表索引优化
CALL SafeCreateIndex('worker_servers', 'idx_status_workers', 'status');
CALL SafeCreateIndex('worker_servers', 'idx_last_heartbeat', 'last_heartbeat');
CALL SafeCreateIndex('worker_servers', 'idx_created_time_workers', 'created_time');
CALL SafeCreateIndex('worker_servers', 'idx_enabled', 'enabled');
CALL SafeCreateIndex('worker_servers', 'idx_current_load', 'current_load');

-- ================================
-- 4. 可选表索引（安全检查）
-- ================================

-- 为可能存在的表安全地创建索引
-- 使用临时存储过程来检查表是否存在

DELIMITER $$
CREATE PROCEDURE SafeAddOptionalIndexes()
BEGIN
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION BEGIN END;

    -- 检查task_execution_logs表是否存在
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'task_execution_logs') THEN
        -- 使用SafeCreateIndex存储过程创建索引
        CALL SafeCreateIndex('task_execution_logs', 'idx_worker_id_tel', 'worker_id');
        CALL SafeCreateIndex('task_execution_logs', 'idx_task_type_tel', 'task_type');
        CALL SafeCreateIndex('task_execution_logs', 'idx_status_tel', 'status');
        CALL SafeCreateIndex('task_execution_logs', 'idx_created_time_tel', 'created_time');
        CALL SafeCreateIndex('task_execution_logs', 'idx_execution_time_tel', 'execution_time');
        CALL SafeCreateIndex('task_execution_logs', 'idx_worker_status_tel', 'worker_id, status');
        CALL SafeCreateIndex('task_execution_logs', 'idx_task_date_tel', 'task_type, created_time');
    END IF;

    -- 检查distributed_tasks表是否存在
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'distributed_tasks') THEN
        -- 使用SafeCreateIndex存储过程创建索引
        CALL SafeCreateIndex('distributed_tasks', 'idx_status_dt', 'status');
        CALL SafeCreateIndex('distributed_tasks', 'idx_assigned_worker_dt', 'assigned_worker_id');
        CALL SafeCreateIndex('distributed_tasks', 'idx_created_time_dt', 'created_time');
        CALL SafeCreateIndex('distributed_tasks', 'idx_scheduled_time_dt', 'scheduled_time');
        CALL SafeCreateIndex('distributed_tasks', 'idx_priority_dt', 'priority');
    END IF;
END$$
DELIMITER ;

-- 执行存储过程
CALL SafeAddOptionalIndexes();

-- 立即删除存储过程（安全清理）
DROP PROCEDURE SafeAddOptionalIndexes;
DROP PROCEDURE SafeCreateIndex;

-- ================================
-- 5. 验证和完成
-- ================================

-- 刷新权限（如果创建了新用户）
-- FLUSH PRIVILEGES;

-- 显示索引创建结果
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    CARDINALITY
FROM information_schema.statistics 
WHERE TABLE_SCHEMA = 'seat_reservation'
AND INDEX_NAME != 'PRIMARY'
ORDER BY TABLE_NAME, INDEX_NAME;

-- 显示表统计信息
SELECT 
    table_name,
    table_rows,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
FROM information_schema.tables 
WHERE table_schema = 'seat_reservation'
ORDER BY (data_length + index_length) DESC;

-- 完成提示
SELECT 'SeatMaster 生产环境数据库安全配置完成！' as status;
SELECT '已创建必要的索引以优化查询性能' as message;
SELECT '未创建任何自动化数据清理或定时任务' as safety_note;
