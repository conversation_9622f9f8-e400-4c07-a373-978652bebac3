-- =====================================================
-- SeatMaster 公告模块数据库表结构
-- 版本: 1.0
-- 创建时间: 2025-07-22
-- =====================================================

-- 1. 公告表 (announcements)
CREATE TABLE IF NOT EXISTS announcements (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '公告ID',
    title VARCHAR(200) NOT NULL COMMENT '公告标题',
    content TEXT NOT NULL COMMENT '公告内容（支持HTML格式）',
    summary VARCHAR(500) COMMENT '公告摘要（用于列表显示）',
    priority INT DEFAULT 0 COMMENT '优先级（数字越大优先级越高）',
    is_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    is_popup BOOLEAN DEFAULT TRUE COMMENT '是否弹窗显示',
    start_time DATETIME COMMENT '生效开始时间',
    end_time DATETIME COMMENT '生效结束时间',
    target_users ENUM('ALL', 'USER', 'ADMIN') DEFAULT 'ALL' COMMENT '目标用户类型',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    created_by BIGINT NOT NULL COMMENT '创建者用户ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_priority_enabled (priority DESC, is_enabled),
    INDEX idx_created_time (created_time DESC),
    INDEX idx_start_end_time (start_time, end_time),
    INDEX idx_target_users (target_users),
    
    -- 外键约束
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
) COMMENT '公告表';

-- 2. 用户公告阅读记录表 (user_announcement_reads)
CREATE TABLE IF NOT EXISTS user_announcement_reads (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    announcement_id BIGINT NOT NULL COMMENT '公告ID',
    read_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '阅读时间',
    is_dismissed BOOLEAN DEFAULT FALSE COMMENT '是否已关闭弹窗',
    
    -- 唯一约束：每个用户对每个公告只能有一条阅读记录
    UNIQUE KEY uk_user_announcement (user_id, announcement_id),
    
    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_announcement_id (announcement_id),
    INDEX idx_read_time (read_time DESC),
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (announcement_id) REFERENCES announcements(id) ON DELETE CASCADE
) COMMENT '用户公告阅读记录表';

-- 3. 插入示例数据（可选）
INSERT INTO announcements (title, content, summary, priority, created_by) VALUES 
('欢迎使用SeatMaster系统', 
 '<h3>欢迎使用SeatMaster座位预约系统！</h3><p>本系统为您提供便捷的座位预约服务，请仔细阅读使用说明。</p><ul><li>每日预约时间：8:00-22:00</li><li>提前预约：支持提前1天预约</li><li>取消预约：请在预约时间前30分钟取消</li></ul>', 
 '欢迎使用SeatMaster座位预约系统，请阅读使用说明。', 
 100, 
 (SELECT id FROM users WHERE role = 'ADMIN' LIMIT 1)),
 
('系统维护通知', 
 '<h3>系统维护通知</h3><p>为了提供更好的服务，系统将在以下时间进行维护：</p><p><strong>维护时间：</strong>每周日 02:00-04:00</p><p><strong>影响范围：</strong>预约功能暂时不可用</p><p>给您带来的不便，敬请谅解！</p>', 
 '系统将在每周日凌晨进行定期维护，预约功能暂时不可用。', 
 80, 
 (SELECT id FROM users WHERE role = 'ADMIN' LIMIT 1));

-- 4. 创建视图：活跃公告视图（便于查询）
CREATE OR REPLACE VIEW active_announcements AS
SELECT 
    a.*,
    u.name as creator_name
FROM announcements a
LEFT JOIN users u ON a.created_by = u.id
WHERE a.is_enabled = TRUE
  AND (a.start_time IS NULL OR a.start_time <= NOW())
  AND (a.end_time IS NULL OR a.end_time >= NOW())
ORDER BY a.priority DESC, a.created_time DESC;
