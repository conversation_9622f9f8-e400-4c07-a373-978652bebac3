-- 插入测试 Worker 数据
-- 用于验证 worker-management 页面显示

USE seat_reservation;

-- 清理现有数据
DELETE FROM worker_servers;

-- 插入测试 Worker 数据
INSERT INTO worker_servers (
    id, name, server_url, priority, status, current_load, max_concurrent_tasks,
    total_tasks_completed, total_tasks_failed, enabled, description, last_heartbeat
) VALUES
('worker-001', '主要副服务器', 'http://localhost:8082', 1, 'ONLINE', 3, 10, 150, 5, TRUE, '主要处理服务器', NOW()),
('worker-002', '备用副服务器', 'http://localhost:8083', 2, 'ONLINE', 1, 8, 89, 2, TRUE, '备用处理服务器', NOW()),
('worker-003', '测试副服务器', 'http://localhost:8084', 3, 'OFFLINE', 0, 5, 45, 8, TRUE, '测试环境服务器', DATE_SUB(NOW(), INTERVAL 10 MINUTE)),
('worker-004', '高性能副服务器', 'http://localhost:8085', 1, 'ONLINE', 7, 15, 320, 12, TRUE, '高性能处理服务器', NOW());

-- 验证插入结果
SELECT 
    id,
    name,
    status,
    current_load,
    max_concurrent_tasks,
    total_tasks_completed,
    total_tasks_failed,
    enabled,
    last_heartbeat
FROM worker_servers;

-- 显示统计信息（模拟后端统计查询）
SELECT 
    COUNT(*) as total_servers,
    SUM(CASE WHEN status = 'ONLINE' THEN 1 ELSE 0 END) as online_servers,
    SUM(CASE WHEN status = 'OFFLINE' THEN 1 ELSE 0 END) as offline_servers,
    SUM(current_load) as total_current_load,
    SUM(total_tasks_completed) as total_completed
FROM worker_servers;

SELECT 'Worker 测试数据插入完成!' as message;
