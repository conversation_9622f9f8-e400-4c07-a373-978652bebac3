-- 数据库结构更新脚本：添加remaining_days到users表，从reservations表移除

USE seat_reservation;

-- 1. 给users表添加remaining_days字段，默认值30天
ALTER TABLE users ADD COLUMN remaining_days INTEGER DEFAULT 30 COMMENT '剩余可预约天数';

-- 2. 更新现有用户的剩余天数
UPDATE users SET remaining_days = 30 WHERE remaining_days IS NULL;

-- 3. 从reservations表移除remaining_days字段（如果存在）
-- 先检查字段是否存在，如果存在则删除
SET @sql = (SELECT IF(
    EXISTS(
        SELECT * FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA = 'seat_reservation' 
        AND TABLE_NAME = 'reservations' 
        AND COLUMN_NAME = 'remaining_days'
    ),
    'ALTER TABLE reservations DROP COLUMN remaining_days',
    'SELECT "Column remaining_days does not exist" as message'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 创建索引优化查询性能
CREATE INDEX idx_reservations_room_seat_date ON reservations(room_id, seat_id, start_time, status);
CREATE INDEX idx_users_remaining_days ON users(remaining_days);

-- 5. 创建存储过程来自动取消剩余天数为0的用户的预约
DELIMITER //
CREATE PROCEDURE CancelReservationsForUsersWithZeroDays()
BEGIN
    UPDATE reservations r
    INNER JOIN users u ON r.user_id = u.id
    SET r.status = 'CANCELLED'
    WHERE u.remaining_days <= 0 
    AND r.status != 'CANCELLED'
    AND r.start_time > NOW();
END //
DELIMITER ;

SELECT 'Database schema updated successfully!' as message; 