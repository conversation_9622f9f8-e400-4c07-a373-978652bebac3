-- 学校视图功能数据库索引优化脚本
-- 创建日期: 2025-08-03
-- 说明: 为学校视图功能优化数据库查询性能，添加必要的复合索引

USE seat_reservation;

-- 显示优化前的信息
SELECT 'Starting school view indexes optimization...' as message;

-- 创建安全索引创建存储过程
DELIMITER $$
CREATE PROCEDURE IF NOT EXISTS SafeCreateIndex(
    IN table_name VARCHAR(64),
    IN index_name VARCHAR(64),
    IN column_list VARCHAR(255)
)
BEGIN
    DECLARE index_exists INT DEFAULT 0;

    -- 检查索引是否已存在
    SELECT COUNT(*) INTO index_exists
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = table_name
    AND index_name = index_name;

    -- 如果索引不存在则创建
    IF index_exists = 0 THEN
        SET @sql = CONCAT('CREATE INDEX ', index_name, ' ON ', table_name, ' (', column_list, ')');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        SELECT CONCAT('Created index: ', index_name, ' on ', table_name) as result;
    ELSE
        SELECT CONCAT('Index already exists: ', index_name, ' on ', table_name) as result;
    END IF;
END$$
DELIMITER ;

-- ==================== 学校视图核心查询优化索引 ====================

-- 1. reservation_logs 表优化索引
-- 用于学校概览查询的核心索引：按房间ID和预约日期聚合
CALL SafeCreateIndex('reservation_logs', 'idx_rl_roomid_date_status', 'roomid, reserve_date, status');

-- 用于今日统计查询的索引：快速筛选今日数据
CALL SafeCreateIndex('reservation_logs', 'idx_rl_date_status', 'reserve_date, status');

-- 用于API响应时间统计的索引：优化平均耗时计算
CALL SafeCreateIndex('reservation_logs', 'idx_rl_api_response_time', 'api_response_time');

-- 用于预约ID关联查询的索引：优化与reservations表的JOIN
CALL SafeCreateIndex('reservation_logs', 'idx_rl_reservation_id', 'reservation_id');

-- 复合索引：优化学校日期统计查询
CALL SafeCreateIndex('reservation_logs', 'idx_rl_roomid_date_time', 'roomid, reserve_date, api_response_time');

-- 2. rooms 表优化索引
-- 学校ID索引（如果不存在）：用于按学校聚合查询
CALL SafeCreateIndex('rooms', 'idx_rooms_school_id', 'school_id');

-- 房间号索引（如果不存在）：用于与reservation_logs表JOIN
CALL SafeCreateIndex('rooms', 'idx_rooms_roomnum', 'roomNum');

-- 复合索引：学校ID + 房间号，优化学校维度查询
CALL SafeCreateIndex('rooms', 'idx_rooms_school_roomnum', 'school_id, roomNum');

-- 3. schools 表优化索引
-- 学校名称索引（如果不存在）：用于学校名称搜索
CALL SafeCreateIndex('schools', 'idx_schools_name', 'name');

-- 4. reservations 表优化索引（用于时间计算）
-- 预约开放时间索引：用于平均耗时计算
CALL SafeCreateIndex('reservations', 'idx_reservations_open_time', 'reservation_open_time');

-- ==================== 性能分析和建议 ====================

-- 显示当前表的统计信息
SELECT 'Table statistics:' as info;

SELECT 
    table_name,
    table_rows,
    data_length,
    index_length,
    ROUND(data_length / 1024 / 1024, 2) as data_mb,
    ROUND(index_length / 1024 / 1024, 2) as index_mb
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
AND table_name IN ('reservation_logs', 'rooms', 'schools', 'reservations')
ORDER BY table_rows DESC;

-- 显示新创建的索引
SELECT 'Newly created indexes:' as info;

SELECT 
    table_name,
    index_name,
    column_name,
    seq_in_index,
    cardinality
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name IN ('reservation_logs', 'rooms', 'schools', 'reservations')
AND index_name LIKE 'idx_%'
ORDER BY table_name, index_name, seq_in_index;

-- 清理存储过程
DROP PROCEDURE IF EXISTS SafeCreateIndex;

-- ==================== 查询性能优化建议 ====================

/*
性能优化分析：

1. 学校概览查询优化：
   - 主要瓶颈：多表JOIN (schools -> rooms -> reservation_logs -> reservations)
   - 优化策略：添加复合索引 idx_rl_roomid_date_status 和 idx_rooms_school_roomnum
   - 预期效果：减少JOIN操作的扫描行数，提升聚合查询性能

2. 学校日期统计查询优化：
   - 主要瓶颈：按日期分组和时间计算
   - 优化策略：添加 idx_rl_roomid_date_time 复合索引
   - 预期效果：快速定位特定学校和日期范围的数据

3. 今日统计查询优化：
   - 主要瓶颈：日期筛选和状态统计
   - 优化策略：添加 idx_rl_date_status 索引
   - 预期效果：快速计算今日执行次数和成功率

4. 平均耗时计算优化：
   - 主要瓶颈：复杂的时间差计算和NULL值处理
   - 优化策略：添加 idx_rl_api_response_time 和 idx_reservations_open_time 索引
   - 预期效果：减少时间计算的数据扫描量

建议的查询优化：
- 考虑在应用层缓存学校基本信息
- 对于大数据量场景，可考虑创建物化视图或定时统计表
- 监控慢查询日志，根据实际使用情况进一步优化
*/

SELECT 'School view indexes optimization completed!' as message;
