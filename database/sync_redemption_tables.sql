-- SeatMaster Redemption Tables Sync Script
-- Create redemption_codes and redemption_logs tables

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- Create redemption_codes table
CREATE TABLE IF NOT EXISTS `redemption_codes` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `days_to_add` int NOT NULL,
  `is_used` tinyint(1) DEFAULT '0',
  `used_by_user_id` bigint DEFAULT NULL,
  `used_time` datetime DEFAULT NULL,
  `created_by_admin_id` bigint NOT NULL,
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `expire_time` datetime DEFAULT NULL,
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `batch_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  <PERSON><PERSON><PERSON> `used_by_user_id` (`used_by_user_id`),
  KEY `created_by_admin_id` (`created_by_admin_id`),
  KEY `idx_code` (`code`),
  KEY `idx_used_status` (`is_used`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create redemption_logs table
CREATE TABLE IF NOT EXISTS `redemption_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `days_added` int NOT NULL,
  `days_before` int NOT NULL,
  `days_after` int NOT NULL,
  `redemption_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_redemption_time` (`redemption_time`),
  KEY `idx_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add foreign key constraints
ALTER TABLE `redemption_codes`
ADD CONSTRAINT `redemption_codes_ibfk_1`
FOREIGN KEY (`used_by_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `redemption_codes`
ADD CONSTRAINT `redemption_codes_ibfk_2`
FOREIGN KEY (`created_by_admin_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `redemption_logs`
ADD CONSTRAINT `redemption_logs_ibfk_1`
FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

SET FOREIGN_KEY_CHECKS = 1;

-- Verify tables created
SHOW TABLES LIKE '%redemption%';
DESCRIBE redemption_codes;
DESCRIBE redemption_logs;
