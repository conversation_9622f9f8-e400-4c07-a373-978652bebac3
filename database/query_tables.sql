-- 查询数据库中的所有表和相关信息
USE seat_reservation;

-- 1. 显示所有表
SELECT '=== 所有表列表 ===' as info;
SHOW TABLES;

-- 2. 查找包含 reservation 或 log 的表
SELECT '=== 包含 reservation 或 log 的表 ===' as info;
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'seat_reservation' 
AND (TABLE_NAME LIKE '%reservation%' OR TABLE_NAME LIKE '%log%');

-- 3. 检查 task_execution_logs 表结构
SELECT '=== task_execution_logs 表结构 ===' as info;
DESCRIBE task_execution_logs;

-- 4. 检查 task_execution_logs 表的详细信息
SELECT '=== task_execution_logs 表详细信息 ===' as info;
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'task_execution_logs'
ORDER BY ORDINAL_POSITION;

-- 5. 查看 task_execution_logs 表的示例数据
SELECT '=== task_execution_logs 示例数据 ===' as info;
SELECT * FROM task_execution_logs LIMIT 3;

-- 6. 检查是否存在 reservation_logs 表
SELECT '=== 检查 reservation_logs 表是否存在 ===' as info;
SELECT COUNT(*) as reservation_logs_exists 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'reservation_logs';

-- 7. 检查所有包含 logs 的表
SELECT '=== 所有包含 logs 的表 ===' as info;
SELECT TABLE_NAME, TABLE_COMMENT
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'seat_reservation' 
AND TABLE_NAME LIKE '%logs%';

-- 8. 检查 reservations 表结构（关联表）
SELECT '=== reservations 表结构 ===' as info;
DESCRIBE reservations;

-- 9. 检查外键关系
SELECT '=== 外键关系 ===' as info;
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND REFERENCED_TABLE_NAME IS NOT NULL
  AND (TABLE_NAME LIKE '%log%' OR REFERENCED_TABLE_NAME LIKE '%log%');

SELECT '=== 查询完成 ===' as info;
