-- 任务执行历史记录表
-- 用于记录每次任务执行的详细结果和响应信息

USE seat_reservation;

-- 创建任务执行历史表
CREATE TABLE IF NOT EXISTS task_execution_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id BIGINT NOT NULL COMMENT '任务ID（对应reservations.id）',
    worker_id VARCHAR(100) COMMENT '执行的副服务器ID',
    execution_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
    response_message TEXT COMMENT '学习通返回的消息',
    response_json TEXT COMMENT '完整的响应JSON',
    execution_duration_ms INT COMMENT '执行耗时（毫秒）',
    is_success BOOLEAN COMMENT '是否成功',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 外键约束，级联删除
    FOREIGN KEY (task_id) REFERENCES reservations(id) ON DELETE CASCADE,
    
    -- 索引优化查询性能
    INDEX idx_task_id (task_id),
    INDEX idx_execution_time (execution_time),
    INDEX idx_worker_id (worker_id),
    INDEX idx_is_success (is_success)
) COMMENT '任务执行历史记录表';

-- 验证表创建
DESCRIBE task_execution_logs;

-- 显示表结构信息
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'seat_reservation' 
AND TABLE_NAME = 'task_execution_logs';

-- 显示索引信息
SHOW INDEX FROM task_execution_logs;

SELECT 'Task execution logs table created successfully!' as message;
