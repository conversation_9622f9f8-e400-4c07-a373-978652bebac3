#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查学校配置文件与MySQL数据库中房间数据的完整性
对比配置文件中的房间与数据库中已导入的房间，找出缺失或不匹配的数据
"""

import os
import re
import mysql.connector
from collections import defaultdict
import pandas as pd

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root',
    'database': 'seat_reservation',
    'charset': 'utf8mb4'
}

def parse_school_config(file_path):
    """解析学校配置文件，提取房间信息"""
    rooms = []
    duration = None
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取预约时长 - 更精确的匹配
    duration_patterns = [
        r'单次预约最长时间[：:]\s*(\d+(?:\.\d+)?)\s*小时',
        r'预约时长[：:]\s*(\d+(?:\.\d+)?)\s*小时',
        r'时长限制[：:]\s*(\d+(?:\.\d+)?)\s*小时',
        r'(\d+(?:\.\d+)?)\s*小时[/／]次',
        r'最长可预约\s*(\d+(?:\.\d+)?)\s*小时'
    ]
    
    for pattern in duration_patterns:
        match = re.search(pattern, content)
        if match:
            duration = float(match.group(1))
            break
    
    # 提取房间信息的多种格式
    room_patterns = [
        r'^(.+?)\s+id[：:]\s*(\d+)\s*$',
        r'^(.+?)\s+id为[：:]\s*(\d+)\s*$', 
        r'^(.+?)--\s*id为[：:]\s*(\d+)\s*$',
        r'^(.+?)\s+(\d+)\s*$'
    ]
    
    lines = content.strip().split('\n')
    for line in lines:
        line = line.strip()
        if not line or any(keyword in line for keyword in ['预约', '时长', '小时', '说明', '注意']):
            continue
            
        for pattern in room_patterns:
            match = re.match(pattern, line)
            if match:
                room_name = match.group(1).strip()
                room_id = match.group(2).strip()
                
                # 清理房间名称，但保留基本格式
                room_name = re.sub(r'\s+', ' ', room_name).strip()
                
                if room_name and room_id and len(room_id) > 0:
                    rooms.append({
                        'name': room_name,
                        'original_id': room_id,
                        'duration': duration
                    })
                break
    
    return rooms, duration

def get_database_data():
    """从数据库获取学校和房间数据"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        cursor = conn.cursor(dictionary=True)
        
        # 获取学校数据
        cursor.execute("SELECT id, name FROM schools ORDER BY id")
        schools = {row['name']: row['id'] for row in cursor.fetchall()}
        
        # 获取房间数据
        cursor.execute("""
            SELECT r.id, r.school_id, r.name, r.max_reservation_hours, r.room_id, s.name as school_name
            FROM rooms r 
            JOIN schools s ON r.school_id = s.id 
            ORDER BY r.school_id, r.id
        """)
        rooms = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        return schools, rooms
        
    except mysql.connector.Error as e:
        print(f"数据库连接错误: {e}")
        return {}, []

def normalize_school_name(filename):
    """标准化学校名称，将文件名转换为数据库中的学校名"""
    school_name = filename.replace('.txt', '')
    return school_name

def compare_data():
    """对比配置文件与数据库数据"""
    config_dir = '../schools_config'
    
    if not os.path.exists(config_dir):
        print(f"配置文件目录不存在: {config_dir}")
        return
    
    # 获取数据库数据
    print("正在连接数据库...")
    db_schools, db_rooms = get_database_data()
    
    if not db_schools:
        print("无法获取数据库数据，请检查数据库连接")
        return
    
    print(f"数据库中有 {len(db_schools)} 所学校，{len(db_rooms)} 个房间")
    
    # 按学校组织数据库房间数据
    db_rooms_by_school = defaultdict(list)
    for room in db_rooms:
        db_rooms_by_school[room['school_name']].append(room)
    
    # 统计信息
    total_config_rooms = 0
    total_missing_rooms = 0
    total_extra_rooms = 0
    missing_schools = []
    duration_mismatches = []
    
    print("\n" + "="*80)
    print("详细对比报告")
    print("="*80)
    
    # 遍历配置文件
    for filename in sorted(os.listdir(config_dir)):
        if not filename.endswith('.txt'):
            continue
            
        school_name = normalize_school_name(filename)
        config_path = os.path.join(config_dir, filename)
        
        print(f"\n📚 学校: {school_name}")
        print("-" * 60)
        
        # 解析配置文件
        try:
            config_rooms, config_duration = parse_school_config(config_path)
            total_config_rooms += len(config_rooms)
            
            print(f"配置文件中的房间数量: {len(config_rooms)}")
            if config_duration is not None:
                print(f"配置文件中的预约时长: {config_duration}小时")
            else:
                print("配置文件中的预约时长: 未设置")
            
            # 检查学校是否存在于数据库中
            if school_name not in db_schools:
                print(f"❌ 学校未在数据库中找到!")
                missing_schools.append(school_name)
                continue
            
            # 获取数据库中该学校的房间
            db_school_rooms = db_rooms_by_school.get(school_name, [])
            print(f"数据库中的房间数量: {len(db_school_rooms)}")
            
            # 获取数据库中的预约时长
            db_duration = None
            if db_school_rooms:
                db_duration = db_school_rooms[0]['max_reservation_hours']
                if db_duration is not None:
                    print(f"数据库中的预约时长: {db_duration}小时")
                else:
                    print("数据库中的预约时长: 未设置")
            
            # 创建房间映射以便对比
            config_room_ids = {room['original_id']: room for room in config_rooms}
            db_room_ids = {room['room_id']: room for room in db_school_rooms if room['room_id']}
            
            # 找出缺失的房间
            missing_rooms = []
            for room_id, room_info in config_room_ids.items():
                if room_id not in db_room_ids:
                    missing_rooms.append(room_info)
            
            # 找出多余的房间
            extra_rooms = []
            for room_id, room_info in db_room_ids.items():
                if room_id not in config_room_ids:
                    extra_rooms.append(room_info)
            
            total_missing_rooms += len(missing_rooms)
            total_extra_rooms += len(extra_rooms)
            
            # 报告房间匹配结果
            if missing_rooms:
                print(f"❌ 缺失房间 ({len(missing_rooms)}个):")
                for i, room in enumerate(missing_rooms):
                    if i < 5:  # 只显示前5个
                        print(f"   - {room['name']} (ID: {room['original_id']})")
                    elif i == 5:
                        print(f"   ... 还有 {len(missing_rooms) - 5} 个房间")
                        break
            
            if extra_rooms:
                print(f"➕ 数据库中多余的房间 ({len(extra_rooms)}个):")
                for i, room in enumerate(extra_rooms):
                    if i < 3:  # 只显示前3个
                        print(f"   - {room['name']} (ID: {room['room_id']})")
                    elif i == 3:
                        print(f"   ... 还有 {len(extra_rooms) - 3} 个房间")
                        break
            
            if not missing_rooms and not extra_rooms:
                print("✅ 房间数据完全匹配!")
            
            # 检查预约时长匹配
            if config_duration != db_duration:
                duration_mismatches.append({
                    'school': school_name,
                    'config': config_duration,
                    'database': db_duration
                })
                print(f"⚠️  预约时长不匹配: 配置({config_duration}) vs 数据库({db_duration})")
            
        except Exception as e:
            print(f"❌ 解析配置文件失败: {e}")
    
    # 检查数据库中是否有配置文件中没有的学校
    config_schools = set()
    for filename in os.listdir(config_dir):
        if filename.endswith('.txt'):
            config_schools.add(normalize_school_name(filename))
    
    extra_db_schools = set(db_schools.keys()) - config_schools
    
    print("\n" + "="*80)
    print("总结报告")
    print("="*80)
    print(f"📊 配置文件总房间数: {total_config_rooms}")
    print(f"📊 数据库总房间数: {len(db_rooms)}")
    print(f"❌ 缺失房间数: {total_missing_rooms}")
    print(f"➕ 多余房间数: {total_extra_rooms}")
    
    if missing_schools:
        print(f"❌ 缺失学校 ({len(missing_schools)}个): {', '.join(missing_schools)}")
    
    if extra_db_schools:
        print(f"➕ 数据库中多余的学校 ({len(extra_db_schools)}个): {', '.join(extra_db_schools)}")
    
    if duration_mismatches:
        print(f"⚠️  预约时长不匹配的学校 ({len(duration_mismatches)}个):")
        for mismatch in duration_mismatches:
            print(f"   - {mismatch['school']}: 配置({mismatch['config']}) vs 数据库({mismatch['database']})")
    
    if total_missing_rooms == 0 and total_extra_rooms == 0 and not missing_schools:
        print("🎉 所有房间数据完全匹配，导入100%成功!")
    else:
        completion_rate = ((total_config_rooms - total_missing_rooms) / total_config_rooms * 100) if total_config_rooms > 0 else 0
        print(f"📈 房间导入完成率: {completion_rate:.1f}%")

def export_db_to_csv():
    """将数据库中的数据导出为CSV格式，便于分析"""
    print("\n=== 导出数据库数据到CSV ===")
    
    db_data = get_database_data()
    if not db_data:
        print("❌ 无法获取数据库数据")
        return
    
    schools = db_data[0]
    rooms = db_data[1]
    
    # 准备CSV数据
    csv_data = []
    for room in rooms:
        csv_data.append({
            'school_name': room['school_name'],
            'room_name': room['name'],
            'max_reservation_hours': room['max_reservation_hours'],
            'room_id': room['room_id']
        })
    
    # 保存为CSV
    df = pd.DataFrame(csv_data)
    csv_file = 'database_rooms_export.csv'
    df.to_csv(csv_file, index=False, encoding='utf-8-sig')
    
    print(f"✅ 数据已导出到: {csv_file}")
    print(f"   总共 {len(csv_data)} 条记录")

if __name__ == "__main__":
    print("学校配置文件与数据库房间数据完整性检查工具")
    print("="*60)
    
    try:
        compare_data()
        
        # 询问是否导出CSV
        response = input("\n是否导出数据库数据到CSV文件？(y/n): ").strip().lower()
        if response in ['y', 'yes', '是']:
            export_db_to_csv()
            
    except Exception as e:
        print(f"运行错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n检查完成！") 