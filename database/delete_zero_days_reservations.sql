-- 删除剩余天数为0的用户预约记录的数据库脚本
-- 替换原来的暂停逻辑，改为直接删除预约记录

USE seat_reservation;

-- 1. 创建存储过程：删除剩余天数为0的用户的预约记录
DELIMITER //
DROP PROCEDURE IF EXISTS DeleteReservationsForUsersWithZeroDays//
CREATE PROCEDURE DeleteReservationsForUsersWithZeroDays()
BEGIN
    DECLARE deleted_count INT DEFAULT 0;
    
    -- 删除剩余天数为0的用户的活跃预约记录
    DELETE r FROM reservations r
    INNER JOIN users u ON r.user_id = u.id
    WHERE u.remaining_days <= 0 
    AND r.status = 'ACTIVE';
    
    -- 获取删除的记录数
    SET deleted_count = ROW_COUNT();
    
    SELECT deleted_count as deleted_reservations;
END //
DELIMITER ;

-- 2. 删除旧的暂停相关存储过程（如果存在）
DROP PROCEDURE IF EXISTS PauseReservationsForUsersWithZeroDays;
DROP PROCEDURE IF EXISTS ResumeReservationsForUsersWithDays;

-- 3. 手动执行一次删除操作，清理现有的剩余天数为0的用户预约
CALL DeleteReservationsForUsersWithZeroDays();

-- 4. 查看当前预约状态分布
SELECT 
    status,
    COUNT(*) as count
FROM reservations 
GROUP BY status;

-- 5. 查看剩余天数为0的用户数量
SELECT 
    COUNT(*) as users_with_zero_days
FROM users 
WHERE remaining_days <= 0 AND role != 'ADMIN';

SELECT 'Delete reservations for zero days users setup completed!' as message;
