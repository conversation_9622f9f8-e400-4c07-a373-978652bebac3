-- 修改学校表wait_time字段的取值范围限制
-- 将范围从0.10-1.00秒扩展到0.1-10.0秒

USE seat_reservation;

-- 1. 删除现有的CHECK约束
ALTER TABLE `schools` DROP CONSTRAINT `chk_wait_time`;

-- 2. 修改字段类型以支持更大的数值范围
-- 从DECIMAL(3,2)改为DECIMAL(4,2)以支持10.00
ALTER TABLE `schools` MODIFY COLUMN `wait_time` DECIMAL(4, 2) NOT NULL DEFAULT 0.50 COMMENT '学校预约操作等待时间（秒）';

-- 3. 添加新的CHECK约束，支持0.1到10.0秒的范围
ALTER TABLE `schools` ADD CONSTRAINT `chk_wait_time` CHECK (`wait_time` >= 0.10 AND `wait_time` <= 10.00);

-- 4. 验证修改结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    NUMERIC_PRECISION,
    NUMERIC_SCALE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'schools' 
  AND COLUMN_NAME = 'wait_time';

-- 5. 查看约束信息
SELECT 
    CONSTRAINT_NAME,
    CHECK_CLAUSE
FROM INFORMATION_SCHEMA.CHECK_CONSTRAINTS 
WHERE CONSTRAINT_SCHEMA = 'seat_reservation' 
  AND CONSTRAINT_NAME = 'chk_wait_time';

-- 6. 测试新的范围限制（可选）
-- 这些语句仅用于测试，实际运行时可以注释掉

-- 测试最小值（应该成功）
-- INSERT INTO schools (name, wait_time) VALUES ('测试学校_最小值', 0.10);

-- 测试最大值（应该成功）
-- INSERT INTO schools (name, wait_time) VALUES ('测试学校_最大值', 10.00);

-- 测试超出范围的值（应该失败）
-- INSERT INTO schools (name, wait_time) VALUES ('测试学校_超出范围', 10.01);

-- 清理测试数据（如果执行了上面的测试）
-- DELETE FROM schools WHERE name LIKE '测试学校_%';

-- 修改完成提示
SELECT '学校表wait_time字段范围限制修改完成：0.10-10.00秒' AS result;
