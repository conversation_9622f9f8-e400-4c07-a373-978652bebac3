-- 预约更新约束优化脚本

-- 1. 备份当前数据
CREATE TABLE IF NOT EXISTS `reservations_backup` LIKE `reservations`;
INSERT INTO `reservations_backup` SELECT * FROM `reservations`;

-- 2. 删除现有的唯一约束
ALTER TABLE `reservations` DROP INDEX `unique_user_reservation`;

-- 3. 添加新的约束，允许用户更新预约
-- 使用触发器来确保每个用户只有一个活跃预约
-- 但在更新操作时不会触发约束冲突

-- 创建触发器：在插入新预约前检查用户是否已有预约
DELIMITER //
CREATE TRIGGER `check_user_reservation_before_insert` BEFORE INSERT ON `reservations`
FOR EACH ROW
BEGIN
    DECLARE existing_count INT;
    
    -- 检查用户是否已有预约
    SELECT COUNT(*) INTO existing_count 
    FROM `reservations` 
    WHERE `user_id` = NEW.`user_id`;
    
    -- 如果已有预约，则阻止插入
    IF existing_count > 0 THEN
        SIGNAL SQLSTATE '45000' 
        SET MESSAGE_TEXT = '用户已有活跃预约，请使用更新操作';
    END IF;
END //
DELIMITER ;

-- 创建索引以提高查询性能
CREATE INDEX `idx_reservations_user_id` ON `reservations` (`user_id`);

-- 4. 添加注释说明约束变更
-- 注意：此脚本将唯一约束替换为触发器约束，以允许预约更新操作
-- 同时保持每个用户只能有一个活跃预约的业务规则
