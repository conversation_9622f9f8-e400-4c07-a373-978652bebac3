-- 创建数据库
CREATE DATABASE IF NOT EXISTS seat_reservation CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE seat_reservation;

-- 创建学校表
CREATE TABLE IF NOT EXISTS schools (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '学校名称',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT '学校表';

-- 创建房间表
CREATE TABLE IF NOT EXISTS rooms (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    school_id BIGINT NOT NULL COMMENT '学校ID',
    name VARCHAR(100) NOT NULL COMMENT '房间名称',
    room_id VARCHAR(20) COMMENT '房间ID',
    max_reservation_hours DECIMAL(4,1) COMMENT '最大预约时长(小时)',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    version INT DEFAULT 0 COMMENT '版本号',
    FOR<PERSON><PERSON><PERSON> KEY (school_id) REFERENCES schools(id) ON DELETE CASCADE
) COMMENT '房间表';

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    role ENUM('USER', 'ADMIN') NOT NULL DEFAULT 'USER' COMMENT '角色',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT '用户表';

-- 创建座位表
CREATE TABLE IF NOT EXISTS seats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    seat_number VARCHAR(20) NOT NULL COMMENT '座位编号',
    room_id BIGINT NOT NULL COMMENT '房间ID',
    status ENUM('AVAILABLE', 'UNAVAILABLE') DEFAULT 'AVAILABLE' COMMENT '座位状态',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    UNIQUE KEY uk_room_seat (room_id, seat_number)
) COMMENT '座位表';

-- 创建预约表
CREATE TABLE IF NOT EXISTS reservations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    seat_id BIGINT NOT NULL COMMENT '座位ID',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    status ENUM('ACTIVE', 'CANCELLED') NOT NULL DEFAULT 'ACTIVE' COMMENT '预约状态',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (seat_id) REFERENCES seats(id) ON DELETE CASCADE
) COMMENT '预约表';

-- 插入示例数据
INSERT INTO schools (name) VALUES 
('清华大学'),
('北京大学'),
('复旦大学');

INSERT INTO rooms (school_id, name) VALUES 
(1, '图书馆一楼'),
(1, '图书馆二楼'),
(2, '自习室A'),
(2, '自习室B'),
(3, '阅览室1');

INSERT INTO seats (seat_number, room_id) VALUES 
('A001', 1), ('A002', 1), ('A003', 1), ('A004', 1), ('A005', 1),
('B001', 2), ('B002', 2), ('B003', 2), ('B004', 2), ('B005', 2),
('C001', 3), ('C002', 3), ('C003', 3), ('C004', 3), ('C005', 3);

-- 创建管理员用户 (密码: admin123)
INSERT INTO users (username, password, name, role) VALUES 
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDa', '系统管理员', 'ADMIN'); 