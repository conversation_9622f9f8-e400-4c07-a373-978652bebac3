-- 修改学校表wait_time字段的取值范围限制
-- 将范围从0.10-10.00秒扩展到0.00-10.00秒，支持零等待时间

USE seat_reservation;

-- ================================
-- 1. 备份当前约束信息（用于回滚）
-- ================================

-- 查看当前约束
SELECT
    CONSTRAINT_NAME,
    CHECK_CLAUSE
FROM INFORMATION_SCHEMA.CHECK_CONSTRAINTS
WHERE CONSTRAINT_SCHEMA = 'seat_reservation'
  AND CONSTRAINT_NAME = 'chk_wait_time';

-- 查看当前字段信息
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    NUMERIC_PRECISION,
    NUMERIC_SCALE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'schools' 
  AND COLUMN_NAME = 'wait_time';

-- ================================
-- 2. 删除现有的CHECK约束
-- ================================

ALTER TABLE `schools` DROP CONSTRAINT `chk_wait_time`;

-- ================================
-- 3. 确认字段类型（应该已经是DECIMAL(4,2)）
-- ================================

-- 如果需要，确保字段类型支持0.00到10.00的范围
-- ALTER TABLE `schools` MODIFY COLUMN `wait_time` DECIMAL(4, 2) NOT NULL DEFAULT 0.50 COMMENT '学校预约操作等待时间（秒）';

-- ================================
-- 4. 添加新的CHECK约束，支持0.00到10.00秒的范围
-- ================================

ALTER TABLE `schools` ADD CONSTRAINT `chk_wait_time` CHECK (`wait_time` >= 0.00 AND `wait_time` <= 10.00);

-- ================================
-- 5. 验证修改结果
-- ================================

-- 检查新约束
SELECT
    CONSTRAINT_NAME,
    CHECK_CLAUSE
FROM INFORMATION_SCHEMA.CHECK_CONSTRAINTS
WHERE CONSTRAINT_SCHEMA = 'seat_reservation'
  AND CONSTRAINT_NAME = 'chk_wait_time';

-- 检查字段信息
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    NUMERIC_PRECISION,
    NUMERIC_SCALE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'schools' 
  AND COLUMN_NAME = 'wait_time';

-- ================================
-- 6. 测试新的范围限制（可选）
-- ================================

-- 测试最小值0.00（应该成功）
-- INSERT INTO schools (name, wait_time) VALUES ('测试学校_零等待', 0.00);

-- 测试中间值5.50（应该成功）
-- INSERT INTO schools (name, wait_time) VALUES ('测试学校_中间值', 5.50);

-- 测试最大值10.00（应该成功）
-- INSERT INTO schools (name, wait_time) VALUES ('测试学校_最大值', 10.00);

-- 测试超出范围的值（应该失败）
-- INSERT INTO schools (name, wait_time) VALUES ('测试学校_超出范围', 10.01);

-- 测试负值（应该失败）
-- INSERT INTO schools (name, wait_time) VALUES ('测试学校_负值', -0.01);

-- 清理测试数据（如果执行了上面的测试）
-- DELETE FROM schools WHERE name LIKE '测试学校_%';

-- ================================
-- 7. 修改完成提示
-- ================================

SELECT '学校表wait_time字段范围限制修改完成：0.00-10.00秒' AS result;
SELECT '现在支持零等待时间设置' AS feature;
SELECT '请更新应用程序代码以匹配新的验证范围' AS next_step;

-- ================================
-- 8. 回滚脚本（紧急情况使用）
-- ================================

-- 如果需要回滚到原来的0.10-10.00范围，执行以下语句：
-- ALTER TABLE schools DROP CONSTRAINT chk_wait_time;
-- ALTER TABLE schools ADD CONSTRAINT chk_wait_time CHECK (wait_time >= 0.10 AND wait_time <= 10.00);
