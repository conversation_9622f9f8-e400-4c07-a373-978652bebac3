-- 检查当前数据库结构脚本
-- 用于分析现有表结构，为分布式扩展做准备

USE seat_reservation;

-- =====================================================
-- 1. 检查所有表
-- =====================================================
SHOW TABLES;

-- =====================================================
-- 2. 检查 users 表结构
-- =====================================================
DESCRIBE users;

SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'users'
ORDER BY ORDINAL_POSITION;

-- =====================================================
-- 3. 检查 schools 表结构
-- =====================================================
DESCRIBE schools;

-- =====================================================
-- 4. 检查 rooms 表结构
-- =====================================================
DESCRIBE rooms;

SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'rooms'
ORDER BY ORDINAL_POSITION;

-- =====================================================
-- 5. 检查 reservations 表结构 (重点)
-- =====================================================
DESCRIBE reservations;

SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'reservations'
ORDER BY ORDINAL_POSITION;

-- =====================================================
-- 6. 检查是否存在 seats 表
-- =====================================================
SELECT COUNT(*) as seats_table_exists 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'seats';

-- =====================================================
-- 7. 检查是否已存在分布式字段
-- =====================================================
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'EXISTS' 
        ELSE 'NOT_EXISTS' 
    END as worker_id_status
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'reservations' 
  AND COLUMN_NAME = 'worker_id';

SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'EXISTS' 
        ELSE 'NOT_EXISTS' 
    END as execution_status_status
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'reservations' 
  AND COLUMN_NAME = 'execution_status';

-- =====================================================
-- 8. 检查是否存在 worker_servers 表
-- =====================================================
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'EXISTS' 
        ELSE 'NOT_EXISTS' 
    END as worker_servers_table_status
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'worker_servers';

-- =====================================================
-- 9. 检查现有数据量
-- =====================================================
SELECT 'users' as table_name, COUNT(*) as record_count FROM users
UNION ALL
SELECT 'schools' as table_name, COUNT(*) as record_count FROM schools
UNION ALL
SELECT 'rooms' as table_name, COUNT(*) as record_count FROM rooms
UNION ALL
SELECT 'reservations' as table_name, COUNT(*) as record_count FROM reservations;

-- =====================================================
-- 10. 检查 reservations 表的实际数据类型
-- =====================================================
SELECT 
    id,
    user_id,
    room_id,
    seat_id,
    start_time,
    end_time,
    status,
    created_time,
    reservation_open_time,
    reservation_type
FROM reservations 
LIMIT 3;

-- =====================================================
-- 11. 检查外键约束
-- =====================================================
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND REFERENCED_TABLE_NAME IS NOT NULL;

-- =====================================================
-- 12. 检查索引
-- =====================================================
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = 'seat_reservation'
ORDER BY TABLE_NAME, INDEX_NAME;

SELECT '数据库结构检查完成!' as message;
