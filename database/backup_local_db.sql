-- ========================================
-- SeatMaster 本地数据库备份脚本
-- 在同步前备份现有数据库结构和数据
-- 创建时间: 2025-01-19
-- ========================================

-- 备份说明：
-- 此脚本用于在同步远程数据库结构前备份本地数据库
-- 执行命令：mysqldump -u root -proot5869087 seat_reservation > backup_$(date +%Y%m%d_%H%M%S).sql

-- 如果需要恢复备份，使用以下命令：
-- mysql -u root -proot5869087 seat_reservation < backup_文件名.sql

-- 备份验证查询
SELECT 'SeatMaster 数据库备份验证' AS message;
SELECT COUNT(*) AS total_tables FROM information_schema.tables WHERE table_schema = 'seat_reservation';
SELECT table_name FROM information_schema.tables WHERE table_schema = 'seat_reservation' ORDER BY table_name;
