-- 添加预约拆分相关字段到reservations表
-- 执行日期: 2025-07-02
-- 说明: 支持长时间预约自动拆分功能

USE seat_reservation;

-- 检查当前表结构
DESCRIBE reservations;

-- 添加拆分相关字段
ALTER TABLE reservations ADD COLUMN is_split_reservation BOOLEAN DEFAULT FALSE COMMENT '是否为拆分预约';
ALTER TABLE reservations ADD COLUMN split_group_id VARCHAR(50) DEFAULT NULL COMMENT '拆分组ID，同一原始预约拆分出的多个预约段共享同一组ID';
ALTER TABLE reservations ADD COLUMN split_sequence INT DEFAULT NULL COMMENT '拆分序号，在拆分组中的序号，从1开始';
ALTER TABLE reservations ADD COLUMN split_total_count INT DEFAULT NULL COMMENT '拆分总数，该拆分组总共有多少个预约段';

-- 创建索引优化查询性能
CREATE INDEX idx_reservations_split_group ON reservations(split_group_id);
CREATE INDEX idx_reservations_split_info ON reservations(is_split_reservation, split_group_id, split_sequence);

-- 验证字段已添加
DESCRIBE reservations;

-- 显示修改后的表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'reservations'
  AND COLUMN_NAME IN ('is_split_reservation', 'split_group_id', 'split_sequence', 'split_total_count')
ORDER BY ORDINAL_POSITION;

-- 检查现有数据
SELECT COUNT(*) as total_reservations FROM reservations;

COMMIT;

-- 显示完成信息
SELECT 'Reservation split fields added successfully!' as message;
