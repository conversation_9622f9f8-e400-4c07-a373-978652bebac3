#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import pandas as pd

def extract_reservation_duration(content):
    """从配置文件中提取单次预约时长"""
    # 匹配"单次预约时长: X.X小时"格式
    duration_pattern = r'单次预约时长:\s*(\d+\.?\d*)小时'
    match = re.search(duration_pattern, content)
    if match:
        return float(match.group(1))
    return None  # 默认为NULL

def extract_rooms_from_file(file_path):
    """从学校配置文件中提取房间信息和预约时长"""
    rooms = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取预约时长
    duration = extract_reservation_duration(content)
    
    # 不同格式的房间信息模式
    patterns = [
        # 格式1: 西北政法大学雁塔校区图书馆-2F-典一阅览室 id: 4693
        r'(.+?)\s+id:\s*(\d+)',
        # 格式2: 连云港市图书馆-一楼大自修室日间场次- id为：6511
        r'(.+?)\s+id为：(\d+)',
        # 格式3: 文学一[二楼南]-- id为：2170
        r'(.+?)--\s+id为：(\d+)',
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, content)
        if matches:
            for match in matches:
                room_name = match[0].strip().rstrip('-')
                if room_name and room_name not in [r[0] for r in rooms]:
                    rooms.append((room_name, match[1], duration))
            break
    
    return rooms

def generate_sql_from_csv(csv_file):
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file, encoding='utf-8')
        
        # 检查必需的列
        required_columns = ['school_name', 'room_name', 'max_reservation_hours', 'room_id']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"错误：CSV文件缺少必需的列: {missing_columns}")
            print(f"当前列: {list(df.columns)}")
            return
            
        # 创建输出内容
        sql_content = []
        
        # 添加头部注释和清空操作
        sql_content.append("-- 清空所有学校和房间数据并重置自增ID")
        sql_content.append("-- 注意：请先手动确保rooms表有以下字段：max_reservation_hours, room_id")
        sql_content.append("DELETE FROM rooms;")
        sql_content.append("DELETE FROM schools;")
        sql_content.append("ALTER TABLE schools AUTO_INCREMENT = 1;")
        sql_content.append("ALTER TABLE rooms AUTO_INCREMENT = 1;")
        sql_content.append("")
        
        # 获取所有学校并生成插入语句
        schools = df['school_name'].unique()
        school_values = []
        for school in schools:
            school_values.append(f"('{school}')")
            
        sql_content.append("-- 添加学校")
        sql_content.append("INSERT INTO schools (name) VALUES")
        sql_content.append(",\n".join(school_values) + ";")
        sql_content.append("")
        
        # 为每个学校生成房间数据
        sql_content.append("-- 添加房间（包含预约时长）")
        
        # 创建学校到ID的映射
        school_id_map = {school: idx + 1 for idx, school in enumerate(schools)}
        
        # 添加注释说明每个学校的预约时长和ID
        for school in schools:
            school_rooms = df[df['school_name'] == school]
            max_hours = school_rooms['max_reservation_hours'].iloc[0]
            max_hours_str = f"{max_hours}小时" if pd.notna(max_hours) else "NULL小时"
            sql_content.append(f"-- {school} (school_id: {school_id_map[school]}) - 预约时长: {max_hours_str}")
        
        # 生成INSERT语句
        current_school = None
        current_batch = []
        
        for _, row in df.iterrows():
            school_name = row['school_name']
            room_name = row['room_name']
            max_hours = row['max_reservation_hours'] if pd.notna(row['max_reservation_hours']) else 'NULL'
            room_id = row['room_id']
            
            school_id = school_id_map[school_name]
            
            if current_school != school_name:
                # 如果有之前的批次，先写入
                if current_batch:
                    sql_content.append("INSERT INTO rooms (school_id, name, max_reservation_hours, room_id) VALUES")
                    sql_content.append(",\n".join(current_batch) + "\n;")
                    current_batch = []
                
                current_school = school_name
            
            # 转义单引号
            room_name_escaped = room_name.replace("'", "''")
            current_batch.append(f"({school_id}, '{room_name_escaped}', {max_hours}, '{room_id}')")
            
            # 如果当前批次达到50条，先写入一批
            if len(current_batch) >= 50:
                sql_content.append("INSERT INTO rooms (school_id, name, max_reservation_hours, room_id) VALUES")
                sql_content.append(",\n".join(current_batch) + "\n;")
                current_batch = []
        
        # 写入最后一批
        if current_batch:
            sql_content.append("INSERT INTO rooms (school_id, name, max_reservation_hours, room_id) VALUES")
            sql_content.append(",\n".join(current_batch) + "\n;")
        
        # 将内容写入文件
        output_file = 'add_schools_and_rooms_simple.sql'
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(sql_content))
        
        print(f"SQL文件已生成: {output_file}")
        print(f"总共处理了 {len(schools)} 个学校，{len(df)} 个房间")
        
        # 显示统计信息
        print("\n学校统计:")
        for school in schools:
            count = len(df[df['school_name'] == school])
            print(f"  {school}: {count} 个房间")
            
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    csv_file = "rooms_data.csv"  # 请确保这个文件存在
    
    try:
        generate_sql_from_csv(csv_file)
    except FileNotFoundError:
        print(f"错误：找不到文件 {csv_file}")
        print("请确保CSV文件存在并包含以下列：school_name, room_name, max_reservation_hours, room_id")
    except Exception as e:
        print(f"运行时错误: {e}") 