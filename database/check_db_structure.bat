@echo off
echo =====================================================
echo 检查 SeatMaster 数据库结构
echo =====================================================

echo 正在连接数据库并检查结构...

"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -proot < check_current_structure.sql > current_structure_report.txt 2>&1

if %ERRORLEVEL% EQU 0 (
    echo 检查完成！结果已保存到 current_structure_report.txt
    echo.
    echo 正在显示检查结果...
    echo.
    type current_structure_report.txt
) else (
    echo 数据库连接失败，请检查MySQL服务是否启动
    echo 错误信息：
    type current_structure_report.txt
)

echo.
echo =====================================================
echo 检查完成
echo =====================================================
pause
