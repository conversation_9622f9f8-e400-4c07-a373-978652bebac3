@echo off
echo 正在修复rooms表结构...

echo 添加school_id字段...
"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -proot seat_reservation -e "ALTER TABLE rooms ADD COLUMN school_id BIGINT NOT NULL COMMENT '学校ID';"

echo 添加name字段...
"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -proot seat_reservation -e "ALTER TABLE rooms ADD COLUMN name VARCHAR(255) NOT NULL COMMENT '房间名称';"

echo 添加max_reservation_hours字段...
"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -proot seat_reservation -e "ALTER TABLE rooms ADD COLUMN max_reservation_hours DECIMAL(4,1) DEFAULT NULL COMMENT '单次预约最大时长(小时)';"

echo 添加外键约束...
"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -proot seat_reservation -e "ALTER TABLE rooms ADD FOREIGN KEY (school_id) REFERENCES schools(id);"

echo 检查表结构...
"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -proot seat_reservation -e "DESCRIBE rooms;"

echo 修复完成！ 