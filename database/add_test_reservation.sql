-- 添加测试预约数据
USE seat_reservation;

-- 为test1用户添加一个当前预约
INSERT INTO reservations (user_id, seat_id, start_time, end_time, status) VALUES 
(2, 1, NOW(), DATE_ADD(NOW(), INTERVAL 2 HOUR), 'ACTIVE');

-- 查看预约信息
SELECT 
    r.id as reservation_id,
    u.username,
    s.name as school_name,
    rm.name as room_name,
    st.seat_number,
    r.start_time,
    r.end_time,
    r.status
FROM reservations r
JOIN users u ON r.user_id = u.id
JOIN seats st ON r.seat_id = st.id
JOIN rooms rm ON st.room_id = rm.id
JOIN schools s ON rm.school_id = s.id
WHERE r.status = 'ACTIVE' AND r.end_time > NOW(); 