-- 简化的数据库结构更新脚本

USE seat_reservation;

-- 1. 检查并添加remaining_days字段到users表
ALTER TABLE users ADD COLUMN remaining_days INTEGER DEFAULT 30 COMMENT '剩余可预约天数';

-- 2. 更新现有用户的剩余天数
UPDATE users SET remaining_days = 30 WHERE remaining_days IS NULL;

-- 3. 检查reservations表是否有remaining_days字段，如果有则删除
-- 这个需要手动检查，因为MySQL语法限制

-- 4. 创建安全索引创建存储过程
DELIMITER $$
CREATE PROCEDURE SafeCreateIndex(
    IN table_name VARCHAR(64),
    IN index_name VARCHAR(64),
    IN column_list VARCHAR(255)
)
BEGIN
    DECLARE index_exists INT DEFAULT 0;

    -- 检查索引是否已存在
    SELECT COUNT(*) INTO index_exists
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = table_name
    AND index_name = index_name;

    -- 如果索引不存在则创建
    IF index_exists = 0 THEN
        SET @sql = CONCAT('CREATE INDEX ', index_name, ' ON ', table_name, ' (', column_list, ')');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END$$
DELIMITER ;

-- 5. 创建索引优化查询性能
CALL SafeCreateIndex('reservations', 'idx_reservations_room_seat_date', 'room_id, seat_id, start_time, status');
CALL SafeCreateIndex('users', 'idx_users_remaining_days', 'remaining_days');

-- 清理存储过程
DROP PROCEDURE SafeCreateIndex;

SELECT 'Database schema update completed!' as message;