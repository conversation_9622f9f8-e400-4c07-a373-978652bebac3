-- 删除 rooms 表中的 total_seats 字段
-- 执行日期: 2025-01-02
-- 说明: 根据用户需求，不再需要显示和维护总座位数信息

USE seat_reservation;

-- 备份当前表结构（可选）
-- CREATE TABLE rooms_backup_20250102 AS SELECT * FROM rooms;

-- 检查当前表结构
DESCRIBE rooms;

-- 删除 total_seats 字段
ALTER TABLE rooms DROP COLUMN total_seats;

-- 验证字段已删除
DESCRIBE rooms;

-- 显示修改后的表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'rooms'
ORDER BY ORDINAL_POSITION;

-- 检查现有数据
SELECT COUNT(*) as total_rooms FROM rooms;
SELECT id, school_id, name, room_id, max_reservation_hours 
FROM rooms 
LIMIT 5;

COMMIT;
