/*
 Navicat Premium Data Transfer

 Source Server         : bigwork
 Source Server Type    : MySQL
 Source Server Version : 80035
 Source Host           : localhost:3306
 Source Schema         : seat_reservation

 Target Server Type    : MySQL
 Target Server Version : 80035
 File Encoding         : 65001

 Date: 22/07/2025 23:54:22
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for announcements
-- ----------------------------
DROP TABLE IF EXISTS `announcements`;
CREATE TABLE `announcements`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '鍏?憡ID',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '鍏?憡鏍囬?',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '鍏?憡鍐呭?锛堟敮鎸丠TML鏍煎紡锛',
  `summary` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '鍏?憡鎽樿?锛堢敤浜庡垪琛ㄦ樉绀猴級',
  `priority` int NULL DEFAULT 0 COMMENT '浼樺厛绾э紙鏁板瓧瓒婂ぇ浼樺厛绾ц秺楂橈級',
  `is_enabled` tinyint(1) NULL DEFAULT 1 COMMENT '鏄?惁鍚?敤',
  `is_popup` tinyint(1) NULL DEFAULT 1 COMMENT '鏄?惁寮圭獥鏄剧ず',
  `start_time` datetime NULL DEFAULT NULL COMMENT '鐢熸晥寮??鏃堕棿',
  `end_time` datetime NULL DEFAULT NULL COMMENT '鐢熸晥缁撴潫鏃堕棿',
  `target_users` enum('ALL','USER','ADMIN') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'ALL' COMMENT '鐩?爣鐢ㄦ埛绫诲瀷',
  `view_count` int NULL DEFAULT 0 COMMENT '鏌ョ湅娆℃暟',
  `created_by` bigint NOT NULL COMMENT '鍒涘缓鑰呯敤鎴稩D',
  `created_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '鍒涘缓鏃堕棿',
  `updated_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '鏇存柊鏃堕棿',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_priority_enabled`(`priority` DESC, `is_enabled` ASC) USING BTREE,
  INDEX `idx_created_time`(`created_time` DESC) USING BTREE,
  INDEX `idx_start_end_time`(`start_time` ASC, `end_time` ASC) USING BTREE,
  INDEX `idx_target_users`(`target_users` ASC) USING BTREE,
  INDEX `created_by`(`created_by` ASC) USING BTREE,
  CONSTRAINT `announcements_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '鍏?憡琛' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of announcements
-- ----------------------------
INSERT INTO `announcements` VALUES (6, '测试', '<p>测试开始，这是测试内容</p>', '这是一条测试', 0, 0, 1, '2025-07-22 00:00:00', '2025-07-23 00:00:00', 'ALL', 0, 14, '2025-07-22 19:51:36', '2025-07-22 19:54:04');
INSERT INTO `announcements` VALUES (7, '测试', '<p>1测试内容</p>', '1', 0, 1, 1, '2025-07-22 00:00:00', '2025-07-31 00:00:00', 'ALL', 1, 14, '2025-07-22 19:54:57', '2025-07-22 19:55:01');

-- ----------------------------
-- Table structure for auto_execution_logs
-- ----------------------------
DROP TABLE IF EXISTS `auto_execution_logs`;
CREATE TABLE `auto_execution_logs`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `reservation_id` bigint NOT NULL,
  `user_id` bigint NOT NULL,
  `execution_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `execution_status` enum('SUCCESS','FAILED','TIMEOUT') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `result_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `execution_duration` int NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_reservation_time`(`reservation_id` ASC, `execution_time` ASC) USING BTREE,
  INDEX `idx_user_status`(`user_id` ASC, `execution_status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of auto_execution_logs
-- ----------------------------

-- ----------------------------
-- Table structure for distributed_config
-- ----------------------------
DROP TABLE IF EXISTS `distributed_config`;
CREATE TABLE `distributed_config`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '閰嶇疆閿',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '閰嶇疆鍊',
  `config_type` enum('STRING','INTEGER','BOOLEAN','JSON') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'STRING' COMMENT '閰嶇疆绫诲瀷',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '閰嶇疆鎻忚堪',
  `is_system` tinyint(1) NULL DEFAULT 0 COMMENT '鏄?惁涓虹郴缁熼厤缃',
  `created_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `config_key`(`config_key` ASC) USING BTREE,
  INDEX `idx_distributed_config_key`(`config_key` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '鍒嗗竷寮忕郴缁熼厤缃?〃' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of distributed_config
-- ----------------------------
INSERT INTO `distributed_config` VALUES (1, 'task.default_priority', '5', 'INTEGER', '榛樿?浠诲姟浼樺厛绾', 1, '2025-06-13 15:01:38', '2025-06-13 15:01:38');
INSERT INTO `distributed_config` VALUES (2, 'task.max_retry_count', '3', 'INTEGER', '鏈?ぇ閲嶈瘯娆℃暟', 1, '2025-06-13 15:01:38', '2025-06-13 15:01:38');
INSERT INTO `distributed_config` VALUES (3, 'task.assignment_timeout', '30', 'INTEGER', '浠诲姟鍒嗛厤瓒呮椂鏃堕棿(绉?', 1, '2025-06-13 15:01:38', '2025-06-13 15:01:38');
INSERT INTO `distributed_config` VALUES (4, 'task.execution_timeout', '300', 'INTEGER', '浠诲姟鎵ц?瓒呮椂鏃堕棿(绉?', 1, '2025-06-13 15:01:38', '2025-06-13 15:01:38');
INSERT INTO `distributed_config` VALUES (5, 'worker.heartbeat_interval', '30', 'INTEGER', '蹇冭烦闂撮殧鏃堕棿(绉?', 1, '2025-06-13 15:01:38', '2025-06-13 15:01:38');
INSERT INTO `distributed_config` VALUES (6, 'worker.offline_threshold', '120', 'INTEGER', '绂荤嚎鍒ゅ畾闃堝?(绉?', 1, '2025-06-13 15:01:38', '2025-06-13 15:01:38');
INSERT INTO `distributed_config` VALUES (7, 'system.auto_assignment_enabled', 'true', 'BOOLEAN', '鏄?惁鍚?敤鑷?姩鍒嗛厤', 1, '2025-06-13 15:01:38', '2025-06-13 15:01:38');
INSERT INTO `distributed_config` VALUES (8, 'system.load_balance_algorithm', 'ROUND_ROBIN', 'STRING', '璐熻浇鍧囪　绠楁硶', 1, '2025-06-13 15:01:38', '2025-06-13 15:01:38');

-- ----------------------------
-- Table structure for execution_logs
-- ----------------------------
DROP TABLE IF EXISTS `execution_logs`;
CREATE TABLE `execution_logs`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `reservation_id` int NOT NULL,
  `success` tinyint(1) NOT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `execution_time_ms` decimal(8, 2) NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_reservation_id`(`reservation_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of execution_logs
-- ----------------------------
INSERT INTO `execution_logs` VALUES (1, 143, 0, '座位已被占用', 50.54, '2025-07-21 11:54:54');
INSERT INTO `execution_logs` VALUES (2, 143, 1, '预约成功', 63.12, '2025-07-21 12:35:33');
INSERT INTO `execution_logs` VALUES (3, 143, 0, '座位已被占用', 53.00, '2025-07-21 13:16:44');
INSERT INTO `execution_logs` VALUES (4, 143, 1, '预约成功', 50.17, '2025-07-21 13:56:37');
INSERT INTO `execution_logs` VALUES (5, 143, 1, '预约成功', 65.30, '2025-07-21 13:59:34');
INSERT INTO `execution_logs` VALUES (6, 143, 0, '座位已被占用', 54.81, '2025-07-21 14:00:05');
INSERT INTO `execution_logs` VALUES (7, 143, 1, '预约成功', 61.32, '2025-07-21 14:03:34');

-- ----------------------------
-- Table structure for redemption_codes
-- ----------------------------
DROP TABLE IF EXISTS `redemption_codes`;
CREATE TABLE `redemption_codes`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `days_to_add` int NOT NULL,
  `is_used` tinyint(1) NULL DEFAULT 0,
  `used_by_user_id` bigint NULL DEFAULT NULL,
  `used_time` datetime NULL DEFAULT NULL,
  `created_by_admin_id` bigint NOT NULL,
  `created_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `expire_time` datetime NULL DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `batch_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `code`(`code` ASC) USING BTREE,
  INDEX `used_by_user_id`(`used_by_user_id` ASC) USING BTREE,
  INDEX `created_by_admin_id`(`created_by_admin_id` ASC) USING BTREE,
  INDEX `idx_code`(`code` ASC) USING BTREE,
  INDEX `idx_used_status`(`is_used` ASC) USING BTREE,
  INDEX `idx_expire_time`(`expire_time` ASC) USING BTREE,
  INDEX `idx_batch_id`(`batch_id` ASC) USING BTREE,
  INDEX `idx_created_time`(`created_time` ASC) USING BTREE,
  CONSTRAINT `redemption_codes_ibfk_1` FOREIGN KEY (`used_by_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT,
  CONSTRAINT `redemption_codes_ibfk_2` FOREIGN KEY (`created_by_admin_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of redemption_codes
-- ----------------------------
INSERT INTO `redemption_codes` VALUES (4, '7554307E6F0755GH', 7, 1, 24, '2025-07-19 17:54:21', 14, '2025-07-19 17:46:16', NULL, '', 'BATCH_20250719_174615');
INSERT INTO `redemption_codes` VALUES (5, '1324078BEE252YZV', 7, 1, 24, '2025-07-19 17:54:05', 14, '2025-07-19 17:53:33', NULL, '', 'BATCH_20250719_175333');
INSERT INTO `redemption_codes` VALUES (6, '13249D7110AA760H', 7, 0, NULL, NULL, 14, '2025-07-19 17:53:33', NULL, '', 'BATCH_20250719_175333');
INSERT INTO `redemption_codes` VALUES (7, '1325234FBED5BJ3F', 7, 1, 24, '2025-07-19 17:53:44', 14, '2025-07-19 17:53:33', NULL, '', 'BATCH_20250719_175333');
INSERT INTO `redemption_codes` VALUES (8, '13254C91443C3OH6', 7, 1, 24, '2025-07-19 17:53:59', 14, '2025-07-19 17:53:33', NULL, '', 'BATCH_20250719_175333');
INSERT INTO `redemption_codes` VALUES (9, '13259372E3F191QQ', 7, 0, NULL, NULL, 14, '2025-07-19 17:53:33', NULL, '', 'BATCH_20250719_175333');
INSERT INTO `redemption_codes` VALUES (10, '13263243FFBF66WL', 7, 0, NULL, NULL, 14, '2025-07-19 17:53:33', NULL, '', 'BATCH_20250719_175333');
INSERT INTO `redemption_codes` VALUES (11, '1326620EC9701V8V', 7, 0, NULL, NULL, 14, '2025-07-19 17:53:33', NULL, '', 'BATCH_20250719_175333');
INSERT INTO `redemption_codes` VALUES (12, '13269F72BDE3CELX', 7, 0, NULL, NULL, 14, '2025-07-19 17:53:33', NULL, '', 'BATCH_20250719_175333');
INSERT INTO `redemption_codes` VALUES (13, '1327378E58637GVC', 7, 0, NULL, NULL, 14, '2025-07-19 17:53:33', NULL, '', 'BATCH_20250719_175333');
INSERT INTO `redemption_codes` VALUES (14, '13275A73407E6X8R', 7, 0, NULL, NULL, 14, '2025-07-19 17:53:33', NULL, '', 'BATCH_20250719_175333');

-- ----------------------------
-- Table structure for redemption_logs
-- ----------------------------
DROP TABLE IF EXISTS `redemption_logs`;
CREATE TABLE `redemption_logs`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `days_added` int NOT NULL,
  `days_before` int NOT NULL,
  `days_after` int NOT NULL,
  `redemption_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `user_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_redemption_time`(`redemption_time` ASC) USING BTREE,
  INDEX `idx_code`(`code` ASC) USING BTREE,
  CONSTRAINT `redemption_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of redemption_logs
-- ----------------------------
INSERT INTO `redemption_logs` VALUES (1, 24, '1325234FBED5BJ3F', 7, 1, 8, '2025-07-19 17:53:44', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0');
INSERT INTO `redemption_logs` VALUES (2, 24, '13254C91443C3OH6', 7, 8, 15, '2025-07-19 17:53:59', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0');
INSERT INTO `redemption_logs` VALUES (3, 24, '1324078BEE252YZV', 7, 15, 22, '2025-07-19 17:54:05', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0');
INSERT INTO `redemption_logs` VALUES (4, 24, '7554307E6F0755GH', 7, 22, 29, '2025-07-19 17:54:21', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0');

-- ----------------------------
-- Table structure for reservation_logs
-- ----------------------------
DROP TABLE IF EXISTS `reservation_logs`;
CREATE TABLE `reservation_logs`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `reservation_id` bigint NOT NULL COMMENT '预约任务ID，关联reservations表',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `roomid` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '房间ID',
  `seatid` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '座位ID',
  `reserve_date` date NOT NULL COMMENT '预约日期',
  `start_time` time NOT NULL COMMENT '开始时间',
  `end_time` time NOT NULL COMMENT '结束时间',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '执行状态：success/error',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `api_response` json NULL COMMENT '学习通API完整响应',
  `api_response_time` timestamp(3) NULL DEFAULT NULL COMMENT 'API响应时间戳(毫秒精度)',
  `attempt_count` int NULL DEFAULT 1 COMMENT '尝试次数',
  `execution_time` decimal(10, 6) NULL DEFAULT NULL COMMENT '执行耗时（秒）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_reservation_id`(`reservation_id` ASC) USING BTREE,
  INDEX `idx_username`(`username` ASC) USING BTREE,
  INDEX `idx_reserve_date`(`reserve_date` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_created_at`(`created_at` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '预约执行结果日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of reservation_logs
-- ----------------------------
INSERT INTO `reservation_logs` VALUES (1, 143, '18755869972', '9086', '055', '2025-07-21', '12:00:00', '15:00:00', 'failed', NULL, NULL, NULL, 3, 7.105332, '2025-07-21 23:51:28');
INSERT INTO `reservation_logs` VALUES (2, 143, '18755869972', '9086', '055', '2025-07-21', '18:00:00', '20:00:00', 'failed', NULL, NULL, NULL, 3, 7.135748, '2025-07-21 23:51:28');
INSERT INTO `reservation_logs` VALUES (3, 143, '18755869972', '9086', '055', '2025-07-21', '15:00:00', '18:00:00', 'failed', NULL, NULL, NULL, 3, 7.135748, '2025-07-21 23:51:28');
INSERT INTO `reservation_logs` VALUES (4, 143, '18755869972', '9086', '055', '2025-07-21', '09:00:00', '12:00:00', 'failed', NULL, NULL, NULL, 3, 7.245200, '2025-07-21 23:51:28');
INSERT INTO `reservation_logs` VALUES (5, 143, '18755869972', '9086', '055', '2025-07-21', '06:00:00', '09:00:00', 'failed', NULL, NULL, NULL, 3, 7.339050, '2025-07-21 23:51:28');
INSERT INTO `reservation_logs` VALUES (6, 143, '18755869972', '9086', '055', '2025-07-21', '12:00:00', '15:00:00', 'failed', '该时间段已过，不可预约！', '{\"msg\": \"该时间段已过，不可预约！\", \"success\": false}', NULL, 3, 7.185143, '2025-07-21 23:58:41');
INSERT INTO `reservation_logs` VALUES (7, 143, '18755869972', '9086', '055', '2025-07-21', '09:00:00', '12:00:00', 'failed', '该时间段已过，不可预约！', '{\"msg\": \"该时间段已过，不可预约！\", \"success\": false}', NULL, 3, 7.218220, '2025-07-21 23:58:41');
INSERT INTO `reservation_logs` VALUES (8, 143, '18755869972', '9086', '055', '2025-07-21', '18:00:00', '20:00:00', 'failed', '该时间段已过，不可预约！', '{\"msg\": \"该时间段已过，不可预约！\", \"success\": false}', NULL, 3, 7.260952, '2025-07-21 23:58:41');
INSERT INTO `reservation_logs` VALUES (9, 143, '18755869972', '9086', '055', '2025-07-21', '06:00:00', '09:00:00', 'failed', '该时间段已过，不可预约！', '{\"msg\": \"该时间段已过，不可预约！\", \"success\": false}', NULL, 3, 7.310954, '2025-07-21 23:58:41');
INSERT INTO `reservation_logs` VALUES (10, 143, '18755869972', '9086', '055', '2025-07-21', '15:00:00', '18:00:00', 'failed', '该时间段已过，不可预约！', '{\"msg\": \"该时间段已过，不可预约！\", \"success\": false}', NULL, 3, 7.369361, '2025-07-21 23:58:41');
INSERT INTO `reservation_logs` VALUES (11, 143, '18755869972', '9086', '055', '2025-07-22', '18:00:00', '20:00:00', 'error', 'local variable \'datetime\' referenced before assignment', '{\"msg\": \"未知错误\", \"success\": false}', NULL, 3, 3.046606, '2025-07-22 00:06:01');
INSERT INTO `reservation_logs` VALUES (12, 143, '18755869972', '9086', '055', '2025-07-22', '09:00:00', '12:00:00', 'error', 'local variable \'datetime\' referenced before assignment', '{\"msg\": \"未知错误\", \"success\": false}', NULL, 3, 3.052121, '2025-07-22 00:06:01');
INSERT INTO `reservation_logs` VALUES (13, 143, '18755869972', '9086', '055', '2025-07-22', '15:00:00', '18:00:00', 'error', 'local variable \'datetime\' referenced before assignment', '{\"msg\": \"未知错误\", \"success\": false}', NULL, 3, 3.048605, '2025-07-22 00:06:01');
INSERT INTO `reservation_logs` VALUES (14, 143, '18755869972', '9086', '055', '2025-07-22', '12:00:00', '15:00:00', 'error', 'local variable \'datetime\' referenced before assignment', '{\"msg\": \"未知错误\", \"success\": false}', NULL, 3, 3.059122, '2025-07-22 00:06:01');
INSERT INTO `reservation_logs` VALUES (15, 143, '18755869972', '9086', '055', '2025-07-22', '06:00:00', '09:00:00', 'error', 'local variable \'datetime\' referenced before assignment', '{\"msg\": \"未知错误\", \"success\": false}', NULL, 3, 3.071123, '2025-07-22 00:06:01');
INSERT INTO `reservation_logs` VALUES (16, 143, '18755869972', '9086', '055', '2025-07-22', '18:00:00', '20:00:00', 'success', NULL, '{\"data\": {\"seatReserve\": {\"id\": 151626679, \"uid\": 192319793, \"today\": \"2025-07-22\", \"deptId\": 126031, \"roomId\": 9086, \"status\": 0, \"endTime\": 1753185600000, \"rDeptId\": 126031, \"seatNum\": \"055\", \"duration\": \"2.0\", \"startTime\": 1753178400000, \"expireTime\": 1753179600000, \"inserttime\": 1753114140000, \"updatetime\": 1753114140000, \"signDuration\": 20, \"learnDuration\": 0, \"firstLevelName\": \"听松学习空间[二楼西]\", \"pushFormUserId\": 0, \"thirdLevelName\": \"\", \"secondLevelName\": \"\"}, \"signDuration\": 20, \"preSignDuration\": 20}, \"success\": true}', '2025-07-22 00:09:00.781', 3, 38.812409, '2025-07-22 00:09:04');
INSERT INTO `reservation_logs` VALUES (17, 143, '18755869972', '9086', '055', '2025-07-22', '12:00:00', '15:00:00', 'success', NULL, '{\"data\": {\"seatReserve\": {\"id\": 151626680, \"uid\": 192319793, \"today\": \"2025-07-22\", \"deptId\": 126031, \"roomId\": 9086, \"status\": 0, \"endTime\": 1753167600000, \"rDeptId\": 126031, \"seatNum\": \"055\", \"duration\": \"3.0\", \"startTime\": 1753156800000, \"expireTime\": 1753158000000, \"inserttime\": 1753114140000, \"updatetime\": 1753114140000, \"signDuration\": 20, \"learnDuration\": 0, \"firstLevelName\": \"听松学习空间[二楼西]\", \"pushFormUserId\": 0, \"thirdLevelName\": \"\", \"secondLevelName\": \"\"}, \"signDuration\": 20, \"preSignDuration\": 20}, \"success\": true}', '2025-07-22 00:09:01.033', 3, 39.065370, '2025-07-22 00:09:04');
INSERT INTO `reservation_logs` VALUES (18, 143, '18755869972', '9086', '055', '2025-07-22', '09:00:00', '12:00:00', 'success', NULL, '{\"data\": {\"seatReserve\": {\"id\": 151626681, \"uid\": 192319793, \"today\": \"2025-07-22\", \"deptId\": 126031, \"roomId\": 9086, \"status\": 0, \"endTime\": 1753156800000, \"rDeptId\": 126031, \"seatNum\": \"055\", \"duration\": \"3.0\", \"startTime\": 1753146000000, \"expireTime\": 1753147200000, \"inserttime\": 1753114140000, \"updatetime\": 1753114140000, \"signDuration\": 20, \"learnDuration\": 0, \"firstLevelName\": \"听松学习空间[二楼西]\", \"pushFormUserId\": 0, \"thirdLevelName\": \"\", \"secondLevelName\": \"\"}, \"signDuration\": 20, \"preSignDuration\": 20}, \"success\": true}', '2025-07-22 00:09:01.246', 3, 39.281253, '2025-07-22 00:09:04');
INSERT INTO `reservation_logs` VALUES (19, 143, '18755869972', '9086', '055', '2025-07-22', '15:00:00', '18:00:00', 'success', NULL, '{\"data\": {\"seatReserve\": {\"id\": 151626682, \"uid\": 192319793, \"today\": \"2025-07-22\", \"deptId\": 126031, \"roomId\": 9086, \"status\": 0, \"endTime\": 1753178400000, \"rDeptId\": 126031, \"seatNum\": \"055\", \"duration\": \"3.0\", \"startTime\": 1753167600000, \"expireTime\": 1753168800000, \"inserttime\": 1753114141000, \"updatetime\": 1753114141000, \"signDuration\": 20, \"learnDuration\": 0, \"firstLevelName\": \"听松学习空间[二楼西]\", \"pushFormUserId\": 0, \"thirdLevelName\": \"\", \"secondLevelName\": \"\"}, \"signDuration\": 20, \"preSignDuration\": 20}, \"success\": true}', '2025-07-22 00:09:01.485', 3, 39.516537, '2025-07-22 00:09:04');
INSERT INTO `reservation_logs` VALUES (20, 143, '18755869972', '9086', '055', '2025-07-22', '06:00:00', '09:00:00', 'failed', '当前预约时段已超出可预约时间范围，请重新选择', '{\"msg\": \"当前预约时段已超出可预约时间范围，请重新选择\", \"success\": false}', '2025-07-22 00:09:04.324', 3, 42.564977, '2025-07-22 00:09:04');
INSERT INTO `reservation_logs` VALUES (21, 143, '18755869972', '9086', '055', '2025-07-22', '06:00:00', '09:00:00', 'failed', '该时间段已过，不可预约！', '{\"msg\": \"该时间段已过，不可预约！\", \"success\": false}', '2025-07-22 08:57:53.502', 3, 6.784146, '2025-07-22 08:57:53');
INSERT INTO `reservation_logs` VALUES (22, 143, '18755869972', '9086', '055', '2025-07-22', '09:00:00', '12:00:00', 'failed', '该时间段您已有预约！', '{\"msg\": \"该时间段您已有预约！\", \"success\": false}', '2025-07-22 08:57:53.572', 3, 6.847147, '2025-07-22 08:57:53');
INSERT INTO `reservation_logs` VALUES (23, 143, '18755869972', '9086', '055', '2025-07-22', '15:00:00', '18:00:00', 'failed', '该时间段您已有预约！', '{\"msg\": \"该时间段您已有预约！\", \"success\": false}', '2025-07-22 08:57:53.656', 3, 6.937846, '2025-07-22 08:57:53');
INSERT INTO `reservation_logs` VALUES (24, 143, '18755869972', '9086', '055', '2025-07-22', '18:00:00', '20:00:00', 'failed', '该时间段您已有预约！', '{\"msg\": \"该时间段您已有预约！\", \"success\": false}', '2025-07-22 08:57:53.663', 3, 6.936843, '2025-07-22 08:57:53');
INSERT INTO `reservation_logs` VALUES (25, 143, '18755869972', '9086', '055', '2025-07-22', '12:00:00', '15:00:00', 'failed', '该时间段您已有预约！', '{\"msg\": \"该时间段您已有预约！\", \"success\": false}', '2025-07-22 08:57:53.753', 3, 7.029795, '2025-07-22 08:57:53');
INSERT INTO `reservation_logs` VALUES (26, 143, '18755869972', '9086', '055', '2025-07-22', '15:00:00', '18:00:00', 'error', 'HTTPSConnectionPool(host=\'passport2.chaoxing.com\', port=443): Max retries exceeded with url: /mlogin?loginType=1&newversion=true&fid= (Caused by ProxyError(\'Unable to connect to proxy\', FileNotFoundError(2, \'No such file or directory\')))', '{}', NULL, 3, 0.339214, '2025-07-22 10:28:29');
INSERT INTO `reservation_logs` VALUES (27, 143, '18755869972', '9086', '055', '2025-07-22', '12:00:00', '15:00:00', 'error', 'HTTPSConnectionPool(host=\'passport2.chaoxing.com\', port=443): Max retries exceeded with url: /mlogin?loginType=1&newversion=true&fid= (Caused by ProxyError(\'Unable to connect to proxy\', FileNotFoundError(2, \'No such file or directory\')))', '{}', NULL, 3, 0.340215, '2025-07-22 10:28:29');
INSERT INTO `reservation_logs` VALUES (28, 143, '18755869972', '9086', '055', '2025-07-22', '06:00:00', '09:00:00', 'error', 'HTTPSConnectionPool(host=\'passport2.chaoxing.com\', port=443): Max retries exceeded with url: /mlogin?loginType=1&newversion=true&fid= (Caused by ProxyError(\'Unable to connect to proxy\', FileNotFoundError(2, \'No such file or directory\')))', '{}', NULL, 3, 0.341216, '2025-07-22 10:28:29');
INSERT INTO `reservation_logs` VALUES (29, 143, '18755869972', '9086', '055', '2025-07-22', '18:00:00', '20:00:00', 'error', 'HTTPSConnectionPool(host=\'passport2.chaoxing.com\', port=443): Max retries exceeded with url: /mlogin?loginType=1&newversion=true&fid= (Caused by ProxyError(\'Unable to connect to proxy\', FileNotFoundError(2, \'No such file or directory\')))', '{}', NULL, 3, 0.339221, '2025-07-22 10:28:29');
INSERT INTO `reservation_logs` VALUES (30, 143, '18755869972', '9086', '055', '2025-07-22', '09:00:00', '12:00:00', 'error', 'HTTPSConnectionPool(host=\'passport2.chaoxing.com\', port=443): Max retries exceeded with url: /mlogin?loginType=1&newversion=true&fid= (Caused by ProxyError(\'Unable to connect to proxy\', FileNotFoundError(2, \'No such file or directory\')))', '{}', NULL, 3, 0.341220, '2025-07-22 10:28:29');
INSERT INTO `reservation_logs` VALUES (31, 143, '18755869972', '9086', '055', '2025-07-22', '12:00:00', '15:00:00', 'failed', '该时间段已过，不可预约！', '{\"msg\": \"该时间段已过，不可预约！\", \"success\": false}', '2025-07-22 20:10:59.670', 3, 6.751816, '2025-07-22 20:11:03');
INSERT INTO `reservation_logs` VALUES (32, 143, '18755869972', '9086', '055', '2025-07-22', '09:00:00', '12:00:00', 'failed', '该时间段已过，不可预约！', '{\"msg\": \"该时间段已过，不可预约！\", \"success\": false}', '2025-07-22 20:10:59.674', 3, 6.829758, '2025-07-22 20:11:03');
INSERT INTO `reservation_logs` VALUES (33, 143, '18755869972', '9086', '055', '2025-07-22', '15:00:00', '18:00:00', 'failed', '该时间段已过，不可预约！', '{\"msg\": \"该时间段已过，不可预约！\", \"success\": false}', '2025-07-22 20:10:59.697', 3, 6.844085, '2025-07-22 20:11:03');
INSERT INTO `reservation_logs` VALUES (34, 143, '18755869972', '9086', '055', '2025-07-22', '18:00:00', '20:00:00', 'failed', '验证码校验未通过，请重新尝试验证', '{\"msg\": \"验证码校验未通过，请重新尝试验证\", \"success\": false}', '2025-07-22 20:10:59.670', 3, 6.874840, '2025-07-22 20:11:03');
INSERT INTO `reservation_logs` VALUES (35, 143, '18755869972', '9086', '055', '2025-07-22', '06:00:00', '09:00:00', 'failed', '该时间段已过，不可预约！', '{\"msg\": \"该时间段已过，不可预约！\", \"success\": false}', '2025-07-22 20:10:59.701', 3, 6.876842, '2025-07-22 20:11:03');

-- ----------------------------
-- Table structure for reservations
-- ----------------------------
DROP TABLE IF EXISTS `reservations`;
CREATE TABLE `reservations`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `room_id` bigint NOT NULL,
  `seat_num` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '座位号',
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `created_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `reservation_open_time` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '预约开放时间设置（用户可选填写）',
  `reservation_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'SAME_DAY' COMMENT '预约类型：SAME_DAY当天预约，ADVANCE_ONE_DAY提前一天预约',
  `last_execution_time` timestamp NULL DEFAULT NULL,
  `execution_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `task_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '浠诲姟ID锛堜娇鐢╮eservations.id锛屼繚鐣欏瓧娈电敤浜庢墿灞曪級',
  `assignment_mode` enum('MANUAL','AUTO') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'AUTO' COMMENT '浠诲姟鍒嗛厤妯″紡锛氭墜鍔?鑷?姩',
  `worker_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '鎵ц?浠诲姟鐨勫壇鏈嶅姟鍣↖D',
  `task_priority` int NULL DEFAULT 5 COMMENT '浠诲姟浼樺厛绾?(1-10, 鏁板瓧瓒婂皬浼樺厛绾ц秺楂?',
  `scheduled_execution_time` datetime NULL DEFAULT NULL COMMENT '璁″垝鎵ц?鏃堕棿',
  `assigned_time` datetime NULL DEFAULT NULL COMMENT '浠诲姟鍒嗛厤鏃堕棿',
  `started_time` datetime NULL DEFAULT NULL COMMENT '寮??鎵ц?鏃堕棿',
  `actual_execution_time` datetime NULL DEFAULT NULL COMMENT '瀹為檯瀹屾垚鏃堕棿',
  `execution_duration` bigint NULL DEFAULT NULL COMMENT '鎵ц?鑰楁椂锛堟?绉掞級',
  `retry_count` int NULL DEFAULT 0 COMMENT '閲嶈瘯娆℃暟',
  `max_retry_count` int NULL DEFAULT 3 COMMENT '鏈?ぇ閲嶈瘯娆℃暟',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '閿欒?淇℃伅',
  `result_data` json NULL COMMENT '鎵ц?缁撴灉鏁版嵁',
  `is_split_reservation` tinyint(1) NULL DEFAULT 0 COMMENT '是否为拆分预约',
  `split_group_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '拆分组ID',
  `split_sequence` int NULL DEFAULT NULL COMMENT '拆分序号',
  `split_total_count` int NULL DEFAULT NULL COMMENT '拆分总数',
  `execution_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'PENDING',
  `target_timestamp_ns` bigint NULL DEFAULT NULL COMMENT '目标执行时间戳（纳秒级）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_user_reservation`(`user_id` ASC) USING BTREE,
  INDEX `idx_reservations_room_seat_date`(`room_id` ASC, `seat_num` ASC, `start_time` ASC) USING BTREE,
  INDEX `idx_worker_id`(`worker_id` ASC) USING BTREE,
  INDEX `idx_task_status`(`worker_id` ASC, `started_time` ASC, `actual_execution_time` ASC) USING BTREE,
  INDEX `idx_reservations_split_group`(`split_group_id` ASC) USING BTREE,
  INDEX `idx_target_timestamp`(`target_timestamp_ns` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 146 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of reservations
-- ----------------------------
INSERT INTO `reservations` VALUES (143, 24, 239, '055', '06:00:00', '20:00:00', '2025-07-20 19:13:57', '00:09:00', 'ADVANCE_ONE_DAY', NULL, NULL, NULL, 'AUTO', 'production_worker', 5, '2025-07-21 14:04:20', NULL, NULL, NULL, NULL, 0, 3, NULL, NULL, 0, NULL, NULL, NULL, 'COMPLETED', 1753070094000000000);

-- ----------------------------
-- Table structure for rooms
-- ----------------------------
DROP TABLE IF EXISTS `rooms`;
CREATE TABLE `rooms`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `school_id` bigint NOT NULL COMMENT '学校ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '房间名称',
  `max_reservation_hours` decimal(4, 1) NULL DEFAULT NULL COMMENT '单次预约最大时长(小时)',
  `created_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `version` int NULL DEFAULT 0 COMMENT '版本号',
  `roomNum` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '房间号码',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '??????',
  `seat_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '座位ID（version=0时使用）',
  `captcha` tinyint(1) NULL DEFAULT 0 COMMENT '是否需要验证码验证',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `school_id`(`school_id` ASC) USING BTREE,
  CONSTRAINT `rooms_ibfk_1` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `rooms_ibfk_2` FOREIGN KEY (`school_id`) REFERENCES `schools` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 243 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '房间表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of rooms
-- ----------------------------
INSERT INTO `rooms` VALUES (1, 1, '图书馆主馆-主馆107-A', 3.0, '2025-06-02 08:16:39', 0, '1769', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (2, 1, '图书馆主馆-主馆107-B', 5.0, '2025-06-02 08:16:39', 0, '3984', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (3, 1, '图书馆主馆-主馆107-C', 5.0, '2025-06-02 08:16:39', 0, '3985', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (4, 1, '图书馆主馆-主馆107-D', 5.0, '2025-06-02 08:16:39', 0, '3986', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (5, 1, '图书馆主馆-主馆312', 5.0, '2025-06-02 08:16:39', 0, '1768', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (6, 1, '图书馆主馆-主馆312B', 5.0, '2025-06-02 08:16:39', 0, '4326', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (7, 1, '图书馆主馆-主馆207', 5.0, '2025-06-02 08:16:39', 0, '4217', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (8, 1, '图书馆主馆-主馆301', 5.0, '2025-06-02 08:16:39', 0, '3993', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (9, 1, '主馆书库-1-5楼书库', 5.0, '2025-06-02 08:16:39', 1, '4597', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (10, 1, '图书馆主馆-主馆507', 5.0, '2025-06-02 08:16:39', 0, '5971', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (11, 1, '主馆公共区域-一楼共享大厅', 5.0, '2025-06-02 08:16:39', 1, '7166', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (12, 1, '主馆公共区域-二楼前厅', 5.0, '2025-06-02 08:16:39', 0, '7167', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (13, 1, '主馆公共区域-三楼前厅', 5.0, '2025-06-02 08:16:39', 1, '7168', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (14, 1, '主馆公共区域-四楼前厅', 5.0, '2025-06-02 08:16:39', 1, '7169', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (15, 1, '主馆公共区域-五楼前厅', 5.0, '2025-06-02 08:16:39', 1, '7170', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (16, 1, '逸夫馆-逸夫馆108', 5.0, '2025-06-02 08:16:39', 0, '8215', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (17, 1, '逸夫馆-逸夫馆207', 5.0, '2025-06-02 08:16:39', 0, '8598', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (18, 1, '逸夫馆-逸夫馆208', 5.0, '2025-06-02 08:16:39', 0, '8599', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (19, 1, '逸夫馆-逸夫馆308', 5.0, '2025-06-02 08:16:39', 0, '8600', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (20, 1, '逸夫馆-逸夫馆207圆厅', 5.0, '2025-06-02 08:16:39', 0, '8601', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (21, 1, '逸夫馆-逸夫馆208 北', 5.0, '2025-06-02 08:16:39', 0, '8800', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (22, 1, '逸夫馆-逸夫一楼大厅西', 5.0, '2025-06-02 08:16:39', 0, '9637', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (23, 1, '逸夫馆-逸夫一楼大厅东', 5.0, '2025-06-02 08:16:39', 0, '9674', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (24, 1, '逸夫馆-逸夫一楼大厅东-B', 5.0, '2025-06-02 08:16:39', 0, '9719', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (25, 1, '图书馆主馆-主馆307', 5.0, '2025-06-02 08:16:39', 0, '12036', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (26, 1, '图书馆主馆-主馆407', 5.0, '2025-06-02 08:16:39', 0, '12038', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (27, 2, '校本部逸夫图书馆-一楼-大厅', NULL, '2025-06-02 08:16:39', 0, '6699', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (28, 2, '校本部逸夫图书馆-一楼-自习室', NULL, '2025-06-02 08:16:39', 0, '6700', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (29, 2, '校本部逸夫图书馆-三楼-大厅', NULL, '2025-06-02 08:16:39', 0, '6702', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (30, 2, '校本部逸夫图书馆-四楼-大厅', NULL, '2025-06-02 08:16:39', 0, '6703', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (31, 2, '校本部逸夫图书馆-五楼-大厅', NULL, '2025-06-02 08:16:39', 0, '6704', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (32, 2, '校本部逸夫图书馆-六楼-自习室', NULL, '2025-06-02 08:16:39', 0, '6705', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (33, 2, '培黎校区图书馆-二楼-自习大厅', NULL, '2025-06-02 08:16:39', 0, '6706', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (34, 2, '培黎校区图书馆-三楼-自习大厅', NULL, '2025-06-02 08:16:39', 0, '6707', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (35, 2, '培黎校区图书馆-四楼-自习大厅', NULL, '2025-06-02 08:16:39', 0, '6708', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (36, 3, '啬园校区-二楼-社科书库一（A-E类)', NULL, '2025-06-02 08:16:39', 0, '6625', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (37, 3, '啬园校区-二楼-社科书库二（F、G类）', NULL, '2025-06-02 08:16:39', 0, '6693', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (38, 3, '啬园校区-二楼-社科书库三（I、J类）', NULL, '2025-06-02 08:16:39', 0, '6709', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (39, 3, '啬园校区-二楼-社科书库四（H、K类）', NULL, '2025-06-02 08:16:39', 0, '6694', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (40, 3, '啬园校区-三楼-自科书库一（N-S类）', NULL, '2025-06-02 08:16:39', 0, '6697', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (41, 3, '啬园校区-三楼-自科书库二（T-TN类）', NULL, '2025-06-02 08:16:39', 1, '6677', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (42, 3, '啬园校区-三楼-自科书库三（TP类）', NULL, '2025-06-02 08:16:39', 1, '6710', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (43, 3, '啬园校区-三楼-自科书库四（TQ-Z类）', NULL, '2025-06-02 08:16:39', 0, '6675', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (44, 3, '啬园校区-四楼-中文现刊阅览室', NULL, '2025-06-02 08:16:39', 0, '6657', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (45, 3, '啬园校区-四楼-外文书刊阅览室', NULL, '2025-06-02 08:16:39', 0, '6656', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (46, 3, '啬园校区-四楼-电子阅览室一', NULL, '2025-06-02 08:16:39', 0, '6676', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (47, 3, '啬园校区-四楼-电子阅览室二', NULL, '2025-06-02 08:16:39', 0, '6670', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (48, 3, '啬园校区-五楼-514阅览室', NULL, '2025-06-02 08:16:39', 0, '9866', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (49, 3, '啬园校区-五楼-516自修室', NULL, '2025-06-02 08:16:39', 0, '8258', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (50, 3, '启秀校区-一楼-启秀工具书阅览室', NULL, '2025-06-02 08:16:39', 0, '6626', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (51, 3, '启秀校区-二楼-启秀中外文现刊阅览室', NULL, '2025-06-02 08:16:39', 0, '6711', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (52, 3, '启秀校区- 三楼-自修阅览室', NULL, '2025-06-02 08:16:39', 0, '9486', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (53, 3, '启东校区-二楼-201书库', NULL, '2025-06-02 08:16:39', 1, '6858', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (54, 3, '启东校区-二楼-203阅览室', NULL, '2025-06-02 08:16:39', 0, '6859', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (55, 3, '启东校区-二楼-205书库', NULL, '2025-06-02 08:16:39', 0, '6860', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (56, 3, '启东校区-三楼-301书库', NULL, '2025-06-02 08:16:39', 1, '6861', NULL, '123', 1);
INSERT INTO `rooms` VALUES (57, 3, '启东校区-三楼-303阅览室', NULL, '2025-06-02 08:16:39', 1, '6862', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (58, 3, '启东校区-三楼-305书库', NULL, '2025-06-02 08:16:39', 1, '6863', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (59, 3, '启东校区-四楼-401书库', NULL, '2025-06-02 08:16:39', 0, '6864', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (60, 3, '启东校区-四楼-403阅览室', NULL, '2025-06-02 08:16:39', 0, '6865', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (61, 3, '启东校区-四楼-405书库', NULL, '2025-06-02 08:16:39', 0, '6866', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (62, 3, '启东校区-四楼-四楼大厅', NULL, '2025-06-02 08:16:39', 0, '6867', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (63, 4, 'A区一楼A103-电子阅览室(现场扫码)-带电源的区域可携带笔记本', NULL, '2025-06-02 08:16:39', 0, '4990', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (64, 4, 'A区一楼-A104-自主学习室', NULL, '2025-06-02 08:16:39', 0, '4991', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (65, 4, 'A区二楼-A202-中文社科图书借阅室', NULL, '2025-06-02 08:16:39', 0, '4994', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (66, 4, 'A区二楼-A203-自主学习室', NULL, '2025-06-02 08:16:39', 0, '4995', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (67, 4, 'A区二楼-A205-自主学习室', NULL, '2025-06-02 08:16:39', 0, '4996', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (68, 4, 'A区二楼-AB区连廊-自主学习座', NULL, '2025-06-02 08:16:39', 0, '4997', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (69, 4, 'A区三楼-A301-外文图书期刊阅览室', NULL, '2025-06-02 08:16:39', 0, '4999', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (70, 4, 'A区三楼-A302-自主学习室', NULL, '2025-06-02 08:16:39', 0, '5000', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (71, 4, 'A区三楼-A304-中文自然科学期刊阅览室', NULL, '2025-06-02 08:16:39', 0, '5001', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (72, 4, 'A区三楼-A305-自主学习室', NULL, '2025-06-02 08:16:39', 0, '5002', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (73, 4, 'A区三楼-大厅-自主学习座', NULL, '2025-06-02 08:16:39', 0, '5003', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (74, 4, 'A区四楼-A406-中文社科报刊阅览室', NULL, '2025-06-02 08:16:39', 0, '5005', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (75, 4, 'A区四楼-大厅-自主学习座', NULL, '2025-06-02 08:16:39', 0, '5006', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (76, 4, 'A区四楼A401-A401（1）-创新班学习空间', NULL, '2025-06-02 08:16:39', 0, '9085', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (77, 4, 'A区二楼-大厅-休闲阅览区', NULL, '2025-06-02 08:16:39', 0, '10075', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (78, 5, '一楼-公共空间', NULL, '2025-06-02 08:16:39', 0, '4690', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (79, 5, '一楼-科技库本书阅览室', NULL, '2025-06-02 08:16:39', 0, '2575', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (80, 5, '二楼-社科库本书阅览室', NULL, '2025-06-02 08:16:39', 0, '2576', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (81, 5, '二楼-电子阅览室', NULL, '2025-06-02 08:16:39', 0, '2577', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (82, 5, '三楼-大厅公共空间', NULL, '2025-06-02 08:16:39', 0, '2579', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (83, 5, '三楼-科技书借阅室A区', NULL, '2025-06-02 08:16:39', 0, '2580', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (84, 5, '三楼-科技书借阅室B区', NULL, '2025-06-02 08:16:39', 0, '2581', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (85, 5, '三楼-科技书借阅室C区', NULL, '2025-06-02 08:16:39', 0, '2582', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (86, 5, '四楼-大厅公共空间', NULL, '2025-06-02 08:16:39', 0, '2583', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (87, 5, '四楼-社科书借阅室A区', NULL, '2025-06-02 08:16:39', 0, '2584', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (88, 5, '四楼-社科书借阅室B区', NULL, '2025-06-02 08:16:39', 0, '2585', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (89, 5, '四楼-社科书借阅室C区', NULL, '2025-06-02 08:16:39', 0, '2586', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (90, 5, '四楼-四楼自习室', NULL, '2025-06-02 08:16:39', 0, '2587', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (91, 5, '五楼-报刊阅览室', NULL, '2025-06-02 08:16:39', 0, '2588', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (92, 5, '五楼-大厅公共空间', NULL, '2025-06-02 08:16:39', 0, '2589', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (93, 5, '五楼-五楼自习室', NULL, '2025-06-02 08:16:39', 0, '2590', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (94, 6, '常青校区馆-四楼南侧-阅览室', NULL, '2025-06-02 08:16:39', 0, '7029', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (95, 6, '常青校区馆-四楼北侧-阅览室', NULL, '2025-06-02 08:16:39', 0, '7028', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (96, 6, '常青校区馆-五楼北侧-阅览室', NULL, '2025-06-02 08:16:39', 0, '7030', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (97, 6, '常青校区馆-七楼-阅览室', NULL, '2025-06-02 08:16:39', 0, '7034', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (98, 6, '金银湖校区馆-一楼-阅览室', NULL, '2025-06-02 08:16:39', 0, '7043', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (99, 6, '金银湖校区馆-二楼-阅览室', NULL, '2025-06-02 08:16:39', 0, '7044', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (100, 6, '金银湖校区馆-三楼-阅览室', NULL, '2025-06-02 08:16:39', 0, '7045', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (101, 6, '金银湖校区馆-四楼东侧-阅览室', NULL, '2025-06-02 08:16:39', 0, '7046', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (102, 6, '金银湖校区馆-六楼-阅览室', NULL, '2025-06-02 08:16:39', 0, '7047', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (103, 6, '金银湖校区馆-七楼-阅览室', NULL, '2025-06-02 08:16:39', 0, '7048', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (104, 7, '图书馆-一楼-自习区', 4.0, '2025-06-02 08:16:39', 0, '8988', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (105, 7, '图书馆-二楼-自习区', 4.0, '2025-06-02 08:16:39', 0, '6760', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (106, 7, '图书馆-三楼-自习区', 4.0, '2025-06-02 08:16:39', 0, '8076', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (107, 7, '图书馆-四楼-自习区', 4.0, '2025-06-02 08:16:39', 0, '8077', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (108, 7, '图书馆-五楼-自习区', 4.0, '2025-06-02 08:16:39', 0, '8078', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (109, 8, '西校区图书馆-一楼东学生自修区（101室）', 4.0, '2025-06-02 08:16:39', 0, '6190', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (110, 8, '西校区图书馆-一楼西学生自修区（102室）', 4.0, '2025-06-02 08:16:39', 0, '6191', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (111, 8, '西校区图书馆-二楼期刊借阅服务区（201室）', 4.0, '2025-06-02 08:16:39', 0, '6192', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (112, 8, '西校区图书馆-五楼学生自修区', 4.0, '2025-06-02 08:16:39', 0, '8075', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (113, 8, '东校区图书馆-学生自修区', 4.0, '2025-06-02 08:16:39', 0, '8173', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (114, 9, '安顺图书中心-四楼-(2405)自修室', 12.0, '2025-06-02 08:16:39', 0, '6258', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (115, 9, '安顺图书中心-五楼-(2505)自修室', 12.0, '2025-06-02 08:16:39', 0, '6259', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (116, 9, '主校区-五楼-北厅', 12.0, '2025-06-02 08:16:39', 0, '6295', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (117, 9, '主校区-四楼-北厅', 12.0, '2025-06-02 08:16:39', 0, '6296', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (118, 9, '主校区-三楼-北厅', 12.0, '2025-06-02 08:16:39', 0, '6297', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (119, 9, '主校区-一楼-107自修室', 12.0, '2025-06-02 08:16:39', 0, '6298', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (120, 9, '主校区-一楼-101自修室', 12.0, '2025-06-02 08:16:39', 0, '6299', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (121, 9, '主校区-二楼-北厅', 12.0, '2025-06-02 08:16:39', 0, '10346', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (122, 9, '主校区-一楼-102自修室', 12.0, '2025-06-02 08:16:39', 0, '10347', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (123, 9, '主校区-一楼-106', 12.0, '2025-06-02 08:16:39', 0, '11540', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (124, 10, '新长校区-七楼-自习室', NULL, '2025-06-02 08:16:39', 0, '6389', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (125, 10, '新长校区-五楼-自习室', NULL, '2025-06-02 08:16:39', 0, '6385', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (126, 10, '新长校区-三楼-自习室（电子阅览室）', NULL, '2025-06-02 08:16:39', 0, '6381', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (127, 10, '新长校区-三楼-自习室（信息共享空间）', NULL, '2025-06-02 08:16:39', 0, '6380', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (128, 10, '通榆校区-一楼-自习室', NULL, '2025-06-02 08:16:39', 0, '6390', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (129, 10, '通榆校区-三楼-自习室', NULL, '2025-06-02 08:16:39', 0, '6391', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (130, 10, '通榆校区-综合楼六楼-自习室', NULL, '2025-06-02 08:16:39', 0, '6392', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (131, 11, '仓山-一楼-大厅', 5.0, '2025-06-02 08:16:39', 0, '3916', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (132, 11, '仓山-二楼-综合书库', 5.0, '2025-06-02 08:16:39', 0, '3919', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (133, 11, '仓山-二楼-阅览厅', 5.0, '2025-06-02 08:16:39', 0, '4018', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (134, 11, '仓山-三楼-阅览厅', 5.0, '2025-06-02 08:16:39', 0, '3923', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (135, 11, '24小时书房-一楼-24小时书房', 5.0, '2025-06-02 08:16:39', 0, '4036', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (136, 11, '旗山-二楼-知明学习室', 5.0, '2025-06-02 08:16:39', 0, '3929', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (137, 11, '旗山-二楼-行笃学习室', 5.0, '2025-06-02 08:16:39', 0, '6425', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (138, 11, '旗山-三楼-立诚学习室', 5.0, '2025-06-02 08:16:39', 0, '4019', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (139, 11, '旗山-三楼-过道阅览区', 5.0, '2025-06-02 08:16:39', 0, '3928', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (140, 11, '旗山-三楼-致广学习室', 5.0, '2025-06-02 08:16:39', 0, '6061', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (141, 11, '旗山-四楼-中文图书厅库', 5.0, '2025-06-02 08:16:39', 0, '3927', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (142, 11, '旗山-五楼-中文图书厅库', 5.0, '2025-06-02 08:16:39', 0, '3926', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (143, 11, '旗山-七楼-中外文图书厅库', 5.0, '2025-06-02 08:16:39', 0, '3925', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (144, 11, '仓山-四楼-中文图书样本库', 5.0, '2025-06-02 08:16:39', 0, '3924', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (145, 11, '协和学院图书馆-一楼- 数字阅读区', 5.0, '2025-06-02 08:16:39', 0, '12041', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (146, 11, '协和学院图书馆-一楼-一楼休闲阅读区', 5.0, '2025-06-02 08:16:39', 0, '12042', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (147, 11, '协和学院图书馆-一楼-一楼自习一区', 5.0, '2025-06-02 08:16:39', 0, '12043', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (148, 11, '协和学院图书馆-一楼-一楼自习二区', 5.0, '2025-06-02 08:16:39', 0, '12044', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (149, 11, '协和学院图书馆-一楼-一楼卡座', 5.0, '2025-06-02 08:16:39', 0, '12045', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (150, 11, '协和学院图书馆-二楼-二楼第四书库', 5.0, '2025-06-02 08:16:39', 0, '12046', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (151, 11, '协和学院图书馆-二楼-二楼第五书库', 5.0, '2025-06-02 08:16:39', 0, '12047', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (152, 11, '协和学院图书馆-二楼-二楼学生阅读厅（202）', 5.0, '2025-06-02 08:16:39', 0, '12048', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (153, 11, '协和学院图书馆-二楼-二楼中厅', 5.0, '2025-06-02 08:16:39', 0, '12049', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (154, 11, '协和学院图书馆-三楼-三楼第一书库', 5.0, '2025-06-02 08:16:39', 0, '12050', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (155, 11, '协和学院图书馆-三楼-三楼第三书库', 5.0, '2025-06-02 08:16:39', 0, '12051', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (156, 11, '协和学院图书馆-三楼-三楼中厅', 5.0, '2025-06-02 08:16:39', 0, '12052', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (157, 12, '石湖校区图书馆-一楼西面-自修室(一)(108)', 12.0, '2025-06-02 08:16:39', 0, '9781', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (158, 12, '石湖校区图书馆-一楼西面-自修室(二)(109)一楼', 12.0, '2025-06-02 08:16:39', 0, '9782', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (159, 12, '石湖校区图书馆-一楼西面-自修室(二)(109)二楼', 12.0, '2025-06-02 08:16:39', 0, '9783', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (160, 12, '石湖校区图书馆-一楼大厅-有声普法驿站', 12.0, '2025-06-02 08:16:39', 0, '10294', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (161, 12, '石湖校区图书馆-一楼-茶吧', 12.0, '2025-06-02 08:16:39', 0, '10295', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (162, 12, '石湖校区图书馆-二楼-公共阅览区', 12.0, '2025-06-02 08:16:39', 0, '9779', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (163, 12, '石湖校区图书馆-二楼-社会科学图书借阅室（一）', 12.0, '2025-06-02 08:16:39', 0, '9780', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (164, 12, '石湖校区图书馆-三楼-公共阅览区', 12.0, '2025-06-02 08:16:39', 0, '9639', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (165, 12, '石湖校区图书馆-三楼-社会科学图书借阅室（二）', 12.0, '2025-06-02 08:16:39', 0, '9741', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (166, 12, '石湖校区图书馆-三楼-社会科学图书借阅室（三）', 12.0, '2025-06-02 08:16:39', 0, '9758', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (167, 12, '石湖校区图书馆-三楼-社会科学图书借阅室（四）', 12.0, '2025-06-02 08:16:39', 0, '9759', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (168, 12, '石湖校区图书馆-四楼-公共阅览区', 12.0, '2025-06-02 08:16:39', 0, '9760', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (169, 12, '石湖校区图书馆-四楼-外文图书借阅室', 12.0, '2025-06-02 08:16:39', 0, '9761', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (170, 12, '石湖校区图书馆-四楼-期刊阅览室', 12.0, '2025-06-02 08:16:39', 0, '9762', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (171, 12, '石湖校区图书馆-四楼-自然科学图书借阅室', 12.0, '2025-06-02 08:16:39', 0, '9763', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (172, 12, '石湖校区图书馆-四楼-期刊阅览室（外文期刊阅览区）', 12.0, '2025-06-02 08:16:39', 0, '10293', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (173, 12, '石湖校区图书馆-五楼-自修室(三)(501-4)', 12.0, '2025-06-02 08:16:39', 0, '10713', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (174, 12, '天平学院图书馆-一楼-自修室', 12.0, '2025-06-02 08:16:39', 0, '12131', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (175, 13, '蚌埠医科大学龙子湖校区图书馆-4F-自习室', NULL, '2025-06-02 08:16:39', 0, '2612', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (176, 13, '蚌埠医科大学龙子湖校区图书馆-3F-三楼大厅', NULL, '2025-06-02 08:16:39', 0, '5811', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (177, 13, '蚌埠医科大学龙子湖校区图书馆-4F-四楼大厅', NULL, '2025-06-02 08:16:39', 0, '5813', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (178, 13, '蚌埠医科大学龙子湖校区图书馆-1F-一楼朗读厅', NULL, '2025-06-02 08:16:39', 0, '6819', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (179, 13, '蚌埠医科大学龙子湖校区图书馆-二楼-沉浸式考研自习室', NULL, '2025-06-02 08:16:39', 0, '8144', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (180, 13, '蚌埠医科大学龙子湖校区图书馆-二楼-沉浸式自习室', NULL, '2025-06-02 08:16:39', 0, '8145', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (181, 14, '西北政法大学雁塔校区图书馆-2F-典一阅览室', 4.0, '2025-06-02 08:16:39', 0, '4693', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (182, 14, '西北政法大学雁塔校区图书馆-2F-法律书库（二层书库）', 4.0, '2025-06-02 08:16:39', 0, '4694', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (183, 14, '西北政法大学雁塔校区图书馆-2F-外文阅览室', 4.0, '2025-06-02 08:16:39', 0, '4695', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (184, 14, '西北政法大学雁塔校区图书馆-2F-马列社科阅览室', 4.0, '2025-06-02 08:16:39', 0, '4697', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (185, 14, '西北政法大学雁塔校区图书馆-3F-法律二（三层书库）', 4.0, '2025-06-02 08:16:39', 0, '4704', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (186, 14, '西北政法大学雁塔校区图书馆-4F-典二阅览室', 4.0, '2025-06-02 08:16:39', 0, '4698', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (187, 14, '西北政法大学雁塔校区图书馆-4F-新书阅览室', 4.0, '2025-06-02 08:16:39', 0, '4699', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (188, 14, '西北政法大学雁塔校区图书馆-4F-中文现刊阅览室', 4.0, '2025-06-02 08:16:39', 0, '4700', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (189, 14, '西北政法大学雁塔校区图书馆-4F-哲学图书阅览室', 4.0, '2025-06-02 08:16:39', 0, '4702', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (190, 14, '西北政法大学雁塔校区图书馆-4F-自习大厅', 4.0, '2025-06-02 08:16:39', 0, '4703', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (191, 14, '西北政法大学雁塔校区图书馆-4F-政治与文教（四层书库）书库', 4.0, '2025-06-02 08:16:39', 0, '4705', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (192, 14, '西北政法大学雁塔校区图书馆-5F-经济图书（五层书库）', 4.0, '2025-06-02 08:16:39', 0, '4706', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (193, 14, '西北政法大学雁塔校区图书馆-6F-语言与历史地理（六层书库）', 4.0, '2025-06-02 08:16:39', 0, '4707', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (194, 15, '武侯校区图书馆-1F-A1阅览室', NULL, '2025-06-02 08:16:39', 0, '6641', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (195, 15, '武侯校区图书馆-1F-A2阅览室', NULL, '2025-06-02 08:16:39', 0, '6642', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (196, 15, '武侯校区图书馆-1F-电子阅览室', NULL, '2025-06-02 08:16:39', 0, '6646', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (197, 15, '武侯校区图书馆-2F-现期期刊阅览室', NULL, '2025-06-02 08:16:39', 0, '7157', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (198, 15, '武侯校区图书馆-3F-A3阅览室（学生阅览室）', NULL, '2025-06-02 08:16:39', 0, '7158', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (199, 15, '武侯校区图书馆-3F-民族文献阅览室', NULL, '2025-06-02 08:16:39', 0, '7159', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (200, 15, '武侯校区图书馆-4F-A4阅览室（学生阅览室）', NULL, '2025-06-02 08:16:39', 0, '7160', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (201, 15, '航空港校区图书馆-3F-圆弧区阅览室', NULL, '2025-06-02 08:16:39', 0, '9671', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (202, 15, '航空港校区图书馆-3F-连廊区阅览室', NULL, '2025-06-02 08:16:39', 0, '9672', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (203, 15, '航空港校区图书馆-2F-圆弧区阅览室', NULL, '2025-06-02 08:16:39', 0, '9988', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (204, 15, '航空港校区图书馆-2F-连廊区阅览室', NULL, '2025-06-02 08:16:39', 0, '9989', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (205, 15, '航空港校区图书馆-2F-南馆阅览室', NULL, '2025-06-02 08:16:39', 0, '9990', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (206, 15, '太平园校区图书馆-1F-第一阅览室', NULL, '2025-06-02 08:16:39', 0, '11074', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (207, 15, '航空港校区图书馆-1F-北馆铸牢学习园地阅览室', NULL, '2025-06-02 08:16:39', 0, '11107', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (208, 16, '四楼借阅室', 7.0, '2025-06-02 08:16:39', 0, '8343', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (209, 16, '三楼借阅室', 7.0, '2025-06-02 08:16:39', 0, '8365', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (210, 16, '一楼借阅室', 7.0, '2025-06-02 08:16:39', 0, '8366', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (211, 16, '二楼大楼梯旁', 7.0, '2025-06-02 08:16:39', 0, '5761', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (212, 16, '二楼边廊圆桌', 7.0, '2025-06-02 08:16:39', 0, '5763', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (213, 16, '二楼考研自习室', 7.0, '2025-06-02 08:16:39', 0, '6487', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (214, 16, '三楼公共区圆桌', 7.0, '2025-06-02 08:16:39', 0, '8244', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (215, 16, '三楼前台折叠椅', 7.0, '2025-06-02 08:16:39', 0, '9060', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (216, 16, '电子阅览室自习区', 7.0, '2025-06-02 08:16:39', 0, '8300', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (217, 16, '电子阅览室电子阅览区', 7.0, '2025-06-02 08:16:39', 0, '11219', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (218, 17, '连云港市图书馆-一楼大自修室日间场次', NULL, '2025-06-02 08:16:39', 0, '6511', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (219, 17, '连云港市图书馆-一楼大自修室晚间场次', NULL, '2025-06-02 08:16:39', 0, '6512', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (220, 17, '连云港市图书馆-一楼北自修室日间场次', NULL, '2025-06-02 08:16:39', 0, '11019', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (221, 17, '连云港市图书馆-一楼北自修室晚间场次', NULL, '2025-06-02 08:16:39', 0, '11020', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (222, 17, '连云港市图书馆-一楼南自修室日间场次', NULL, '2025-06-02 08:16:39', 0, '11021', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (223, 17, '连云港市图书馆-一楼南自修室晚间场次', NULL, '2025-06-02 08:16:39', 0, '11022', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (224, 18, '文学一[二楼南]', 3.0, '2025-06-02 08:16:39', 0, '2170', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (225, 18, '文学二[三楼北]', 3.0, '2025-06-02 08:16:39', 0, '2172', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (226, 18, '自科一[三楼西]', 3.0, '2025-06-02 08:16:39', 0, '2173', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (227, 18, '自科二[三楼南]', 3.0, '2025-06-02 08:16:39', 0, '2174', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (228, 18, '社科一[四楼南]', 3.0, '2025-06-02 08:16:39', 0, '2175', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (229, 18, '公共区域[四楼中]', 3.0, '2025-06-02 08:16:39', 0, '2177', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (230, 18, '横江校区[三楼西]', 3.0, '2025-06-02 08:16:39', 0, '2178', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (231, 18, '横江校区[三楼东]', 3.0, '2025-06-02 08:16:39', 0, '2179', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (232, 18, '外文[四楼北]', 3.0, '2025-06-02 08:16:39', 0, '4904', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (233, 18, '自习室一[一楼西]', 3.0, '2025-06-02 08:16:39', 0, '5792', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (234, 18, '自习室二[一楼北]', 3.0, '2025-06-02 08:16:39', 0, '5793', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (235, 18, '建筑艺术[二楼北]', 3.0, '2025-06-02 08:16:39', 0, '9080', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (236, 18, '自科三[五楼北]', 3.0, '2025-06-02 08:16:39', 0, '9081', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (237, 18, '自习室三[一楼西]', 3.0, '2025-06-02 08:16:39', 0, '9082', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (238, 18, '社科二[四楼北]', 3.0, '2025-06-02 08:16:39', 0, '9083', NULL, NULL, 0);
INSERT INTO `rooms` VALUES (239, 18, '听松学习空间[二楼西]', 3.0, '2025-06-02 08:16:39', 0, '9086', NULL, NULL, 1);
INSERT INTO `rooms` VALUES (240, 1, '测试房间1', 8.0, '2025-06-05 11:09:34', 0, '0001', '测试1', NULL, 0);

-- ----------------------------
-- Table structure for schools
-- ----------------------------
DROP TABLE IF EXISTS `schools`;
CREATE TABLE `schools`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '学校名称',
  `created_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `wait_time` decimal(3, 2) NOT NULL DEFAULT 0.50 COMMENT '瀛︽牎棰勭害鎿嶄綔绛夊緟鏃堕棿锛堢?锛',
  PRIMARY KEY (`id`) USING BTREE,
  CONSTRAINT `chk_wait_time` CHECK ((`wait_time` >= 0.10) and (`wait_time` <= 1.00))
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '学校表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of schools
-- ----------------------------
INSERT INTO `schools` VALUES (1, '九江学院', '2025-06-02 08:16:38', 0.10);
INSERT INTO `schools` VALUES (2, '兰州城市学院', '2025-06-02 08:16:38', 0.50);
INSERT INTO `schools` VALUES (3, '南通大学', '2025-06-02 08:16:38', 0.50);
INSERT INTO `schools` VALUES (4, '吉林农业大学', '2025-06-02 08:16:38', 0.50);
INSERT INTO `schools` VALUES (5, '天津职业技术师范大学', '2025-06-02 08:16:38', 0.50);
INSERT INTO `schools` VALUES (6, '武汉轻工业大学', '2025-06-02 08:16:38', 0.50);
INSERT INTO `schools` VALUES (7, '河南城建大学', '2025-06-02 08:16:38', 0.50);
INSERT INTO `schools` VALUES (8, '湖南医药学院', '2025-06-02 08:16:38', 0.50);
INSERT INTO `schools` VALUES (9, '潍坊学院', '2025-06-02 08:16:38', 0.50);
INSERT INTO `schools` VALUES (10, '盐城师范大学', '2025-06-02 08:16:38', 0.50);
INSERT INTO `schools` VALUES (11, '福建师范大学', '2025-06-02 08:16:38', 0.50);
INSERT INTO `schools` VALUES (12, '苏州科技大学', '2025-06-02 08:16:38', 0.50);
INSERT INTO `schools` VALUES (13, '蚌埠医科大学', '2025-06-02 08:16:38', 0.50);
INSERT INTO `schools` VALUES (14, '西北政法大学雁塔校区', '2025-06-02 08:16:38', 0.50);
INSERT INTO `schools` VALUES (15, '西南民族大学', '2025-06-02 08:16:38', 0.50);
INSERT INTO `schools` VALUES (16, '达康学院', '2025-06-02 08:16:38', 0.50);
INSERT INTO `schools` VALUES (17, '连云港图书馆', '2025-06-02 08:16:38', 0.50);
INSERT INTO `schools` VALUES (18, '黄山学院', '2025-06-02 08:16:38', 0.15);

-- ----------------------------
-- Table structure for task_logs
-- ----------------------------
DROP TABLE IF EXISTS `task_logs`;
CREATE TABLE `task_logs`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '鏃ュ織ID',
  `reservation_id` bigint NULL DEFAULT NULL COMMENT '预约记录ID（心跳日志可为空）',
  `task_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '任务ID（心跳日志可为空）',
  `worker_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '鎵ц?鏈嶅姟鍣↖D',
  `log_type` enum('TASK_CREATED','TASK_ASSIGNED','TASK_START','TASK_PROGRESS','TASK_COMPLETE','TASK_ERROR','TASK_RETRY','HEARTBEAT') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '鏃ュ織绫诲瀷',
  `log_level` enum('DEBUG','INFO','WARN','ERROR') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'INFO' COMMENT '鏃ュ織绾у埆',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '鏃ュ織娑堟伅',
  `execution_time` bigint NULL DEFAULT NULL COMMENT '鎵ц?鑰楁椂(姣??)',
  `memory_usage` bigint NULL DEFAULT NULL COMMENT '鍐呭瓨浣跨敤閲?瀛楄妭)',
  `cpu_usage` decimal(5, 2) NULL DEFAULT NULL COMMENT 'CPU浣跨敤鐜?%)',
  `extra_data` json NULL COMMENT '鎵╁睍鏁版嵁',
  `stack_trace` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '閿欒?鍫嗘爤(浠呴敊璇?棩蹇?',
  `created_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '鍒涘缓鏃堕棿',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_task_logs_reservation_id`(`reservation_id` ASC) USING BTREE,
  INDEX `idx_task_logs_task_id`(`task_id` ASC) USING BTREE,
  INDEX `idx_task_logs_worker_id`(`worker_id` ASC) USING BTREE,
  INDEX `idx_task_logs_log_type`(`log_type` ASC) USING BTREE,
  INDEX `idx_task_logs_created_time`(`created_time` ASC) USING BTREE,
  CONSTRAINT `task_logs_ibfk_1` FOREIGN KEY (`reservation_id`) REFERENCES `reservations` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 74 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '浠诲姟鎵ц?鏃ュ織琛' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of task_logs
-- ----------------------------
INSERT INTO `task_logs` VALUES (1, NULL, NULL, 'worker-003', 'HEARTBEAT', 'DEBUG', '服务器注册: ?????', NULL, NULL, NULL, NULL, NULL, '2025-06-19 20:23:26');
INSERT INTO `task_logs` VALUES (2, NULL, NULL, 'worker-004', 'HEARTBEAT', 'DEBUG', '服务器注册: ?????2', NULL, NULL, NULL, NULL, NULL, '2025-06-19 20:24:19');
INSERT INTO `task_logs` VALUES (3, NULL, NULL, 'worker-003', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-19 20:26:00');
INSERT INTO `task_logs` VALUES (4, NULL, NULL, 'worker-004', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-19 20:27:00');
INSERT INTO `task_logs` VALUES (5, NULL, NULL, 'worker-005', 'HEARTBEAT', 'DEBUG', '服务器注册: 本地1', NULL, NULL, NULL, NULL, NULL, '2025-06-19 20:31:16');
INSERT INTO `task_logs` VALUES (6, NULL, NULL, 'worker-005', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-19 20:34:00');
INSERT INTO `task_logs` VALUES (7, NULL, NULL, 'worker-006', 'HEARTBEAT', 'DEBUG', '服务器注册: ???????', NULL, NULL, NULL, NULL, NULL, '2025-06-19 20:38:24');
INSERT INTO `task_logs` VALUES (8, NULL, NULL, 'worker-007', 'HEARTBEAT', 'DEBUG', '服务器注册: 测试中文服务器2', NULL, NULL, NULL, NULL, NULL, '2025-06-19 20:40:20');
INSERT INTO `task_logs` VALUES (9, NULL, NULL, 'worker-006', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-19 20:40:41');
INSERT INTO `task_logs` VALUES (10, NULL, NULL, 'worker-001', 'HEARTBEAT', 'DEBUG', '服务器注册: 主服务器', NULL, NULL, NULL, NULL, NULL, '2025-06-19 20:42:36');
INSERT INTO `task_logs` VALUES (11, NULL, NULL, 'worker-002', 'HEARTBEAT', 'DEBUG', '服务器注册: 备用服务器', NULL, NULL, NULL, NULL, NULL, '2025-06-19 20:42:36');
INSERT INTO `task_logs` VALUES (12, NULL, NULL, 'worker-003', 'HEARTBEAT', 'DEBUG', '服务器注册: 测试服务器', NULL, NULL, NULL, NULL, NULL, '2025-06-19 20:42:36');
INSERT INTO `task_logs` VALUES (13, NULL, NULL, 'worker-004', 'HEARTBEAT', 'DEBUG', '服务器注册: 主服务器', NULL, NULL, NULL, NULL, NULL, '2025-06-19 20:43:04');
INSERT INTO `task_logs` VALUES (14, NULL, NULL, 'worker-005', 'HEARTBEAT', 'DEBUG', '服务器注册: 备用服务器', NULL, NULL, NULL, NULL, NULL, '2025-06-19 20:43:16');
INSERT INTO `task_logs` VALUES (15, NULL, NULL, 'worker-001', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-19 20:44:53');
INSERT INTO `task_logs` VALUES (16, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-19 20:44:53');
INSERT INTO `task_logs` VALUES (17, NULL, NULL, 'worker-003', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-19 20:44:53');
INSERT INTO `task_logs` VALUES (18, NULL, NULL, 'worker-001', 'HEARTBEAT', 'DEBUG', '状态更新: ONLINE, 负载: 0/30', NULL, NULL, NULL, NULL, NULL, '2025-06-19 21:35:13');
INSERT INTO `task_logs` VALUES (19, NULL, NULL, 'worker-001', 'HEARTBEAT', 'DEBUG', '状态更新: ONLINE, 负载: 0/30', NULL, NULL, NULL, NULL, NULL, '2025-06-19 21:35:17');
INSERT INTO `task_logs` VALUES (20, NULL, NULL, 'worker-001', 'HEARTBEAT', 'DEBUG', '状态更新: ONLINE, 负载: 0/30', NULL, NULL, NULL, NULL, NULL, '2025-06-19 21:35:20');
INSERT INTO `task_logs` VALUES (21, NULL, NULL, 'worker-002', 'HEARTBEAT', 'DEBUG', '状态更新: OFFLINE, 负载: 0/20', NULL, NULL, NULL, NULL, NULL, '2025-06-19 21:35:20');
INSERT INTO `task_logs` VALUES (22, NULL, NULL, 'worker-001', 'HEARTBEAT', 'DEBUG', '状态更新: ONLINE, 负载: 0/30', NULL, NULL, NULL, NULL, NULL, '2025-06-19 21:35:22');
INSERT INTO `task_logs` VALUES (23, NULL, NULL, 'worker-001', 'HEARTBEAT', 'DEBUG', '状态更新: ONLINE, 负载: 0/30', NULL, NULL, NULL, NULL, NULL, '2025-06-19 21:36:33');
INSERT INTO `task_logs` VALUES (24, NULL, NULL, 'worker-001', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-19 21:39:19');
INSERT INTO `task_logs` VALUES (25, NULL, NULL, 'worker-001', 'HEARTBEAT', 'DEBUG', '状态更新: ONLINE, 负载: 0/30', NULL, NULL, NULL, NULL, NULL, '2025-06-19 21:52:26');
INSERT INTO `task_logs` VALUES (26, NULL, NULL, 'worker-002', 'HEARTBEAT', 'DEBUG', '状态更新: OFFLINE, 负载: 0/20', NULL, NULL, NULL, NULL, NULL, '2025-06-19 21:52:28');
INSERT INTO `task_logs` VALUES (27, NULL, NULL, 'worker-001', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-19 21:55:19');
INSERT INTO `task_logs` VALUES (28, NULL, NULL, 'worker-001', 'HEARTBEAT', 'DEBUG', '状态更新: ONLINE, 负载: 0/30', NULL, NULL, NULL, NULL, NULL, '2025-06-19 22:56:36');
INSERT INTO `task_logs` VALUES (29, NULL, NULL, 'worker-002', 'HEARTBEAT', 'DEBUG', '状态更新: ONLINE, 负载: 0/20', NULL, NULL, NULL, NULL, NULL, '2025-06-19 22:56:38');
INSERT INTO `task_logs` VALUES (30, NULL, NULL, 'worker-003', 'HEARTBEAT', 'DEBUG', '状态更新: OFFLINE, 负载: 0/15', NULL, NULL, NULL, NULL, NULL, '2025-06-19 22:56:43');
INSERT INTO `task_logs` VALUES (31, NULL, NULL, 'worker-001', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-19 22:59:29');
INSERT INTO `task_logs` VALUES (32, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-19 22:59:29');
INSERT INTO `task_logs` VALUES (33, NULL, NULL, 'worker-001', 'HEARTBEAT', 'DEBUG', '状态更新: ONLINE, 负载: 0/30', NULL, NULL, NULL, NULL, NULL, '2025-06-20 01:13:29');
INSERT INTO `task_logs` VALUES (34, NULL, NULL, 'worker-002', 'HEARTBEAT', 'DEBUG', '状态更新: ONLINE, 负载: 0/20', NULL, NULL, NULL, NULL, NULL, '2025-06-20 01:13:31');
INSERT INTO `task_logs` VALUES (35, NULL, NULL, 'worker-001', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-20 01:16:29');
INSERT INTO `task_logs` VALUES (36, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-20 01:16:29');
INSERT INTO `task_logs` VALUES (37, NULL, NULL, 'worker-001', 'HEARTBEAT', 'DEBUG', '状态更新: OFFLINE, 负载: 0/30', NULL, NULL, NULL, NULL, NULL, '2025-06-20 09:29:25');
INSERT INTO `task_logs` VALUES (38, NULL, NULL, 'worker-002', 'HEARTBEAT', 'DEBUG', '状态更新: OFFLINE, 负载: 0/20', NULL, NULL, NULL, NULL, NULL, '2025-06-20 09:29:27');
INSERT INTO `task_logs` VALUES (39, NULL, NULL, 'worker-003', 'HEARTBEAT', 'DEBUG', '状态更新: OFFLINE, 负载: 0/15', NULL, NULL, NULL, NULL, NULL, '2025-06-20 09:29:27');
INSERT INTO `task_logs` VALUES (40, NULL, NULL, 'worker-002', 'HEARTBEAT', 'DEBUG', '状态更新: ONLINE, 负载: 0/20', NULL, NULL, NULL, NULL, NULL, '2025-06-20 09:43:41');
INSERT INTO `task_logs` VALUES (41, NULL, NULL, 'worker-001', 'HEARTBEAT', 'DEBUG', '状态更新: OFFLINE, 负载: 0/30', NULL, NULL, NULL, NULL, NULL, '2025-06-20 09:43:44');
INSERT INTO `task_logs` VALUES (42, NULL, NULL, 'worker-003', 'HEARTBEAT', 'DEBUG', '状态更新: OFFLINE, 负载: 0/15', NULL, NULL, NULL, NULL, NULL, '2025-06-20 09:43:47');
INSERT INTO `task_logs` VALUES (43, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-20 09:46:24');
INSERT INTO `task_logs` VALUES (44, NULL, NULL, 'worker-002', 'HEARTBEAT', 'DEBUG', '状态更新: ONLINE, 负载: 0/20', NULL, NULL, NULL, NULL, NULL, '2025-06-20 10:20:10');
INSERT INTO `task_logs` VALUES (45, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-20 10:58:13');
INSERT INTO `task_logs` VALUES (46, NULL, NULL, 'worker-001', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 10:34:48');
INSERT INTO `task_logs` VALUES (47, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 10:35:48');
INSERT INTO `task_logs` VALUES (48, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 10:59:47');
INSERT INTO `task_logs` VALUES (49, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 11:10:47');
INSERT INTO `task_logs` VALUES (50, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 11:14:47');
INSERT INTO `task_logs` VALUES (51, NULL, NULL, 'worker-001', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 15:55:47');
INSERT INTO `task_logs` VALUES (52, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 15:55:47');
INSERT INTO `task_logs` VALUES (53, NULL, NULL, 'worker-001', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 15:59:47');
INSERT INTO `task_logs` VALUES (54, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 15:59:47');
INSERT INTO `task_logs` VALUES (55, NULL, NULL, 'worker-001', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 16:09:47');
INSERT INTO `task_logs` VALUES (56, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 16:09:47');
INSERT INTO `task_logs` VALUES (57, NULL, NULL, 'worker-001', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 16:21:47');
INSERT INTO `task_logs` VALUES (58, NULL, NULL, 'worker-001', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 16:30:59');
INSERT INTO `task_logs` VALUES (59, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 16:30:59');
INSERT INTO `task_logs` VALUES (60, NULL, NULL, 'worker-001', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 16:49:59');
INSERT INTO `task_logs` VALUES (61, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 17:09:45');
INSERT INTO `task_logs` VALUES (62, NULL, NULL, 'worker-001', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 17:10:45');
INSERT INTO `task_logs` VALUES (63, NULL, NULL, 'worker-001', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 19:03:04');
INSERT INTO `task_logs` VALUES (64, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 19:03:04');
INSERT INTO `task_logs` VALUES (65, NULL, NULL, 'worker-001', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 20:02:53');
INSERT INTO `task_logs` VALUES (66, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 20:02:53');
INSERT INTO `task_logs` VALUES (67, NULL, NULL, 'worker-001', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 20:20:09');
INSERT INTO `task_logs` VALUES (68, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 20:20:09');
INSERT INTO `task_logs` VALUES (69, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 21:37:32');
INSERT INTO `task_logs` VALUES (70, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 23:41:34');
INSERT INTO `task_logs` VALUES (71, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-21 23:45:45');
INSERT INTO `task_logs` VALUES (72, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-22 00:35:39');
INSERT INTO `task_logs` VALUES (73, NULL, NULL, 'worker-002', 'TASK_ERROR', 'ERROR', '服务器心跳超时，已设置为离线状态', NULL, NULL, NULL, NULL, NULL, '2025-06-22 08:53:48');

-- ----------------------------
-- Table structure for user_announcement_reads
-- ----------------------------
DROP TABLE IF EXISTS `user_announcement_reads`;
CREATE TABLE `user_announcement_reads`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '璁板綍ID',
  `user_id` bigint NOT NULL COMMENT '鐢ㄦ埛ID',
  `announcement_id` bigint NOT NULL COMMENT '鍏?憡ID',
  `read_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '闃呰?鏃堕棿',
  `is_dismissed` tinyint(1) NULL DEFAULT 0 COMMENT '鏄?惁宸插叧闂?脊绐',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_announcement`(`user_id` ASC, `announcement_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_announcement_id`(`announcement_id` ASC) USING BTREE,
  INDEX `idx_read_time`(`read_time` DESC) USING BTREE,
  CONSTRAINT `user_announcement_reads_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `user_announcement_reads_ibfk_2` FOREIGN KEY (`announcement_id`) REFERENCES `announcements` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '鐢ㄦ埛鍏?憡闃呰?璁板綍琛' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user_announcement_reads
-- ----------------------------
INSERT INTO `user_announcement_reads` VALUES (5, 24, 7, '2025-07-22 19:55:01', 1);

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '姓名',
  `role` enum('USER','ADMIN') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'USER' COMMENT '角色',
  `created_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `remaining_days` int NULL DEFAULT 1 COMMENT '鍓╀綑鍙??绾﹀ぉ鏁',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE,
  INDEX `idx_users_remaining_days`(`remaining_days` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES (1, 'csskrtao', 'Tsk20040301', 'Test User 1', 'USER', '2025-05-31 21:55:05', 13);
INSERT INTO `users` VALUES (12, 'test2', 'test123', 'Test User 2', 'USER', '2025-05-31 21:55:05', 0);
INSERT INTO `users` VALUES (13, 'test3', 'test123', 'telasi', 'USER', '2025-05-31 21:55:05', 19);
INSERT INTO `users` VALUES (14, 'admin', 'admin123', 'System Administrator', 'ADMIN', '2025-05-31 22:27:27', 25);
INSERT INTO `users` VALUES (15, 'test4', '123456', '陶承程', 'USER', '2025-06-01 15:25:04', 0);
INSERT INTO `users` VALUES (16, 'admin2', 'admin', 'Administrator', 'ADMIN', '2025-06-01 17:46:42', 30);
INSERT INTO `users` VALUES (18, 'testUser123', '123456', '????', 'USER', '2025-06-02 17:49:58', 346);
INSERT INTO `users` VALUES (19, 'testuser', 'password123', 'Test User', 'USER', '2025-06-02 18:37:22', 180);
INSERT INTO `users` VALUES (21, '15948168409', 'SYF20030517.', '吉农测试人员', 'USER', '2025-06-08 18:12:22', 91);
INSERT INTO `users` VALUES (23, '17328495433', 'asd123456', '猪王', 'USER', '2025-06-28 23:18:51', 90);
INSERT INTO `users` VALUES (24, '18755869972', 'tcc123698741', '陶承程', 'USER', '2025-06-30 09:45:49', 26);
INSERT INTO `users` VALUES (25, '18770789060', '770324gg', '九江校内人员', 'USER', '2025-07-02 17:40:38', 115);
INSERT INTO `users` VALUES (26, '15779424883', '13672259883jiang', '九江', 'USER', '2025-07-02 17:54:19', 360);
INSERT INTO `users` VALUES (27, '13915826118', '20021212Mjq@', '南通测试人员', 'USER', '2025-07-03 18:55:14', 94);
INSERT INTO `users` VALUES (28, '15262871256', '2983360209Ker', '南通测试人员2', 'USER', '2025-07-05 17:17:50', 6);
INSERT INTO `users` VALUES (29, '19851790609', 'pp406031pppp', '南通测试人员3', 'USER', '2025-07-05 17:25:59', 16);
INSERT INTO `users` VALUES (30, '17519229730', 'D1850429441', '吉农测试人员', 'USER', '2025-07-05 17:30:43', 0);
INSERT INTO `users` VALUES (31, '13225626606', 'qzy12345678', '黄山测试', 'USER', '2025-07-19 23:35:54', 28);

-- ----------------------------
-- Table structure for worker_servers
-- ----------------------------
DROP TABLE IF EXISTS `worker_servers`;
CREATE TABLE `worker_servers`  (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '鏈嶅姟鍣ㄥ敮涓?爣璇嗙?',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '鏈嶅姟鍣ㄥ悕绉',
  `server_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '鏈嶅姟鍣ㄨ?闂?湴鍧',
  `priority` int NULL DEFAULT 5 COMMENT '鏈嶅姟鍣ㄤ紭鍏堢骇 (1-10)',
  `max_concurrent_tasks` int NULL DEFAULT 20 COMMENT '鏈?ぇ骞跺彂浠诲姟鏁',
  `supported_operations` json NULL COMMENT '鏀?寔鐨勬搷浣滅被鍨',
  `current_load` int NULL DEFAULT 0 COMMENT '褰撳墠璐熻浇浠诲姟鏁',
  `status` enum('ONLINE','BUSY','OFFLINE','MAINTENANCE') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'OFFLINE' COMMENT '鏈嶅姟鍣ㄧ姸鎬',
  `last_heartbeat` datetime NULL DEFAULT NULL COMMENT '鏈?悗蹇冭烦鏃堕棿',
  `total_tasks_completed` bigint NULL DEFAULT 0 COMMENT '绱??瀹屾垚浠诲姟鏁',
  `total_tasks_failed` bigint NULL DEFAULT 0 COMMENT '绱??澶辫触浠诲姟鏁',
  `average_execution_time` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '骞冲潎鎵ц?鏃堕棿(绉?',
  `enabled` tinyint(1) NULL DEFAULT 1 COMMENT '鏄?惁鍚?敤',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '鏈嶅姟鍣ㄦ弿杩',
  `tags` json NULL COMMENT '鏈嶅姟鍣ㄦ爣绛',
  `created_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '鍒涘缓鏃堕棿',
  `updated_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '鏇存柊鏃堕棿',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_worker_servers_status`(`status` ASC) USING BTREE,
  INDEX `idx_worker_servers_priority`(`priority` ASC) USING BTREE,
  INDEX `idx_worker_servers_enabled`(`enabled` ASC) USING BTREE,
  INDEX `idx_worker_servers_last_heartbeat`(`last_heartbeat` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '鍓?湇鍔″櫒閰嶇疆琛' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of worker_servers
-- ----------------------------
INSERT INTO `worker_servers` VALUES ('worker-8083', '自动注册-worker-8083', 'http://localhost:8083', 5, 10, NULL, 0, 'OFFLINE', '2025-07-22 23:05:00', 0, 0, 0.00, 1, '通过心跳自动注册的Worker', NULL, '2025-07-22 16:25:33', '2025-07-22 23:05:02');

-- ----------------------------
-- View structure for active_announcements
-- ----------------------------
DROP VIEW IF EXISTS `active_announcements`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `active_announcements` AS select `a`.`id` AS `id`,`a`.`title` AS `title`,`a`.`content` AS `content`,`a`.`summary` AS `summary`,`a`.`priority` AS `priority`,`a`.`is_enabled` AS `is_enabled`,`a`.`is_popup` AS `is_popup`,`a`.`start_time` AS `start_time`,`a`.`end_time` AS `end_time`,`a`.`target_users` AS `target_users`,`a`.`view_count` AS `view_count`,`a`.`created_by` AS `created_by`,`a`.`created_time` AS `created_time`,`a`.`updated_time` AS `updated_time`,`u`.`name` AS `creator_name` from (`announcements` `a` left join `users` `u` on((`a`.`created_by` = `u`.`id`))) where ((`a`.`is_enabled` = true) and ((`a`.`start_time` is null) or (`a`.`start_time` <= now())) and ((`a`.`end_time` is null) or (`a`.`end_time` >= now()))) order by `a`.`priority` desc,`a`.`created_time` desc;

-- ----------------------------
-- View structure for v_task_execution_history
-- ----------------------------
DROP VIEW IF EXISTS `v_task_execution_history`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_task_execution_history` AS select `r`.`id` AS `reservation_id`,`r`.`user_id` AS `user_id`,`r`.`room_id` AS `room_id`,`r`.`execution_status` AS `task_status`,`r`.`worker_id` AS `worker_id`,`r`.`task_priority` AS `task_priority`,`r`.`scheduled_execution_time` AS `scheduled_execution_time`,`r`.`actual_execution_time` AS `actual_execution_time`,`r`.`execution_duration` AS `execution_duration`,`r`.`retry_count` AS `retry_count`,`r`.`error_message` AS `error_message`,`ws`.`name` AS `worker_name`,`u`.`username` AS `username`,`rm`.`name` AS `room_name` from (((`reservations` `r` left join `worker_servers` `ws` on((`r`.`worker_id` = `ws`.`id`))) left join `users` `u` on((`r`.`user_id` = `u`.`id`))) left join `rooms` `rm` on((`r`.`room_id` = `rm`.`id`))) where (`r`.`execution_status` is not null);

-- ----------------------------
-- View structure for v_task_status_stats
-- ----------------------------
DROP VIEW IF EXISTS `v_task_status_stats`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_task_status_stats` AS select `reservations`.`execution_status` AS `task_status`,count(0) AS `task_count`,avg(`reservations`.`execution_duration`) AS `avg_duration`,min(`reservations`.`created_time`) AS `earliest_task`,max(`reservations`.`created_time`) AS `latest_task` from `reservations` where (`reservations`.`execution_status` is not null) group by `reservations`.`execution_status`;

-- ----------------------------
-- View structure for v_worker_load_stats
-- ----------------------------
DROP VIEW IF EXISTS `v_worker_load_stats`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_worker_load_stats` AS select `ws`.`id` AS `id`,`ws`.`name` AS `name`,`ws`.`status` AS `status`,`ws`.`current_load` AS `current_load`,`ws`.`max_concurrent_tasks` AS `max_concurrent_tasks`,round(((`ws`.`current_load` / `ws`.`max_concurrent_tasks`) * 100),2) AS `load_percentage`,`ws`.`total_tasks_completed` AS `total_tasks_completed`,`ws`.`total_tasks_failed` AS `total_tasks_failed`,`ws`.`average_execution_time` AS `average_execution_time`,`ws`.`last_heartbeat` AS `last_heartbeat` from `worker_servers` `ws`;

SET FOREIGN_KEY_CHECKS = 1;
