-- 重命名 rooms 表中的 original_room_id 字段为 room_id
-- 注意：请在运行前备份数据库

USE seat_reservation;

-- 检查当前表结构
DESCRIBE rooms;

-- 检查是否存在 original_room_id 字段
SELECT COUNT(*) as field_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'rooms' 
  AND COLUMN_NAME = 'original_room_id';

-- 如果字段存在，则进行重命名
-- 方法1：直接重命名列（推荐）
ALTER TABLE rooms 
CHANGE COLUMN original_room_id room_id VARCHAR(20) COMMENT '房间ID';

-- 验证修改结果
DESCRIBE rooms;

-- 检查数据是否完整
SELECT COUNT(*) as total_rooms, 
       COUNT(room_id) as rooms_with_id,
       COUNT(*) - COUNT(room_id) as rooms_without_id
FROM rooms;

-- 显示前几条记录确认
SELECT id, school_id, name, room_id, max_reservation_hours 
FROM rooms 
LIMIT 5; 