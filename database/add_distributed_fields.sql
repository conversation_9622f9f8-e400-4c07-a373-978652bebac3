-- SeatMaster 分布式字段扩展脚本
-- 为现有数据库添加分布式任务管理所需的字段和表

USE seat_reservation;

-- =====================================================
-- 1. 为 reservations 表添加分布式任务字段
-- =====================================================

-- 检查并添加分布式字段
ALTER TABLE reservations ADD COLUMN IF NOT EXISTS worker_id VARCHAR(100) COMMENT '分配的副服务器ID';
ALTER TABLE reservations ADD COLUMN IF NOT EXISTS execution_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '执行状态: PENDING/RUNNING/SUCCESS/FAILED';
ALTER TABLE reservations ADD COLUMN IF NOT EXISTS execution_result TEXT COMMENT '执行结果详情';
ALTER TABLE reservations ADD COLUMN IF NOT EXISTS last_execution_time DATETIME COMMENT '最后执行时间';
ALTER TABLE reservations ADD COLUMN IF NOT EXISTS retry_count INT DEFAULT 0 COMMENT '重试次数';

-- 创建安全索引创建存储过程
DELIMITER $$
CREATE PROCEDURE SafeCreateIndex(
    IN table_name VARCHAR(64),
    IN index_name VARCHAR(64),
    IN column_list VARCHAR(255)
)
BEGIN
    DECLARE index_exists INT DEFAULT 0;

    -- 检查索引是否已存在
    SELECT COUNT(*) INTO index_exists
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = table_name
    AND index_name = index_name;

    -- 如果索引不存在则创建
    IF index_exists = 0 THEN
        SET @sql = CONCAT('CREATE INDEX ', index_name, ' ON ', table_name, ' (', column_list, ')');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END$$
DELIMITER ;

-- 创建分布式相关索引
CALL SafeCreateIndex('reservations', 'idx_worker_id', 'worker_id');
CALL SafeCreateIndex('reservations', 'idx_execution_status', 'execution_status');
CALL SafeCreateIndex('reservations', 'idx_last_execution_time', 'last_execution_time');

-- =====================================================
-- 2. 创建副服务器管理表
-- =====================================================

CREATE TABLE IF NOT EXISTS worker_servers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    worker_id VARCHAR(100) UNIQUE NOT NULL COMMENT '副服务器唯一标识',
    name VARCHAR(200) NOT NULL COMMENT '副服务器名称',
    host VARCHAR(100) NOT NULL COMMENT '服务器主机地址',
    port INT NOT NULL COMMENT '服务器端口',
    status VARCHAR(20) DEFAULT 'OFFLINE' COMMENT '服务器状态: ONLINE/OFFLINE/BUSY/ERROR',
    current_load INT DEFAULT 0 COMMENT '当前负载(正在处理的任务数)',
    max_concurrent_tasks INT DEFAULT 10 COMMENT '最大并发任务数',
    total_tasks_completed BIGINT DEFAULT 0 COMMENT '总完成任务数',
    total_tasks_failed BIGINT DEFAULT 0 COMMENT '总失败任务数',
    last_heartbeat DATETIME COMMENT '最后心跳时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '副服务器管理表';

-- 创建副服务器表索引
CALL SafeCreateIndex('worker_servers', 'idx_worker_id_ws', 'worker_id');
CALL SafeCreateIndex('worker_servers', 'idx_status_ws', 'status');
CALL SafeCreateIndex('worker_servers', 'idx_last_heartbeat_ws', 'last_heartbeat');

-- =====================================================
-- 3. 插入默认副服务器配置
-- =====================================================

-- 清理可能存在的测试数据
DELETE FROM worker_servers WHERE worker_id IN ('worker-001', 'worker-002', 'worker-003');

-- 插入默认副服务器配置
INSERT INTO worker_servers (worker_id, name, host, port, max_concurrent_tasks) VALUES
('worker-001', '副服务器1', 'localhost', 8082, 10),
('worker-002', '副服务器2', 'localhost', 8083, 10),
('worker-003', '副服务器3', 'localhost', 8084, 10);

-- =====================================================
-- 4. 数据一致性检查和修复
-- =====================================================

-- 检查reservations表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'reservations'
  AND COLUMN_NAME IN ('worker_id', 'execution_status', 'execution_result', 'last_execution_time', 'retry_count')
ORDER BY ORDINAL_POSITION;

-- 检查worker_servers表
SELECT COUNT(*) as worker_count FROM worker_servers;
SELECT worker_id, name, host, port, status FROM worker_servers;

-- =====================================================
-- 5. 验证扩展结果
-- =====================================================

-- 显示扩展后的reservations表结构
DESCRIBE reservations;

-- 显示worker_servers表结构
DESCRIBE worker_servers;

-- 检查索引创建情况
SHOW INDEX FROM reservations WHERE Key_name IN ('idx_worker_id', 'idx_execution_status', 'idx_last_execution_time');
SHOW INDEX FROM worker_servers WHERE Key_name IN ('idx_worker_id_ws', 'idx_status_ws', 'idx_last_heartbeat_ws');

-- 清理存储过程
DROP PROCEDURE SafeCreateIndex;

-- =====================================================
-- 6. 测试数据插入 (可选)
-- =====================================================

-- 测试分布式字段是否正常工作
-- UPDATE reservations SET 
--     worker_id = 'worker-001',
--     execution_status = 'PENDING'
-- WHERE id = 1 AND worker_id IS NULL;

-- 显示完成信息
SELECT 'SeatMaster 分布式字段扩展完成!' as message;
SELECT '已添加字段: worker_id, execution_status, execution_result, last_execution_time, retry_count' as added_fields;
SELECT '已创建表: worker_servers' as created_tables;
SELECT CONCAT('已配置 ', COUNT(*), ' 个默认副服务器') as configured_workers FROM worker_servers;

COMMIT;
