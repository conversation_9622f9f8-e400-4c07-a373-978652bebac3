-- 添加PAUSED状态到reservations表
-- 当用户剩余天数为0时，预约状态设为PAUSED而不是CANCELLED

USE seat_reservation;

-- 1. 修改reservations表的status字段，添加PAUSED状态
ALTER TABLE reservations
MODIFY status ENUM('ACTIVE', 'CANCELLED', 'PAUSED') NOT NULL DEFAULT 'ACTIVE';

-- 2. 将现有的因剩余天数为0而被取消的预约改为暂停状态
-- 这里我们假设最近被取消的预约可能是因为剩余天数为0
UPDATE reservations r
INNER JOIN users u ON r.user_id = u.id
SET r.status = 'PAUSED'
WHERE r.status = 'CANCELLED' 
AND u.remaining_days = 0
AND r.created_time > DATE_SUB(NOW(), INTERVAL 7 DAY); -- 只更新最近7天的记录

-- 3. 创建存储过程：暂停剩余天数为0的用户的预约
DELIMITER //
DROP PROCEDURE IF EXISTS PauseReservationsForUsersWithZeroDays//
CREATE PROCEDURE PauseReservationsForUsersWithZeroDays()
BEGIN
    -- 将剩余天数为0的用户的活跃预约设为暂停状态
    UPDATE reservations r
    INNER JOIN users u ON r.user_id = u.id
    SET r.status = 'PAUSED'
    WHERE u.remaining_days <= 0 
    AND r.status = 'ACTIVE';
    
    SELECT ROW_COUNT() as paused_reservations;
END //
DELIMITER ;

-- 4. 创建存储过程：恢复有剩余天数的用户的暂停预约
DELIMITER //
DROP PROCEDURE IF EXISTS ResumeReservationsForUsersWithDays//
CREATE PROCEDURE ResumeReservationsForUsersWithDays()
BEGIN
    -- 将有剩余天数的用户的暂停预约恢复为活跃状态
    UPDATE reservations r
    INNER JOIN users u ON r.user_id = u.id
    SET r.status = 'ACTIVE'
    WHERE u.remaining_days > 0 
    AND r.status = 'PAUSED'
    AND r.end_time > NOW(); -- 只恢复未过期的预约
    
    SELECT ROW_COUNT() as resumed_reservations;
END //
DELIMITER ;

-- 5. 查看更新结果
SELECT 
    status,
    COUNT(*) as count
FROM reservations 
GROUP BY status;

SELECT 'PAUSED status added to reservations table successfully!' as message;
