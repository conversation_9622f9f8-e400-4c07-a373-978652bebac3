-- 修复 worker_servers 表结构以匹配 Java 实体类
-- 解决表结构不匹配问题

USE seat_reservation;

-- =====================================================
-- 1. 备份现有数据（如果有的话）
-- =====================================================

-- 创建临时表备份数据
CREATE TABLE IF NOT EXISTS worker_servers_backup AS 
SELECT * FROM worker_servers;

-- =====================================================
-- 2. 删除现有表并重新创建
-- =====================================================

DROP TABLE IF EXISTS worker_servers;

-- 重新创建 worker_servers 表，匹配 Java 实体类结构
CREATE TABLE worker_servers (
    id VARCHAR(100) PRIMARY KEY COMMENT '副服务器唯一标识（作为主键）',
    name VARCHAR(200) NOT NULL COMMENT '副服务器名称',
    server_url VARCHAR(500) COMMENT '服务器完整URL',
    priority INT DEFAULT 1 COMMENT '优先级',
    supported_operations TEXT COMMENT '支持的操作（JSON格式）',
    status VARCHAR(20) DEFAULT 'OFFLINE' COMMENT '服务器状态: ONLINE/OFFLINE/BUSY/ERROR',
    current_load INT DEFAULT 0 COMMENT '当前负载(正在处理的任务数)',
    max_concurrent_tasks INT DEFAULT 10 COMMENT '最大并发任务数',
    total_tasks_completed BIGINT DEFAULT 0 COMMENT '总完成任务数',
    total_tasks_failed BIGINT DEFAULT 0 COMMENT '总失败任务数',
    average_execution_time DOUBLE DEFAULT NULL COMMENT '平均执行时间',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    description TEXT COMMENT '描述',
    tags TEXT COMMENT '标签（JSON格式）',
    last_heartbeat DATETIME COMMENT '最后心跳时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_status (status),
    INDEX idx_enabled (enabled),
    INDEX idx_last_heartbeat (last_heartbeat),
    INDEX idx_current_load (current_load)
) COMMENT '副服务器管理表（修复版）';

-- =====================================================
-- 3. 插入默认副服务器配置
-- =====================================================

-- 插入默认副服务器配置，使用字符串ID
INSERT INTO worker_servers (
    id, name, server_url, priority, status, current_load, max_concurrent_tasks,
    total_tasks_completed, total_tasks_failed, enabled, description
) VALUES
('worker-001', '副服务器1', 'http://localhost:8082', 1, 'OFFLINE', 0, 10, 0, 0, TRUE, '默认副服务器1'),
('worker-002', '副服务器2', 'http://localhost:8083', 2, 'OFFLINE', 0, 10, 0, 0, TRUE, '默认副服务器2'),
('worker-003', '副服务器3', 'http://localhost:8084', 3, 'OFFLINE', 0, 10, 0, 0, TRUE, '默认副服务器3');

-- =====================================================
-- 4. 验证修复结果
-- =====================================================

-- 检查表结构
DESCRIBE worker_servers;

-- 检查数据
SELECT id, name, server_url, status, current_load, max_concurrent_tasks, enabled FROM worker_servers;

-- 检查索引
SHOW INDEX FROM worker_servers;

-- =====================================================
-- 5. 清理备份表（可选）
-- =====================================================

-- 如果确认修复成功，可以删除备份表
-- DROP TABLE IF EXISTS worker_servers_backup;

-- 显示完成信息
SELECT 'worker_servers 表结构修复完成!' as message;
SELECT CONCAT('已配置 ', COUNT(*), ' 个默认副服务器') as configured_workers FROM worker_servers;

COMMIT;
