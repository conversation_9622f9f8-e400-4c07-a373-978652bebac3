-- 添加预约开放时间和预约类型字段到reservations表
-- 修复预约开放时间更新失效问题

USE seat_reservation;

-- 检查当前表结构
DESCRIBE reservations;

-- 检查是否已存在reservation_open_time字段
SELECT COUNT(*) as reservation_open_time_exists
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'reservations' 
  AND COLUMN_NAME = 'reservation_open_time';

-- 检查是否已存在reservation_type字段
SELECT COUNT(*) as reservation_type_exists
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'reservations' 
  AND COLUMN_NAME = 'reservation_type';

-- 添加reservation_open_time字段（如果不存在）
SET @sql = (SELECT IF(
    EXISTS(
        SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'seat_reservation' 
        AND TABLE_NAME = 'reservations' 
        AND COLUMN_NAME = 'reservation_open_time'
    ),
    'SELECT "reservation_open_time field already exists" as message',
    'ALTER TABLE reservations ADD COLUMN reservation_open_time VARCHAR(100) DEFAULT NULL COMMENT "预约开放时间"'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加reservation_type字段（如果不存在）
SET @sql = (SELECT IF(
    EXISTS(
        SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'seat_reservation' 
        AND TABLE_NAME = 'reservations' 
        AND COLUMN_NAME = 'reservation_type'
    ),
    'SELECT "reservation_type field already exists" as message',
    'ALTER TABLE reservations ADD COLUMN reservation_type VARCHAR(50) DEFAULT "SAME_DAY" COMMENT "预约类型：SAME_DAY当天预约，ADVANCE_ONE_DAY提前一天预约"'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建安全索引创建存储过程
DELIMITER $$
CREATE PROCEDURE SafeCreateIndex(
    IN table_name VARCHAR(64),
    IN index_name VARCHAR(64),
    IN column_list VARCHAR(255)
)
BEGIN
    DECLARE index_exists INT DEFAULT 0;

    -- 检查索引是否已存在
    SELECT COUNT(*) INTO index_exists
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = table_name
    AND index_name = index_name;

    -- 如果索引不存在则创建
    IF index_exists = 0 THEN
        SET @sql = CONCAT('CREATE INDEX ', index_name, ' ON ', table_name, ' (', column_list, ')');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END$$
DELIMITER ;

-- 创建索引优化查询性能
CALL SafeCreateIndex('reservations', 'idx_reservations_open_time', 'reservation_open_time');
CALL SafeCreateIndex('reservations', 'idx_reservations_type', 'reservation_type');

-- 验证字段已添加
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'seat_reservation' 
  AND TABLE_NAME = 'reservations'
  AND COLUMN_NAME IN ('reservation_open_time', 'reservation_type')
ORDER BY ORDINAL_POSITION;

-- 检查现有数据
SELECT COUNT(*) as total_reservations FROM reservations;

-- 显示部分数据验证字段
SELECT 
    id,
    user_id,
    room_id,
    seat_num,
    start_time,
    end_time,
    reservation_open_time,
    reservation_type,
    created_time
FROM reservations 
ORDER BY created_time DESC
LIMIT 5;

-- 清理存储过程
DROP PROCEDURE SafeCreateIndex;

COMMIT;

-- 显示完成信息
SELECT 'Reservation time fields added successfully!' as message;
