@echo off
chcp 65001
echo 正在导入中文学校和房间数据...

"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -proot --default-character-set=utf8mb4 seat_reservation < add_schools_and_rooms_simple.sql

echo 检查导入结果...
"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -proot --default-character-set=utf8mb4 seat_reservation -e "SELECT COUNT(*) as school_count FROM schools;"
"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -proot --default-character-set=utf8mb4 seat_reservation -e "SELECT COUNT(*) as room_count FROM rooms;"
"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -proot --default-character-set=utf8mb4 seat_reservation -e "SELECT * FROM schools LIMIT 5;"

echo 导入完成！ 