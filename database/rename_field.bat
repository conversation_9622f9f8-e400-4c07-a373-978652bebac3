@echo off
chcp 65001 >nul
echo === 重命名房间表字段 ===
echo 将 original_room_id 字段重命名为 room_id
echo.

echo 正在执行字段重命名...
echo.

REM 尝试不同的MySQL路径
set MYSQL_PATH=""
if exist "C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" (
    set MYSQL_PATH="C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe"
) else if exist "C:\Program Files\MySQL\MySQL Server 5.7\bin\mysql.exe" (
    set MYSQL_PATH="C:\Program Files\MySQL\MySQL Server 5.7\bin\mysql.exe"
) else if exist "C:\xampp\mysql\bin\mysql.exe" (
    set MYSQL_PATH="C:\xampp\mysql\bin\mysql.exe"
) else (
    echo 未找到MySQL安装路径，请手动执行以下SQL：
    echo.
    type rename_room_id_field.sql
    pause
    exit /b 1
)

echo 使用MySQL路径: %MYSQL_PATH%
echo.

REM 执行SQL脚本
%MYSQL_PATH% -u root -p seat_reservation < rename_room_id_field.sql

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 字段重命名成功完成！
    echo.
    echo 修改内容：
    echo   - original_room_id -^> room_id
    echo.
    echo 请验证数据是否正确：
    echo   %MYSQL_PATH% -u root -p -e "USE seat_reservation; DESCRIBE rooms;"
) else (
    echo.
    echo ❌ 字段重命名失败
    echo 请检查MySQL连接和权限设置
)

echo.
pause 