-- 增强任务执行日志表，添加详细执行信息字段
-- 执行日期: 2025-07-02
-- 说明: 保存学习通API执行的详细信息，支持普通预约和拆分预约

USE seat_reservation;

-- 检查当前表结构
DESCRIBE task_execution_logs;

-- 添加详细执行信息字段
ALTER TABLE task_execution_logs ADD COLUMN step1_login_duration_ms INT DEFAULT NULL COMMENT '步骤1登录耗时(毫秒)';
ALTER TABLE task_execution_logs ADD COLUMN step2_token_duration_ms INT DEFAULT NULL COMMENT '步骤2获取Token耗时(毫秒)';
ALTER TABLE task_execution_logs ADD COLUMN step3_submit_duration_ms INT DEFAULT NULL COMMENT '步骤3提交预约耗时(毫秒)';
ALTER TABLE task_execution_logs ADD COLUMN total_duration_ms INT DEFAULT NULL COMMENT '总执行时间(毫秒)';
ALTER TABLE task_execution_logs ADD COLUMN execution_start_time DATETIME DEFAULT NULL COMMENT '执行开始时间';
ALTER TABLE task_execution_logs ADD COLUMN execution_end_time DATETIME DEFAULT NULL COMMENT '执行结束时间';
ALTER TABLE task_execution_logs ADD COLUMN error_details TEXT DEFAULT NULL COMMENT '详细错误信息';
ALTER TABLE task_execution_logs ADD COLUMN api_response_json TEXT DEFAULT NULL COMMENT '学习通API原始响应';
ALTER TABLE task_execution_logs ADD COLUMN execution_status VARCHAR(20) DEFAULT NULL COMMENT '执行状态(SUCCESS/FAILED/ERROR)';
ALTER TABLE task_execution_logs ADD COLUMN user_info VARCHAR(200) DEFAULT NULL COMMENT '用户信息(用户名/手机号等)';
ALTER TABLE task_execution_logs ADD COLUMN seat_info VARCHAR(100) DEFAULT NULL COMMENT '座位信息';
ALTER TABLE task_execution_logs ADD COLUMN room_info VARCHAR(100) DEFAULT NULL COMMENT '房间信息';

-- 拆分预约相关字段
ALTER TABLE task_execution_logs ADD COLUMN is_split_reservation BOOLEAN DEFAULT FALSE COMMENT '是否为拆分预约';
ALTER TABLE task_execution_logs ADD COLUMN split_group_id VARCHAR(50) DEFAULT NULL COMMENT '拆分组ID';
ALTER TABLE task_execution_logs ADD COLUMN split_sequence INT DEFAULT NULL COMMENT '拆分序号';
ALTER TABLE task_execution_logs ADD COLUMN split_total_count INT DEFAULT NULL COMMENT '拆分总数';
ALTER TABLE task_execution_logs ADD COLUMN split_time_range VARCHAR(50) DEFAULT NULL COMMENT '拆分时间段(如08:00-11:00)';
ALTER TABLE task_execution_logs ADD COLUMN original_duration_hours DECIMAL(4,1) DEFAULT NULL COMMENT '原始预约时长(小时)';

-- 创建索引优化查询性能
CREATE INDEX idx_execution_status ON task_execution_logs(execution_status);
CREATE INDEX idx_execution_start_time ON task_execution_logs(execution_start_time);
CREATE INDEX idx_total_duration ON task_execution_logs(total_duration_ms);
CREATE INDEX idx_user_info ON task_execution_logs(user_info);
CREATE INDEX idx_split_group ON task_execution_logs(split_group_id);
CREATE INDEX idx_split_info ON task_execution_logs(is_split_reservation, split_group_id, split_sequence);

-- 验证字段已添加
DESCRIBE task_execution_logs;

COMMIT;

-- 显示完成信息
SELECT 'Task execution logs table enhanced successfully!' as message;
