# 🚨 生产环境数据库安全提醒

## 快速安全部署

### ✅ 推荐使用（安全）
```bash
# 1. 备份数据库
mysqldump -u root -p seat_reservation > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. 使用安全脚本
mysql -u root -p < database/production-setup-safe.sql
```

### ❌ 避免使用（有风险）
```bash
# 不要直接使用原始脚本！
# mysql -u root -p < database/production-setup.sql  # 包含数据删除风险
```

## 🔍 脚本对比

| 功能 | production-setup.sql | production-setup-safe.sql |
|------|---------------------|---------------------------|
| 索引优化 | ✅ | ✅ |
| 用户创建 | ✅ | ✅ (可选) |
| 字符集设置 | ✅ | ✅ |
| **自动数据删除** | ❌ 危险 | ✅ 已移除 |
| **定时任务** | ❌ 危险 | ✅ 已移除 |
| **全局设置更改** | ❌ 危险 | ✅ 已移除 |

## 🚨 原始脚本的危险操作

### 1. 自动删除数据
```sql
-- 危险：会删除90天前的数据
DELETE FROM task_execution_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
DELETE FROM reservations WHERE status IN ('completed', 'cancelled') AND reservation_date < DATE_SUB(CURDATE(), INTERVAL days_to_keep DAY);
```

### 2. 自动定时任务
```sql
-- 危险：每天自动执行数据清理
CREATE EVENT daily_cleanup ON SCHEDULE EVERY 1 DAY DO CALL CleanupOldLogs(90);
```

### 3. 全局设置更改
```sql
-- 危险：影响整个MySQL实例
SET GLOBAL event_scheduler = ON;
```

## ✅ 安全脚本的特点

1. **只读优化**：仅创建索引，不删除数据
2. **可选配置**：用户创建部分已注释
3. **安全检查**：使用 `IF NOT EXISTS` 避免冲突
4. **立即清理**：临时存储过程执行后立即删除
5. **详细验证**：提供配置结果查询

## 📞 紧急情况

如果意外执行了原始脚本：

1. **立即停止**：如果脚本还在运行，按 Ctrl+C 停止
2. **检查损失**：查看是否有数据被删除
3. **恢复数据**：从备份恢复（这就是为什么备份很重要）
4. **禁用定时任务**：
   ```sql
   DROP EVENT IF EXISTS daily_cleanup;
   DROP EVENT IF EXISTS hourly_stats_update;
   SET GLOBAL event_scheduler = OFF;
   ```

## 📋 部署检查清单

- [ ] 已创建数据库备份
- [ ] 使用 `production-setup-safe.sql` 脚本
- [ ] 验证索引创建成功
- [ ] 检查应用连接正常
- [ ] 确认没有创建定时任务
- [ ] 确认没有自动数据删除

## 🔗 相关文档

- 详细指南：`docs/生产环境数据库安全部署指南.md`
- 安全脚本：`database/production-setup-safe.sql`
- 修复记录：`issues/生产环境数据库脚本修复记录.md`
