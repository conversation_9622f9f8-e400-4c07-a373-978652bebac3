# 重命名数据库字段：original_room_id -> room_id
# PowerShell脚本

Write-Host "=== 重命名房间表字段 ===" -ForegroundColor Green
Write-Host "将 original_room_id 字段重命名为 room_id" -ForegroundColor Yellow
Write-Host ""

# 检查MySQL是否运行
$mysqlProcess = Get-Process mysqld -ErrorAction SilentlyContinue
if (-not $mysqlProcess) {
    Write-Host "❌ MySQL服务未运行，请先启动MySQL服务" -ForegroundColor Red
    exit 1
}

Write-Host "✅ MySQL服务正在运行" -ForegroundColor Green

# 执行重命名操作
Write-Host "正在执行字段重命名..." -ForegroundColor Yellow

try {
    # 使用Get-Content读取SQL文件并通过管道传递给mysql
    Get-Content "rename_room_id_field.sql" | mysql -u root -p seat_reservation
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 字段重命名成功完成！" -ForegroundColor Green
        Write-Host ""
        Write-Host "修改内容："
        Write-Host "  - original_room_id -> room_id" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "请验证数据是否正确：" -ForegroundColor Yellow
        Write-Host "  mysql -u root -p -e `"USE seat_reservation; DESCRIBE rooms;`"" -ForegroundColor Gray
    } else {
        Write-Host "❌ 字段重命名失败" -ForegroundColor Red
        Write-Host "请检查MySQL连接和权限设置" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 执行过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "按任意键继续..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") 