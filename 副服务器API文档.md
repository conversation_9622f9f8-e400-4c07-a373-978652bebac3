# SeatMaster 副服务器 API 文档

## 概述

SeatMaster 副服务器（Worker Server）是基于 Jetty 的轻量级服务器，负责执行学习通座位预约任务。服务器提供完整的 RESTful API 接口，支持任务管理、状态监控、健康检查等功能。

**服务器信息：**
- 默认端口：`8082`
- 基础URL：`http://localhost:8082`
- 服务名称：`SeatMaster Worker Server Simple`
- 工作节点ID：`worker-001`

---

## API 接口列表

### 1. 健康检查接口

#### 1.1 服务健康检查
```http
GET /api/health
```

**描述：** 检查服务器基本健康状态

**响应示例：**
```json
{
  "status": "UP",
  "timestamp": "2025-06-24T18:30:45.123",
  "service": "SeatMaster Worker Server Simple"
}
```

**响应字段：**
- `status`: 服务状态（UP/DOWN）
- `timestamp`: 检查时间戳
- `service`: 服务名称

---

### 2. 状态监控接口

#### 2.1 详细状态查询
```http
GET /api/status
```

**描述：** 获取服务器详细运行状态，包括内存使用、处理器信息、数据库状态等

**响应示例：**
```json
{
  "status": "RUNNING",
  "timestamp": "2025-06-24T18:30:45.123",
  "service": "SeatMaster Worker Server Simple",
  "memory": {
    "total": **********,
    "used": 268435456,
    "free": 805306368
  },
  "processors": 8,
  "database": "AVAILABLE",
  "taskService": "RUNNING"
}
```

**响应字段：**
- `status`: 服务运行状态
- `memory`: 内存使用情况（字节）
  - `total`: 总内存
  - `used`: 已使用内存
  - `free`: 可用内存
- `processors`: 可用处理器数量
- `database`: 数据库连接状态
- `taskService`: 任务服务状态

---

### 3. 任务管理接口

#### 3.1 添加任务到队列
```http
POST /api/tasks/add
Content-Type: application/json
```

**描述：** 将新的预约任务添加到执行队列

**请求体示例：**
```json
{
  "reservationId": 123,
  "taskId": "task_1750759232126_53",
  "executeTime": "2025-06-24T18:00:00",
  "warmupTime": "2025-06-24T17:59:00",
  "priority": 5,
  "reservationType": "SAME_DAY"
}
```

**请求字段：**
- `reservationId` (必需): 预约记录ID
- `taskId` (可选): 任务唯一标识符
- `executeTime` (可选): 执行时间
- `warmupTime` (可选): 预热时间
- `priority` (可选): 任务优先级
- `reservationType` (可选): 预约类型

**成功响应示例：**
```json
{
  "success": true,
  "message": "任务添加成功",
  "reservationId": 123,
  "taskId": "task_1750759232126_53",
  "timestamp": "2025-06-24T18:30:45.123"
}
```

**错误响应示例：**
```json
{
  "success": false,
  "error": "任务队列已满",
  "message": "请稍后重试",
  "timestamp": "2025-06-24T18:30:45.123"
}
```

#### 3.2 立即执行任务
```http
POST /api/tasks/execute/{taskId}
```

**描述：** 立即执行指定的任务，跳过队列等待

**路径参数：**
- `taskId`: 任务唯一标识符

**成功响应示例：**
```json
{
  "success": true,
  "taskId": "task_1750759232126_53",
  "message": "任务执行成功",
  "executionTime": 1250,
  "timestamp": "2025-06-24T18:30:45.123",
  "data": {
    "seat": "2",
    "room": "A区一楼-A104-自主学习室"
  }
}
```

**失败响应示例：**
```json
{
  "success": false,
  "taskId": "task_1750759232126_53",
  "message": "预约失败",
  "executionTime": 850,
  "timestamp": "2025-06-24T18:30:45.123",
  "error": "该时间段已过，不可预约！"
}
```

**响应字段：**
- `success`: 执行是否成功
- `taskId`: 任务ID
- `message`: 执行结果消息
- `executionTime`: 执行耗时（毫秒）
- `data`: 成功时的额外数据
- `error`: 失败时的错误信息

#### 3.3 获取任务状态
```http
GET /api/tasks/
```

**描述：** 获取任务执行统计信息

**响应示例：**
```json
{
  "queueSize": 2,
  "activeThreads": 3,
  "completedTasks": 15,
  "failedTasks": 2,
  "totalTasks": 17,
  "maxConcurrentTasks": 10,
  "successRate": "88.24%",
  "timestamp": "2025-06-24T18:30:45.123"
}
```

**响应字段：**
- `queueSize`: 当前队列中的任务数量
- `activeThreads`: 活跃工作线程数
- `completedTasks`: 已完成任务数
- `failedTasks`: 失败任务数
- `totalTasks`: 总任务数
- `maxConcurrentTasks`: 最大并发任务数
- `successRate`: 成功率百分比

#### 3.4 获取队列详细信息
```http
GET /api/tasks/queue
```

**描述：** 获取任务队列的详细信息，包括等待中的任务列表

**响应示例：**
```json
{
  "queueSize": 2,
  "maxQueueSize": 100,
  "pendingTasks": [
    {
      "taskId": "task_1750759232126_53",
      "reservationId": 83,
      "warmupTime": "2025-06-24T17:59:00",
      "executeTime": "2025-06-24T18:00:00",
      "status": "PENDING"
    },
    {
      "taskId": "task_1750759232127_605",
      "reservationId": 85,
      "warmupTime": "2025-06-24T17:59:00",
      "executeTime": "2025-06-24T18:00:00",
      "status": "PENDING"
    }
  ],
  "timestamp": "2025-06-24T18:30:45.123"
}
```

**响应字段：**
- `queueSize`: 当前队列大小
- `maxQueueSize`: 最大队列容量
- `pendingTasks`: 等待中的任务列表
- `timestamp`: 查询时间戳

#### 3.5 创建测试任务
```http
GET /api/tasks/test
```

**描述：** 创建测试任务用于验证系统功能，使用真实的预约数据

**响应示例：**
```json
{
  "success": true,
  "message": "测试任务创建成功",
  "createdCount": 2,
  "totalCount": 2,
  "tasks": [
    {
      "reservationId": 83,
      "warmupTime": "2025-06-24 17:59:00",
      "executeTime": "2025-06-24 18:00:00"
    },
    {
      "reservationId": 85,
      "warmupTime": "2025-06-24 17:59:00",
      "executeTime": "2025-06-24 18:00:00"
    }
  ],
  "openTime": "18:00",
  "currentTime": "2025-06-24 17:58:00",
  "timestamp": "2025-06-24T18:30:45.123"
}
```

**响应字段：**
- `success`: 创建是否成功
- `createdCount`: 成功创建的任务数
- `totalCount`: 尝试创建的总任务数
- `tasks`: 创建的任务列表
- `openTime`: 预约开放时间
- `currentTime`: 当前时间

#### 3.6 获取已安排任务详情
```http
GET /api/tasks/scheduled
```

**描述：** 获取队列中等待执行的任务详细信息

**响应示例：**
```json
{
  "scheduledTasks": [
    {
      "reservationId": 83,
      "username": "张三",
      "schoolName": "北京大学",
      "roomName": "A区一楼-A104-自主学习室",
      "reservation_open_time": "08:00",
      "reservation_type": "SAME_DAY",
      "seatId": "A12",
      "startTime": "09:00",
      "endTime": "17:00",
      "warmupTime": "2025-06-24 17:59:00",
      "executeTime": "2025-06-24 18:00:00",
      "priority": 5,
      "status": "SCHEDULED",
      "addedTime": "2025-06-24 17:55:00",
      "minutesToExecution": 5,
      "timeToExecution": "5 分钟后"
    }
  ],
  "totalScheduled": 1,
  "queueCapacity": 100,
  "queueUsage": "1.0%",
  "timestamp": "2025-06-24T18:30:45.123"
}
```

**响应字段：**
- `scheduledTasks`: 已安排任务列表
  - `username`: 用户名
  - `schoolName`: 学校名称
  - `roomName`: 房间名称
  - `reservation_open_time`: 预约开放时间
  - `reservation_type`: 预约类型
  - `seatId`: 座位号
  - `reservationId`: 预约ID
- `totalScheduled`: 已安排任务总数
- `queueCapacity`: 队列最大容量
- `queueUsage`: 队列使用率

#### 3.7 获取正在执行任务详情
```http
GET /api/tasks/running
```

**描述：** 获取当前正在执行的任务详细信息

**响应示例：**
```json
{
  "runningTasks": [
    {
      "reservationId": 85,
      "username": "李四",
      "schoolName": "清华大学",
      "roomName": "B区二楼-B205-研讨室",
      "reservation_open_time": "08:00",
      "reservation_type": "SAME_DAY",
      "seatId": "B08",
      "startTime": "2025-06-24 18:00:15",
      "priority": 5,
      "status": "RUNNING",
      "runningTime": "2 分钟"
    }
  ],
  "totalRunning": 1,
  "maxConcurrentTasks": 10,
  "concurrencyUsage": "10.0%",
  "timestamp": "2025-06-24T18:30:45.123"
}
```

**响应字段：**
- `runningTasks`: 正在执行任务列表
  - `username`: 用户名
  - `schoolName`: 学校名称
  - `roomName`: 房间名称
  - `reservation_open_time`: 预约开放时间
  - `reservation_type`: 预约类型
  - `seatId`: 座位号
  - `runningTime`: 运行时间
- `totalRunning`: 正在执行任务总数
- `maxConcurrentTasks`: 最大并发任务数
- `concurrencyUsage`: 并发使用率

#### 3.8 获取已完成任务详情
```http
GET /api/tasks/completed
```

**描述：** 获取已完成任务的详细信息（最近50个）

**响应示例：**
```json
{
  "completedTasks": [
    {
      "reservationId": 83,
      "username": "张三",
      "schoolName": "北京大学",
      "roomName": "A区一楼-A104-自主学习室",
      "reservation_open_time": "08:00",
      "reservation_type": "SAME_DAY",
      "seatId": "A12",
      "success": true,
      "message": "预约任务执行成功",
      "executionTime": "1250 ms",
      "completedTime": "2025-06-24 18:00:30",
      "status": "SUCCESS",
      "data": {
        "seat": "2",
        "room": "A区一楼-A104-自主学习室"
      }
    },
    {
      "reservationId": 85,
      "username": "李四",
      "schoolName": "清华大学",
      "roomName": "B区二楼-B205-研讨室",
      "reservation_open_time": "08:00",
      "reservation_type": "SAME_DAY",
      "seatId": "B08",
      "success": false,
      "message": "预约失败",
      "executionTime": "850 ms",
      "completedTime": "2025-06-24 18:00:25",
      "status": "FAILED",
      "error": "该时间段已过，不可预约！"
    }
  ],
  "totalCompleted": 15,
  "recentCount": 2,
  "successCount": 13,
  "failedCount": 2,
  "successRate": "86.7%",
  "timestamp": "2025-06-24T18:30:45.123"
}
```

**响应字段：**
- `completedTasks`: 已完成任务列表（按完成时间倒序）
  - `username`: 用户名
  - `schoolName`: 学校名称
  - `roomName`: 房间名称
  - `reservation_open_time`: 预约开放时间
  - `reservation_type`: 预约类型
  - `seatId`: 座位号
  - `success`: 执行是否成功
  - `message`: 执行结果消息
  - `executionTime`: 执行耗时
  - `data`: 成功时的额外数据
  - `error`: 失败时的错误信息
- `totalCompleted`: 总完成任务数
- `recentCount`: 返回的最近任务数
- `successCount`: 成功任务数
- `failedCount`: 失败任务数
- `successRate`: 成功率

#### 3.9 清理任务队列
```http
GET /api/tasks/clear
```

**描述：** 清理所有任务队列，包括内存队列和数据库中的待执行任务

**响应示例：**
```json
{
  "success": true,
  "message": "任务队列清理完成",
  "clearedCount": 6,
  "timestamp": "2025-06-24T18:30:45.123"
}
```

**响应字段：**
- `success`: 清理是否成功
- `clearedCount`: 清理的任务总数（内存+数据库）
- `timestamp`: 清理完成时间

---

## 错误响应格式

所有API在发生错误时都会返回统一的错误响应格式：

```json
{
  "success": false,
  "error": "错误类型",
  "message": "详细错误信息",
  "timestamp": "2025-06-24T18:30:45.123"
}
```

### 常见错误码

- `400 Bad Request`: 请求参数错误
- `404 Not Found`: 资源不存在
- `500 Internal Server Error`: 服务器内部错误
- `503 Service Unavailable`: 服务不可用（如队列已满）

---

## 使用示例

### PowerShell 示例

```powershell
# 健康检查
Invoke-WebRequest -Uri "http://localhost:8082/api/health" -Method GET

# 获取状态
Invoke-WebRequest -Uri "http://localhost:8082/api/status" -Method GET

# 添加任务
$body = @{
    reservationId = 123
    priority = 5
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:8082/api/tasks/add" -Method POST -Body $body -ContentType "application/json"

# 创建测试任务
Invoke-WebRequest -Uri "http://localhost:8082/api/tasks/test" -Method GET

# 获取已安排任务
Invoke-WebRequest -Uri "http://localhost:8082/api/tasks/scheduled" -Method GET

# 获取正在执行任务
Invoke-WebRequest -Uri "http://localhost:8082/api/tasks/running" -Method GET

# 获取已完成任务
Invoke-WebRequest -Uri "http://localhost:8082/api/tasks/completed" -Method GET

# 清理队列
Invoke-WebRequest -Uri "http://localhost:8082/api/tasks/clear" -Method GET
```

### cURL 示例

```bash
# 健康检查
curl -X GET http://localhost:8082/api/health

# 获取任务状态
curl -X GET http://localhost:8082/api/tasks/

# 添加任务
curl -X POST http://localhost:8082/api/tasks/add \
  -H "Content-Type: application/json" \
  -d '{"reservationId": 123, "priority": 5}'

# 立即执行任务
curl -X POST http://localhost:8082/api/tasks/execute/task_123

# 获取队列详情
curl -X GET http://localhost:8082/api/tasks/queue

# 获取已安排任务详情
curl -X GET http://localhost:8082/api/tasks/scheduled

# 获取正在执行任务详情
curl -X GET http://localhost:8082/api/tasks/running

# 获取已完成任务详情
curl -X GET http://localhost:8082/api/tasks/completed
```

---

## 配置信息

### 服务器配置
- **端口**: 8082
- **工作节点ID**: worker-001
- **最大并发任务数**: 10
- **队列最大容量**: 100

### 数据库配置
- **URL**: `********************************************`
- **用户名**: root
- **连接池**: HikariCP

### 主服务器配置
- **主服务器URL**: http://localhost:8081
- **心跳间隔**: 30秒
- **注册重试间隔**: 60秒

---

## 与主服务器的交互

### 1. 自动注册到主服务器

副服务器启动时会自动向主服务器注册：

```http
POST http://localhost:8081/api/worker/register
Content-Type: application/json

{
  "workerId": "worker-001",
  "name": "SeatMaster Worker Server Simple",
  "host": "localhost",
  "port": 8082,
  "maxConcurrentTasks": 10,
  "supportedOperations": ["XUEXITONG_RESERVATION"]
}
```

### 2. 定期发送心跳

每30秒向主服务器发送心跳：

```http
POST http://localhost:8081/api/worker/heartbeat/worker-001
Content-Type: application/json

{
  "workerId": "worker-001",
  "status": "ONLINE",
  "currentLoad": 3,
  "timestamp": 1719564000000
}
```

**心跳数据说明：**
- `workerId`: 工作节点ID
- `status`: 当前状态 (ONLINE/OFFLINE/BUSY/ERROR)
- `currentLoad`: 当前负载（活跃任务数）
- `timestamp`: 时间戳

### 3. 负载数据计算

副服务器通过以下方式计算真实负载：

```java
// 获取当前活跃任务数
public int getCurrentLoad() {
    try {
        return taskExecutionService.getActiveThreadCount();
    } catch (Exception e) {
        logger.warn("获取当前负载失败，返回默认值0", e);
        return 0;
    }
}
```

**负载率计算公式：**
```
负载率 = (currentLoad / maxConcurrentTasks) * 100
```

**示例：**
- 最大并发任务数：10
- 当前活跃任务数：3
- 负载率：(3 / 10) * 100 = 30%

### 4. 主服务器管理接口

主服务器提供以下接口管理副服务器：

#### 4.1 获取所有副服务器
```http
GET http://localhost:8081/api/admin/worker-management/servers
```

#### 4.2 获取在线副服务器
```http
GET http://localhost:8081/api/admin/worker-management/servers/online
```

#### 4.3 副服务器健康检查
```http
POST http://localhost:8081/api/admin/worker-management/servers/worker-001/health-check
```

#### 4.4 获取副服务器统计
```http
GET http://localhost:8081/api/admin/worker-management/statistics
```

### 5. 任务分配流程

1. **主服务器**：创建预约任务
2. **主服务器**：选择可用的副服务器
3. **主服务器**：通过API分配任务给副服务器
4. **副服务器**：接收任务并加入执行队列
5. **副服务器**：执行任务并更新状态
6. **副服务器**：通过心跳报告负载变化

---

## 注意事项

1. **任务执行**: 所有任务都会经过预热→执行的两阶段流程
2. **真实API调用**: 系统会真正调用学习通API进行预约操作
3. **错误处理**: 完善的错误处理和日志记录
4. **性能监控**: 提供详细的性能指标和执行统计
5. **数据库清理**: 清理操作会同时清理内存和数据库中的任务

---

## 更新日志

- **v2.1**: 添加真实学习通API调用支持
- **v2.0**: 完善任务队列管理和数据库清理功能
- **v1.0**: 基础API接口实现
