# SeatMaster MySQL 性能优化配置
# 根据不同硬件配置和负载特点进行调优

[mysqld]
# ================================
# 基础配置
# ================================
user = mysql
port = 3306
bind-address = 0.0.0.0
socket = /var/run/mysqld/mysqld.sock
pid-file = /var/run/mysqld/mysqld.pid
datadir = /var/lib/mysql
tmpdir = /tmp

# ================================
# 字符集配置
# ================================
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'
skip-character-set-client-handshake

# ================================
# 连接配置
# ================================
# 最大连接数 (根据应用需求调整)
max_connections = 500

# 最大错误连接数
max_connect_errors = 100000

# 每用户最大连接数
max_user_connections = 200

# 连接队列大小
back_log = 200

# 连接超时设置
wait_timeout = 28800
interactive_timeout = 28800
connect_timeout = 10

# 网络超时设置
net_read_timeout = 30
net_write_timeout = 60

# ================================
# 内存配置 (根据服务器内存调整)
# ================================

# InnoDB缓冲池 (建议设为可用内存的70-80%)
# 4GB服务器建议值
innodb_buffer_pool_size = 2G
innodb_buffer_pool_instances = 8

# 8GB服务器建议值
# innodb_buffer_pool_size = 6G
# innodb_buffer_pool_instances = 16

# 查询缓存 (MySQL 8.0已移除)
# query_cache_type = 1
# query_cache_size = 256M
# query_cache_limit = 4M

# 排序缓冲区
sort_buffer_size = 4M
read_buffer_size = 2M
read_rnd_buffer_size = 8M
join_buffer_size = 8M

# 线程缓存
thread_cache_size = 50
thread_stack = 256K

# 表缓存
table_open_cache = 4000
table_definition_cache = 2000

# 临时表
tmp_table_size = 256M
max_heap_table_size = 256M

# MyISAM配置
key_buffer_size = 128M
myisam_sort_buffer_size = 128M

# ================================
# InnoDB性能配置
# ================================

# 文件格式
innodb_file_format = Barracuda
innodb_file_per_table = 1
innodb_large_prefix = 1

# 日志配置
innodb_log_file_size = 512M
innodb_log_files_in_group = 2
innodb_log_buffer_size = 64M

# 刷新策略
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# 并发配置
innodb_thread_concurrency = 0
innodb_read_io_threads = 8
innodb_write_io_threads = 8

# 锁配置
innodb_lock_wait_timeout = 50
innodb_deadlock_detect = ON

# 脏页配置
innodb_max_dirty_pages_pct = 75
innodb_max_dirty_pages_pct_lwm = 10

# IO配置
innodb_io_capacity = 1000
innodb_io_capacity_max = 2000

# 自适应哈希索引
innodb_adaptive_hash_index = ON

# 统计信息
innodb_stats_persistent = ON
innodb_stats_auto_recalc = ON

# ================================
# 复制配置 (如果使用主从复制)
# ================================
# server-id = 1
# log-bin = mysql-bin
# binlog_format = ROW
# expire_logs_days = 7
# max_binlog_size = 500M
# sync_binlog = 1

# 从库配置
# relay-log = relay-bin
# relay-log-index = relay-bin.index
# log-slave-updates = 1
# read_only = 1

# ================================
# 日志配置
# ================================

# 错误日志
log-error = /var/log/mysql/error.log

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 1
log_queries_not_using_indexes = 1
log_slow_admin_statements = 1
log_slow_slave_statements = 1
min_examined_row_limit = 1000

# 二进制日志
log-bin = /var/log/mysql/mysql-bin
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 500M

# 通用查询日志 (生产环境通常关闭)
# general_log = 1
# general_log_file = /var/log/mysql/general.log

# ================================
# 安全配置
# ================================
# 禁用本地文件加载
local_infile = 0

# 禁用符号链接
symbolic-links = 0

# SQL模式
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION

# ================================
# 性能监控配置
# ================================
# 性能模式
performance_schema = ON
performance_schema_max_table_instances = 12500
performance_schema_max_table_handles = 4000

# 信息模式
information_schema_stats_expiry = 86400

# ================================
# 优化器配置
# ================================
# 优化器开关
optimizer_switch = 'index_merge=on,index_merge_union=on,index_merge_sort_union=on,index_merge_intersection=on,engine_condition_pushdown=on,index_condition_pushdown=on,mrr=on,mrr_cost_based=on,block_nested_loop=on,batched_key_access=off,materialization=on,semijoin=on,loosescan=on,firstmatch=on,duplicateweedout=on,subquery_materialization_cost_based=on,use_index_extensions=on,condition_fanout_filter=on,derived_merge=on'

# 优化器搜索深度
optimizer_search_depth = 62

# 优化器剪枝级别
optimizer_prune_level = 1

# ================================
# 分区配置
# ================================
# 开启分区支持
# plugin-load-add = ha_partition.so

# ================================
# 其他性能配置
# ================================
# 禁用DNS解析
skip-name-resolve = 1

# 禁用主机缓存
host_cache_size = 0

# 自动提交
autocommit = 1

# 事务隔离级别
transaction_isolation = READ-COMMITTED

# 最大包大小
max_allowed_packet = 64M

# 组提交
binlog_group_commit_sync_delay = 0
binlog_group_commit_sync_no_delay_count = 0

[mysql]
default-character-set = utf8mb4

[mysqldump]
quick
max_allowed_packet = 64M
single-transaction
routines
triggers

[mysql_safe]
log-error = /var/log/mysql/error.log
pid-file = /var/run/mysqld/mysqld.pid

# ================================
# 监控查询示例
# ================================

# 查看当前连接数
# SELECT COUNT(*) FROM information_schema.PROCESSLIST;

# 查看缓冲池使用情况
# SELECT POOL_ID, POOL_SIZE, FREE_BUFFERS, DATABASE_PAGES 
# FROM information_schema.INNODB_BUFFER_POOL_STATS;

# 查看慢查询统计
# SELECT COUNT(*) FROM mysql.slow_log WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR);

# 查看表锁等待
# SELECT * FROM information_schema.INNODB_LOCKS;

# 查看事务状态
# SELECT * FROM information_schema.INNODB_TRX;
