# SeatMaster JVM 性能优化配置
# 根据不同环境和硬件配置选择合适的JVM参数

# ================================
# 基础内存配置
# ================================

# 小型部署 (2GB RAM)
JVM_OPTS_SMALL="-Xms512m -Xmx1g"

# 中型部署 (4GB RAM)
JVM_OPTS_MEDIUM="-Xms1g -Xmx2g"

# 大型部署 (8GB+ RAM)
JVM_OPTS_LARGE="-Xms2g -Xmx4g"

# ================================
# 垃圾回收器配置
# ================================

# G1GC配置 (推荐用于大多数场景)
GC_OPTS_G1="-XX:+UseG1GC \
-XX:MaxGCPauseMillis=200 \
-XX:G1HeapRegionSize=16m \
-XX:G1NewSizePercent=30 \
-XX:G1MaxNewSizePercent=40 \
-XX:G1MixedGCCountTarget=8 \
-XX:InitiatingHeapOccupancyPercent=45 \
-XX:G1MixedGCLiveThresholdPercent=85 \
-XX:G1HeapWastePercent=5"

# ZGC配置 (适用于大内存、低延迟场景)
GC_OPTS_ZGC="-XX:+UnlockExperimentalVMOptions \
-XX:+UseZGC \
-XX:+UseLargePages"

# Parallel GC配置 (适用于吞吐量优先场景)
GC_OPTS_PARALLEL="-XX:+UseParallelGC \
-XX:ParallelGCThreads=4 \
-XX:+UseParallelOldGC"

# ================================
# 性能调优参数
# ================================

# JIT编译优化
JIT_OPTS="-XX:+TieredCompilation \
-XX:TieredStopAtLevel=4 \
-XX:+UseCodeCacheFlushing \
-XX:ReservedCodeCacheSize=256m \
-XX:InitialCodeCacheSize=64m"

# 字符串优化
STRING_OPTS="-XX:+UseStringDeduplication \
-XX:+OptimizeStringConcat \
-XX:+UseCompressedOops \
-XX:+UseCompressedClassPointers"

# 类加载优化
CLASS_OPTS="-XX:+TieredCompilation \
-XX:+UseSharedSpaces \
-XX:SharedArchiveFile=app-cds.jsa"

# ================================
# 监控和诊断参数
# ================================

# GC日志配置
GC_LOG_OPTS="-Xlog:gc*:logs/gc.log:time,tags \
-XX:+UseGCLogFileRotation \
-XX:NumberOfGCLogFiles=5 \
-XX:GCLogFileSize=100M"

# JFR配置
JFR_OPTS="-XX:+FlightRecorder \
-XX:StartFlightRecording=duration=60s,filename=logs/app-startup.jfr \
-XX:FlightRecorderOptions=settings=profile"

# 堆转储配置
HEAP_DUMP_OPTS="-XX:+HeapDumpOnOutOfMemoryError \
-XX:HeapDumpPath=logs/heapdump.hprof \
-XX:+ExitOnOutOfMemoryError"

# ================================
# 系统属性配置
# ================================

# 基础系统属性
SYSTEM_OPTS="-Djava.awt.headless=true \
-Dfile.encoding=UTF-8 \
-Duser.timezone=Asia/Shanghai \
-Djava.security.egd=file:/dev/./urandom \
-Djava.net.preferIPv4Stack=true"

# 网络优化
NETWORK_OPTS="-Djava.net.useSystemProxies=true \
-Dnetworkaddress.cache.ttl=60 \
-Dnetworkaddress.cache.negative.ttl=10"

# 临时目录配置
TEMP_OPTS="-Djava.io.tmpdir=/tmp/seatmaster \
-Dspring.profiles.active=prod"

# ================================
# 完整配置组合
# ================================

# 生产环境推荐配置 (4GB RAM)
PRODUCTION_JVM_OPTS="${JVM_OPTS_MEDIUM} \
${GC_OPTS_G1} \
${JIT_OPTS} \
${STRING_OPTS} \
${GC_LOG_OPTS} \
${HEAP_DUMP_OPTS} \
${SYSTEM_OPTS} \
${NETWORK_OPTS} \
${TEMP_OPTS}"

# 开发环境配置
DEVELOPMENT_JVM_OPTS="${JVM_OPTS_SMALL} \
${GC_OPTS_G1} \
${SYSTEM_OPTS} \
-XX:+PrintGCDetails \
-XX:+PrintGCTimeStamps"

# 高性能配置 (8GB+ RAM)
HIGH_PERFORMANCE_JVM_OPTS="${JVM_OPTS_LARGE} \
${GC_OPTS_G1} \
${JIT_OPTS} \
${STRING_OPTS} \
${CLASS_OPTS} \
${GC_LOG_OPTS} \
${JFR_OPTS} \
${HEAP_DUMP_OPTS} \
${SYSTEM_OPTS} \
${NETWORK_OPTS} \
${TEMP_OPTS} \
-XX:+AggressiveOpts \
-XX:+UseFastAccessorMethods"

# ================================
# 使用说明
# ================================

# 在启动脚本中使用：
# export JAVA_OPTS="$PRODUCTION_JVM_OPTS"
# java $JAVA_OPTS -jar application.jar

# 在systemd服务中使用：
# Environment="JAVA_OPTS=${PRODUCTION_JVM_OPTS}"

# 在Docker中使用：
# ENV JAVA_OPTS="${PRODUCTION_JVM_OPTS}"
