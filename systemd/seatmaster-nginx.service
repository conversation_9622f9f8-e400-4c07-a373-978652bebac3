# SeatMaster Nginx systemd 服务配置文件
# 自定义Nginx服务配置（如果不使用系统默认nginx）
# 安装位置: /etc/systemd/system/seatmaster-nginx.service

[Unit]
Description=SeatMaster Nginx Service - 座位预约系统反向代理
Documentation=https://nginx.org/en/docs/
After=network.target seatmaster-backend.service seatmaster-frontend.service
Wants=seatmaster-backend.service seatmaster-frontend.service
Requires=network.target

[Service]
Type=forking
User=nginx
Group=nginx
WorkingDirectory=/etc/nginx

# 启动命令
ExecStartPre=/usr/sbin/nginx -t -c /etc/nginx/nginx.conf
ExecStart=/usr/sbin/nginx -c /etc/nginx/nginx.conf
ExecReload=/bin/kill -s HUP $MAINPID
ExecStop=/bin/kill -s QUIT $MAINPID

# PID文件
PIDFile=/var/run/nginx.pid

# 重启策略
Restart=always
RestartSec=5
StartLimitInterval=300
StartLimitBurst=5

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/nginx /var/cache/nginx /var/run

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 标准输出和错误输出
StandardOutput=journal
StandardError=journal
SyslogIdentifier=seatmaster-nginx

[Install]
WantedBy=multi-user.target
