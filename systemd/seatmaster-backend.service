# SeatMaster Backend systemd 服务配置文件
# 安装位置: /etc/systemd/system/seatmaster-backend.service

[Unit]
Description=SeatMaster Backend Service - 座位预约系统后端服务
Documentation=https://github.com/yourusername/seatmaster
After=network.target mysql.service redis.service
Wants=mysql.service redis.service
Requires=network.target

[Service]
Type=simple
User=seatmaster
Group=seatmaster
WorkingDirectory=/opt/seatmaster

# 环境变量文件
EnvironmentFile=-/opt/seatmaster/.env.production

# 启动命令
ExecStart=/usr/bin/java \
    -Xms1g \
    -Xmx2g \
    -XX:+UseG1GC \
    -XX:MaxGCPauseMillis=200 \
    -XX:+UseStringDeduplication \
    -XX:+OptimizeStringConcat \
    -Djava.awt.headless=true \
    -Dfile.encoding=UTF-8 \
    -Duser.timezone=Asia/Shanghai \
    -Dspring.profiles.active=prod \
    -Dlogging.file.path=/var/log/seatmaster \
    -jar /opt/seatmaster/seat-reservation-backend-1.0.0.jar

# 启动前检查
ExecStartPre=/bin/bash -c 'if [ ! -f /opt/seatmaster/seat-reservation-backend-1.0.0.jar ]; then echo "JAR file not found"; exit 1; fi'

# 优雅停止
ExecStop=/bin/kill -TERM $MAINPID
TimeoutStopSec=30
KillMode=mixed
KillSignal=SIGTERM

# 重启策略
Restart=always
RestartSec=10
StartLimitInterval=300
StartLimitBurst=5

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/seatmaster /opt/seatmaster/uploads /opt/seatmaster/temp

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 标准输出和错误输出
StandardOutput=journal
StandardError=journal
SyslogIdentifier=seatmaster-backend

# 环境变量
Environment=JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
Environment=SPRING_PROFILES_ACTIVE=prod
Environment=SERVER_PORT=8081

[Install]
WantedBy=multi-user.target
