# SeatMaster Frontend systemd 服务配置文件
# 使用PM2管理前端服务
# 安装位置: /etc/systemd/system/seatmaster-frontend.service

[Unit]
Description=SeatMaster Frontend Service - 座位预约系统前端服务
Documentation=https://github.com/yourusername/seatmaster
After=network.target seatmaster-backend.service
Wants=seatmaster-backend.service
Requires=network.target

[Service]
Type=forking
User=seatmaster
Group=seatmaster
WorkingDirectory=/opt/seatmaster/frontend

# 环境变量
Environment=NODE_ENV=production
Environment=PORT=3000
Environment=PM2_HOME=/opt/seatmaster/.pm2

# 启动命令
ExecStart=/usr/bin/pm2 start ecosystem.config.js --env production
ExecReload=/usr/bin/pm2 reload ecosystem.config.js --env production
ExecStop=/usr/bin/pm2 stop ecosystem.config.js

# 启动前检查
ExecStartPre=/bin/bash -c 'if [ ! -f /opt/seatmaster/frontend/ecosystem.config.js ]; then echo "PM2 config not found"; exit 1; fi'
ExecStartPre=/usr/bin/pm2 kill

# 重启策略
Restart=always
RestartSec=10
StartLimitInterval=300
StartLimitBurst=5

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/seatmaster /opt/seatmaster/.pm2

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 标准输出和错误输出
StandardOutput=journal
StandardError=journal
SyslogIdentifier=seatmaster-frontend

[Install]
WantedBy=multi-user.target
