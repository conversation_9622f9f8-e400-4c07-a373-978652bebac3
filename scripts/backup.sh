#!/bin/bash

# SeatMaster 备份脚本
# 支持数据库、应用文件、配置文件的完整备份

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_TYPE="${1:-full}"  # full, database, files, config
BACKUP_BASE_DIR="${BACKUP_DIR:-/var/backups/seatmaster}"
RETENTION_DAYS="${RETENTION_DAYS:-30}"

# 数据库配置
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-3306}"
DB_NAME="${DB_NAME:-seat_reservation}"
DB_USER="${DB_USER:-root}"
DB_PASSWORD="${DB_PASSWORD:-root5869087}"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "SeatMaster 备份脚本"
    echo ""
    echo "用法: $0 [备份类型]"
    echo ""
    echo "备份类型:"
    echo "  full       完整备份 (默认)"
    echo "  database   仅数据库备份"
    echo "  files      仅应用文件备份"
    echo "  config     仅配置文件备份"
    echo ""
    echo "环境变量:"
    echo "  BACKUP_DIR       备份目录 (默认: /var/backups/seatmaster)"
    echo "  RETENTION_DAYS   保留天数 (默认: 30)"
    echo "  DB_HOST          数据库主机 (默认: localhost)"
    echo "  DB_USER          数据库用户 (默认: root)"
    echo "  DB_PASSWORD      数据库密码"
    echo ""
    echo "示例:"
    echo "  $0 full          # 完整备份"
    echo "  $0 database      # 仅备份数据库"
    echo "  BACKUP_DIR=/backup $0 full  # 指定备份目录"
    echo ""
}

# 检查依赖
check_dependencies() {
    log_step "检查备份依赖..."
    
    # 检查mysqldump
    if ! command -v mysqldump &> /dev/null; then
        log_error "mysqldump未安装"
        exit 1
    fi
    
    # 检查tar
    if ! command -v tar &> /dev/null; then
        log_error "tar未安装"
        exit 1
    fi
    
    # 检查gzip
    if ! command -v gzip &> /dev/null; then
        log_error "gzip未安装"
        exit 1
    fi
    
    log_info "依赖检查通过"
}

# 创建备份目录
create_backup_dirs() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    BACKUP_DIR="$BACKUP_BASE_DIR/$timestamp"
    
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$BACKUP_DIR/database"
    mkdir -p "$BACKUP_DIR/application"
    mkdir -p "$BACKUP_DIR/config"
    mkdir -p "$BACKUP_DIR/logs"
    
    log_info "备份目录创建: $BACKUP_DIR"
}

# 数据库备份
backup_database() {
    log_step "备份数据库..."
    
    local db_backup_file="$BACKUP_DIR/database/seatmaster_$(date +%Y%m%d_%H%M%S).sql"
    
    # 检查数据库连接
    if ! mysqladmin ping -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" --silent; then
        log_error "无法连接到数据库"
        return 1
    fi
    
    # 备份数据库结构和数据
    mysqldump \
        -h "$DB_HOST" \
        -P "$DB_PORT" \
        -u "$DB_USER" \
        -p"$DB_PASSWORD" \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        --opt \
        --comments \
        --dump-date \
        "$DB_NAME" > "$db_backup_file"
    
    # 压缩备份文件
    gzip "$db_backup_file"
    
    # 备份数据库配置
    if [ -f "/etc/mysql/my.cnf" ]; then
        cp "/etc/mysql/my.cnf" "$BACKUP_DIR/database/"
    fi
    
    if [ -d "/etc/mysql/conf.d" ]; then
        cp -r "/etc/mysql/conf.d" "$BACKUP_DIR/database/"
    fi
    
    # 生成数据库信息
    cat > "$BACKUP_DIR/database/info.txt" << EOF
数据库备份信息
==============
备份时间: $(date)
数据库主机: $DB_HOST:$DB_PORT
数据库名称: $DB_NAME
备份文件: $(basename "$db_backup_file").gz
备份大小: $(du -h "$db_backup_file.gz" | cut -f1)

数据库统计:
$(mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "
SELECT 
    table_name as '表名',
    table_rows as '行数',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as '大小(MB)'
FROM information_schema.tables 
WHERE table_schema = '$DB_NAME'
ORDER BY (data_length + index_length) DESC;
" 2>/dev/null || echo "无法获取数据库统计信息")
EOF
    
    log_info "数据库备份完成: $(basename "$db_backup_file").gz"
}

# 应用文件备份
backup_application() {
    log_step "备份应用文件..."
    
    # 备份后端应用
    if [ -d "/opt/seatmaster" ]; then
        tar -czf "$BACKUP_DIR/application/backend.tar.gz" -C /opt seatmaster
        log_info "后端应用备份完成"
    fi
    
    # 备份前端文件
    if [ -d "/var/www/seatmaster" ]; then
        tar -czf "$BACKUP_DIR/application/frontend.tar.gz" -C /var/www seatmaster
        log_info "前端文件备份完成"
    fi
    
    # 备份源代码 (如果存在)
    if [ -d "$PROJECT_ROOT" ]; then
        tar -czf "$BACKUP_DIR/application/source.tar.gz" \
            --exclude="node_modules" \
            --exclude="target" \
            --exclude="dist" \
            --exclude=".git" \
            --exclude="logs" \
            -C "$(dirname "$PROJECT_ROOT")" "$(basename "$PROJECT_ROOT")"
        log_info "源代码备份完成"
    fi
    
    # 备份上传文件
    if [ -d "/var/seatmaster/uploads" ]; then
        tar -czf "$BACKUP_DIR/application/uploads.tar.gz" -C /var/seatmaster uploads
        log_info "上传文件备份完成"
    fi
    
    # 生成应用信息
    cat > "$BACKUP_DIR/application/info.txt" << EOF
应用文件备份信息
================
备份时间: $(date)

文件列表:
$(ls -lh "$BACKUP_DIR/application/" | grep -v "^total" | awk '{print $9 ": " $5}')

应用版本信息:
$(if [ -f "/opt/seatmaster/version.txt" ]; then cat /opt/seatmaster/version.txt; else echo "版本信息不可用"; fi)

运行状态:
$(systemctl is-active seatmaster-backend 2>/dev/null || echo "后端服务状态未知")
$(systemctl is-active seatmaster-frontend 2>/dev/null || echo "前端服务状态未知")
EOF
}

# 配置文件备份
backup_config() {
    log_step "备份配置文件..."
    
    # 备份Nginx配置
    if [ -d "/etc/nginx" ]; then
        tar -czf "$BACKUP_DIR/config/nginx.tar.gz" -C /etc nginx
        log_info "Nginx配置备份完成"
    fi
    
    # 备份systemd服务文件
    if [ -f "/etc/systemd/system/seatmaster-backend.service" ]; then
        cp /etc/systemd/system/seatmaster-*.service "$BACKUP_DIR/config/"
        log_info "systemd服务文件备份完成"
    fi
    
    # 备份SSL证书
    if [ -d "/etc/nginx/ssl" ]; then
        tar -czf "$BACKUP_DIR/config/ssl.tar.gz" -C /etc/nginx ssl
        log_info "SSL证书备份完成"
    fi
    
    # 备份环境配置
    if [ -f "/opt/seatmaster/.env.production" ]; then
        cp "/opt/seatmaster/.env.production" "$BACKUP_DIR/config/"
        log_info "环境配置备份完成"
    fi
    
    # 备份系统配置
    mkdir -p "$BACKUP_DIR/config/system"
    
    # 内核参数
    if [ -f "/etc/sysctl.d/99-seatmaster-performance.conf" ]; then
        cp "/etc/sysctl.d/99-seatmaster-performance.conf" "$BACKUP_DIR/config/system/"
    fi
    
    # 文件描述符限制
    if [ -f "/etc/security/limits.d/99-seatmaster.conf" ]; then
        cp "/etc/security/limits.d/99-seatmaster.conf" "$BACKUP_DIR/config/system/"
    fi
    
    # 防火墙规则
    if command -v ufw &> /dev/null; then
        ufw status verbose > "$BACKUP_DIR/config/system/ufw-rules.txt"
    elif command -v firewall-cmd &> /dev/null; then
        firewall-cmd --list-all > "$BACKUP_DIR/config/system/firewall-rules.txt"
    fi
    
    # 生成配置信息
    cat > "$BACKUP_DIR/config/info.txt" << EOF
配置文件备份信息
================
备份时间: $(date)

配置文件列表:
$(find "$BACKUP_DIR/config" -type f -name "*.tar.gz" -o -name "*.service" -o -name ".env.*" | while read file; do
    echo "$(basename "$file"): $(du -h "$file" | cut -f1)"
done)

系统配置:
$(ls -la "$BACKUP_DIR/config/system/" 2>/dev/null || echo "无系统配置文件")
EOF
}

# 日志备份
backup_logs() {
    log_step "备份日志文件..."
    
    # 备份应用日志
    if [ -d "/var/log/seatmaster" ]; then
        tar -czf "$BACKUP_DIR/logs/application.tar.gz" -C /var/log seatmaster
        log_info "应用日志备份完成"
    fi
    
    # 备份Nginx日志
    if [ -d "/var/log/nginx" ]; then
        tar -czf "$BACKUP_DIR/logs/nginx.tar.gz" \
            --exclude="*.gz" \
            -C /var/log nginx
        log_info "Nginx日志备份完成"
    fi
    
    # 备份MySQL日志
    if [ -d "/var/log/mysql" ]; then
        tar -czf "$BACKUP_DIR/logs/mysql.tar.gz" -C /var/log mysql
        log_info "MySQL日志备份完成"
    fi
    
    # 备份系统日志 (最近7天)
    journalctl --since="7 days ago" > "$BACKUP_DIR/logs/system.log"
    gzip "$BACKUP_DIR/logs/system.log"
    log_info "系统日志备份完成"
}

# 生成备份清单
generate_manifest() {
    log_step "生成备份清单..."
    
    cat > "$BACKUP_DIR/MANIFEST.txt" << EOF
SeatMaster 备份清单
==================

备份信息:
  备份时间: $(date)
  备份类型: $BACKUP_TYPE
  备份目录: $BACKUP_DIR
  执行用户: $(whoami)
  主机名称: $(hostname)

系统信息:
  操作系统: $(uname -a)
  内存信息: $(free -h | grep "Mem:" | awk '{print $2 " 总计, " $3 " 已用, " $7 " 可用"}')
  磁盘信息: $(df -h / | tail -1 | awk '{print $2 " 总计, " $3 " 已用, " $4 " 可用"}')

备份内容:
$(find "$BACKUP_DIR" -type f -name "*.tar.gz" -o -name "*.sql.gz" -o -name "*.log.gz" | while read file; do
    echo "  $(basename "$file"): $(du -h "$file" | cut -f1)"
done)

文件校验:
$(find "$BACKUP_DIR" -type f \( -name "*.tar.gz" -o -name "*.sql.gz" \) -exec md5sum {} \; | sed 's/^/  /')

恢复说明:
  1. 停止SeatMaster服务
  2. 恢复数据库: gunzip -c database/*.sql.gz | mysql -u root -p seat_reservation
  3. 恢复应用文件: tar -xzf application/backend.tar.gz -C /opt/
  4. 恢复配置文件: tar -xzf config/nginx.tar.gz -C /etc/
  5. 重启服务

备份完成时间: $(date)
总备份大小: $(du -sh "$BACKUP_DIR" | cut -f1)
EOF
    
    log_info "备份清单生成完成"
}

# 清理旧备份
cleanup_old_backups() {
    log_step "清理旧备份..."
    
    local deleted_count=0
    
    # 删除超过保留期的备份
    find "$BACKUP_BASE_DIR" -maxdepth 1 -type d -name "20*" -mtime +$RETENTION_DAYS | while read old_backup; do
        rm -rf "$old_backup"
        deleted_count=$((deleted_count + 1))
        log_info "删除旧备份: $(basename "$old_backup")"
    done
    
    if [ $deleted_count -eq 0 ]; then
        log_info "没有需要清理的旧备份"
    else
        log_info "清理了 $deleted_count 个旧备份"
    fi
}

# 发送备份通知
send_notification() {
    local status="$1"
    local message="$2"
    
    # 邮件通知 (如果配置了)
    if [ -n "$BACKUP_EMAIL" ] && command -v mail &> /dev/null; then
        echo "$message" | mail -s "SeatMaster备份通知 - $status" "$BACKUP_EMAIL"
    fi
    
    # Webhook通知 (如果配置了)
    if [ -n "$BACKUP_WEBHOOK" ]; then
        curl -X POST "$BACKUP_WEBHOOK" \
            -H "Content-Type: application/json" \
            -d "{\"status\":\"$status\",\"message\":\"$message\",\"timestamp\":\"$(date -Iseconds)\"}" \
            &> /dev/null || true
    fi
    
    # 系统日志
    logger -t seatmaster-backup "$status: $message"
}

# 执行备份
perform_backup() {
    local start_time=$(date +%s)
    
    case "$BACKUP_TYPE" in
        full)
            backup_database
            backup_application
            backup_config
            backup_logs
            ;;
        database)
            backup_database
            ;;
        files)
            backup_application
            ;;
        config)
            backup_config
            ;;
        *)
            log_error "无效的备份类型: $BACKUP_TYPE"
            exit 1
            ;;
    esac
    
    generate_manifest
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    local backup_size=$(du -sh "$BACKUP_DIR" | cut -f1)
    
    log_info "备份完成 - 耗时: ${duration}秒, 大小: $backup_size"
    
    # 发送成功通知
    send_notification "SUCCESS" "SeatMaster备份成功完成。类型: $BACKUP_TYPE, 大小: $backup_size, 耗时: ${duration}秒"
}

# 显示备份结果
show_backup_result() {
    log_info "================================"
    log_info "SeatMaster 备份完成!"
    log_info "================================"
    echo ""
    echo "备份信息:"
    echo "  类型: $BACKUP_TYPE"
    echo "  目录: $BACKUP_DIR"
    echo "  大小: $(du -sh "$BACKUP_DIR" | cut -f1)"
    echo ""
    echo "备份内容:"
    find "$BACKUP_DIR" -type f -name "*.tar.gz" -o -name "*.sql.gz" -o -name "*.log.gz" | while read file; do
        echo "  $(basename "$file"): $(du -h "$file" | cut -f1)"
    done
    echo ""
    echo "恢复命令:"
    echo "  完整恢复: ./scripts/restore.sh $BACKUP_DIR"
    echo "  数据库恢复: ./scripts/restore.sh $BACKUP_DIR database"
    echo ""
    echo "备份清单: $BACKUP_DIR/MANIFEST.txt"
    echo ""
}

# 主函数
main() {
    log_info "开始SeatMaster备份..."
    log_info "备份类型: $BACKUP_TYPE"
    
    if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        show_help
        exit 0
    fi
    
    check_dependencies
    create_backup_dirs
    
    # 执行备份
    if perform_backup; then
        cleanup_old_backups
        show_backup_result
    else
        log_error "备份失败"
        send_notification "FAILED" "SeatMaster备份失败，请检查日志"
        exit 1
    fi
    
    log_info "备份完成!"
}

# 信号处理
trap 'log_error "备份被中断"; send_notification "INTERRUPTED" "SeatMaster备份被中断"; exit 1' SIGINT SIGTERM

# 执行主函数
main "$@"
