#!/bin/bash

# SeatMaster 自动备份配置脚本
# 配置定时备份任务和备份策略

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_SCHEDULE="${BACKUP_SCHEDULE:-daily}"  # daily, weekly, custom
BACKUP_TIME="${BACKUP_TIME:-02:00}"
BACKUP_DIR="${BACKUP_DIR:-/var/backups/seatmaster}"
RETENTION_DAYS="${RETENTION_DAYS:-30}"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "SeatMaster 自动备份配置脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -s, --schedule SCHEDULE  备份计划 (daily/weekly/custom)"
    echo "  -t, --time TIME         备份时间 (HH:MM格式)"
    echo "  -d, --dir DIR           备份目录"
    echo "  -r, --retention DAYS    保留天数"
    echo "  -e, --email EMAIL       通知邮箱"
    echo "  -w, --webhook URL       通知Webhook"
    echo "  -h, --help              显示帮助"
    echo ""
    echo "环境变量:"
    echo "  BACKUP_SCHEDULE    备份计划 (默认: daily)"
    echo "  BACKUP_TIME        备份时间 (默认: 02:00)"
    echo "  BACKUP_DIR         备份目录 (默认: /var/backups/seatmaster)"
    echo "  RETENTION_DAYS     保留天数 (默认: 30)"
    echo "  BACKUP_EMAIL       通知邮箱"
    echo "  BACKUP_WEBHOOK     通知Webhook"
    echo ""
    echo "示例:"
    echo "  $0 --schedule daily --time 03:00"
    echo "  $0 --schedule weekly --retention 60"
    echo "  $0 --email <EMAIL> --webhook https://hooks.slack.com/..."
    echo ""
}

# 解析参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -s|--schedule)
                BACKUP_SCHEDULE="$2"
                shift 2
                ;;
            -t|--time)
                BACKUP_TIME="$2"
                shift 2
                ;;
            -d|--dir)
                BACKUP_DIR="$2"
                shift 2
                ;;
            -r|--retention)
                RETENTION_DAYS="$2"
                shift 2
                ;;
            -e|--email)
                BACKUP_EMAIL="$2"
                shift 2
                ;;
            -w|--webhook)
                BACKUP_WEBHOOK="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查权限
check_permissions() {
    if [ "$EUID" -ne 0 ]; then
        log_error "备份配置需要root权限"
        exit 1
    fi
}

# 验证参数
validate_args() {
    # 验证备份计划
    if [[ "$BACKUP_SCHEDULE" != "daily" && "$BACKUP_SCHEDULE" != "weekly" && "$BACKUP_SCHEDULE" != "custom" ]]; then
        log_error "无效的备份计划: $BACKUP_SCHEDULE"
        exit 1
    fi
    
    # 验证时间格式
    if ! [[ "$BACKUP_TIME" =~ ^[0-2][0-9]:[0-5][0-9]$ ]]; then
        log_error "无效的时间格式: $BACKUP_TIME (应为 HH:MM)"
        exit 1
    fi
    
    # 验证保留天数
    if ! [[ "$RETENTION_DAYS" =~ ^[0-9]+$ ]] || [ "$RETENTION_DAYS" -lt 1 ]; then
        log_error "无效的保留天数: $RETENTION_DAYS"
        exit 1
    fi
}

# 创建备份目录
create_backup_dirs() {
    log_step "创建备份目录..."
    
    mkdir -p "$BACKUP_DIR"
    mkdir -p /var/log/seatmaster
    
    # 设置权限
    chmod 755 "$BACKUP_DIR"
    chown root:root "$BACKUP_DIR"
    
    log_info "备份目录创建完成: $BACKUP_DIR"
}

# 创建备份配置文件
create_backup_config() {
    log_step "创建备份配置文件..."
    
    cat > /etc/seatmaster/backup.conf << EOF
# SeatMaster 备份配置文件
# 自动生成于 $(date)

# 备份目录
BACKUP_DIR="$BACKUP_DIR"

# 保留策略
RETENTION_DAYS=$RETENTION_DAYS

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=seat_reservation
DB_USER=root
DB_PASSWORD=root5869087

# 通知配置
BACKUP_EMAIL="${BACKUP_EMAIL:-}"
BACKUP_WEBHOOK="${BACKUP_WEBHOOK:-}"

# 备份选项
BACKUP_COMPRESS=true
BACKUP_VERIFY=true
BACKUP_ENCRYPT=false

# 日志配置
LOG_FILE=/var/log/seatmaster/backup.log
LOG_LEVEL=INFO
EOF
    
    # 设置权限
    chmod 600 /etc/seatmaster/backup.conf
    
    log_info "备份配置文件创建完成"
}

# 创建备份包装脚本
create_backup_wrapper() {
    log_step "创建备份包装脚本..."
    
    cat > /usr/local/bin/seatmaster-backup-wrapper.sh << 'EOF'
#!/bin/bash

# SeatMaster 备份包装脚本
# 用于cron任务调用

# 加载配置
if [ -f /etc/seatmaster/backup.conf ]; then
    source /etc/seatmaster/backup.conf
fi

# 设置日志
LOG_FILE="${LOG_FILE:-/var/log/seatmaster/backup.log}"
exec 1> >(tee -a "$LOG_FILE")
exec 2>&1

echo "========================================"
echo "SeatMaster 自动备份开始: $(date)"
echo "========================================"

# 执行备份
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_SCRIPT="/root/seatMaster/scripts/backup.sh"

if [ -f "$BACKUP_SCRIPT" ]; then
    # 导出环境变量
    export BACKUP_DIR
    export RETENTION_DAYS
    export DB_HOST
    export DB_PORT
    export DB_NAME
    export DB_USER
    export DB_PASSWORD
    export BACKUP_EMAIL
    export BACKUP_WEBHOOK
    
    # 执行备份
    "$BACKUP_SCRIPT" full
    
    backup_status=$?
    
    if [ $backup_status -eq 0 ]; then
        echo "备份成功完成: $(date)"
        
        # 发送成功通知
        if [ -n "$BACKUP_EMAIL" ] && command -v mail &> /dev/null; then
            echo "SeatMaster自动备份成功完成" | mail -s "备份成功 - $(date +%Y-%m-%d)" "$BACKUP_EMAIL"
        fi
    else
        echo "备份失败: $(date)"
        
        # 发送失败通知
        if [ -n "$BACKUP_EMAIL" ] && command -v mail &> /dev/null; then
            echo "SeatMaster自动备份失败，请检查日志" | mail -s "备份失败 - $(date +%Y-%m-%d)" "$BACKUP_EMAIL"
        fi
        
        exit $backup_status
    fi
else
    echo "错误: 备份脚本不存在: $BACKUP_SCRIPT"
    exit 1
fi

echo "========================================"
echo "SeatMaster 自动备份结束: $(date)"
echo "========================================"
EOF
    
    chmod +x /usr/local/bin/seatmaster-backup-wrapper.sh
    
    log_info "备份包装脚本创建完成"
}

# 配置cron任务
setup_cron_job() {
    log_step "配置cron任务..."
    
    # 解析时间
    local hour=$(echo "$BACKUP_TIME" | cut -d: -f1)
    local minute=$(echo "$BACKUP_TIME" | cut -d: -f2)
    
    # 生成cron表达式
    local cron_expr
    case "$BACKUP_SCHEDULE" in
        daily)
            cron_expr="$minute $hour * * *"
            ;;
        weekly)
            cron_expr="$minute $hour * * 0"  # 每周日
            ;;
        custom)
            log_warn "自定义计划需要手动配置cron表达式"
            cron_expr="# $minute $hour * * * # 请根据需要修改"
            ;;
    esac
    
    # 创建cron任务文件
    cat > /etc/cron.d/seatmaster-backup << EOF
# SeatMaster 自动备份任务
# 生成于 $(date)

SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin

# 备份任务 ($BACKUP_SCHEDULE at $BACKUP_TIME)
$cron_expr root /usr/local/bin/seatmaster-backup-wrapper.sh

# 日志轮转 (每日凌晨1点)
0 1 * * * root find /var/log/seatmaster -name "*.log" -size +100M -exec gzip {} \;

# 清理旧日志 (每周清理)
0 2 * * 0 root find /var/log/seatmaster -name "*.log.gz" -mtime +30 -delete
EOF
    
    # 设置权限
    chmod 644 /etc/cron.d/seatmaster-backup
    
    # 重启cron服务
    systemctl restart cron || systemctl restart crond
    
    log_info "cron任务配置完成: $BACKUP_SCHEDULE at $BACKUP_TIME"
}

# 创建备份监控脚本
create_backup_monitor() {
    log_step "创建备份监控脚本..."
    
    cat > /usr/local/bin/seatmaster-backup-monitor.sh << 'EOF'
#!/bin/bash

# SeatMaster 备份监控脚本

# 配置
BACKUP_DIR="${BACKUP_DIR:-/var/backups/seatmaster}"
LOG_FILE="/var/log/seatmaster/backup.log"
ALERT_EMAIL="${BACKUP_EMAIL:-}"

echo "SeatMaster 备份监控报告 - $(date)"
echo "=================================="

# 检查备份目录
if [ -d "$BACKUP_DIR" ]; then
    echo "备份目录: $BACKUP_DIR"
    echo "总大小: $(du -sh "$BACKUP_DIR" 2>/dev/null | cut -f1 || echo '未知')"
    
    # 最近的备份
    latest_backup=$(find "$BACKUP_DIR" -maxdepth 1 -type d -name "20*" | sort | tail -1)
    if [ -n "$latest_backup" ]; then
        echo "最新备份: $(basename "$latest_backup")"
        echo "备份大小: $(du -sh "$latest_backup" | cut -f1)"
        echo "备份时间: $(stat -c %y "$latest_backup" | cut -d. -f1)"
        
        # 检查备份完整性
        if [ -f "$latest_backup/MANIFEST.txt" ]; then
            echo "备份状态: 完整"
        else
            echo "备份状态: 不完整"
        fi
    else
        echo "警告: 未找到备份文件"
    fi
    
    # 备份数量统计
    backup_count=$(find "$BACKUP_DIR" -maxdepth 1 -type d -name "20*" | wc -l)
    echo "备份总数: $backup_count"
    
    # 磁盘空间检查
    disk_usage=$(df "$BACKUP_DIR" | tail -1 | awk '{print $5}' | sed 's/%//')
    echo "磁盘使用率: ${disk_usage}%"
    
    if [ "$disk_usage" -gt 85 ]; then
        echo "警告: 磁盘空间不足"
    fi
else
    echo "错误: 备份目录不存在"
fi

echo ""

# 检查备份日志
if [ -f "$LOG_FILE" ]; then
    echo "最近的备份日志:"
    echo "================"
    tail -20 "$LOG_FILE" | grep -E "(备份|错误|失败|成功)" | tail -5
    
    # 检查最近的错误
    recent_errors=$(grep -c "错误\|失败\|ERROR\|FAILED" "$LOG_FILE" 2>/dev/null || echo "0")
    if [ "$recent_errors" -gt 0 ]; then
        echo ""
        echo "警告: 发现 $recent_errors 个错误"
    fi
else
    echo "警告: 备份日志文件不存在"
fi

echo ""

# 检查cron任务
echo "备份计划:"
echo "========="
if [ -f "/etc/cron.d/seatmaster-backup" ]; then
    grep -v "^#" /etc/cron.d/seatmaster-backup | grep -v "^$"
else
    echo "警告: 备份计划未配置"
fi

echo ""
echo "监控完成 - $(date)"
EOF
    
    chmod +x /usr/local/bin/seatmaster-backup-monitor.sh
    
    # 添加监控任务到cron
    echo "# 备份监控 (每日早上8点)" >> /etc/cron.d/seatmaster-backup
    echo "0 8 * * * root /usr/local/bin/seatmaster-backup-monitor.sh >> /var/log/seatmaster/backup-monitor.log 2>&1" >> /etc/cron.d/seatmaster-backup
    
    log_info "备份监控脚本创建完成"
}

# 测试备份配置
test_backup_config() {
    log_step "测试备份配置..."
    
    # 测试备份脚本
    if [ -f "$SCRIPT_DIR/backup.sh" ]; then
        log_info "备份脚本存在"
    else
        log_error "备份脚本不存在: $SCRIPT_DIR/backup.sh"
        return 1
    fi
    
    # 测试数据库连接
    if command -v mysql &> /dev/null; then
        if mysql -h localhost -u root -proot5869087 -e "SELECT 1" &>/dev/null; then
            log_info "数据库连接测试通过"
        else
            log_warn "数据库连接测试失败"
        fi
    else
        log_warn "MySQL客户端未安装"
    fi
    
    # 测试备份目录权限
    if [ -w "$BACKUP_DIR" ]; then
        log_info "备份目录权限正常"
    else
        log_error "备份目录权限不足: $BACKUP_DIR"
        return 1
    fi
    
    # 测试邮件通知 (如果配置了)
    if [ -n "$BACKUP_EMAIL" ]; then
        if command -v mail &> /dev/null; then
            echo "SeatMaster备份配置测试" | mail -s "备份配置测试" "$BACKUP_EMAIL" 2>/dev/null && \
                log_info "邮件通知测试成功" || log_warn "邮件通知测试失败"
        else
            log_warn "邮件客户端未安装，无法发送通知"
        fi
    fi
    
    log_info "备份配置测试完成"
}

# 显示配置结果
show_setup_result() {
    log_info "================================"
    log_info "SeatMaster 自动备份配置完成!"
    log_info "================================"
    echo ""
    echo "备份配置:"
    echo "  计划: $BACKUP_SCHEDULE"
    echo "  时间: $BACKUP_TIME"
    echo "  目录: $BACKUP_DIR"
    echo "  保留: $RETENTION_DAYS 天"
    if [ -n "$BACKUP_EMAIL" ]; then
        echo "  邮件: $BACKUP_EMAIL"
    fi
    if [ -n "$BACKUP_WEBHOOK" ]; then
        echo "  Webhook: $BACKUP_WEBHOOK"
    fi
    echo ""
    echo "管理命令:"
    echo "  手动备份: $SCRIPT_DIR/backup.sh"
    echo "  备份监控: /usr/local/bin/seatmaster-backup-monitor.sh"
    echo "  查看日志: tail -f /var/log/seatmaster/backup.log"
    echo "  查看计划: cat /etc/cron.d/seatmaster-backup"
    echo ""
    echo "配置文件:"
    echo "  备份配置: /etc/seatmaster/backup.conf"
    echo "  cron任务: /etc/cron.d/seatmaster-backup"
    echo "  日志文件: /var/log/seatmaster/backup.log"
    echo ""
    echo "下次备份时间:"
    case "$BACKUP_SCHEDULE" in
        daily)
            echo "  每日 $BACKUP_TIME"
            ;;
        weekly)
            echo "  每周日 $BACKUP_TIME"
            ;;
        custom)
            echo "  自定义计划 (需手动配置)"
            ;;
    esac
    echo ""
}

# 主函数
main() {
    log_info "开始配置SeatMaster自动备份..."
    
    parse_args "$@"
    check_permissions
    validate_args
    
    # 创建必要目录
    mkdir -p /etc/seatmaster
    
    create_backup_dirs
    create_backup_config
    create_backup_wrapper
    setup_cron_job
    create_backup_monitor
    test_backup_config
    show_setup_result
    
    log_info "自动备份配置完成!"
}

# 信号处理
trap 'log_error "配置被中断"; exit 1' SIGINT SIGTERM

# 执行主函数
main "$@"
