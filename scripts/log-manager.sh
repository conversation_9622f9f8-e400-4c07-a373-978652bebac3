#!/bin/bash

# SeatMaster 日志管理脚本
# 用于日志收集、分析和清理

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_BASE_DIR="/var/log/seatmaster"
BACKUP_DIR="/var/backups/seatmaster/logs"
RETENTION_DAYS="${RETENTION_DAYS:-30}"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "SeatMaster 日志管理脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  collect    收集所有日志"
    echo "  analyze    分析日志"
    echo "  clean      清理旧日志"
    echo "  rotate     轮转日志"
    echo "  backup     备份日志"
    echo "  tail       实时查看日志"
    echo "  search     搜索日志"
    echo ""
    echo "选项:"
    echo "  --days N       保留天数（默认30）"
    echo "  --service S    指定服务（backend/frontend/nginx）"
    echo "  --level L      日志级别（ERROR/WARN/INFO/DEBUG）"
    echo "  --pattern P    搜索模式"
    echo "  --lines N      显示行数"
    echo ""
    echo "示例:"
    echo "  $0 collect                    # 收集所有日志"
    echo "  $0 analyze --service backend  # 分析后端日志"
    echo "  $0 clean --days 7             # 清理7天前的日志"
    echo "  $0 search --pattern ERROR     # 搜索错误日志"
    echo "  $0 tail --service backend     # 实时查看后端日志"
    echo ""
}

# 收集日志
collect_logs() {
    log_step "收集系统日志..."
    
    local output_dir="$BACKUP_DIR/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$output_dir"
    
    # 收集应用日志
    if [ -d "$LOG_BASE_DIR" ]; then
        cp -r "$LOG_BASE_DIR" "$output_dir/application"
        log_info "应用日志已收集"
    fi
    
    # 收集系统日志
    journalctl -u seatmaster-backend --since "24 hours ago" > "$output_dir/backend-system.log"
    journalctl -u seatmaster-frontend --since "24 hours ago" > "$output_dir/frontend-system.log"
    journalctl -u nginx --since "24 hours ago" > "$output_dir/nginx-system.log"
    log_info "系统日志已收集"
    
    # 收集Nginx访问日志
    if [ -f "/var/log/nginx/access.log" ]; then
        cp /var/log/nginx/access.log "$output_dir/"
    fi
    if [ -f "/var/log/nginx/error.log" ]; then
        cp /var/log/nginx/error.log "$output_dir/"
    fi
    log_info "Nginx日志已收集"
    
    # 收集系统信息
    cat > "$output_dir/system-info.txt" << EOF
收集时间: $(date)
系统信息: $(uname -a)
内存使用: $(free -h)
磁盘使用: $(df -h)
CPU信息: $(lscpu | head -10)
网络连接: $(ss -tuln)
进程信息: $(ps aux | grep -E "(java|nginx|mysql)" | grep -v grep)
EOF
    
    # 压缩日志
    cd "$(dirname "$output_dir")"
    tar -czf "$(basename "$output_dir").tar.gz" "$(basename "$output_dir")"
    rm -rf "$output_dir"
    
    log_info "日志收集完成: $(basename "$output_dir").tar.gz"
}

# 分析日志
analyze_logs() {
    local service="${1:-all}"
    
    log_step "分析日志 (服务: $service)..."
    
    local report_file="/tmp/seatmaster-log-analysis-$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
SeatMaster 日志分析报告
=====================

分析时间: $(date)
分析服务: $service

EOF
    
    # 分析后端日志
    if [ "$service" = "all" ] || [ "$service" = "backend" ]; then
        echo "后端日志分析:" >> "$report_file"
        echo "=============" >> "$report_file"
        
        local backend_log="$LOG_BASE_DIR/seatmaster-backend.log"
        if [ -f "$backend_log" ]; then
            echo "错误统计:" >> "$report_file"
            grep -c "ERROR" "$backend_log" 2>/dev/null | sed 's/^/  ERROR: /' >> "$report_file"
            grep -c "WARN" "$backend_log" 2>/dev/null | sed 's/^/  WARN: /' >> "$report_file"
            
            echo "" >> "$report_file"
            echo "最近错误 (最新10条):" >> "$report_file"
            grep "ERROR" "$backend_log" | tail -10 | sed 's/^/  /' >> "$report_file"
            
            echo "" >> "$report_file"
            echo "异常统计:" >> "$report_file"
            grep -o "Exception[^:]*" "$backend_log" | sort | uniq -c | sort -nr | head -5 | sed 's/^/  /' >> "$report_file"
        else
            echo "  后端日志文件不存在" >> "$report_file"
        fi
        echo "" >> "$report_file"
    fi
    
    # 分析Nginx日志
    if [ "$service" = "all" ] || [ "$service" = "nginx" ]; then
        echo "Nginx日志分析:" >> "$report_file"
        echo "==============" >> "$report_file"
        
        local nginx_access="/var/log/nginx/access.log"
        local nginx_error="/var/log/nginx/error.log"
        
        if [ -f "$nginx_access" ]; then
            echo "访问统计 (今日):" >> "$report_file"
            awk -v today="$(date +%d/%b/%Y)" '$0 ~ today {print $9}' "$nginx_access" | sort | uniq -c | sort -nr | head -10 | sed 's/^/  /' >> "$report_file"
            
            echo "" >> "$report_file"
            echo "热门页面 (今日前10):" >> "$report_file"
            awk -v today="$(date +%d/%b/%Y)" '$0 ~ today {print $7}' "$nginx_access" | sort | uniq -c | sort -nr | head -10 | sed 's/^/  /' >> "$report_file"
            
            echo "" >> "$report_file"
            echo "IP访问统计 (今日前10):" >> "$report_file"
            awk -v today="$(date +%d/%b/%Y)" '$0 ~ today {print $1}' "$nginx_access" | sort | uniq -c | sort -nr | head -10 | sed 's/^/  /' >> "$report_file"
        fi
        
        if [ -f "$nginx_error" ]; then
            echo "" >> "$report_file"
            echo "错误统计 (今日):" >> "$report_file"
            grep "$(date +%Y/%m/%d)" "$nginx_error" | wc -l | sed 's/^/  总错误数: /' >> "$report_file"
        fi
        echo "" >> "$report_file"
    fi
    
    # 分析系统资源
    echo "系统资源分析:" >> "$report_file"
    echo "============" >> "$report_file"
    echo "当前内存使用:" >> "$report_file"
    free -h | sed 's/^/  /' >> "$report_file"
    echo "" >> "$report_file"
    echo "当前磁盘使用:" >> "$report_file"
    df -h | sed 's/^/  /' >> "$report_file"
    echo "" >> "$report_file"
    echo "当前负载:" >> "$report_file"
    uptime | sed 's/^/  /' >> "$report_file"
    
    log_info "日志分析完成: $report_file"
    
    # 显示摘要
    echo ""
    echo "分析摘要:"
    echo "========"
    if [ -f "$LOG_BASE_DIR/seatmaster-backend.log" ]; then
        local error_count=$(grep -c "ERROR" "$LOG_BASE_DIR/seatmaster-backend.log" 2>/dev/null || echo "0")
        local warn_count=$(grep -c "WARN" "$LOG_BASE_DIR/seatmaster-backend.log" 2>/dev/null || echo "0")
        echo "后端错误数: $error_count"
        echo "后端警告数: $warn_count"
    fi
    
    if [ -f "/var/log/nginx/access.log" ]; then
        local today_requests=$(awk -v today="$(date +%d/%b/%Y)" '$0 ~ today' /var/log/nginx/access.log | wc -l)
        echo "今日请求数: $today_requests"
    fi
}

# 清理日志
clean_logs() {
    local days="${1:-$RETENTION_DAYS}"
    
    log_step "清理 $days 天前的日志..."
    
    # 清理应用日志
    find "$LOG_BASE_DIR" -name "*.log" -mtime +$days -delete 2>/dev/null || true
    find "$LOG_BASE_DIR" -name "*.log.*" -mtime +$days -delete 2>/dev/null || true
    
    # 清理备份日志
    find "$BACKUP_DIR" -name "*.tar.gz" -mtime +$days -delete 2>/dev/null || true
    
    # 清理系统日志
    journalctl --vacuum-time=${days}d
    
    log_info "日志清理完成"
}

# 轮转日志
rotate_logs() {
    log_step "轮转日志文件..."
    
    # 轮转应用日志
    for log_file in "$LOG_BASE_DIR"/*.log; do
        if [ -f "$log_file" ] && [ -s "$log_file" ]; then
            local base_name=$(basename "$log_file" .log)
            local timestamp=$(date +%Y%m%d_%H%M%S)
            
            mv "$log_file" "${log_file}.${timestamp}"
            touch "$log_file"
            
            # 压缩旧日志
            gzip "${log_file}.${timestamp}"
            
            log_info "轮转日志: $log_file"
        fi
    done
    
    # 重启服务以重新打开日志文件
    systemctl reload seatmaster-backend || true
    systemctl reload nginx || true
    
    log_info "日志轮转完成"
}

# 备份日志
backup_logs() {
    log_step "备份日志文件..."
    
    local backup_file="$BACKUP_DIR/logs-backup-$(date +%Y%m%d_%H%M%S).tar.gz"
    mkdir -p "$BACKUP_DIR"
    
    # 创建备份
    tar -czf "$backup_file" \
        -C / \
        var/log/seatmaster \
        var/log/nginx/access.log* \
        var/log/nginx/error.log* \
        2>/dev/null || true
    
    log_info "日志备份完成: $backup_file"
}

# 实时查看日志
tail_logs() {
    local service="${1:-backend}"
    local lines="${2:-50}"
    
    case "$service" in
        backend)
            if [ -f "$LOG_BASE_DIR/seatmaster-backend.log" ]; then
                tail -f -n "$lines" "$LOG_BASE_DIR/seatmaster-backend.log"
            else
                journalctl -u seatmaster-backend -f -n "$lines"
            fi
            ;;
        frontend)
            journalctl -u seatmaster-frontend -f -n "$lines"
            ;;
        nginx)
            tail -f -n "$lines" /var/log/nginx/access.log /var/log/nginx/error.log
            ;;
        all)
            tail -f -n "$lines" \
                "$LOG_BASE_DIR"/*.log \
                /var/log/nginx/access.log \
                /var/log/nginx/error.log \
                2>/dev/null || true
            ;;
        *)
            log_error "未知服务: $service"
            exit 1
            ;;
    esac
}

# 搜索日志
search_logs() {
    local pattern="$1"
    local service="${2:-all}"
    local level="${3:-all}"
    
    if [ -z "$pattern" ]; then
        log_error "请提供搜索模式"
        exit 1
    fi
    
    log_step "搜索日志 (模式: $pattern, 服务: $service, 级别: $level)..."
    
    local search_files=()
    
    case "$service" in
        backend)
            search_files+=("$LOG_BASE_DIR/seatmaster-backend.log")
            ;;
        nginx)
            search_files+=("/var/log/nginx/access.log" "/var/log/nginx/error.log")
            ;;
        all)
            search_files+=("$LOG_BASE_DIR"/*.log)
            search_files+=("/var/log/nginx/access.log" "/var/log/nginx/error.log")
            ;;
    esac
    
    # 构建grep命令
    local grep_cmd="grep -n"
    
    if [ "$level" != "all" ]; then
        grep_cmd="$grep_cmd -E '$level.*$pattern|$pattern.*$level'"
    else
        grep_cmd="$grep_cmd '$pattern'"
    fi
    
    # 执行搜索
    for file in "${search_files[@]}"; do
        if [ -f "$file" ]; then
            echo "=== $file ==="
            eval "$grep_cmd '$file'" | head -20 || true
            echo ""
        fi
    done
}

# 主函数
main() {
    local command="${1:-help}"
    shift || true
    
    # 解析参数
    local service="all"
    local days="$RETENTION_DAYS"
    local level="all"
    local pattern=""
    local lines="50"
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --service)
                service="$2"
                shift 2
                ;;
            --days)
                days="$2"
                shift 2
                ;;
            --level)
                level="$2"
                shift 2
                ;;
            --pattern)
                pattern="$2"
                shift 2
                ;;
            --lines)
                lines="$2"
                shift 2
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 创建必要目录
    mkdir -p "$LOG_BASE_DIR"
    mkdir -p "$BACKUP_DIR"
    
    case "$command" in
        collect)
            collect_logs
            ;;
        analyze)
            analyze_logs "$service"
            ;;
        clean)
            clean_logs "$days"
            ;;
        rotate)
            rotate_logs
            ;;
        backup)
            backup_logs
            ;;
        tail)
            tail_logs "$service" "$lines"
            ;;
        search)
            search_logs "$pattern" "$service" "$level"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
