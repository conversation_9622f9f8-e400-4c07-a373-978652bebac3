#!/bin/bash

# SeatMaster 恢复脚本
# 支持从备份中恢复数据库、应用文件、配置文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
BACKUP_DIR="$1"
RESTORE_TYPE="${2:-full}"  # full, database, files, config
FORCE_RESTORE="${3:-false}"

# 数据库配置
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-3306}"
DB_NAME="${DB_NAME:-seat_reservation}"
DB_USER="${DB_USER:-root}"
DB_PASSWORD="${DB_PASSWORD:-root5869087}"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "SeatMaster 恢复脚本"
    echo ""
    echo "用法: $0 <备份目录> [恢复类型] [强制恢复]"
    echo ""
    echo "参数:"
    echo "  备份目录     备份文件所在目录"
    echo "  恢复类型     full|database|files|config (默认: full)"
    echo "  强制恢复     true|false (默认: false)"
    echo ""
    echo "恢复类型:"
    echo "  full       完整恢复 (默认)"
    echo "  database   仅恢复数据库"
    echo "  files      仅恢复应用文件"
    echo "  config     仅恢复配置文件"
    echo ""
    echo "环境变量:"
    echo "  DB_HOST      数据库主机 (默认: localhost)"
    echo "  DB_USER      数据库用户 (默认: root)"
    echo "  DB_PASSWORD  数据库密码"
    echo ""
    echo "示例:"
    echo "  $0 /var/backups/seatmaster/20241122_143000 full"
    echo "  $0 /var/backups/seatmaster/20241122_143000 database"
    echo "  $0 /backup/seatmaster database true"
    echo ""
}

# 检查参数
check_args() {
    if [ -z "$BACKUP_DIR" ]; then
        log_error "请指定备份目录"
        show_help
        exit 1
    fi
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_error "备份目录不存在: $BACKUP_DIR"
        exit 1
    fi
    
    if [ ! -f "$BACKUP_DIR/MANIFEST.txt" ]; then
        log_error "备份清单文件不存在: $BACKUP_DIR/MANIFEST.txt"
        exit 1
    fi
    
    if [[ "$RESTORE_TYPE" != "full" && "$RESTORE_TYPE" != "database" && "$RESTORE_TYPE" != "files" && "$RESTORE_TYPE" != "config" ]]; then
        log_error "无效的恢复类型: $RESTORE_TYPE"
        show_help
        exit 1
    fi
}

# 检查权限
check_permissions() {
    if [ "$EUID" -ne 0 ]; then
        log_error "恢复操作需要root权限"
        exit 1
    fi
}

# 显示备份信息
show_backup_info() {
    log_step "备份信息:"
    
    if [ -f "$BACKUP_DIR/MANIFEST.txt" ]; then
        echo ""
        head -20 "$BACKUP_DIR/MANIFEST.txt"
        echo ""
    fi
    
    echo "备份内容:"
    find "$BACKUP_DIR" -type f -name "*.tar.gz" -o -name "*.sql.gz" -o -name "*.log.gz" | while read file; do
        echo "  $(basename "$file"): $(du -h "$file" | cut -f1)"
    done
    echo ""
}

# 确认恢复操作
confirm_restore() {
    if [ "$FORCE_RESTORE" = "true" ]; then
        log_warn "强制恢复模式，跳过确认"
        return 0
    fi
    
    log_warn "警告: 恢复操作将覆盖现有数据!"
    echo ""
    echo "恢复信息:"
    echo "  备份目录: $BACKUP_DIR"
    echo "  恢复类型: $RESTORE_TYPE"
    echo "  目标系统: $(hostname)"
    echo ""
    
    read -p "确认要继续恢复吗? (yes/no): " confirm
    if [ "$confirm" != "yes" ]; then
        log_info "恢复操作已取消"
        exit 0
    fi
}

# 停止服务
stop_services() {
    log_step "停止SeatMaster服务..."
    
    # 停止应用服务
    systemctl stop seatmaster-frontend || log_warn "前端服务停止失败"
    systemctl stop seatmaster-backend || log_warn "后端服务停止失败"
    systemctl stop nginx || log_warn "Nginx服务停止失败"
    
    # 等待服务完全停止
    sleep 5
    
    log_info "服务停止完成"
}

# 启动服务
start_services() {
    log_step "启动SeatMaster服务..."
    
    # 启动数据库 (如果需要)
    systemctl start mysql || systemctl start mariadb || log_warn "数据库服务启动失败"
    
    # 等待数据库启动
    sleep 10
    
    # 启动应用服务
    systemctl start seatmaster-backend || log_warn "后端服务启动失败"
    systemctl start seatmaster-frontend || log_warn "前端服务启动失败"
    systemctl start nginx || log_warn "Nginx服务启动失败"
    
    # 检查服务状态
    sleep 10
    
    local backend_status=$(systemctl is-active seatmaster-backend 2>/dev/null || echo "unknown")
    local frontend_status=$(systemctl is-active seatmaster-frontend 2>/dev/null || echo "unknown")
    local nginx_status=$(systemctl is-active nginx 2>/dev/null || echo "unknown")
    
    log_info "服务状态:"
    echo "  后端服务: $backend_status"
    echo "  前端服务: $frontend_status"
    echo "  Nginx服务: $nginx_status"
}

# 恢复数据库
restore_database() {
    log_step "恢复数据库..."
    
    # 查找数据库备份文件
    local db_backup=$(find "$BACKUP_DIR/database" -name "*.sql.gz" | head -1)
    if [ -z "$db_backup" ]; then
        log_error "未找到数据库备份文件"
        return 1
    fi
    
    log_info "数据库备份文件: $(basename "$db_backup")"
    
    # 检查数据库连接
    if ! mysqladmin ping -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" --silent; then
        log_error "无法连接到数据库"
        return 1
    fi
    
    # 备份当前数据库 (如果存在)
    if mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME" 2>/dev/null; then
        log_warn "备份当前数据库..."
        mysqldump -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" \
            --single-transaction --routines --triggers \
            "$DB_NAME" | gzip > "/tmp/current_db_backup_$(date +%Y%m%d_%H%M%S).sql.gz"
    fi
    
    # 删除现有数据库
    mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" \
        -e "DROP DATABASE IF EXISTS $DB_NAME; CREATE DATABASE $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    
    # 恢复数据库
    log_info "正在恢复数据库..."
    gunzip -c "$db_backup" | mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME"
    
    # 验证恢复结果
    local table_count=$(mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" \
        -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$DB_NAME';" -s -N)
    
    log_info "数据库恢复完成，共恢复 $table_count 个表"
    
    # 恢复数据库配置
    if [ -f "$BACKUP_DIR/database/my.cnf" ]; then
        cp "$BACKUP_DIR/database/my.cnf" /etc/mysql/my.cnf.restored
        log_info "数据库配置文件已恢复到 /etc/mysql/my.cnf.restored"
    fi
    
    if [ -d "$BACKUP_DIR/database/conf.d" ]; then
        cp -r "$BACKUP_DIR/database/conf.d" /etc/mysql/conf.d.restored
        log_info "数据库配置目录已恢复到 /etc/mysql/conf.d.restored"
    fi
}

# 恢复应用文件
restore_files() {
    log_step "恢复应用文件..."
    
    # 恢复后端应用
    if [ -f "$BACKUP_DIR/application/backend.tar.gz" ]; then
        log_info "恢复后端应用..."
        
        # 备份当前应用 (如果存在)
        if [ -d "/opt/seatmaster" ]; then
            mv "/opt/seatmaster" "/opt/seatmaster.backup.$(date +%Y%m%d_%H%M%S)"
        fi
        
        # 恢复应用文件
        tar -xzf "$BACKUP_DIR/application/backend.tar.gz" -C /opt/
        
        # 设置权限
        chown -R seatmaster:seatmaster /opt/seatmaster
        chmod +x /opt/seatmaster/*.jar 2>/dev/null || true
        
        log_info "后端应用恢复完成"
    fi
    
    # 恢复前端文件
    if [ -f "$BACKUP_DIR/application/frontend.tar.gz" ]; then
        log_info "恢复前端文件..."
        
        # 备份当前前端 (如果存在)
        if [ -d "/var/www/seatmaster" ]; then
            mv "/var/www/seatmaster" "/var/www/seatmaster.backup.$(date +%Y%m%d_%H%M%S)"
        fi
        
        # 恢复前端文件
        mkdir -p /var/www
        tar -xzf "$BACKUP_DIR/application/frontend.tar.gz" -C /var/www/
        
        # 设置权限
        chown -R www-data:www-data /var/www/seatmaster
        
        log_info "前端文件恢复完成"
    fi
    
    # 恢复上传文件
    if [ -f "$BACKUP_DIR/application/uploads.tar.gz" ]; then
        log_info "恢复上传文件..."
        
        mkdir -p /var/seatmaster
        tar -xzf "$BACKUP_DIR/application/uploads.tar.gz" -C /var/seatmaster/
        
        # 设置权限
        chown -R seatmaster:seatmaster /var/seatmaster/uploads
        
        log_info "上传文件恢复完成"
    fi
}

# 恢复配置文件
restore_config() {
    log_step "恢复配置文件..."
    
    # 恢复Nginx配置
    if [ -f "$BACKUP_DIR/config/nginx.tar.gz" ]; then
        log_info "恢复Nginx配置..."
        
        # 备份当前配置
        if [ -d "/etc/nginx" ]; then
            tar -czf "/etc/nginx.backup.$(date +%Y%m%d_%H%M%S).tar.gz" -C /etc nginx
        fi
        
        # 恢复配置
        tar -xzf "$BACKUP_DIR/config/nginx.tar.gz" -C /etc/
        
        # 测试配置
        if nginx -t; then
            log_info "Nginx配置恢复成功"
        else
            log_error "Nginx配置测试失败，请检查配置"
        fi
    fi
    
    # 恢复systemd服务文件
    if ls "$BACKUP_DIR/config/"*.service &> /dev/null; then
        log_info "恢复systemd服务文件..."
        
        cp "$BACKUP_DIR/config/"*.service /etc/systemd/system/
        systemctl daemon-reload
        
        log_info "systemd服务文件恢复完成"
    fi
    
    # 恢复SSL证书
    if [ -f "$BACKUP_DIR/config/ssl.tar.gz" ]; then
        log_info "恢复SSL证书..."
        
        mkdir -p /etc/nginx
        tar -xzf "$BACKUP_DIR/config/ssl.tar.gz" -C /etc/nginx/
        
        # 设置权限
        chmod 600 /etc/nginx/ssl/privkey.pem 2>/dev/null || true
        chmod 644 /etc/nginx/ssl/fullchain.pem 2>/dev/null || true
        
        log_info "SSL证书恢复完成"
    fi
    
    # 恢复环境配置
    if [ -f "$BACKUP_DIR/config/.env.production" ]; then
        log_info "恢复环境配置..."
        
        cp "$BACKUP_DIR/config/.env.production" /opt/seatmaster/
        chown seatmaster:seatmaster /opt/seatmaster/.env.production
        
        log_info "环境配置恢复完成"
    fi
    
    # 恢复系统配置
    if [ -d "$BACKUP_DIR/config/system" ]; then
        log_info "恢复系统配置..."
        
        # 内核参数
        if [ -f "$BACKUP_DIR/config/system/99-seatmaster-performance.conf" ]; then
            cp "$BACKUP_DIR/config/system/99-seatmaster-performance.conf" /etc/sysctl.d/
        fi
        
        # 文件描述符限制
        if [ -f "$BACKUP_DIR/config/system/99-seatmaster.conf" ]; then
            cp "$BACKUP_DIR/config/system/99-seatmaster.conf" /etc/security/limits.d/
        fi
        
        log_info "系统配置恢复完成"
    fi
}

# 验证恢复结果
verify_restore() {
    log_step "验证恢复结果..."
    
    local errors=0
    
    # 检查数据库
    if [ "$RESTORE_TYPE" = "full" ] || [ "$RESTORE_TYPE" = "database" ]; then
        if mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME" 2>/dev/null; then
            local table_count=$(mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" \
                -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$DB_NAME';" -s -N)
            log_info "数据库验证通过，共 $table_count 个表"
        else
            log_error "数据库验证失败"
            errors=$((errors + 1))
        fi
    fi
    
    # 检查应用文件
    if [ "$RESTORE_TYPE" = "full" ] || [ "$RESTORE_TYPE" = "files" ]; then
        if [ -f "/opt/seatmaster/seat-reservation-backend-1.0.0.jar" ]; then
            log_info "后端应用文件验证通过"
        else
            log_error "后端应用文件验证失败"
            errors=$((errors + 1))
        fi
        
        if [ -f "/var/www/seatmaster/index.html" ]; then
            log_info "前端文件验证通过"
        else
            log_error "前端文件验证失败"
            errors=$((errors + 1))
        fi
    fi
    
    # 检查配置文件
    if [ "$RESTORE_TYPE" = "full" ] || [ "$RESTORE_TYPE" = "config" ]; then
        if nginx -t &>/dev/null; then
            log_info "Nginx配置验证通过"
        else
            log_error "Nginx配置验证失败"
            errors=$((errors + 1))
        fi
    fi
    
    if [ $errors -eq 0 ]; then
        log_info "恢复验证通过"
        return 0
    else
        log_error "恢复验证失败，发现 $errors 个错误"
        return 1
    fi
}

# 显示恢复结果
show_restore_result() {
    log_info "================================"
    log_info "SeatMaster 恢复完成!"
    log_info "================================"
    echo ""
    echo "恢复信息:"
    echo "  备份目录: $BACKUP_DIR"
    echo "  恢复类型: $RESTORE_TYPE"
    echo "  恢复时间: $(date)"
    echo ""
    echo "服务状态:"
    echo "  后端服务: $(systemctl is-active seatmaster-backend 2>/dev/null || echo 'unknown')"
    echo "  前端服务: $(systemctl is-active seatmaster-frontend 2>/dev/null || echo 'unknown')"
    echo "  Nginx服务: $(systemctl is-active nginx 2>/dev/null || echo 'unknown')"
    echo ""
    echo "访问地址:"
    echo "  前端: http://localhost:3000"
    echo "  后端: http://localhost:8081"
    echo ""
    echo "后续步骤:"
    echo "  1. 检查服务日志"
    echo "  2. 验证应用功能"
    echo "  3. 检查数据完整性"
    echo "  4. 更新DNS和证书 (如需要)"
    echo ""
}

# 主函数
main() {
    log_info "开始SeatMaster恢复..."
    
    if [ "$1" = "-h" ] || [ "$1" = "--help" ] || [ -z "$1" ]; then
        show_help
        exit 0
    fi
    
    check_args
    check_permissions
    show_backup_info
    confirm_restore
    
    # 停止服务
    stop_services
    
    # 执行恢复
    case "$RESTORE_TYPE" in
        full)
            restore_database
            restore_files
            restore_config
            ;;
        database)
            restore_database
            ;;
        files)
            restore_files
            ;;
        config)
            restore_config
            ;;
    esac
    
    # 启动服务
    start_services
    
    # 验证恢复结果
    if verify_restore; then
        show_restore_result
        log_info "恢复成功完成!"
    else
        log_error "恢复过程中发现问题，请检查日志"
        exit 1
    fi
}

# 信号处理
trap 'log_error "恢复被中断"; exit 1' SIGINT SIGTERM

# 执行主函数
main "$@"
