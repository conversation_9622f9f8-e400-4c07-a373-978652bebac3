#!/bin/bash

# SeatMaster 健康检查脚本
# 用于监控系统健康状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="/var/log/seatmaster/health-check.log"
ALERT_FILE="/var/log/seatmaster/alerts.log"
CHECK_INTERVAL="${CHECK_INTERVAL:-60}"  # 检查间隔（秒）
ALERT_THRESHOLD="${ALERT_THRESHOLD:-3}"  # 连续失败次数阈值

# 服务配置
BACKEND_URL="http://localhost:8081"
FRONTEND_URL="http://localhost:3000"
DB_HOST="localhost"
DB_PORT="3306"

# 计数器文件
COUNTER_DIR="/tmp/seatmaster-health"
mkdir -p "$COUNTER_DIR"

# 日志函数
log_info() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1"
    echo -e "${GREEN}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

log_warn() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [WARN] $1"
    echo -e "${YELLOW}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
}

log_error() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1"
    echo -e "${RED}$msg${NC}"
    echo "$msg" >> "$LOG_FILE"
    echo "$msg" >> "$ALERT_FILE"
}

log_debug() {
    local msg="[$(date '+%Y-%m-%d %H:%M:%S')] [DEBUG] $1"
    echo -e "${BLUE}$msg${NC}"
    if [ "$DEBUG" = "true" ]; then
        echo "$msg" >> "$LOG_FILE"
    fi
}

# 发送告警
send_alert() {
    local service="$1"
    local message="$2"
    local severity="${3:-warning}"
    
    log_error "ALERT [$severity] $service: $message"
    
    # 发送邮件告警（如果配置了）
    if command -v mail &> /dev/null && [ -n "$ALERT_EMAIL" ]; then
        echo "$message" | mail -s "SeatMaster Alert: $service" "$ALERT_EMAIL"
    fi
    
    # 发送到Webhook（如果配置了）
    if [ -n "$ALERT_WEBHOOK" ]; then
        curl -X POST "$ALERT_WEBHOOK" \
            -H "Content-Type: application/json" \
            -d "{\"service\":\"$service\",\"message\":\"$message\",\"severity\":\"$severity\",\"timestamp\":\"$(date -Iseconds)\"}" \
            &> /dev/null || true
    fi
    
    # 记录到系统日志
    logger -t seatmaster-health "ALERT [$severity] $service: $message"
}

# 检查服务状态
check_service_status() {
    local service="$1"
    local counter_file="$COUNTER_DIR/${service}_failures"
    
    if systemctl is-active --quiet "$service"; then
        log_debug "Service $service is running"
        rm -f "$counter_file"
        return 0
    else
        log_warn "Service $service is not running"
        
        # 增加失败计数
        local failures=$(cat "$counter_file" 2>/dev/null || echo "0")
        failures=$((failures + 1))
        echo "$failures" > "$counter_file"
        
        if [ "$failures" -ge "$ALERT_THRESHOLD" ]; then
            send_alert "$service" "Service has been down for $failures consecutive checks" "critical"
        fi
        
        return 1
    fi
}

# 检查HTTP端点
check_http_endpoint() {
    local name="$1"
    local url="$2"
    local timeout="${3:-10}"
    local counter_file="$COUNTER_DIR/${name}_failures"
    
    if curl -f -s --max-time "$timeout" "$url" > /dev/null; then
        log_debug "HTTP endpoint $name ($url) is healthy"
        rm -f "$counter_file"
        return 0
    else
        log_warn "HTTP endpoint $name ($url) is not responding"
        
        # 增加失败计数
        local failures=$(cat "$counter_file" 2>/dev/null || echo "0")
        failures=$((failures + 1))
        echo "$failures" > "$counter_file"
        
        if [ "$failures" -ge "$ALERT_THRESHOLD" ]; then
            send_alert "$name" "HTTP endpoint has been unreachable for $failures consecutive checks" "critical"
        fi
        
        return 1
    fi
}

# 检查数据库连接
check_database() {
    local counter_file="$COUNTER_DIR/database_failures"
    
    if mysqladmin ping -h "$DB_HOST" -P "$DB_PORT" --silent 2>/dev/null; then
        log_debug "Database connection is healthy"
        rm -f "$counter_file"
        return 0
    else
        log_warn "Database connection failed"
        
        # 增加失败计数
        local failures=$(cat "$counter_file" 2>/dev/null || echo "0")
        failures=$((failures + 1))
        echo "$failures" > "$counter_file"
        
        if [ "$failures" -ge "$ALERT_THRESHOLD" ]; then
            send_alert "database" "Database connection has been failing for $failures consecutive checks" "critical"
        fi
        
        return 1
    fi
}

# 检查磁盘空间
check_disk_space() {
    local threshold="${DISK_THRESHOLD:-85}"  # 磁盘使用率阈值（百分比）
    local counter_file="$COUNTER_DIR/disk_failures"
    
    local usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    
    if [ "$usage" -lt "$threshold" ]; then
        log_debug "Disk usage is $usage% (threshold: $threshold%)"
        rm -f "$counter_file"
        return 0
    else
        log_warn "Disk usage is high: $usage% (threshold: $threshold%)"
        
        # 增加失败计数
        local failures=$(cat "$counter_file" 2>/dev/null || echo "0")
        failures=$((failures + 1))
        echo "$failures" > "$counter_file"
        
        if [ "$failures" -ge "$ALERT_THRESHOLD" ]; then
            send_alert "disk-space" "Disk usage has been above $threshold% for $failures consecutive checks (current: $usage%)" "warning"
        fi
        
        return 1
    fi
}

# 检查内存使用
check_memory_usage() {
    local threshold="${MEMORY_THRESHOLD:-90}"  # 内存使用率阈值（百分比）
    local counter_file="$COUNTER_DIR/memory_failures"
    
    local usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    
    if [ "$usage" -lt "$threshold" ]; then
        log_debug "Memory usage is $usage% (threshold: $threshold%)"
        rm -f "$counter_file"
        return 0
    else
        log_warn "Memory usage is high: $usage% (threshold: $threshold%)"
        
        # 增加失败计数
        local failures=$(cat "$counter_file" 2>/dev/null || echo "0")
        failures=$((failures + 1))
        echo "$failures" > "$counter_file"
        
        if [ "$failures" -ge "$ALERT_THRESHOLD" ]; then
            send_alert "memory" "Memory usage has been above $threshold% for $failures consecutive checks (current: $usage%)" "warning"
        fi
        
        return 1
    fi
}

# 检查CPU使用率
check_cpu_usage() {
    local threshold="${CPU_THRESHOLD:-80}"  # CPU使用率阈值（百分比）
    local counter_file="$COUNTER_DIR/cpu_failures"
    
    # 获取1分钟平均CPU使用率
    local usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
    usage=${usage%.*}  # 去掉小数部分
    
    if [ "$usage" -lt "$threshold" ]; then
        log_debug "CPU usage is $usage% (threshold: $threshold%)"
        rm -f "$counter_file"
        return 0
    else
        log_warn "CPU usage is high: $usage% (threshold: $threshold%)"
        
        # 增加失败计数
        local failures=$(cat "$counter_file" 2>/dev/null || echo "0")
        failures=$((failures + 1))
        echo "$failures" > "$counter_file"
        
        if [ "$failures" -ge "$ALERT_THRESHOLD" ]; then
            send_alert "cpu" "CPU usage has been above $threshold% for $failures consecutive checks (current: $usage%)" "warning"
        fi
        
        return 1
    fi
}

# 检查日志错误
check_log_errors() {
    local log_file="/var/log/seatmaster/seatmaster-backend.log"
    local error_pattern="ERROR|FATAL|Exception"
    local time_window=300  # 5分钟
    local counter_file="$COUNTER_DIR/log_errors"
    
    if [ ! -f "$log_file" ]; then
        return 0
    fi
    
    # 检查最近5分钟的错误日志
    local recent_errors=$(find "$log_file" -newermt "-${time_window} seconds" -exec grep -c "$error_pattern" {} \; 2>/dev/null || echo "0")
    
    if [ "$recent_errors" -eq 0 ]; then
        log_debug "No recent errors in application logs"
        rm -f "$counter_file"
        return 0
    else
        log_warn "Found $recent_errors recent errors in application logs"
        
        # 记录错误计数
        echo "$recent_errors" > "$counter_file"
        
        if [ "$recent_errors" -gt 10 ]; then
            send_alert "application-logs" "High number of errors in application logs: $recent_errors in last 5 minutes" "warning"
        fi
        
        return 1
    fi
}

# 执行单次健康检查
run_health_check() {
    log_info "Starting health check..."
    
    local overall_status=0
    
    # 检查系统服务
    check_service_status "seatmaster-backend" || overall_status=1
    check_service_status "seatmaster-frontend" || overall_status=1
    check_service_status "nginx" || overall_status=1
    
    # 检查HTTP端点
    check_http_endpoint "backend-health" "$BACKEND_URL/actuator/health" || overall_status=1
    check_http_endpoint "frontend" "$FRONTEND_URL" || overall_status=1
    
    # 检查数据库
    check_database || overall_status=1
    
    # 检查系统资源
    check_disk_space || overall_status=1
    check_memory_usage || overall_status=1
    check_cpu_usage || overall_status=1
    
    # 检查应用日志
    check_log_errors || overall_status=1
    
    if [ "$overall_status" -eq 0 ]; then
        log_info "Health check completed - All systems healthy"
    else
        log_warn "Health check completed - Some issues detected"
    fi
    
    return $overall_status
}

# 持续监控模式
monitor_mode() {
    log_info "Starting continuous monitoring mode (interval: ${CHECK_INTERVAL}s)"
    
    while true; do
        run_health_check
        sleep "$CHECK_INTERVAL"
    done
}

# 显示帮助信息
show_help() {
    echo "SeatMaster 健康检查脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -m, --monitor  持续监控模式"
    echo "  -c, --check    执行单次检查"
    echo ""
    echo "环境变量:"
    echo "  CHECK_INTERVAL     检查间隔（秒，默认60）"
    echo "  ALERT_THRESHOLD    告警阈值（连续失败次数，默认3）"
    echo "  DISK_THRESHOLD     磁盘使用率阈值（百分比，默认85）"
    echo "  MEMORY_THRESHOLD   内存使用率阈值（百分比，默认90）"
    echo "  CPU_THRESHOLD      CPU使用率阈值（百分比，默认80）"
    echo "  ALERT_EMAIL        告警邮箱地址"
    echo "  ALERT_WEBHOOK      告警Webhook URL"
    echo "  DEBUG              调试模式（true/false）"
    echo ""
}

# 主函数
main() {
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"
    
    case "${1:-check}" in
        -h|--help)
            show_help
            ;;
        -m|--monitor)
            monitor_mode
            ;;
        -c|--check|*)
            run_health_check
            ;;
    esac
}

# 信号处理
trap 'log_info "Health check interrupted"; exit 0' SIGINT SIGTERM

# 执行主函数
main "$@"
