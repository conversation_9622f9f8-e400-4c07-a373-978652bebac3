#!/bin/bash

# SeatMaster 更新脚本
# 用于滚动更新生产环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
UPDATE_TYPE="${1:-rolling}"  # rolling, blue-green, immediate

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "SeatMaster 更新脚本"
    echo ""
    echo "用法: $0 [更新类型]"
    echo ""
    echo "更新类型:"
    echo "  rolling      滚动更新 (默认)"
    echo "  blue-green   蓝绿部署"
    echo "  immediate    立即更新"
    echo ""
    echo "示例:"
    echo "  $0 rolling     # 滚动更新"
    echo "  $0 blue-green  # 蓝绿部署"
    echo "  $0 immediate   # 立即更新"
    echo ""
}

# 检查参数
check_args() {
    if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        show_help
        exit 0
    fi
    
    if [ "$UPDATE_TYPE" != "rolling" ] && [ "$UPDATE_TYPE" != "blue-green" ] && [ "$UPDATE_TYPE" != "immediate" ]; then
        log_error "无效的更新类型: $UPDATE_TYPE"
        show_help
        exit 1
    fi
}

# 检查权限
check_permissions() {
    if [ "$EUID" -ne 0 ]; then
        log_error "更新脚本需要root权限"
        exit 1
    fi
}

# 预更新检查
pre_update_check() {
    log_step "执行预更新检查..."
    
    # 检查服务状态
    if ! systemctl is-active --quiet seatmaster-backend; then
        log_error "后端服务未运行"
        exit 1
    fi
    
    if ! systemctl is-active --quiet seatmaster-frontend; then
        log_error "前端服务未运行"
        exit 1
    fi
    
    # 检查磁盘空间
    local available_space=$(df /opt | tail -1 | awk '{print $4}')
    if [ "$available_space" -lt 1048576 ]; then  # 1GB
        log_error "磁盘空间不足，至少需要1GB可用空间"
        exit 1
    fi
    
    # 检查内存
    local available_memory=$(free -m | awk 'NR==2{print $7}')
    if [ "$available_memory" -lt 512 ]; then
        log_warn "可用内存较少: ${available_memory}MB"
    fi
    
    # 检查数据库连接
    if ! mysqladmin ping -h localhost --silent; then
        log_error "数据库连接失败"
        exit 1
    fi
    
    log_info "预更新检查通过"
}

# 备份当前版本
backup_current() {
    log_step "备份当前版本..."
    
    local backup_dir="/var/backups/seatmaster/update_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份应用文件
    cp -r /opt/seatmaster "$backup_dir/"
    
    # 备份数据库
    mysqldump -u root -p seat_reservation > "$backup_dir/database_backup.sql"
    
    # 备份配置文件
    cp -r /etc/nginx/conf.d "$backup_dir/" 2>/dev/null || true
    cp /etc/systemd/system/seatmaster-*.service "$backup_dir/" 2>/dev/null || true
    
    echo "$backup_dir" > /tmp/seatmaster_backup_path
    log_info "备份完成: $backup_dir"
}

# 滚动更新
rolling_update() {
    log_step "执行滚动更新..."
    
    # 1. 更新后端
    log_info "更新后端服务..."
    
    # 停止后端服务
    systemctl stop seatmaster-backend
    
    # 替换JAR文件
    cp "$PROJECT_ROOT/backend/target/seat-reservation-backend-1.0.0.jar" /opt/seatmaster/
    
    # 启动后端服务
    systemctl start seatmaster-backend
    
    # 等待后端启动
    local retry_count=0
    while [ $retry_count -lt 30 ]; do
        if curl -f http://localhost:8081/actuator/health &> /dev/null; then
            log_info "后端服务启动成功"
            break
        fi
        sleep 2
        retry_count=$((retry_count + 1))
    done
    
    if [ $retry_count -eq 30 ]; then
        log_error "后端服务启动失败"
        rollback
        exit 1
    fi
    
    # 2. 更新前端
    log_info "更新前端服务..."
    
    # 停止前端服务
    systemctl stop seatmaster-frontend
    
    # 替换前端文件
    rm -rf /var/www/seatmaster/*
    cp -r "$PROJECT_ROOT/frontend/dist/"* /var/www/seatmaster/
    
    # 启动前端服务
    systemctl start seatmaster-frontend
    
    # 等待前端启动
    retry_count=0
    while [ $retry_count -lt 15 ]; do
        if curl -f http://localhost:3000 &> /dev/null; then
            log_info "前端服务启动成功"
            break
        fi
        sleep 2
        retry_count=$((retry_count + 1))
    done
    
    if [ $retry_count -eq 15 ]; then
        log_error "前端服务启动失败"
        rollback
        exit 1
    fi
    
    log_info "滚动更新完成"
}

# 蓝绿部署
blue_green_update() {
    log_step "执行蓝绿部署..."
    
    # 创建绿色环境
    log_info "创建绿色环境..."
    
    # 复制当前配置到绿色环境
    mkdir -p /opt/seatmaster-green
    cp -r /opt/seatmaster/* /opt/seatmaster-green/
    
    # 更新绿色环境
    cp "$PROJECT_ROOT/backend/target/seat-reservation-backend-1.0.0.jar" /opt/seatmaster-green/
    
    # 启动绿色环境（使用不同端口）
    cd /opt/seatmaster-green
    java -jar seat-reservation-backend-1.0.0.jar --server.port=8082 &
    local green_pid=$!
    
    # 等待绿色环境启动
    local retry_count=0
    while [ $retry_count -lt 30 ]; do
        if curl -f http://localhost:8082/actuator/health &> /dev/null; then
            log_info "绿色环境启动成功"
            break
        fi
        sleep 2
        retry_count=$((retry_count + 1))
    done
    
    if [ $retry_count -eq 30 ]; then
        log_error "绿色环境启动失败"
        kill $green_pid 2>/dev/null || true
        rm -rf /opt/seatmaster-green
        exit 1
    fi
    
    # 切换流量到绿色环境
    log_info "切换流量到绿色环境..."
    
    # 更新Nginx配置
    sed -i 's/backend:8081/localhost:8082/g' /etc/nginx/conf.d/seatmaster.conf
    nginx -s reload
    
    # 等待一段时间确保切换成功
    sleep 10
    
    # 停止蓝色环境
    log_info "停止蓝色环境..."
    systemctl stop seatmaster-backend
    
    # 将绿色环境变为新的蓝色环境
    rm -rf /opt/seatmaster-old
    mv /opt/seatmaster /opt/seatmaster-old
    mv /opt/seatmaster-green /opt/seatmaster
    
    # 恢复Nginx配置并重启服务
    sed -i 's/localhost:8082/backend:8081/g' /etc/nginx/conf.d/seatmaster.conf
    systemctl start seatmaster-backend
    nginx -s reload
    
    # 清理
    kill $green_pid 2>/dev/null || true
    
    log_info "蓝绿部署完成"
}

# 立即更新
immediate_update() {
    log_step "执行立即更新..."
    
    # 停止所有服务
    systemctl stop seatmaster-frontend
    systemctl stop seatmaster-backend
    
    # 更新文件
    cp "$PROJECT_ROOT/backend/target/seat-reservation-backend-1.0.0.jar" /opt/seatmaster/
    rm -rf /var/www/seatmaster/*
    cp -r "$PROJECT_ROOT/frontend/dist/"* /var/www/seatmaster/
    
    # 启动所有服务
    systemctl start seatmaster-backend
    systemctl start seatmaster-frontend
    
    log_info "立即更新完成"
}

# 更新后验证
post_update_verify() {
    log_step "执行更新后验证..."
    
    # 等待服务稳定
    sleep 30
    
    # 检查后端健康状态
    if curl -f http://localhost:8081/actuator/health &> /dev/null; then
        log_info "后端健康检查通过"
    else
        log_error "后端健康检查失败"
        rollback
        exit 1
    fi
    
    # 检查前端可访问性
    if curl -f http://localhost:3000 &> /dev/null; then
        log_info "前端可访问性检查通过"
    else
        log_error "前端可访问性检查失败"
        rollback
        exit 1
    fi
    
    # 检查数据库连接
    if curl -f http://localhost:8081/api/health/db &> /dev/null; then
        log_info "数据库连接检查通过"
    else
        log_warn "数据库连接检查失败"
    fi
    
    log_info "更新后验证完成"
}

# 回滚
rollback() {
    log_step "执行回滚..."
    
    local backup_path=$(cat /tmp/seatmaster_backup_path 2>/dev/null || echo "")
    
    if [ -z "$backup_path" ] || [ ! -d "$backup_path" ]; then
        log_error "找不到备份路径，无法回滚"
        return 1
    fi
    
    # 停止服务
    systemctl stop seatmaster-frontend
    systemctl stop seatmaster-backend
    
    # 恢复文件
    rm -rf /opt/seatmaster
    cp -r "$backup_path/seatmaster" /opt/
    
    # 恢复前端文件
    rm -rf /var/www/seatmaster/*
    # 这里需要从备份中恢复前端文件，但备份中可能没有
    # 可以考虑保留一个稳定版本的前端文件
    
    # 启动服务
    systemctl start seatmaster-backend
    systemctl start seatmaster-frontend
    
    log_info "回滚完成"
}

# 清理
cleanup() {
    log_step "执行清理..."
    
    # 清理临时文件
    rm -f /tmp/seatmaster_backup_path
    
    # 清理旧的备份（保留最近5个）
    find /var/backups/seatmaster -type d -name "update_*" | sort -r | tail -n +6 | xargs rm -rf
    
    # 清理Docker镜像（如果使用Docker）
    if command -v docker &> /dev/null; then
        docker image prune -f
    fi
    
    log_info "清理完成"
}

# 显示更新结果
show_result() {
    log_info "================================"
    log_info "SeatMaster 更新完成!"
    log_info "================================"
    echo ""
    echo "更新信息:"
    echo "  类型: $UPDATE_TYPE"
    echo "  时间: $(date)"
    echo ""
    echo "服务状态:"
    echo "  后端: $(systemctl is-active seatmaster-backend)"
    echo "  前端: $(systemctl is-active seatmaster-frontend)"
    echo "  Nginx: $(systemctl is-active nginx)"
    echo ""
    echo "访问地址:"
    echo "  前端: http://localhost:3000"
    echo "  后端: http://localhost:8081"
    echo ""
}

# 主函数
main() {
    log_info "开始SeatMaster更新..."
    log_info "更新类型: $UPDATE_TYPE"
    
    check_args "$@"
    check_permissions
    pre_update_check
    backup_current
    
    # 根据更新类型执行更新
    case "$UPDATE_TYPE" in
        rolling)
            rolling_update
            ;;
        blue-green)
            blue_green_update
            ;;
        immediate)
            immediate_update
            ;;
    esac
    
    post_update_verify
    cleanup
    show_result
    
    log_info "更新完成!"
}

# 信号处理
trap 'log_error "更新被中断，正在回滚..."; rollback; exit 1' SIGINT SIGTERM

# 执行主函数
main "$@"
