#!/bin/bash

# SeatMaster 构建脚本
# 用于构建前端和后端应用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BUILD_ENV="${1:-prod}"
SKIP_TESTS="${2:-false}"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "SeatMaster 构建脚本"
    echo ""
    echo "用法: $0 [环境] [跳过测试]"
    echo ""
    echo "环境选项:"
    echo "  dev        开发环境"
    echo "  prod       生产环境 (默认)"
    echo ""
    echo "跳过测试:"
    echo "  true       跳过测试"
    echo "  false      运行测试 (默认)"
    echo ""
    echo "示例:"
    echo "  $0 prod false      # 生产环境构建并运行测试"
    echo "  $0 dev true        # 开发环境构建跳过测试"
    echo ""
}

# 检查参数
check_args() {
    if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        show_help
        exit 0
    fi
    
    if [ "$BUILD_ENV" != "dev" ] && [ "$BUILD_ENV" != "prod" ]; then
        log_error "无效的环境参数: $BUILD_ENV"
        show_help
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    log_step "检查构建依赖..."
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        log_error "Java未安装"
        exit 1
    fi
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm未安装"
        exit 1
    fi
    
    log_info "依赖检查通过"
}

# 清理构建产物
clean_build() {
    log_step "清理构建产物..."
    
    # 清理后端
    cd "$PROJECT_ROOT/backend"
    mvn clean
    
    # 清理前端
    cd "$PROJECT_ROOT/frontend"
    if [ -d "dist" ]; then
        rm -rf dist
    fi
    if [ -d "node_modules" ]; then
        log_info "保留node_modules目录"
    fi
    
    log_info "清理完成"
}

# 构建后端
build_backend() {
    log_step "构建后端应用..."
    
    cd "$PROJECT_ROOT/backend"
    
    # 设置Maven参数
    local mvn_args="clean package"
    
    if [ "$SKIP_TESTS" = "true" ]; then
        mvn_args="$mvn_args -DskipTests"
    fi
    
    if [ "$BUILD_ENV" = "prod" ]; then
        mvn_args="$mvn_args -Pprod"
    fi
    
    # 执行构建
    log_info "执行命令: mvn $mvn_args"
    mvn $mvn_args
    
    # 检查构建结果
    if [ ! -f "target/seat-reservation-backend-1.0.0.jar" ]; then
        log_error "后端构建失败，JAR文件不存在"
        exit 1
    fi
    
    # 显示JAR文件信息
    local jar_size=$(du -h "target/seat-reservation-backend-1.0.0.jar" | cut -f1)
    log_info "后端构建完成，JAR文件大小: $jar_size"
}

# 构建前端
build_frontend() {
    log_step "构建前端应用..."
    
    cd "$PROJECT_ROOT/frontend"
    
    # 安装依赖
    log_info "安装前端依赖..."
    npm ci
    
    # 运行测试（如果不跳过）
    if [ "$SKIP_TESTS" = "false" ]; then
        log_info "运行前端测试..."
        npm run test:unit || log_warn "前端测试失败，继续构建"
    fi
    
    # 构建应用
    if [ "$BUILD_ENV" = "prod" ]; then
        log_info "构建生产版本..."
        npm run build
        
        # 检查构建结果
        if [ ! -d "dist" ]; then
            log_error "前端构建失败，dist目录不存在"
            exit 1
        fi
        
        # 显示构建信息
        local dist_size=$(du -sh dist | cut -f1)
        log_info "前端构建完成，dist目录大小: $dist_size"
        
        # 显示主要文件
        log_info "主要构建文件:"
        find dist -name "*.js" -o -name "*.css" -o -name "*.html" | head -10 | while read file; do
            local file_size=$(du -h "$file" | cut -f1)
            echo "  $file ($file_size)"
        done
    else
        log_info "开发环境跳过前端构建"
    fi
}

# 构建Docker镜像
build_docker() {
    log_step "构建Docker镜像..."
    
    cd "$PROJECT_ROOT"
    
    # 构建后端镜像
    log_info "构建后端Docker镜像..."
    docker build -t seatmaster-backend:latest ./backend
    
    # 构建前端镜像
    if [ "$BUILD_ENV" = "prod" ]; then
        log_info "构建前端Docker镜像..."
        docker build -t seatmaster-frontend:latest ./frontend
    fi
    
    # 显示镜像信息
    log_info "Docker镜像构建完成:"
    docker images | grep seatmaster
}

# 运行质量检查
quality_check() {
    log_step "运行质量检查..."
    
    # 后端质量检查
    cd "$PROJECT_ROOT/backend"
    
    # 运行SpotBugs（如果配置了）
    if mvn help:describe -Dplugin=com.github.spotbugs:spotbugs-maven-plugin &> /dev/null; then
        log_info "运行SpotBugs检查..."
        mvn spotbugs:check || log_warn "SpotBugs检查发现问题"
    fi
    
    # 运行Checkstyle（如果配置了）
    if mvn help:describe -Dplugin=org.apache.maven.plugins:maven-checkstyle-plugin &> /dev/null; then
        log_info "运行Checkstyle检查..."
        mvn checkstyle:check || log_warn "Checkstyle检查发现问题"
    fi
    
    # 前端质量检查
    cd "$PROJECT_ROOT/frontend"
    
    # 运行ESLint
    if [ -f ".eslintrc.js" ] || [ -f ".eslintrc.json" ]; then
        log_info "运行ESLint检查..."
        npm run lint || log_warn "ESLint检查发现问题"
    fi
    
    log_info "质量检查完成"
}

# 生成构建报告
generate_report() {
    log_step "生成构建报告..."
    
    local report_file="$PROJECT_ROOT/build-report.txt"
    
    cat > "$report_file" << EOF
SeatMaster 构建报告
==================

构建时间: $(date)
构建环境: $BUILD_ENV
跳过测试: $SKIP_TESTS

后端信息:
--------
EOF
    
    if [ -f "$PROJECT_ROOT/backend/target/seat-reservation-backend-1.0.0.jar" ]; then
        echo "JAR文件: $(du -h "$PROJECT_ROOT/backend/target/seat-reservation-backend-1.0.0.jar")" >> "$report_file"
        echo "JAR创建时间: $(stat -c %y "$PROJECT_ROOT/backend/target/seat-reservation-backend-1.0.0.jar")" >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF

前端信息:
--------
EOF
    
    if [ -d "$PROJECT_ROOT/frontend/dist" ]; then
        echo "dist目录: $(du -sh "$PROJECT_ROOT/frontend/dist")" >> "$report_file"
        echo "主要文件数量: $(find "$PROJECT_ROOT/frontend/dist" -type f | wc -l)" >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF

系统信息:
--------
操作系统: $(uname -a)
Java版本: $(java -version 2>&1 | head -1)
Maven版本: $(mvn -version | head -1)
Node.js版本: $(node --version)
npm版本: $(npm --version)
EOF
    
    log_info "构建报告已生成: $report_file"
}

# 显示构建结果
show_result() {
    log_info "================================"
    log_info "SeatMaster 构建完成!"
    log_info "================================"
    echo ""
    echo "构建信息:"
    echo "  环境: $BUILD_ENV"
    echo "  跳过测试: $SKIP_TESTS"
    echo "  构建时间: $(date)"
    echo ""
    
    if [ -f "$PROJECT_ROOT/backend/target/seat-reservation-backend-1.0.0.jar" ]; then
        local jar_size=$(du -h "$PROJECT_ROOT/backend/target/seat-reservation-backend-1.0.0.jar" | cut -f1)
        echo "后端构建产物:"
        echo "  JAR文件: backend/target/seat-reservation-backend-1.0.0.jar ($jar_size)"
    fi
    
    if [ -d "$PROJECT_ROOT/frontend/dist" ]; then
        local dist_size=$(du -sh "$PROJECT_ROOT/frontend/dist" | cut -f1)
        echo "前端构建产物:"
        echo "  dist目录: frontend/dist ($dist_size)"
    fi
    
    echo ""
    echo "下一步:"
    echo "  部署到开发环境: ./scripts/deploy.sh dev"
    echo "  部署到生产环境: ./scripts/deploy.sh prod"
    echo "  Docker部署: ./scripts/deploy.sh prod docker"
    echo ""
}

# 主函数
main() {
    log_info "开始SeatMaster构建..."
    log_info "环境: $BUILD_ENV, 跳过测试: $SKIP_TESTS"
    
    check_args "$@"
    check_dependencies
    clean_build
    build_backend
    build_frontend
    
    if [ "$SKIP_TESTS" = "false" ]; then
        quality_check
    fi
    
    generate_report
    show_result
    
    log_info "构建完成!"
}

# 信号处理
trap 'log_error "构建被中断"; exit 1' SIGINT SIGTERM

# 执行主函数
main "$@"
