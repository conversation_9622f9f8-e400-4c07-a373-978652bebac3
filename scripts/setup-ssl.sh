#!/bin/bash

# SeatMaster SSL证书配置脚本
# 支持Let's Encrypt和自签名证书

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
DOMAIN="${1:-localhost}"
EMAIL="${2:-<EMAIL>}"
CERT_TYPE="${3:-letsencrypt}"  # letsencrypt, selfsigned
SSL_DIR="/etc/nginx/ssl"
WEBROOT="/var/www/certbot"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "SeatMaster SSL证书配置脚本"
    echo ""
    echo "用法: $0 [域名] [邮箱] [证书类型]"
    echo ""
    echo "参数:"
    echo "  域名        SSL证书域名 (默认: localhost)"
    echo "  邮箱        Let's Encrypt注册邮箱 (默认: <EMAIL>)"
    echo "  证书类型    letsencrypt 或 selfsigned (默认: letsencrypt)"
    echo ""
    echo "示例:"
    echo "  $0 yourdomain.com <EMAIL> letsencrypt"
    echo "  $0 localhost <EMAIL> selfsigned"
    echo ""
}

# 检查参数
check_args() {
    if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        show_help
        exit 0
    fi
    
    if [ "$CERT_TYPE" != "letsencrypt" ] && [ "$CERT_TYPE" != "selfsigned" ]; then
        log_error "无效的证书类型: $CERT_TYPE"
        show_help
        exit 1
    fi
}

# 检查权限
check_permissions() {
    if [ "$EUID" -ne 0 ]; then
        log_error "SSL配置需要root权限"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    log_step "检查依赖..."
    
    # 检查Nginx
    if ! command -v nginx &> /dev/null; then
        log_error "Nginx未安装"
        exit 1
    fi
    
    # 检查OpenSSL
    if ! command -v openssl &> /dev/null; then
        log_error "OpenSSL未安装"
        exit 1
    fi
    
    # 检查Certbot（如果使用Let's Encrypt）
    if [ "$CERT_TYPE" = "letsencrypt" ]; then
        if ! command -v certbot &> /dev/null; then
            log_warn "Certbot未安装，正在安装..."
            apt-get update
            apt-get install -y certbot python3-certbot-nginx
        fi
    fi
    
    log_info "依赖检查完成"
}

# 创建SSL目录
create_ssl_dirs() {
    log_step "创建SSL目录..."
    
    mkdir -p "$SSL_DIR"
    mkdir -p "$WEBROOT"
    
    # 设置权限
    chmod 755 "$SSL_DIR"
    chmod 755 "$WEBROOT"
    
    log_info "SSL目录创建完成"
}

# 生成自签名证书
generate_selfsigned_cert() {
    log_step "生成自签名证书..."
    
    local key_file="$SSL_DIR/privkey.pem"
    local cert_file="$SSL_DIR/fullchain.pem"
    local csr_file="$SSL_DIR/cert.csr"
    local config_file="$SSL_DIR/openssl.conf"
    
    # 创建OpenSSL配置文件
    cat > "$config_file" << EOF
[req]
default_bits = 2048
prompt = no
default_md = sha256
distinguished_name = dn
req_extensions = v3_req

[dn]
C=CN
ST=Beijing
L=Beijing
O=SeatMaster
OU=IT Department
CN=$DOMAIN

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = $DOMAIN
DNS.2 = www.$DOMAIN
DNS.3 = localhost
IP.1 = 127.0.0.1
EOF
    
    # 生成私钥
    openssl genrsa -out "$key_file" 2048
    
    # 生成证书签名请求
    openssl req -new -key "$key_file" -out "$csr_file" -config "$config_file"
    
    # 生成自签名证书
    openssl x509 -req -in "$csr_file" -signkey "$key_file" -out "$cert_file" -days 365 -extensions v3_req -extfile "$config_file"
    
    # 设置权限
    chmod 600 "$key_file"
    chmod 644 "$cert_file"
    
    # 清理临时文件
    rm -f "$csr_file" "$config_file"
    
    log_info "自签名证书生成完成"
    log_warn "自签名证书仅用于测试，生产环境请使用有效的SSL证书"
}

# 获取Let's Encrypt证书
get_letsencrypt_cert() {
    log_step "获取Let's Encrypt证书..."
    
    # 检查域名解析
    if ! nslookup "$DOMAIN" &> /dev/null; then
        log_error "域名 $DOMAIN 无法解析，请检查DNS配置"
        exit 1
    fi
    
    # 创建临时Nginx配置用于验证
    local temp_config="/etc/nginx/conf.d/temp-ssl.conf"
    cat > "$temp_config" << EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    
    location /.well-known/acme-challenge/ {
        root $WEBROOT;
        allow all;
    }
    
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}
EOF
    
    # 重新加载Nginx
    nginx -t && nginx -s reload
    
    # 获取证书
    certbot certonly \
        --webroot \
        --webroot-path="$WEBROOT" \
        --email "$EMAIL" \
        --agree-tos \
        --no-eff-email \
        --domains "$DOMAIN,www.$DOMAIN"
    
    # 复制证书到SSL目录
    cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" "$SSL_DIR/"
    cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" "$SSL_DIR/"
    cp "/etc/letsencrypt/live/$DOMAIN/chain.pem" "$SSL_DIR/"
    
    # 设置权限
    chmod 600 "$SSL_DIR/privkey.pem"
    chmod 644 "$SSL_DIR/fullchain.pem"
    chmod 644 "$SSL_DIR/chain.pem"
    
    # 删除临时配置
    rm -f "$temp_config"
    
    log_info "Let's Encrypt证书获取完成"
}

# 配置SSL参数
configure_ssl_params() {
    log_step "配置SSL参数..."
    
    local ssl_params_file="/etc/nginx/conf.d/ssl-params.conf"
    
    cat > "$ssl_params_file" << EOF
# SSL配置参数

# SSL协议版本
ssl_protocols TLSv1.2 TLSv1.3;

# SSL密码套件
ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA:ECDHE-RSA-AES256-SHA:DHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA256:DHE-RSA-AES128-SHA:DHE-RSA-AES256-SHA:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!SRP:!CAMELLIA;

# 优先使用服务器密码套件
ssl_prefer_server_ciphers off;

# SSL会话缓存
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
ssl_session_tickets off;

# OCSP Stapling
ssl_stapling on;
ssl_stapling_verify on;
resolver ******* ******* valid=300s;
resolver_timeout 5s;

# 安全头
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;

# DH参数
ssl_dhparam /etc/nginx/ssl/dhparam.pem;
EOF
    
    # 生成DH参数（如果不存在）
    if [ ! -f "/etc/nginx/ssl/dhparam.pem" ]; then
        log_info "生成DH参数（这可能需要几分钟）..."
        openssl dhparam -out "/etc/nginx/ssl/dhparam.pem" 2048
    fi
    
    log_info "SSL参数配置完成"
}

# 更新Nginx配置
update_nginx_config() {
    log_step "更新Nginx配置..."
    
    local site_config="/etc/nginx/conf.d/seatmaster-ssl.conf"
    
    cat > "$site_config" << EOF
# SeatMaster HTTPS配置

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    
    # Let's Encrypt验证路径
    location /.well-known/acme-challenge/ {
        root $WEBROOT;
        allow all;
    }
    
    # 重定向到HTTPS
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}

# HTTPS服务器
server {
    listen 443 ssl http2;
    server_name $DOMAIN www.$DOMAIN;
    
    # SSL证书配置
    ssl_certificate $SSL_DIR/fullchain.pem;
    ssl_certificate_key $SSL_DIR/privkey.pem;
    ssl_trusted_certificate $SSL_DIR/chain.pem;
    
    # 包含SSL参数
    include /etc/nginx/conf.d/ssl-params.conf;
    
    # 日志配置
    access_log /var/log/nginx/seatmaster-ssl.access.log;
    error_log /var/log/nginx/seatmaster-ssl.error.log;
    
    # 包含主站点配置
    include /etc/nginx/conf.d/seatmaster.conf;
}
EOF
    
    # 测试Nginx配置
    if nginx -t; then
        nginx -s reload
        log_info "Nginx配置更新完成"
    else
        log_error "Nginx配置测试失败"
        exit 1
    fi
}

# 设置证书自动续期
setup_cert_renewal() {
    if [ "$CERT_TYPE" = "letsencrypt" ]; then
        log_step "设置证书自动续期..."
        
        # 创建续期脚本
        local renewal_script="/usr/local/bin/renew-ssl.sh"
        cat > "$renewal_script" << EOF
#!/bin/bash

# SeatMaster SSL证书续期脚本

# 续期证书
certbot renew --quiet

# 复制新证书
if [ -f "/etc/letsencrypt/live/$DOMAIN/privkey.pem" ]; then
    cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" "$SSL_DIR/"
    cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" "$SSL_DIR/"
    cp "/etc/letsencrypt/live/$DOMAIN/chain.pem" "$SSL_DIR/"
    
    # 重新加载Nginx
    nginx -s reload
    
    echo "\$(date): SSL证书续期完成" >> /var/log/ssl-renewal.log
fi
EOF
        
        chmod +x "$renewal_script"
        
        # 添加到crontab
        (crontab -l 2>/dev/null; echo "0 3 * * * $renewal_script") | crontab -
        
        log_info "证书自动续期设置完成"
    fi
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        # 允许HTTP和HTTPS
        ufw allow 80/tcp
        ufw allow 443/tcp
        
        # 允许SSH（如果还没有允许）
        ufw allow 22/tcp
        
        log_info "UFW防火墙规则已更新"
    elif command -v firewall-cmd &> /dev/null; then
        # CentOS/RHEL防火墙
        firewall-cmd --permanent --add-service=http
        firewall-cmd --permanent --add-service=https
        firewall-cmd --reload
        
        log_info "firewalld防火墙规则已更新"
    else
        log_warn "未检测到防火墙，请手动配置防火墙规则"
    fi
}

# 验证SSL配置
verify_ssl() {
    log_step "验证SSL配置..."
    
    # 检查证书文件
    if [ ! -f "$SSL_DIR/privkey.pem" ] || [ ! -f "$SSL_DIR/fullchain.pem" ]; then
        log_error "SSL证书文件不存在"
        exit 1
    fi
    
    # 验证证书
    if openssl x509 -in "$SSL_DIR/fullchain.pem" -text -noout &> /dev/null; then
        log_info "SSL证书验证通过"
    else
        log_error "SSL证书验证失败"
        exit 1
    fi
    
    # 检查证书有效期
    local expiry_date=$(openssl x509 -in "$SSL_DIR/fullchain.pem" -noout -enddate | cut -d= -f2)
    log_info "证书有效期至: $expiry_date"
    
    # 测试HTTPS连接
    if curl -k -s "https://$DOMAIN" &> /dev/null; then
        log_info "HTTPS连接测试通过"
    else
        log_warn "HTTPS连接测试失败，请检查配置"
    fi
}

# 显示配置结果
show_result() {
    log_info "================================"
    log_info "SSL配置完成!"
    log_info "================================"
    echo ""
    echo "SSL信息:"
    echo "  域名: $DOMAIN"
    echo "  证书类型: $CERT_TYPE"
    echo "  证书位置: $SSL_DIR/"
    echo ""
    echo "访问地址:"
    echo "  HTTPS: https://$DOMAIN"
    echo "  HTTP: http://$DOMAIN (自动重定向到HTTPS)"
    echo ""
    echo "证书管理:"
    if [ "$CERT_TYPE" = "letsencrypt" ]; then
        echo "  续期命令: certbot renew"
        echo "  自动续期: 已配置 (每日3点执行)"
    else
        echo "  证书类型: 自签名证书"
        echo "  有效期: 365天"
    fi
    echo ""
    echo "安全检查:"
    echo "  SSL Labs测试: https://www.ssllabs.com/ssltest/analyze.html?d=$DOMAIN"
    echo ""
}

# 主函数
main() {
    log_info "开始SSL配置..."
    log_info "域名: $DOMAIN, 邮箱: $EMAIL, 类型: $CERT_TYPE"
    
    check_args "$@"
    check_permissions
    check_dependencies
    create_ssl_dirs
    
    # 根据证书类型生成证书
    case "$CERT_TYPE" in
        letsencrypt)
            get_letsencrypt_cert
            ;;
        selfsigned)
            generate_selfsigned_cert
            ;;
    esac
    
    configure_ssl_params
    update_nginx_config
    setup_cert_renewal
    configure_firewall
    verify_ssl
    show_result
    
    log_info "SSL配置完成!"
}

# 信号处理
trap 'log_error "SSL配置被中断"; exit 1' SIGINT SIGTERM

# 执行主函数
main "$@"
