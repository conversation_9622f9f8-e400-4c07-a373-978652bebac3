#!/bin/bash

# SeatMaster 性能调优脚本
# 用于系统和应用性能优化

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TUNE_TYPE="${1:-all}"  # all, system, jvm, database, frontend

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${PURPLE}[SUCCESS]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "SeatMaster 性能调优脚本"
    echo ""
    echo "用法: $0 [调优类型]"
    echo ""
    echo "调优类型:"
    echo "  all        全部优化 (默认)"
    echo "  system     系统级优化"
    echo "  jvm        JVM优化"
    echo "  database   数据库优化"
    echo "  frontend   前端优化"
    echo ""
    echo "示例:"
    echo "  $0 all       # 全部优化"
    echo "  $0 system    # 仅系统优化"
    echo "  $0 jvm       # 仅JVM优化"
    echo ""
}

# 检查权限
check_permissions() {
    if [ "$EUID" -ne 0 ]; then
        log_error "性能调优需要root权限"
        exit 1
    fi
}

# 获取系统信息
get_system_info() {
    log_step "获取系统信息..."
    
    # CPU信息
    CPU_CORES=$(nproc)
    CPU_MODEL=$(lscpu | grep "Model name" | cut -d: -f2 | xargs)
    
    # 内存信息
    TOTAL_RAM=$(free -m | awk 'NR==2{print $2}')
    AVAILABLE_RAM=$(free -m | awk 'NR==2{print $7}')
    
    # 磁盘信息
    DISK_TOTAL=$(df -h / | awk 'NR==2{print $2}')
    DISK_USED=$(df -h / | awk 'NR==2{print $3}')
    DISK_AVAIL=$(df -h / | awk 'NR==2{print $4}')
    
    log_info "系统信息:"
    echo "  CPU: $CPU_MODEL ($CPU_CORES cores)"
    echo "  内存: ${AVAILABLE_RAM}MB 可用 / ${TOTAL_RAM}MB 总计"
    echo "  磁盘: ${DISK_USED} 已用 / ${DISK_TOTAL} 总计 (${DISK_AVAIL} 可用)"
}

# 系统级性能优化
tune_system() {
    log_step "系统级性能优化..."
    
    # 内核参数优化
    cat > /etc/sysctl.d/99-seatmaster-performance.conf << 'EOF'
# SeatMaster 性能优化内核参数

# 网络性能优化
net.core.rmem_default = 262144
net.core.rmem_max = 16777216
net.core.wmem_default = 262144
net.core.wmem_max = 16777216
net.core.netdev_max_backlog = 5000
net.core.somaxconn = 65535

# TCP优化
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_congestion_control = bbr
net.ipv4.tcp_slow_start_after_idle = 0
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_keepalive_probes = 3
net.ipv4.tcp_keepalive_intvl = 15
net.ipv4.tcp_max_syn_backlog = 8192
net.ipv4.tcp_max_tw_buckets = 5000

# 文件系统优化
fs.file-max = 2097152
fs.nr_open = 1048576

# 虚拟内存优化
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
vm.vfs_cache_pressure = 50
vm.min_free_kbytes = 65536

# 进程优化
kernel.pid_max = 4194304
kernel.threads-max = 4194304
EOF
    
    # 应用内核参数
    sysctl -p /etc/sysctl.d/99-seatmaster-performance.conf
    
    # 文件描述符限制
    cat > /etc/security/limits.d/99-seatmaster.conf << 'EOF'
# SeatMaster 文件描述符限制
* soft nofile 65536
* hard nofile 65536
* soft nproc 32768
* hard nproc 32768
root soft nofile 65536
root hard nofile 65536
seatmaster soft nofile 65536
seatmaster hard nofile 65536
seatmaster soft nproc 32768
seatmaster hard nproc 32768
EOF
    
    # 优化磁盘调度器
    echo 'ACTION=="add|change", KERNEL=="sd[a-z]", ATTR{queue/scheduler}="mq-deadline"' > /etc/udev/rules.d/60-seatmaster-scheduler.rules
    
    # 禁用透明大页
    echo never > /sys/kernel/mm/transparent_hugepage/enabled
    echo never > /sys/kernel/mm/transparent_hugepage/defrag
    
    # 添加到启动脚本
    cat > /etc/rc.local << 'EOF'
#!/bin/bash
# SeatMaster 启动优化
echo never > /sys/kernel/mm/transparent_hugepage/enabled
echo never > /sys/kernel/mm/transparent_hugepage/defrag
exit 0
EOF
    chmod +x /etc/rc.local
    
    log_success "系统级优化完成"
}

# JVM性能优化
tune_jvm() {
    log_step "JVM性能优化..."
    
    # 根据内存大小选择JVM配置
    if [ "$TOTAL_RAM" -lt 2048 ]; then
        JVM_CONFIG="SMALL"
        HEAP_SIZE="1g"
    elif [ "$TOTAL_RAM" -lt 4096 ]; then
        JVM_CONFIG="MEDIUM"
        HEAP_SIZE="2g"
    else
        JVM_CONFIG="LARGE"
        HEAP_SIZE="4g"
    fi
    
    # 创建JVM配置文件
    cat > /opt/seatmaster/jvm.conf << EOF
# SeatMaster JVM 配置 (${JVM_CONFIG})
# 自动生成于 $(date)

# 基础内存配置
JAVA_OPTS="-Xms${HEAP_SIZE} -Xmx${HEAP_SIZE}"

# G1GC配置
JAVA_OPTS="\$JAVA_OPTS -XX:+UseG1GC"
JAVA_OPTS="\$JAVA_OPTS -XX:MaxGCPauseMillis=200"
JAVA_OPTS="\$JAVA_OPTS -XX:G1HeapRegionSize=16m"
JAVA_OPTS="\$JAVA_OPTS -XX:G1NewSizePercent=30"
JAVA_OPTS="\$JAVA_OPTS -XX:G1MaxNewSizePercent=40"
JAVA_OPTS="\$JAVA_OPTS -XX:InitiatingHeapOccupancyPercent=45"

# 性能优化
JAVA_OPTS="\$JAVA_OPTS -XX:+TieredCompilation"
JAVA_OPTS="\$JAVA_OPTS -XX:+UseStringDeduplication"
JAVA_OPTS="\$JAVA_OPTS -XX:+OptimizeStringConcat"
JAVA_OPTS="\$JAVA_OPTS -XX:+UseCompressedOops"

# 监控配置
JAVA_OPTS="\$JAVA_OPTS -Xlog:gc*:logs/gc.log:time,tags"
JAVA_OPTS="\$JAVA_OPTS -XX:+HeapDumpOnOutOfMemoryError"
JAVA_OPTS="\$JAVA_OPTS -XX:HeapDumpPath=logs/heapdump.hprof"

# 系统属性
JAVA_OPTS="\$JAVA_OPTS -Djava.awt.headless=true"
JAVA_OPTS="\$JAVA_OPTS -Dfile.encoding=UTF-8"
JAVA_OPTS="\$JAVA_OPTS -Duser.timezone=Asia/Shanghai"
JAVA_OPTS="\$JAVA_OPTS -Djava.security.egd=file:/dev/./urandom"

export JAVA_OPTS
EOF
    
    # 创建JVM监控脚本
    cat > /usr/local/bin/jvm-monitor.sh << 'EOF'
#!/bin/bash

# SeatMaster JVM 监控脚本

PID=$(pgrep -f "seat-reservation-backend")
if [ -z "$PID" ]; then
    echo "SeatMaster后端进程未运行"
    exit 1
fi

echo "SeatMaster JVM 监控报告 - $(date)"
echo "=================================="

# 基础信息
echo "进程ID: $PID"
echo "JVM版本: $(java -version 2>&1 | head -1)"

# 内存使用
echo ""
echo "内存使用情况:"
jstat -gc $PID | tail -1 | awk '{
    printf "  堆内存使用: %.1f%%\n", ($3+$4+$6+$8)/($1+$2+$5+$7)*100
    printf "  Eden区: %.1fMB/%.1fMB\n", $6/1024, $5/1024
    printf "  Survivor区: %.1fMB/%.1fMB\n", $8/1024, $7/1024
    printf "  老年代: %.1fMB/%.1fMB\n", $4/1024, $3/1024
    printf "  元空间: %.1fMB\n", $10/1024
}'

# GC统计
echo ""
echo "GC统计:"
jstat -gccapacity $PID | tail -1 | awk '{
    printf "  Young GC次数: %d\n", $14
    printf "  Full GC次数: %d\n", $16
    printf "  GC总时间: %.3fs\n", ($15+$17)/1000
}'

# 线程信息
echo ""
echo "线程信息:"
jstack $PID | grep "java.lang.Thread.State" | sort | uniq -c | while read count state; do
    echo "  $state: $count"
done

# 类加载信息
echo ""
echo "类加载信息:"
jstat -class $PID | tail -1 | awk '{
    printf "  已加载类: %d\n", $1
    printf "  已卸载类: %d\n", $3
    printf "  类加载时间: %.3fs\n", $4/1000
}'
EOF
    
    chmod +x /usr/local/bin/jvm-monitor.sh
    
    log_success "JVM优化完成 (配置: $JVM_CONFIG, 堆大小: $HEAP_SIZE)"
}

# 数据库性能优化
tune_database() {
    log_step "数据库性能优化..."
    
    # 备份原配置
    if [ -f "/etc/mysql/my.cnf" ]; then
        cp /etc/mysql/my.cnf /etc/mysql/my.cnf.backup.$(date +%Y%m%d)
    fi
    
    # 应用性能配置
    cp "$PROJECT_ROOT/config/database-performance.cnf" /etc/mysql/conf.d/performance.cnf
    
    # 根据内存大小调整InnoDB缓冲池
    local innodb_buffer_size
    if [ "$TOTAL_RAM" -lt 2048 ]; then
        innodb_buffer_size="512M"
    elif [ "$TOTAL_RAM" -lt 4096 ]; then
        innodb_buffer_size="1G"
    elif [ "$TOTAL_RAM" -lt 8192 ]; then
        innodb_buffer_size="2G"
    else
        innodb_buffer_size="4G"
    fi
    
    # 更新配置
    sed -i "s/innodb_buffer_pool_size = 2G/innodb_buffer_pool_size = $innodb_buffer_size/" /etc/mysql/conf.d/performance.cnf
    
    # 创建数据库监控脚本
    cat > /usr/local/bin/mysql-monitor.sh << 'EOF'
#!/bin/bash

# SeatMaster MySQL 监控脚本

echo "SeatMaster MySQL 监控报告 - $(date)"
echo "=================================="

# 连接信息
mysql -e "
SELECT 
    'Connection Info' as Metric,
    VARIABLE_VALUE as Value 
FROM performance_schema.global_status 
WHERE VARIABLE_NAME IN ('Threads_connected', 'Threads_running', 'Max_used_connections')
UNION ALL
SELECT 
    'Buffer Pool Usage' as Metric,
    CONCAT(
        ROUND(
            (SELECT VARIABLE_VALUE FROM performance_schema.global_status WHERE VARIABLE_NAME = 'Innodb_buffer_pool_pages_data') * 16 / 1024, 2
        ), 'MB / ',
        ROUND(
            (SELECT VARIABLE_VALUE FROM performance_schema.global_variables WHERE VARIABLE_NAME = 'innodb_buffer_pool_size') / 1024 / 1024, 2
        ), 'MB'
    ) as Value
UNION ALL
SELECT 
    'Slow Queries' as Metric,
    VARIABLE_VALUE as Value
FROM performance_schema.global_status 
WHERE VARIABLE_NAME = 'Slow_queries'
UNION ALL
SELECT 
    'QPS' as Metric,
    VARIABLE_VALUE as Value
FROM performance_schema.global_status 
WHERE VARIABLE_NAME = 'Queries';"

# 最耗时的查询
echo ""
echo "最耗时的查询 (Top 5):"
mysql -e "
SELECT 
    TRUNCATE(TIMER_WAIT/1000000000000,6) as exec_time,
    SQL_TEXT
FROM performance_schema.events_statements_history_long 
WHERE SQL_TEXT NOT LIKE '%performance_schema%'
ORDER BY TIMER_WAIT DESC 
LIMIT 5;"

# InnoDB状态
echo ""
echo "InnoDB状态:"
mysql -e "SHOW ENGINE INNODB STATUS\G" | grep -A 20 "BUFFER POOL AND MEMORY"
EOF
    
    chmod +x /usr/local/bin/mysql-monitor.sh
    
    # 重启MySQL服务
    systemctl restart mysql || systemctl restart mariadb
    
    log_success "数据库优化完成 (InnoDB缓冲池: $innodb_buffer_size)"
}

# 前端性能优化
tune_frontend() {
    log_step "前端性能优化..."
    
    # 检查Node.js版本
    local node_version=$(node --version | cut -d'v' -f2)
    log_info "Node.js版本: $node_version"
    
    # 优化npm配置
    npm config set registry https://registry.npmmirror.com
    npm config set cache /tmp/npm-cache
    npm config set progress false
    npm config set loglevel warn
    
    # 清理和重新安装依赖
    cd "$PROJECT_ROOT/frontend"
    rm -rf node_modules package-lock.json
    npm install --production=false
    
    # 构建优化版本
    npm run build
    
    # 分析构建结果
    if [ -d "dist" ]; then
        local dist_size=$(du -sh dist | cut -f1)
        log_info "构建产物大小: $dist_size"
        
        # 显示主要文件大小
        echo "主要文件:"
        find dist -name "*.js" -o -name "*.css" | head -10 | while read file; do
            local size=$(du -h "$file" | cut -f1)
            echo "  $file: $size"
        done
    fi
    
    log_success "前端优化完成"
}

# 创建性能监控脚本
create_performance_monitor() {
    log_step "创建性能监控脚本..."
    
    cat > /usr/local/bin/seatmaster-performance.sh << 'EOF'
#!/bin/bash

# SeatMaster 性能监控脚本

echo "SeatMaster 性能监控报告"
echo "======================="
echo "时间: $(date)"
echo ""

# 系统负载
echo "系统负载:"
uptime | sed 's/^/  /'
echo ""

# CPU使用率
echo "CPU使用率:"
top -bn1 | grep "Cpu(s)" | sed 's/^/  /'
echo ""

# 内存使用
echo "内存使用:"
free -h | sed 's/^/  /'
echo ""

# 磁盘使用
echo "磁盘使用:"
df -h | grep -v tmpfs | sed 's/^/  /'
echo ""

# 网络连接
echo "网络连接:"
ss -tuln | grep -E "(8081|3000|3306)" | sed 's/^/  /'
echo ""

# 进程状态
echo "关键进程:"
ps aux | grep -E "(java|nginx|mysql)" | grep -v grep | awk '{print "  " $1 " " $2 " " $3 "% " $4 "% " $11}' | head -10
echo ""

# JVM监控 (如果可用)
if command -v jstat &> /dev/null; then
    PID=$(pgrep -f "seat-reservation-backend")
    if [ -n "$PID" ]; then
        echo "JVM GC统计:"
        jstat -gc $PID | tail -1 | awk '{printf "  Young GC: %d次, Full GC: %d次, GC时间: %.3fs\n", $14, $16, ($15+$17)/1000}'
        echo ""
    fi
fi

# 应用响应时间
echo "应用响应时间:"
if curl -s -w "%{time_total}" -o /dev/null http://localhost:8081/actuator/health 2>/dev/null; then
    echo "  后端健康检查: ${BASH_REMATCH[0]}s"
fi
if curl -s -w "%{time_total}" -o /dev/null http://localhost:3000 2>/dev/null; then
    echo "  前端首页: ${BASH_REMATCH[0]}s"
fi
echo ""

echo "监控完成"
EOF
    
    chmod +x /usr/local/bin/seatmaster-performance.sh
    
    # 添加到crontab (每5分钟执行一次)
    (crontab -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/seatmaster-performance.sh >> /var/log/seatmaster/performance.log 2>&1") | crontab -
    
    log_success "性能监控脚本创建完成"
}

# 显示优化结果
show_tuning_result() {
    log_info "================================"
    log_info "性能调优完成!"
    log_info "================================"
    echo ""
    echo "优化项目:"
    case "$TUNE_TYPE" in
        all)
            echo "  ✓ 系统级优化"
            echo "  ✓ JVM优化"
            echo "  ✓ 数据库优化"
            echo "  ✓ 前端优化"
            echo "  ✓ 性能监控"
            ;;
        system)
            echo "  ✓ 系统级优化"
            ;;
        jvm)
            echo "  ✓ JVM优化"
            ;;
        database)
            echo "  ✓ 数据库优化"
            ;;
        frontend)
            echo "  ✓ 前端优化"
            ;;
    esac
    echo ""
    echo "监控命令:"
    echo "  系统性能: /usr/local/bin/seatmaster-performance.sh"
    echo "  JVM监控: /usr/local/bin/jvm-monitor.sh"
    echo "  数据库监控: /usr/local/bin/mysql-monitor.sh"
    echo ""
    echo "配置文件:"
    echo "  JVM配置: /opt/seatmaster/jvm.conf"
    echo "  数据库配置: /etc/mysql/conf.d/performance.cnf"
    echo "  内核参数: /etc/sysctl.d/99-seatmaster-performance.conf"
    echo ""
    echo "建议:"
    echo "  1. 重启系统以应用所有优化"
    echo "  2. 监控应用性能指标"
    echo "  3. 根据实际负载调整参数"
    echo "  4. 定期检查性能监控日志"
    echo ""
}

# 主函数
main() {
    log_info "开始SeatMaster性能调优..."
    log_info "调优类型: $TUNE_TYPE"
    
    if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        show_help
        exit 0
    fi
    
    check_permissions
    get_system_info
    
    case "$TUNE_TYPE" in
        all)
            tune_system
            tune_jvm
            tune_database
            tune_frontend
            create_performance_monitor
            ;;
        system)
            tune_system
            ;;
        jvm)
            tune_jvm
            ;;
        database)
            tune_database
            ;;
        frontend)
            tune_frontend
            ;;
        *)
            log_error "无效的调优类型: $TUNE_TYPE"
            show_help
            exit 1
            ;;
    esac
    
    show_tuning_result
    log_success "性能调优完成!"
}

# 信号处理
trap 'log_error "性能调优被中断"; exit 1' SIGINT SIGTERM

# 执行主函数
main "$@"
