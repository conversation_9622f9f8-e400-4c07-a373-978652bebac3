#!/bin/bash

# SeatMaster 系统服务安装脚本
# 用于安装和配置systemd服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root权限运行此脚本"
        exit 1
    fi
}

# 检查系统
check_system() {
    log_info "检查系统环境..."
    
    # 检查systemd
    if ! command -v systemctl &> /dev/null; then
        log_error "systemd未安装或不可用"
        exit 1
    fi
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        log_error "Java未安装，请先安装Java 8或更高版本"
        exit 1
    fi
    
    # 检查Node.js和PM2
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装，请先安装Node.js"
        exit 1
    fi
    
    if ! command -v pm2 &> /dev/null; then
        log_warn "PM2未安装，正在安装..."
        npm install -g pm2
    fi
    
    # 检查Nginx
    if ! command -v nginx &> /dev/null; then
        log_warn "Nginx未安装，请先安装Nginx"
    fi
    
    log_info "系统环境检查完成"
}

# 创建用户和目录
create_user_and_dirs() {
    log_info "创建用户和目录..."
    
    # 创建seatmaster用户
    if ! id "seatmaster" &>/dev/null; then
        useradd -r -s /bin/bash -d /opt/seatmaster seatmaster
        log_info "创建用户: seatmaster"
    else
        log_info "用户seatmaster已存在"
    fi
    
    # 创建目录
    mkdir -p /opt/seatmaster
    mkdir -p /opt/seatmaster/frontend
    mkdir -p /opt/seatmaster/uploads
    mkdir -p /opt/seatmaster/temp
    mkdir -p /var/log/seatmaster
    mkdir -p /var/backups/seatmaster
    
    # 设置权限
    chown -R seatmaster:seatmaster /opt/seatmaster
    chown -R seatmaster:seatmaster /var/log/seatmaster
    chown -R seatmaster:seatmaster /var/backups/seatmaster
    
    log_info "用户和目录创建完成"
}

# 安装systemd服务文件
install_service_files() {
    log_info "安装systemd服务文件..."
    
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local systemd_dir="$script_dir/../systemd"
    
    # 复制服务文件
    if [ -f "$systemd_dir/seatmaster-backend.service" ]; then
        cp "$systemd_dir/seatmaster-backend.service" /etc/systemd/system/
        log_info "安装后端服务文件"
    fi
    
    if [ -f "$systemd_dir/seatmaster-frontend.service" ]; then
        cp "$systemd_dir/seatmaster-frontend.service" /etc/systemd/system/
        log_info "安装前端服务文件"
    fi
    
    if [ -f "$systemd_dir/seatmaster-nginx.service" ]; then
        cp "$systemd_dir/seatmaster-nginx.service" /etc/systemd/system/
        log_info "安装Nginx服务文件"
    fi
    
    # 重新加载systemd
    systemctl daemon-reload
    log_info "systemd配置重新加载完成"
}

# 配置日志轮转
setup_logrotate() {
    log_info "配置日志轮转..."
    
    cat > /etc/logrotate.d/seatmaster << 'EOF'
/var/log/seatmaster/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 seatmaster seatmaster
    postrotate
        systemctl reload seatmaster-backend || true
    endscript
}

/var/log/nginx/seatmaster*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 nginx nginx
    postrotate
        systemctl reload nginx || true
    endscript
}
EOF
    
    log_info "日志轮转配置完成"
}

# 启用服务
enable_services() {
    log_info "启用系统服务..."
    
    # 启用后端服务
    if systemctl list-unit-files | grep -q "seatmaster-backend.service"; then
        systemctl enable seatmaster-backend.service
        log_info "启用后端服务"
    fi
    
    # 启用前端服务
    if systemctl list-unit-files | grep -q "seatmaster-frontend.service"; then
        systemctl enable seatmaster-frontend.service
        log_info "启用前端服务"
    fi
    
    # 启用Nginx服务（如果存在自定义配置）
    if systemctl list-unit-files | grep -q "seatmaster-nginx.service"; then
        systemctl enable seatmaster-nginx.service
        log_info "启用Nginx服务"
    elif systemctl list-unit-files | grep -q "nginx.service"; then
        systemctl enable nginx.service
        log_info "启用系统Nginx服务"
    fi
    
    log_info "服务启用完成"
}

# 创建管理脚本
create_management_scripts() {
    log_info "创建管理脚本..."
    
    # 创建服务管理脚本
    cat > /usr/local/bin/seatmaster << 'EOF'
#!/bin/bash

# SeatMaster 服务管理脚本

case "$1" in
    start)
        echo "启动SeatMaster服务..."
        systemctl start seatmaster-backend
        systemctl start seatmaster-frontend
        systemctl start nginx
        ;;
    stop)
        echo "停止SeatMaster服务..."
        systemctl stop nginx
        systemctl stop seatmaster-frontend
        systemctl stop seatmaster-backend
        ;;
    restart)
        echo "重启SeatMaster服务..."
        systemctl restart seatmaster-backend
        systemctl restart seatmaster-frontend
        systemctl restart nginx
        ;;
    status)
        echo "SeatMaster服务状态:"
        echo "===================="
        echo "后端服务:"
        systemctl status seatmaster-backend --no-pager -l
        echo ""
        echo "前端服务:"
        systemctl status seatmaster-frontend --no-pager -l
        echo ""
        echo "Nginx服务:"
        systemctl status nginx --no-pager -l
        ;;
    logs)
        case "$2" in
            backend)
                journalctl -u seatmaster-backend -f
                ;;
            frontend)
                journalctl -u seatmaster-frontend -f
                ;;
            nginx)
                journalctl -u nginx -f
                ;;
            *)
                echo "用法: seatmaster logs [backend|frontend|nginx]"
                ;;
        esac
        ;;
    *)
        echo "用法: seatmaster {start|stop|restart|status|logs}"
        echo "日志查看: seatmaster logs [backend|frontend|nginx]"
        exit 1
        ;;
esac
EOF
    
    chmod +x /usr/local/bin/seatmaster
    log_info "管理脚本创建完成: /usr/local/bin/seatmaster"
}

# 显示安装结果
show_result() {
    log_info "================================"
    log_info "SeatMaster 系统服务安装完成!"
    log_info "================================"
    echo ""
    echo "服务管理命令:"
    echo "  启动服务: seatmaster start"
    echo "  停止服务: seatmaster stop"
    echo "  重启服务: seatmaster restart"
    echo "  查看状态: seatmaster status"
    echo "  查看日志: seatmaster logs [backend|frontend|nginx]"
    echo ""
    echo "systemctl命令:"
    echo "  systemctl start seatmaster-backend"
    echo "  systemctl start seatmaster-frontend"
    echo "  systemctl status seatmaster-backend"
    echo ""
    echo "日志位置:"
    echo "  应用日志: /var/log/seatmaster/"
    echo "  系统日志: journalctl -u seatmaster-backend"
    echo ""
    echo "配置文件:"
    echo "  环境配置: /opt/seatmaster/.env.production"
    echo "  服务配置: /etc/systemd/system/seatmaster-*.service"
    echo ""
}

# 主函数
main() {
    log_info "开始安装SeatMaster系统服务..."
    
    check_root
    check_system
    create_user_and_dirs
    install_service_files
    setup_logrotate
    enable_services
    create_management_scripts
    show_result
    
    log_info "安装完成!"
}

# 执行主函数
main "$@"
