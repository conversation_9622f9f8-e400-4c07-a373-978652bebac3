#!/bin/bash

# MySQL SQL语法测试脚本
# 用于验证修复后的SQL文件语法正确性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TEST_DB_NAME="seatmaster_syntax_test"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${BLUE}[SUCCESS]${NC} $1"
}

# 检查MySQL连接
check_mysql_connection() {
    log_info "检查MySQL连接..."
    
    if ! mysql -e "SELECT 1" &> /dev/null; then
        log_error "无法连接到MySQL，请检查MySQL服务状态和认证配置"
        exit 1
    fi
    
    log_success "MySQL连接正常"
}

# 创建测试数据库
create_test_database() {
    log_info "创建测试数据库: $TEST_DB_NAME"
    
    mysql -e "DROP DATABASE IF EXISTS $TEST_DB_NAME;"
    mysql -e "CREATE DATABASE $TEST_DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    
    log_success "测试数据库创建完成"
}

# 测试SQL文件语法
test_sql_file() {
    local sql_file="$1"
    local file_name=$(basename "$sql_file")
    
    log_info "测试文件: $file_name"
    
    # 检查文件是否存在
    if [ ! -f "$sql_file" ]; then
        log_error "文件不存在: $sql_file"
        return 1
    fi
    
    # 执行SQL文件进行语法检查
    if mysql "$TEST_DB_NAME" < "$sql_file" &> /dev/null; then
        log_success "✓ $file_name - 语法正确"
        return 0
    else
        log_error "✗ $file_name - 语法错误"
        log_error "错误详情:"
        mysql "$TEST_DB_NAME" < "$sql_file" 2>&1 | head -10
        return 1
    fi
}

# 清理测试数据库
cleanup_test_database() {
    log_info "清理测试数据库..."
    mysql -e "DROP DATABASE IF EXISTS $TEST_DB_NAME;" &> /dev/null || true
    log_success "测试数据库已清理"
}

# 主测试函数
main() {
    log_info "开始MySQL SQL语法测试..."
    
    # 检查MySQL连接
    check_mysql_connection
    
    # 创建测试数据库
    create_test_database
    
    # 需要测试的SQL文件列表
    local sql_files=(
        "$PROJECT_ROOT/database/init.sql"
        "$PROJECT_ROOT/database/production-setup-safe.sql"
        "$PROJECT_ROOT/database/add_distributed_fields.sql"
        "$PROJECT_ROOT/database/add_reservation_time_fields.sql"
        "$PROJECT_ROOT/database/update_schema_simple.sql"
        "$PROJECT_ROOT/database/update_wait_time_range.sql"
    )
    
    local total_files=${#sql_files[@]}
    local passed_files=0
    local failed_files=0
    
    echo ""
    log_info "测试 $total_files 个SQL文件..."
    echo ""
    
    # 逐个测试SQL文件
    for sql_file in "${sql_files[@]}"; do
        if test_sql_file "$sql_file"; then
            ((passed_files++))
        else
            ((failed_files++))
        fi
        echo ""
    done
    
    # 显示测试结果
    echo "================================"
    log_info "测试结果汇总:"
    echo "  总文件数: $total_files"
    echo "  通过: $passed_files"
    echo "  失败: $failed_files"
    echo "================================"
    
    # 清理测试数据库
    cleanup_test_database
    
    # 返回结果
    if [ $failed_files -eq 0 ]; then
        log_success "所有SQL文件语法测试通过！"
        return 0
    else
        log_error "有 $failed_files 个文件语法测试失败"
        return 1
    fi
}

# 信号处理
trap 'cleanup_test_database; log_error "测试被中断"; exit 1' SIGINT SIGTERM

# 执行主函数
main "$@"
