#!/bin/bash

# SeatMaster 一键部署脚本
# 支持开发环境和生产环境部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOY_ENV="${1:-dev}"
DEPLOY_TYPE="${2:-traditional}"  # traditional, docker, hybrid

# MySQL配置
MYSQL_CONFIG_FILE="$PROJECT_ROOT/.mysql_config"
DB_PASSWORD="${DB_PASSWORD:-}"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

log_success() {
    echo -e "${CYAN}[SUCCESS]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "SeatMaster 一键部署脚本"
    echo ""
    echo "用法: $0 [环境] [部署类型]"
    echo ""
    echo "环境选项:"
    echo "  dev        开发环境 (默认)"
    echo "  prod       生产环境"
    echo ""
    echo "部署类型:"
    echo "  traditional  传统部署 (systemd + PM2) (默认)"
    echo "  docker       Docker容器化部署"
    echo "  hybrid       混合部署 (数据库传统 + 应用Docker)"
    echo ""
    echo "示例:"
    echo "  $0 dev traditional     # 开发环境传统部署"
    echo "  $0 prod docker         # 生产环境Docker部署"
    echo "  $0 prod hybrid         # 生产环境混合部署"
    echo ""
}

# 设置MySQL认证
setup_mysql_auth() {
    log_step "设置MySQL认证..."

    # 检查是否设置了环境变量
    if [ -n "$DB_PASSWORD" ]; then
        log_info "使用环境变量中的数据库密码"
        return 0
    fi

    # 检查是否存在MySQL配置文件
    if [ -f "$MYSQL_CONFIG_FILE" ]; then
        log_info "使用MySQL配置文件: $MYSQL_CONFIG_FILE"
        return 0
    fi

    # 提示用户创建配置文件
    log_warn "未找到数据库密码配置"
    echo "请选择以下方式之一来配置数据库密码："
    echo "1. 创建MySQL配置文件（推荐）"
    echo "2. 设置环境变量"
    echo "3. 手动输入密码"
    echo ""
    read -p "请选择 (1-3): " choice

    case $choice in
        1)
            create_mysql_config
            ;;
        2)
            read -s -p "请输入数据库密码: " DB_PASSWORD
            echo ""
            export DB_PASSWORD
            ;;
        3)
            log_info "将在需要时提示输入密码"
            ;;
        *)
            log_error "无效选择"
            exit 1
            ;;
    esac
}

# 创建MySQL配置文件
create_mysql_config() {
    log_info "创建MySQL配置文件..."

    read -s -p "请输入MySQL root密码: " mysql_password
    echo ""

    cat > "$MYSQL_CONFIG_FILE" << EOF
[client]
user=root
password=$mysql_password
host=localhost
EOF

    # 设置安全权限
    chmod 600 "$MYSQL_CONFIG_FILE"

    log_success "MySQL配置文件创建完成: $MYSQL_CONFIG_FILE"
    log_warn "注意: 请妥善保管此文件，不要提交到版本控制系统"
}

# 检查参数
check_args() {
    if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        show_help
        exit 0
    fi

    if [ "$DEPLOY_ENV" != "dev" ] && [ "$DEPLOY_ENV" != "prod" ]; then
        log_error "无效的环境参数: $DEPLOY_ENV"
        show_help
        exit 1
    fi

    if [ "$DEPLOY_TYPE" != "traditional" ] && [ "$DEPLOY_TYPE" != "docker" ] && [ "$DEPLOY_TYPE" != "hybrid" ]; then
        log_error "无效的部署类型: $DEPLOY_TYPE"
        show_help
        exit 1
    fi
}

# 检查系统依赖
check_dependencies() {
    log_step "检查系统依赖..."
    
    local missing_deps=()
    
    # 检查基础工具
    for cmd in git curl wget; do
        if ! command -v $cmd &> /dev/null; then
            missing_deps+=($cmd)
        fi
    done
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        missing_deps+=(java)
    fi
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        missing_deps+=(maven)
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        missing_deps+=(nodejs)
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        missing_deps+=(npm)
    fi
    
    # Docker部署需要检查Docker
    if [ "$DEPLOY_TYPE" = "docker" ] || [ "$DEPLOY_TYPE" = "hybrid" ]; then
        if ! command -v docker &> /dev/null; then
            missing_deps+=(docker)
        fi
        
        if ! command -v docker-compose &> /dev/null; then
            missing_deps+=(docker-compose)
        fi
    fi
    
    # 传统部署需要检查systemd
    if [ "$DEPLOY_TYPE" = "traditional" ]; then
        if ! command -v systemctl &> /dev/null; then
            missing_deps+=(systemd)
        fi
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "缺少以下依赖: ${missing_deps[*]}"
        log_error "请先安装缺少的依赖"
        exit 1
    fi
    
    log_info "依赖检查通过"
}

# 检查权限
check_permissions() {
    log_step "检查权限..."
    
    if [ "$DEPLOY_ENV" = "prod" ]; then
        if [ "$EUID" -ne 0 ]; then
            log_error "生产环境部署需要root权限"
            exit 1
        fi
    fi
    
    # 检查Docker权限
    if [ "$DEPLOY_TYPE" = "docker" ] || [ "$DEPLOY_TYPE" = "hybrid" ]; then
        if ! docker info &> /dev/null; then
            log_error "无法访问Docker，请检查Docker服务状态和用户权限"
            exit 1
        fi
    fi
    
    log_info "权限检查通过"
}

# 备份现有部署
backup_existing() {
    log_step "备份现有部署..."
    
    local backup_dir="/var/backups/seatmaster/$(date +%Y%m%d_%H%M%S)"
    
    if [ "$DEPLOY_ENV" = "prod" ]; then
        mkdir -p "$backup_dir"
        
        # 备份应用文件
        if [ -d "/opt/seatmaster" ]; then
            cp -r /opt/seatmaster "$backup_dir/"
            log_info "应用文件备份完成"
        fi
        
        # 备份数据库
        if command -v mysqldump &> /dev/null; then
            if [ -f "$MYSQL_CONFIG_FILE" ]; then
                mysqldump --defaults-file="$MYSQL_CONFIG_FILE" seat_reservation > "$backup_dir/database_backup.sql" 2>/dev/null || true
            elif [ -n "$DB_PASSWORD" ]; then
                mysqldump -u root -p"$DB_PASSWORD" seat_reservation > "$backup_dir/database_backup.sql" 2>/dev/null || true
            else
                mysqldump -u root -p seat_reservation > "$backup_dir/database_backup.sql" 2>/dev/null || true
            fi
            log_info "数据库备份完成"
        fi
        
        # 备份配置文件
        if [ -d "/etc/nginx/conf.d" ]; then
            cp -r /etc/nginx/conf.d "$backup_dir/"
            log_info "Nginx配置备份完成"
        fi
        
        log_info "备份保存在: $backup_dir"
    else
        log_info "开发环境跳过备份"
    fi
}

# 构建后端
build_backend() {
    log_step "构建后端应用..."
    
    cd "$PROJECT_ROOT/backend"
    
    # 清理并构建
    mvn clean package -DskipTests -Pprod
    
    if [ ! -f "target/seat-reservation-backend-1.0.0.jar" ]; then
        log_error "后端构建失败"
        exit 1
    fi
    
    log_info "后端构建完成"
}

# 构建前端
build_frontend() {
    log_step "构建前端应用..."
    
    cd "$PROJECT_ROOT/frontend"
    
    # 安装依赖
    npm ci --production=false
    
    # 构建生产版本
    if [ "$DEPLOY_ENV" = "prod" ]; then
        npm run build
    else
        log_info "开发环境跳过前端构建"
    fi
    
    log_info "前端构建完成"
}

# 传统部署
deploy_traditional() {
    log_step "执行传统部署..."
    
    # 创建部署目录
    if [ "$DEPLOY_ENV" = "prod" ]; then
        mkdir -p /opt/seatmaster
        mkdir -p /var/log/seatmaster
        
        # 复制后端JAR
        cp "$PROJECT_ROOT/backend/target/seat-reservation-backend-1.0.0.jar" /opt/seatmaster/
        
        # 复制前端构建产物
        if [ -d "$PROJECT_ROOT/frontend/dist" ]; then
            rm -rf /var/www/seatmaster
            mkdir -p /var/www/seatmaster
            cp -r "$PROJECT_ROOT/frontend/dist/"* /var/www/seatmaster/
        fi
        
        # 复制配置文件
        cp "$PROJECT_ROOT/.env.production" /opt/seatmaster/
        
        # 设置权限
        chown -R seatmaster:seatmaster /opt/seatmaster
        chown -R www-data:www-data /var/www/seatmaster
        
        # 安装系统服务
        "$SCRIPT_DIR/install-services.sh"
        
        log_info "传统部署完成"
    else
        log_info "开发环境使用现有启动脚本"
    fi
}

# Docker部署
deploy_docker() {
    log_step "执行Docker部署..."
    
    cd "$PROJECT_ROOT"
    
    # 复制环境变量文件
    if [ ! -f ".env" ]; then
        cp ".env.docker" ".env"
        log_warn "请编辑 .env 文件配置环境变量"
    fi
    
    # 构建镜像
    docker-compose build
    
    # 启动服务
    if [ "$DEPLOY_ENV" = "prod" ]; then
        docker-compose --profile production up -d
    else
        docker-compose up -d
    fi
    
    log_info "Docker部署完成"
}

# 混合部署
deploy_hybrid() {
    log_step "执行混合部署..."
    
    # 传统方式部署数据库
    log_info "使用传统方式部署数据库..."
    
    # Docker方式部署应用
    log_info "使用Docker方式部署应用..."
    
    cd "$PROJECT_ROOT"
    
    # 只启动应用容器，不启动数据库容器
    docker-compose up -d backend frontend nginx
    
    log_info "混合部署完成"
}

# 初始化数据库
init_database() {
    log_step "初始化数据库..."

    # 确定MySQL命令参数
    local mysql_cmd_args=""
    if [ -f "$MYSQL_CONFIG_FILE" ]; then
        mysql_cmd_args="--defaults-file=$MYSQL_CONFIG_FILE"
    elif [ -n "$DB_PASSWORD" ]; then
        mysql_cmd_args="-u root -p$DB_PASSWORD"
    else
        mysql_cmd_args="-u root -p"
    fi

    if [ "$DEPLOY_ENV" = "prod" ]; then
        # 执行生产环境数据库安全配置
        log_info "使用安全版本的生产环境数据库配置..."
        if [ -f "$PROJECT_ROOT/database/production-setup-safe.sql" ]; then
            mysql $mysql_cmd_args < "$PROJECT_ROOT/database/production-setup-safe.sql" || true
            log_info "生产环境数据库安全配置完成"
        else
            log_warn "未找到安全版本的生产环境配置文件，跳过数据库优化"
        fi
    else
        # 执行开发环境数据库初始化
        mysql $mysql_cmd_args seat_reservation < "$PROJECT_ROOT/database/init.sql" || true

        # 执行等待时间范围更新脚本
        if [ -f "$PROJECT_ROOT/database/update_wait_time_range.sql" ]; then
            log_info "执行等待时间范围更新..."
            mysql $mysql_cmd_args < "$PROJECT_ROOT/database/update_wait_time_range.sql" || true
        fi

        log_info "开发环境数据库初始化完成"
    fi
}

# 健康检查
health_check() {
    log_step "执行健康检查..."
    
    local backend_url="http://localhost:8081/actuator/health"
    local frontend_url="http://localhost:3000"
    
    # 等待服务启动
    sleep 30
    
    # 检查后端
    if curl -f "$backend_url" &> /dev/null; then
        log_success "后端服务健康检查通过"
    else
        log_warn "后端服务健康检查失败"
    fi
    
    # 检查前端
    if curl -f "$frontend_url" &> /dev/null; then
        log_success "前端服务健康检查通过"
    else
        log_warn "前端服务健康检查失败"
    fi
}

# 显示部署结果
show_result() {
    log_success "================================"
    log_success "SeatMaster 部署完成!"
    log_success "================================"
    echo ""
    echo "部署信息:"
    echo "  环境: $DEPLOY_ENV"
    echo "  类型: $DEPLOY_TYPE"
    echo "  时间: $(date)"
    echo ""
    echo "访问地址:"
    echo "  前端: http://localhost:3000"
    echo "  后端: http://localhost:8081"
    echo "  API文档: http://localhost:8081/swagger-ui.html"
    echo ""
    
    if [ "$DEPLOY_TYPE" = "traditional" ]; then
        echo "服务管理:"
        echo "  启动: seatmaster start"
        echo "  停止: seatmaster stop"
        echo "  状态: seatmaster status"
        echo "  日志: seatmaster logs [backend|frontend|nginx]"
    elif [ "$DEPLOY_TYPE" = "docker" ]; then
        echo "Docker管理:"
        echo "  启动: docker-compose up -d"
        echo "  停止: docker-compose down"
        echo "  状态: docker-compose ps"
        echo "  日志: docker-compose logs -f [service]"
    fi
    
    echo ""
    echo "配置文件:"
    if [ "$DEPLOY_ENV" = "prod" ]; then
        echo "  环境配置: /opt/seatmaster/.env.production"
        echo "  日志目录: /var/log/seatmaster/"
    else
        echo "  环境配置: $PROJECT_ROOT/.env"
        echo "  日志目录: $PROJECT_ROOT/logs/"
    fi
    echo ""
}

# 主函数
main() {
    log_info "开始SeatMaster部署..."
    log_info "环境: $DEPLOY_ENV, 类型: $DEPLOY_TYPE"

    check_args "$@"
    check_dependencies
    check_permissions
    setup_mysql_auth
    backup_existing
    
    # 构建应用
    build_backend
    build_frontend
    
    # 根据部署类型执行部署
    case "$DEPLOY_TYPE" in
        traditional)
            deploy_traditional
            ;;
        docker)
            deploy_docker
            ;;
        hybrid)
            deploy_hybrid
            ;;
    esac
    
    # 初始化数据库
    init_database
    
    # 健康检查
    health_check
    
    # 显示结果
    show_result
    
    log_success "部署完成!"
}

# 信号处理
trap 'log_error "部署被中断"; exit 1' SIGINT SIGTERM

# 执行主函数
main "$@"
