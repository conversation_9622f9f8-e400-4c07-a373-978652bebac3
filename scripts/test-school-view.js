/**
 * 学校视图功能验证脚本
 * 用于自动化测试学校视图的基本功能
 */

// 测试配置
const TEST_CONFIG = {
  baseUrl: 'http://localhost:8080/api/admin/execution-logs',
  timeout: 10000,
  retryCount: 3
};

// 测试结果记录
const testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

/**
 * 执行HTTP请求
 */
async function makeRequest(url, options = {}) {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), TEST_CONFIG.timeout);
  
  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_TOKEN_HERE', // 需要替换为实际token
        ...options.headers
      }
    });
    
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

/**
 * 测试用例：学校概览API
 */
async function testSchoolsOverview() {
  console.log('🧪 测试学校概览API...');
  
  try {
    // 基本查询测试
    const response = await makeRequest(`${TEST_CONFIG.baseUrl}/schools-overview?page=1&size=10`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    // 验证响应结构
    if (!data.success) {
      throw new Error('API返回失败状态');
    }
    
    if (!data.data || !Array.isArray(data.data.schools)) {
      throw new Error('响应数据结构不正确');
    }
    
    // 验证学校数据字段
    if (data.data.schools.length > 0) {
      const school = data.data.schools[0];
      const requiredFields = ['schoolId', 'schoolName', 'totalExecutions', 'successRate', 'avgDurationMs', 'todayExecutions', 'todaySuccessRate'];
      
      for (const field of requiredFields) {
        if (!(field in school)) {
          throw new Error(`学校数据缺少字段: ${field}`);
        }
      }
    }
    
    console.log('✅ 学校概览API测试通过');
    testResults.passed++;
    
    // 测试分页参数
    await testPagination();
    
    // 测试排序参数
    await testSorting();
    
  } catch (error) {
    console.error('❌ 学校概览API测试失败:', error.message);
    testResults.failed++;
    testResults.errors.push(`学校概览API: ${error.message}`);
  }
}

/**
 * 测试分页功能
 */
async function testPagination() {
  console.log('🧪 测试分页功能...');
  
  try {
    // 测试不同页面大小
    const pageSizes = [5, 10, 20];
    
    for (const size of pageSizes) {
      const response = await makeRequest(`${TEST_CONFIG.baseUrl}/schools-overview?page=1&size=${size}`);
      const data = await response.json();
      
      if (data.data.schools.length > size) {
        throw new Error(`页面大小${size}返回了${data.data.schools.length}条记录`);
      }
    }
    
    console.log('✅ 分页功能测试通过');
    testResults.passed++;
    
  } catch (error) {
    console.error('❌ 分页功能测试失败:', error.message);
    testResults.failed++;
    testResults.errors.push(`分页功能: ${error.message}`);
  }
}

/**
 * 测试排序功能
 */
async function testSorting() {
  console.log('🧪 测试排序功能...');
  
  try {
    const sortFields = ['totalExecutions', 'successRate', 'avgDurationMs', 'todayExecutions', 'todaySuccessRate'];
    const sortOrders = ['asc', 'desc'];
    
    for (const sortBy of sortFields) {
      for (const sortOrder of sortOrders) {
        const response = await makeRequest(`${TEST_CONFIG.baseUrl}/schools-overview?page=1&size=5&sortBy=${sortBy}&sortOrder=${sortOrder}`);
        
        if (!response.ok) {
          throw new Error(`排序测试失败: ${sortBy} ${sortOrder}`);
        }
        
        const data = await response.json();
        
        if (!data.success) {
          throw new Error(`排序返回错误: ${sortBy} ${sortOrder}`);
        }
      }
    }
    
    console.log('✅ 排序功能测试通过');
    testResults.passed++;
    
  } catch (error) {
    console.error('❌ 排序功能测试失败:', error.message);
    testResults.failed++;
    testResults.errors.push(`排序功能: ${error.message}`);
  }
}

/**
 * 测试学校日期统计API
 */
async function testSchoolDailyStats() {
  console.log('🧪 测试学校日期统计API...');
  
  try {
    // 首先获取一个学校ID
    const overviewResponse = await makeRequest(`${TEST_CONFIG.baseUrl}/schools-overview?page=1&size=1`);
    const overviewData = await overviewResponse.json();
    
    if (!overviewData.data.schools.length) {
      console.log('⚠️ 没有学校数据，跳过日期统计测试');
      return;
    }
    
    const schoolId = overviewData.data.schools[0].schoolId;
    
    // 测试基本查询
    const response = await makeRequest(`${TEST_CONFIG.baseUrl}/school/${schoolId}/daily-stats`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    // 验证响应结构
    if (!data.success) {
      throw new Error('API返回失败状态');
    }
    
    if (!data.data || !Array.isArray(data.data.dailyStats)) {
      throw new Error('日期统计数据结构不正确');
    }
    
    // 验证日期统计字段
    if (data.data.dailyStats.length > 0) {
      const stat = data.data.dailyStats[0];
      const requiredFields = ['date', 'dailyExecutions', 'dailySuccessRate', 'dailyAvgDurationMs'];
      
      for (const field of requiredFields) {
        if (!(field in stat)) {
          throw new Error(`日期统计数据缺少字段: ${field}`);
        }
      }
    }
    
    console.log('✅ 学校日期统计API测试通过');
    testResults.passed++;
    
  } catch (error) {
    console.error('❌ 学校日期统计API测试失败:', error.message);
    testResults.failed++;
    testResults.errors.push(`学校日期统计API: ${error.message}`);
  }
}

/**
 * 测试错误处理
 */
async function testErrorHandling() {
  console.log('🧪 测试错误处理...');
  
  try {
    // 测试无效学校ID
    const response = await makeRequest(`${TEST_CONFIG.baseUrl}/school/99999/daily-stats`);
    
    // 应该返回错误或空数据，但不应该崩溃
    if (response.ok) {
      const data = await response.json();
      if (data.success && data.data.dailyStats.length > 0) {
        console.log('⚠️ 无效学校ID返回了数据，可能需要检查');
      }
    }
    
    // 测试无效参数
    const invalidResponse = await makeRequest(`${TEST_CONFIG.baseUrl}/schools-overview?page=-1&size=0`);
    
    console.log('✅ 错误处理测试通过');
    testResults.passed++;
    
  } catch (error) {
    console.error('❌ 错误处理测试失败:', error.message);
    testResults.failed++;
    testResults.errors.push(`错误处理: ${error.message}`);
  }
}

/**
 * 性能测试
 */
async function testPerformance() {
  console.log('🧪 测试API性能...');
  
  try {
    const startTime = Date.now();
    
    // 并发请求测试
    const promises = [];
    for (let i = 0; i < 5; i++) {
      promises.push(makeRequest(`${TEST_CONFIG.baseUrl}/schools-overview?page=1&size=10`));
    }
    
    await Promise.all(promises);
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`📊 5个并发请求耗时: ${duration}ms`);
    
    if (duration > 5000) {
      throw new Error(`性能测试失败: 5个并发请求耗时${duration}ms，超过5秒阈值`);
    }
    
    console.log('✅ 性能测试通过');
    testResults.passed++;
    
  } catch (error) {
    console.error('❌ 性能测试失败:', error.message);
    testResults.failed++;
    testResults.errors.push(`性能测试: ${error.message}`);
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始学校视图功能测试...\n');
  
  // 执行所有测试
  await testSchoolsOverview();
  await testSchoolDailyStats();
  await testErrorHandling();
  await testPerformance();
  
  // 输出测试结果
  console.log('\n📋 测试结果汇总:');
  console.log(`✅ 通过: ${testResults.passed}`);
  console.log(`❌ 失败: ${testResults.failed}`);
  
  if (testResults.errors.length > 0) {
    console.log('\n🐛 错误详情:');
    testResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error}`);
    });
  }
  
  const successRate = (testResults.passed / (testResults.passed + testResults.failed)) * 100;
  console.log(`\n📊 成功率: ${successRate.toFixed(1)}%`);
  
  if (successRate === 100) {
    console.log('🎉 所有测试通过！');
  } else if (successRate >= 80) {
    console.log('⚠️ 大部分测试通过，但有一些问题需要关注');
  } else {
    console.log('🚨 测试失败率较高，需要修复问题');
  }
}

// 运行测试
if (typeof window !== 'undefined') {
  // 浏览器环境
  window.runSchoolViewTests = runTests;
  console.log('💡 在浏览器控制台中运行: runSchoolViewTests()');
} else {
  // Node.js环境
  runTests().catch(console.error);
}

module.exports = { runTests, testResults };
