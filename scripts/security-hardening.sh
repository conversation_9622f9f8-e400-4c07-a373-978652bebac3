#!/bin/bash

# SeatMaster 安全加固脚本
# 用于系统和应用安全配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查权限
check_permissions() {
    if [ "$EUID" -ne 0 ]; then
        log_error "安全加固需要root权限"
        exit 1
    fi
}

# 系统更新
update_system() {
    log_step "更新系统..."
    
    if command -v apt-get &> /dev/null; then
        apt-get update
        apt-get upgrade -y
        apt-get autoremove -y
    elif command -v yum &> /dev/null; then
        yum update -y
    fi
    
    log_info "系统更新完成"
}

# 配置防火墙
configure_firewall() {
    log_step "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        # Ubuntu/Debian UFW配置
        ufw --force reset
        
        # 默认策略
        ufw default deny incoming
        ufw default allow outgoing
        
        # 允许SSH
        ufw allow 22/tcp
        
        # 允许HTTP/HTTPS
        ufw allow 80/tcp
        ufw allow 443/tcp
        
        # 允许应用端口
        ufw allow 8081/tcp  # 后端API
        ufw allow 3000/tcp  # 前端开发服务器
        
        # 允许数据库（仅本地）
        ufw allow from 127.0.0.1 to any port 3306
        ufw allow from ::1 to any port 3306
        
        # 启用防火墙
        ufw --force enable
        
        log_info "UFW防火墙配置完成"
        
    elif command -v firewall-cmd &> /dev/null; then
        # CentOS/RHEL firewalld配置
        systemctl enable firewalld
        systemctl start firewalld
        
        # 移除不必要的服务
        firewall-cmd --permanent --remove-service=dhcpv6-client || true
        
        # 添加必要的服务
        firewall-cmd --permanent --add-service=ssh
        firewall-cmd --permanent --add-service=http
        firewall-cmd --permanent --add-service=https
        
        # 添加自定义端口
        firewall-cmd --permanent --add-port=8081/tcp
        firewall-cmd --permanent --add-port=3000/tcp
        
        # 重新加载配置
        firewall-cmd --reload
        
        log_info "firewalld防火墙配置完成"
    else
        log_warn "未检测到防火墙，请手动配置iptables规则"
    fi
}

# SSH安全配置
secure_ssh() {
    log_step "配置SSH安全..."
    
    local ssh_config="/etc/ssh/sshd_config"
    local backup_config="${ssh_config}.backup.$(date +%Y%m%d)"
    
    # 备份原配置
    cp "$ssh_config" "$backup_config"
    
    # 安全配置
    cat > "$ssh_config" << 'EOF'
# SeatMaster SSH安全配置

# 基础配置
Port 22
Protocol 2
HostKey /etc/ssh/ssh_host_rsa_key
HostKey /etc/ssh/ssh_host_ecdsa_key
HostKey /etc/ssh/ssh_host_ed25519_key

# 安全配置
PermitRootLogin no
PasswordAuthentication yes
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys
PermitEmptyPasswords no
ChallengeResponseAuthentication no
UsePAM yes

# 连接配置
MaxAuthTries 3
MaxSessions 10
ClientAliveInterval 300
ClientAliveCountMax 2
LoginGraceTime 60

# 协议配置
X11Forwarding no
PrintMotd no
AcceptEnv LANG LC_*
Subsystem sftp /usr/lib/openssh/sftp-server

# 日志配置
SyslogFacility AUTH
LogLevel INFO

# 限制用户
AllowUsers seatmaster
DenyUsers root

# 其他安全配置
StrictModes yes
IgnoreRhosts yes
HostbasedAuthentication no
PermitUserEnvironment no
Compression delayed
TCPKeepAlive yes
UsePrivilegeSeparation yes
EOF
    
    # 重启SSH服务
    systemctl restart sshd
    
    log_info "SSH安全配置完成"
    log_warn "请确保在断开连接前测试SSH连接"
}

# 配置Fail2Ban
setup_fail2ban() {
    log_step "配置Fail2Ban..."
    
    # 安装Fail2Ban
    if ! command -v fail2ban-server &> /dev/null; then
        if command -v apt-get &> /dev/null; then
            apt-get install -y fail2ban
        elif command -v yum &> /dev/null; then
            yum install -y epel-release
            yum install -y fail2ban
        fi
    fi
    
    # 配置Fail2Ban
    cat > /etc/fail2ban/jail.local << 'EOF'
[DEFAULT]
# 默认配置
bantime = 3600
findtime = 600
maxretry = 3
backend = auto
usedns = warn
logencoding = auto
enabled = false
mode = normal
filter = %(__name__)s[mode=%(mode)s]

# 邮件通知配置
destemail = <EMAIL>
sendername = Fail2Ban
mta = sendmail
action = %(action_mwl)s

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 3

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 3

[nginx-botsearch]
enabled = true
filter = nginx-botsearch
port = http,https
logpath = /var/log/nginx/access.log
maxretry = 2
bantime = 7200

# SeatMaster应用保护
[seatmaster-auth]
enabled = true
filter = seatmaster-auth
port = http,https
logpath = /var/log/seatmaster/seatmaster-backend.log
maxretry = 5
bantime = 1800
EOF
    
    # 创建SeatMaster认证过滤器
    cat > /etc/fail2ban/filter.d/seatmaster-auth.conf << 'EOF'
[Definition]
failregex = ^.*Authentication failed for user.*from <HOST>.*$
            ^.*Invalid login attempt from <HOST>.*$
            ^.*Failed login for.*from <HOST>.*$
ignoreregex =
EOF
    
    # 启动Fail2Ban
    systemctl enable fail2ban
    systemctl restart fail2ban
    
    log_info "Fail2Ban配置完成"
}

# 数据库安全配置
secure_database() {
    log_step "配置数据库安全..."
    
    # MySQL安全配置
    if command -v mysql &> /dev/null; then
        # 创建MySQL安全配置文件
        cat > /etc/mysql/conf.d/security.cnf << 'EOF'
[mysqld]
# 安全配置
skip-symbolic-links = 1
local-infile = 0
skip-show-database

# 网络安全
bind-address = 127.0.0.1
skip-networking = 0

# 日志配置
log-error = /var/log/mysql/error.log
slow-query-log = 1
slow-query-log-file = /var/log/mysql/slow.log
long_query_time = 2

# 连接限制
max_connections = 200
max_connect_errors = 100000
max_user_connections = 50

# 其他安全配置
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION
EOF
        
        # 重启MySQL
        systemctl restart mysql || systemctl restart mariadb
        
        log_info "MySQL安全配置完成"
    fi
}

# 应用安全配置
secure_application() {
    log_step "配置应用安全..."
    
    # 创建应用安全配置
    local security_config="/opt/seatmaster/security.properties"
    cat > "$security_config" << 'EOF'
# SeatMaster应用安全配置

# 会话安全
session.timeout=1800
session.cookie.secure=true
session.cookie.httpOnly=true
session.cookie.sameSite=Strict

# CSRF保护
csrf.enabled=true
csrf.token.timeout=3600

# XSS保护
xss.protection.enabled=true
content.type.options=nosniff
frame.options=SAMEORIGIN

# 请求限制
rate.limit.enabled=true
rate.limit.requests.per.minute=60
rate.limit.burst.size=10

# 文件上传安全
upload.max.file.size=10MB
upload.allowed.types=image/jpeg,image/png,image/gif,application/pdf
upload.scan.virus=true

# 密码策略
password.min.length=8
password.require.uppercase=true
password.require.lowercase=true
password.require.digit=true
password.require.special=true
password.max.attempts=5
password.lockout.duration=1800

# API安全
api.key.required=true
api.rate.limit=100
api.cors.allowed.origins=https://yourdomain.com
api.https.only=true

# 日志安全
log.sensitive.data=false
log.max.level=INFO
audit.log.enabled=true
EOF
    
    # 设置权限
    chown seatmaster:seatmaster "$security_config"
    chmod 600 "$security_config"
    
    log_info "应用安全配置完成"
}

# 系统加固
system_hardening() {
    log_step "系统加固..."
    
    # 禁用不必要的服务
    local services_to_disable=(
        "telnet"
        "rsh"
        "rlogin"
        "vsftpd"
        "xinetd"
        "avahi-daemon"
        "cups"
    )
    
    for service in "${services_to_disable[@]}"; do
        if systemctl is-enabled "$service" &> /dev/null; then
            systemctl disable "$service"
            systemctl stop "$service"
            log_info "已禁用服务: $service"
        fi
    done
    
    # 内核参数调优
    cat > /etc/sysctl.d/99-seatmaster-security.conf << 'EOF'
# SeatMaster安全内核参数

# 网络安全
net.ipv4.ip_forward = 0
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.default.send_redirects = 0
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.default.accept_redirects = 0
net.ipv4.conf.all.secure_redirects = 0
net.ipv4.conf.default.secure_redirects = 0
net.ipv4.conf.all.accept_source_route = 0
net.ipv4.conf.default.accept_source_route = 0
net.ipv4.conf.all.log_martians = 1
net.ipv4.conf.default.log_martians = 1
net.ipv4.icmp_echo_ignore_broadcasts = 1
net.ipv4.icmp_ignore_bogus_error_responses = 1
net.ipv4.tcp_syncookies = 1

# IPv6安全
net.ipv6.conf.all.accept_redirects = 0
net.ipv6.conf.default.accept_redirects = 0
net.ipv6.conf.all.accept_source_route = 0
net.ipv6.conf.default.accept_source_route = 0

# 其他安全参数
kernel.dmesg_restrict = 1
kernel.kptr_restrict = 2
kernel.yama.ptrace_scope = 1
fs.suid_dumpable = 0
EOF
    
    # 应用内核参数
    sysctl -p /etc/sysctl.d/99-seatmaster-security.conf
    
    # 设置文件权限
    chmod 644 /etc/passwd
    chmod 600 /etc/shadow
    chmod 644 /etc/group
    chmod 600 /etc/gshadow
    
    log_info "系统加固完成"
}

# 配置日志审计
setup_audit_logging() {
    log_step "配置审计日志..."
    
    # 安装auditd
    if ! command -v auditctl &> /dev/null; then
        if command -v apt-get &> /dev/null; then
            apt-get install -y auditd audispd-plugins
        elif command -v yum &> /dev/null; then
            yum install -y audit
        fi
    fi
    
    # 配置审计规则
    cat > /etc/audit/rules.d/seatmaster.rules << 'EOF'
# SeatMaster审计规则

# 删除所有现有规则
-D

# 设置缓冲区大小
-b 8192

# 失败模式
-f 1

# 监控重要文件
-w /etc/passwd -p wa -k identity
-w /etc/group -p wa -k identity
-w /etc/shadow -p wa -k identity
-w /etc/sudoers -p wa -k identity

# 监控系统调用
-a always,exit -F arch=b64 -S adjtimex -S settimeofday -k time-change
-a always,exit -F arch=b32 -S adjtimex -S settimeofday -S stime -k time-change
-a always,exit -F arch=b64 -S clock_settime -k time-change
-a always,exit -F arch=b32 -S clock_settime -k time-change

# 监控网络配置
-a always,exit -F arch=b64 -S sethostname -S setdomainname -k system-locale
-a always,exit -F arch=b32 -S sethostname -S setdomainname -k system-locale

# 监控登录事件
-w /var/log/faillog -p wa -k logins
-w /var/log/lastlog -p wa -k logins
-w /var/log/tallylog -p wa -k logins

# 监控进程和会话
-w /var/run/utmp -p wa -k session
-w /var/log/wtmp -p wa -k logins
-w /var/log/btmp -p wa -k logins

# 监控权限修改
-a always,exit -F arch=b64 -S chmod -S fchmod -S fchmodat -F auid>=1000 -F auid!=4294967295 -k perm_mod
-a always,exit -F arch=b32 -S chmod -S fchmod -S fchmodat -F auid>=1000 -F auid!=4294967295 -k perm_mod

# 监控SeatMaster相关文件
-w /opt/seatmaster -p wa -k seatmaster-files
-w /etc/nginx/conf.d/seatmaster.conf -p wa -k seatmaster-config
-w /etc/systemd/system/seatmaster-*.service -p wa -k seatmaster-services

# 使规则不可变
-e 2
EOF
    
    # 启动auditd
    systemctl enable auditd
    systemctl restart auditd
    
    log_info "审计日志配置完成"
}

# 安装安全工具
install_security_tools() {
    log_step "安装安全工具..."
    
    local tools=()
    
    if command -v apt-get &> /dev/null; then
        tools=(
            "rkhunter"      # Rootkit检测
            "chkrootkit"    # Rootkit检测
            "lynis"         # 安全审计
            "clamav"        # 病毒扫描
            "aide"          # 文件完整性检查
        )
        
        apt-get update
        for tool in "${tools[@]}"; do
            apt-get install -y "$tool" || log_warn "无法安装 $tool"
        done
    elif command -v yum &> /dev/null; then
        tools=(
            "rkhunter"
            "chkrootkit"
            "lynis"
            "clamav"
            "aide"
        )
        
        yum install -y epel-release
        for tool in "${tools[@]}"; do
            yum install -y "$tool" || log_warn "无法安装 $tool"
        done
    fi
    
    # 配置ClamAV
    if command -v freshclam &> /dev/null; then
        freshclam
        systemctl enable clamav-freshclam || systemctl enable clamd
    fi
    
    log_info "安全工具安装完成"
}

# 创建安全检查脚本
create_security_check_script() {
    log_step "创建安全检查脚本..."
    
    local check_script="/usr/local/bin/security-check.sh"
    cat > "$check_script" << 'EOF'
#!/bin/bash

# SeatMaster安全检查脚本

echo "SeatMaster安全检查报告 - $(date)"
echo "=================================="

# 检查系统更新
echo "1. 系统更新检查:"
if command -v apt-get &> /dev/null; then
    apt list --upgradable 2>/dev/null | wc -l | sed 's/^/   可更新包数量: /'
elif command -v yum &> /dev/null; then
    yum check-update --quiet | wc -l | sed 's/^/   可更新包数量: /'
fi

# 检查防火墙状态
echo "2. 防火墙状态:"
if command -v ufw &> /dev/null; then
    ufw status | head -1 | sed 's/^/   /'
elif command -v firewall-cmd &> /dev/null; then
    firewall-cmd --state | sed 's/^/   /'
fi

# 检查失败登录
echo "3. 失败登录检查:"
grep "Failed password" /var/log/auth.log 2>/dev/null | tail -5 | sed 's/^/   /'

# 检查Fail2Ban状态
echo "4. Fail2Ban状态:"
if command -v fail2ban-client &> /dev/null; then
    fail2ban-client status | sed 's/^/   /'
fi

# 检查监听端口
echo "5. 监听端口:"
ss -tuln | grep LISTEN | sed 's/^/   /'

# 检查磁盘使用
echo "6. 磁盘使用:"
df -h | grep -v tmpfs | sed 's/^/   /'

# 检查内存使用
echo "7. 内存使用:"
free -h | sed 's/^/   /'

# 检查进程
echo "8. 关键进程:"
ps aux | grep -E "(nginx|java|mysql)" | grep -v grep | sed 's/^/   /'

echo ""
echo "检查完成 - $(date)"
EOF
    
    chmod +x "$check_script"
    
    # 添加到crontab（每日检查）
    (crontab -l 2>/dev/null; echo "0 6 * * * $check_script > /var/log/security-check.log 2>&1") | crontab -
    
    log_info "安全检查脚本创建完成"
}

# 显示安全配置结果
show_security_result() {
    log_info "================================"
    log_info "安全加固完成!"
    log_info "================================"
    echo ""
    echo "已完成的安全配置:"
    echo "  ✓ 系统更新"
    echo "  ✓ 防火墙配置"
    echo "  ✓ SSH安全加固"
    echo "  ✓ Fail2Ban入侵防护"
    echo "  ✓ 数据库安全配置"
    echo "  ✓ 应用安全配置"
    echo "  ✓ 系统内核加固"
    echo "  ✓ 审计日志配置"
    echo "  ✓ 安全工具安装"
    echo "  ✓ 安全检查脚本"
    echo ""
    echo "安全检查命令:"
    echo "  系统安全检查: /usr/local/bin/security-check.sh"
    echo "  Rootkit检查: rkhunter --check"
    echo "  病毒扫描: clamscan -r /"
    echo "  安全审计: lynis audit system"
    echo ""
    echo "日志位置:"
    echo "  审计日志: /var/log/audit/audit.log"
    echo "  安全检查: /var/log/security-check.log"
    echo "  Fail2Ban: /var/log/fail2ban.log"
    echo ""
    echo "重要提醒:"
    echo "  1. 请定期更新系统和应用"
    echo "  2. 监控安全日志和告警"
    echo "  3. 定期运行安全检查"
    echo "  4. 备份重要数据和配置"
    echo ""
}

# 主函数
main() {
    log_info "开始SeatMaster安全加固..."
    
    check_permissions
    update_system
    configure_firewall
    secure_ssh
    setup_fail2ban
    secure_database
    secure_application
    system_hardening
    setup_audit_logging
    install_security_tools
    create_security_check_script
    show_security_result
    
    log_info "安全加固完成!"
}

# 信号处理
trap 'log_error "安全加固被中断"; exit 1' SIGINT SIGTERM

# 执行主函数
main "$@"
