# SeatMaster Worker 端口配置使用指南

## 概述

SeatMaster Worker Server 现在支持多种灵活的端口配置方式，包括增强的命令行参数解析、配置验证、端口检测等功能。

## 支持的配置方式

### 1. 命令行参数（推荐）

java -jar target/worker-server-simple-1.0.0.jar -p 8083 --worker-id=worker-8083

#### 端口配置
```bash
# 简写格式
java -jar worker-server.jar -p 8082

# 标准格式
java -jar worker-server.jar --port=8083

# Spring Boot格式
java -jar worker-server.jar --server.port=8084
```

#### Worker配置
```bash
# Worker ID
java -jar worker-server.jar -id worker-001
java -jar worker-server.jar --worker-id=worker-002
java -jar worker-server.jar --worker.id=worker-003

# Worker名称
java -jar worker-server.jar -name "Worker1"
java -jar worker-server.jar --worker-name="Worker2"
java -jar worker-server.jar --worker.name="Worker3"
```

#### 完整配置示例
```bash
java -jar worker-server.jar \
  -p 8085 \
  --worker-id=production-worker-001 \
  --worker-name="生产环境Worker1" \
  --master-url=http://*************:8081 \
  --max-tasks=20 \
  --db-url=******************************************** \
  --db-user=root \
  --db-password=password
```

### 2. 环境变量
```bash
export WORKER_PORT=8082
export WORKER_ID=worker-001
export WORKER_NAME="Worker1"
export MASTER_URL=http://localhost:8081
export DATABASE_URL=********************************************
export DATABASE_USERNAME=root
export DATABASE_PASSWORD=root

java -jar worker-server.jar
```

### 3. 系统属性
```bash
java -Dserver.port=8082 \
     -Dworker.id=worker-001 \
     -Dworker.name="Worker1" \
     -jar worker-server.jar
```

## 参数验证功能

### 端口验证
- 端口范围：1024-65535
- 自动检测端口是否被占用
- 提供可用端口推荐

### Worker ID验证
- 只能包含字母、数字、下划线、横线
- 长度：3-50个字符
- 不能为空

### 配置验证
- 数据库URL格式检查
- 主服务器URL格式检查
- 线程池配置合理性检查

## 工具和脚本

### 1. 帮助信息
```bash
java -jar worker-server.jar --help
java -jar worker-server.jar -h
java -jar worker-server.jar -?
```

### 2. 配置向导
```bash
# 交互式配置生成
java -cp target/classes com.seatmaster.worker.util.ConfigWizard
```

### 3. 端口检测工具
```bash
# 检查单个端口
java -cp target/classes com.seatmaster.worker.util.PortChecker 8082

# 检查端口范围
java -cp target/classes com.seatmaster.worker.util.PortChecker 8080 8090

# 推荐可用端口
java -cp target/classes com.seatmaster.worker.util.PortChecker recommend 8082
```

### 4. 管理脚本
- `worker_manager.bat` - Worker管理工具
- `start_worker_examples.bat` - 启动示例脚本
- `test_worker_args.bat` - 参数测试脚本

## 使用场景

### 场景1：开发环境快速启动
```bash
# 使用默认配置
java -jar worker-server.jar

# 指定端口
java -jar worker-server.jar -p 8083
```

### 场景2：生产环境部署
```bash
# 完整配置
java -jar worker-server.jar \
  --port=8082 \
  --worker-id=prod-worker-01 \
  --worker-name="生产Worker1" \
  --master-url=http://master.company.com:8081 \
  --max-tasks=50 \
  --db-url=******************************************* \
  --db-user=seatmaster \
  --db-password=secure_password
```

### 场景3：批量部署多个Worker
```bash
# Worker 1
java -jar worker-server.jar -p 8082 -id worker-001 -name "Worker1" &

# Worker 2  
java -jar worker-server.jar -p 8083 -id worker-002 -name "Worker2" &

# Worker 3
java -jar worker-server.jar -p 8084 -id worker-003 -name "Worker3" &
```

### 场景4：Docker容器部署
```dockerfile
# Dockerfile
FROM openjdk:11-jre-slim
COPY worker-server.jar /app/
WORKDIR /app

# 使用环境变量配置
ENV WORKER_PORT=8082
ENV WORKER_ID=docker-worker
ENV MASTER_URL=http://master:8081

CMD ["java", "-jar", "worker-server.jar"]
```

## 错误处理

### 常见错误及解决方案

1. **端口被占用**
   ```
   错误: 端口已被占用: 8082
   解决: 使用端口检测工具查找可用端口
   ```

2. **Worker ID格式错误**
   ```
   错误: Worker ID只能包含字母、数字、下划线和横线
   解决: 修改Worker ID格式，如 worker-001
   ```

3. **数据库连接失败**
   ```
   错误: 数据库URL格式无效
   解决: 确保URL以 jdbc: 开头
   ```

## 最佳实践

1. **端口规划**
   - 开发环境：8082-8089
   - 测试环境：8090-8099
   - 生产环境：8100-8199

2. **Worker命名**
   - 使用有意义的ID：`prod-worker-001`
   - 包含环境信息：`dev-worker-01`
   - 避免特殊字符

3. **配置管理**
   - 生产环境使用环境变量
   - 开发环境使用命令行参数
   - 使用配置向导生成标准配置

4. **监控和维护**
   - 定期检查端口状态
   - 监控Worker运行状态
   - 使用管理脚本简化操作

## 故障排除

### 启动失败排查步骤

1. 检查端口是否可用
2. 验证配置参数格式
3. 确认数据库连接
4. 检查主服务器可达性
5. 查看详细错误日志

### 常用调试命令

```bash
# 检查Java进程
jps | grep SimpleWorkerApplication

# 检查端口占用
netstat -an | grep :8082

# 查看详细启动日志
java -jar worker-server.jar --debug
```

---

**注意**: 本指南基于最新的Worker端口配置增强功能，确保使用最新版本的代码。
