# SeatMaster 生产环境配置文件
# 请根据实际环境修改以下配置

# ================================
# 应用基础配置
# ================================
SPRING_PROFILES_ACTIVE=prod
SERVER_PORT=8081
APP_NAME=SeatMaster
APP_VERSION=1.0.0
APP_ENVIRONMENT=production

# ================================
# 数据库配置
# ================================
DB_HOST=localhost
DB_PORT=3306
DB_NAME=seat_reservation
DB_USERNAME=root
DB_PASSWORD=root5869087
DB_SSL=true

# 数据库连接池配置
DB_MAX_POOL_SIZE=50
DB_MIN_IDLE=20
DB_CONNECTION_TIMEOUT=30000
DB_IDLE_TIMEOUT=600000
DB_MAX_LIFETIME=1800000
DB_LEAK_DETECTION=60000

# ================================
# 服务器配置
# ================================
SERVER_MAX_THREADS=200
SERVER_MIN_THREADS=10
SERVER_MAX_CONNECTIONS=8192
SERVER_ACCEPT_COUNT=100
SERVER_CONNECTION_TIMEOUT=20000

# ================================
# 安全配置
# ================================
JWT_SECRET=seatmaster-production-secret-key-2024-very-long-and-secure-please-change-this
JWT_EXPIRATION=86400000

# CORS配置
CORS_ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# ================================
# 日志配置
# ================================
LOG_PATH=/var/log/seatmaster
LOG_MAX_SIZE=100MB
LOG_MAX_HISTORY=30
LOG_TOTAL_SIZE=10GB
LOG_LEVEL=INFO

# ================================
# 缓存配置 (Redis)
# ================================
CACHE_TYPE=redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0
REDIS_TIMEOUT=5000
REDIS_MAX_ACTIVE=20
REDIS_MAX_IDLE=10
REDIS_MIN_IDLE=5
REDIS_MAX_WAIT=5000

# ================================
# 异步任务配置
# ================================
ASYNC_CORE_POOL_SIZE=10
ASYNC_MAX_POOL_SIZE=50
ASYNC_QUEUE_CAPACITY=1000
ASYNC_KEEP_ALIVE=60

# ================================
# 分布式任务配置
# ================================
TASK_HEARTBEAT_INTERVAL=30000
TASK_TIMEOUT=300000
TASK_RETRY_COUNT=3
TASK_BATCH_SIZE=100

# ================================
# JVM 配置
# ================================
JAVA_OPTS=-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+UseStringDeduplication -XX:+OptimizeStringConcat

# ================================
# 监控配置
# ================================
MANAGEMENT_ENDPOINTS_ENABLED=true
METRICS_EXPORT_ENABLED=true
HEALTH_CHECK_ENABLED=true

# ================================
# 文件上传配置
# ================================
FILE_UPLOAD_MAX_SIZE=10MB
FILE_UPLOAD_MAX_REQUEST_SIZE=50MB
FILE_UPLOAD_PATH=/var/seatmaster/uploads

# ================================
# 邮件配置 (可选)
# ================================
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-mail-password
MAIL_FROM=<EMAIL>

# ================================
# 备份配置
# ================================
BACKUP_ENABLED=true
BACKUP_PATH=/var/backups/seatmaster
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE=0 2 * * *

# ================================
# SSL/TLS 配置
# ================================
SSL_ENABLED=true
SSL_CERT_PATH=/etc/ssl/certs/seatmaster.crt
SSL_KEY_PATH=/etc/ssl/private/seatmaster.key

# ================================
# 外部服务配置
# ================================
# 学习通API配置
XUEXITONG_API_ENABLED=true
XUEXITONG_API_TIMEOUT=30000
XUEXITONG_API_RETRY_COUNT=3

# ================================
# 性能监控配置
# ================================
PERFORMANCE_MONITORING_ENABLED=true
SLOW_QUERY_THRESHOLD=1000
REQUEST_TIMEOUT=30000

# ================================
# 集群配置 (如果使用集群部署)
# ================================
CLUSTER_ENABLED=false
CLUSTER_NODE_ID=node-1
CLUSTER_NODES=node-1:8081,node-2:8082

# ================================
# 开发调试配置 (生产环境应设为false)
# ================================
DEBUG_ENABLED=false
SQL_DEBUG_ENABLED=false
ACTUATOR_SECURITY_ENABLED=true
