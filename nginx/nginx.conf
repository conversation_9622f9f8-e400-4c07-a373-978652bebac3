# SeatMaster Nginx 主配置文件 - 生产环境

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# 优化worker进程
worker_rlimit_nofile 65535;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
    accept_mutex off;
}

http {
    # ================================
    # 基础配置
    # ================================
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 字符集
    charset utf-8;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '$request_time $upstream_response_time';
    
    log_format detailed '$remote_addr - $remote_user [$time_local] "$request" '
                       '$status $body_bytes_sent "$http_referer" '
                       '"$http_user_agent" "$http_x_forwarded_for" '
                       'rt=$request_time uct="$upstream_connect_time" '
                       'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    access_log /var/log/nginx/access.log main;
    
    # ================================
    # 性能优化
    # ================================
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 1000;
    types_hash_max_size 2048;
    server_names_hash_bucket_size 128;
    client_header_buffer_size 32k;
    large_client_header_buffers 4 32k;
    client_max_body_size 50m;
    client_body_buffer_size 128k;
    client_header_timeout 30s;
    client_body_timeout 30s;
    send_timeout 30s;
    
    # 连接池优化
    upstream_keepalive_connections 32;
    upstream_keepalive_requests 1000;
    upstream_keepalive_timeout 60s;
    
    # ================================
    # Gzip压缩配置
    # ================================
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        text/x-component
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        application/rdf+xml
        application/rss+xml
        application/x-font-ttf
        application/x-web-app-manifest+json
        application/xhtml+xml
        application/xml
        font/opentype
        image/svg+xml
        image/x-icon;
    
    # ================================
    # Brotli压缩配置（如果支持）
    # ================================
    # brotli on;
    # brotli_comp_level 6;
    # brotli_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # ================================
    # 安全配置
    # ================================
    # 隐藏nginx版本
    server_tokens off;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header X-Download-Options "noopen" always;
    add_header X-Permitted-Cross-Domain-Policies "none" always;
    
    # ================================
    # 限流配置
    # ================================
    # 定义限流区域
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=general:10m rate=30r/s;
    
    # 连接数限制
    limit_conn_zone $binary_remote_addr zone=perip:10m;
    limit_conn_zone $server_name zone=perserver:10m;
    
    # ================================
    # 缓存配置
    # ================================
    # 代理缓存路径
    proxy_cache_path /var/cache/nginx/proxy levels=1:2 keys_zone=proxy_cache:10m max_size=1g inactive=60m use_temp_path=off;
    
    # FastCGI缓存路径
    fastcgi_cache_path /var/cache/nginx/fastcgi levels=1:2 keys_zone=fastcgi_cache:10m max_size=1g inactive=60m use_temp_path=off;
    
    # ================================
    # 上游服务器配置
    # ================================
    upstream backend_servers {
        least_conn;
        server backend:8081 max_fails=3 fail_timeout=30s weight=1;
        # 如果有多个后端实例，可以添加更多服务器
        # server backend2:8081 max_fails=3 fail_timeout=30s weight=1;
        
        keepalive 32;
        keepalive_requests 1000;
        keepalive_timeout 60s;
    }
    
    upstream frontend_servers {
        server frontend:80 max_fails=3 fail_timeout=30s;
        # 如果有多个前端实例
        # server frontend2:80 max_fails=3 fail_timeout=30s;
        
        keepalive 16;
    }
    
    # ================================
    # 地理位置配置（可选）
    # ================================
    # geoip_country /usr/share/GeoIP/GeoIP.dat;
    # geoip_city /usr/share/GeoIP/GeoLiteCity.dat;
    
    # ================================
    # 包含站点配置
    # ================================
    include /etc/nginx/conf.d/*.conf;
}
