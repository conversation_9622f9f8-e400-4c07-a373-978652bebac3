<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>formatDateTime 性能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .performance-good {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .performance-warning {
            border-left-color: #ffc107;
            background-color: #fff3cd;
        }
        .performance-bad {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .stats-table th, .stats-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .stats-table th {
            background-color: #f2f2f2;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        code {
            background-color: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <h1>formatDateTime 性能测试</h1>
    
    <div class="test-container">
        <h2>缓存优化版本测试</h2>
        <button onclick="runPerformanceTest()">运行性能测试</button>
        <button onclick="runCacheTest()">测试缓存效果</button>
        <button onclick="runStressTest()">压力测试</button>
        <button onclick="clearResults()">清空结果</button>
        
        <div class="progress" id="progress-container" style="display: none;">
            <div class="progress-bar" id="progress-bar"></div>
        </div>
        
        <div id="test-results"></div>
    </div>

    <div class="test-container">
        <h2>格式化结果验证</h2>
        <div id="format-validation"></div>
    </div>

    <script>
        // 复制优化后的缓存系统
        class DateTimeCache {
            constructor(maxSize = 1000) {
                this.cache = new Map()
                this.maxSize = maxSize
                this.accessOrder = new Map()
                this.accessCounter = 0
            }

            get(key) {
                if (this.cache.has(key)) {
                    this.accessOrder.set(key, ++this.accessCounter)
                    return this.cache.get(key)
                }
                return null
            }

            set(key, value) {
                if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
                    this._evictLRU()
                }
                this.cache.set(key, value)
                this.accessOrder.set(key, ++this.accessCounter)
            }

            _evictLRU() {
                let lruKey = null
                let lruAccess = Infinity
                
                for (const [key, accessTime] of this.accessOrder) {
                    if (accessTime < lruAccess) {
                        lruAccess = accessTime
                        lruKey = key
                    }
                }
                
                if (lruKey !== null) {
                    this.cache.delete(lruKey)
                    this.accessOrder.delete(lruKey)
                }
            }

            clear() {
                this.cache.clear()
                this.accessOrder.clear()
                this.accessCounter = 0
            }

            size() {
                return this.cache.size
            }
        }

        const dateTimeCache = new DateTimeCache(1000)
        const performanceStats = {
            totalCalls: 0,
            cacheHits: 0,
            cacheMisses: 0,
            formatTime: 0,
            
            reset() {
                this.totalCalls = 0
                this.cacheHits = 0
                this.cacheMisses = 0
                this.formatTime = 0
            },
            
            getHitRate() {
                return this.totalCalls > 0 ? (this.cacheHits / this.totalCalls * 100).toFixed(2) : 0
            },
            
            getStats() {
                return {
                    totalCalls: this.totalCalls,
                    cacheHits: this.cacheHits,
                    cacheMisses: this.cacheMisses,
                    hitRate: this.getHitRate() + '%',
                    avgFormatTime: this.totalCalls > 0 ? (this.formatTime / this.totalCalls).toFixed(3) + 'ms' : '0ms',
                    cacheSize: dateTimeCache.size()
                }
            }
        }

        const dateFormatter = new Intl.DateTimeFormat('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            fractionalSecondDigits: 3,
            hour12: false
        })

        // 优化后的 formatDateTime 函数
        const formatDateTime = (dateTime) => {
            const startTime = performance.now()
            performanceStats.totalCalls++
            
            try {
                if (!dateTime) return '未知'
                
                const inputKey = typeof dateTime === 'string' ? dateTime : dateTime.toString()
                
                const cached = dateTimeCache.get(inputKey)
                if (cached !== null) {
                    performanceStats.cacheHits++
                    performanceStats.formatTime += performance.now() - startTime
                    return cached
                }
                
                performanceStats.cacheMisses++
                
                const date = new Date(dateTime)
                if (isNaN(date.getTime())) {
                    const errorResult = '无效时间'
                    dateTimeCache.set(inputKey, errorResult)
                    performanceStats.formatTime += performance.now() - startTime
                    return errorResult
                }

                let formatted = dateFormatter.format(date)
                formatted = formatted
                    .replace(/年|月/g, '/')
                    .replace(/日/g, '')
                    .replace(/\s+/g, ' ')
                    .trim()
                
                if (formatted.includes('-')) {
                    formatted = formatted.replace(/-/g, '/')
                }
                
                dateTimeCache.set(inputKey, formatted)
                performanceStats.formatTime += performance.now() - startTime
                return formatted
            } catch (error) {
                console.warn('时间格式化失败:', error, 'Input:', dateTime)
                const errorResult = '格式错误'
                if (dateTime) {
                    const inputKey = typeof dateTime === 'string' ? dateTime : dateTime.toString()
                    dateTimeCache.set(inputKey, errorResult)
                }
                performanceStats.formatTime += performance.now() - startTime
                return errorResult
            }
        }

        // 原始版本（用于对比）
        const formatDateTimeOriginal = (dateTime) => {
            try {
                if (!dateTime) return '未知'
                const date = new Date(dateTime)
                if (isNaN(date.getTime())) return '无效时间'

                const year = date.getFullYear()
                const month = String(date.getMonth() + 1).padStart(2, '0')
                const day = String(date.getDate()).padStart(2, '0')
                const hours = String(date.getHours()).padStart(2, '0')
                const minutes = String(date.getMinutes()).padStart(2, '0')
                const seconds = String(date.getSeconds()).padStart(2, '0')
                const milliseconds = String(date.getMilliseconds()).padStart(3, '0')

                return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}.${milliseconds}`
            } catch (error) {
                console.warn('时间格式化失败:', error)
                return '格式错误'
            }
        }

        // 测试函数
        function generateTestData(count) {
            const testData = []
            const baseTime = new Date('2025-07-25T21:48:28.123Z').getTime()
            
            for (let i = 0; i < count; i++) {
                // 生成一些重复的时间戳来测试缓存效果
                const offset = Math.floor(i / 5) * 1000 + (i % 5) * 100
                testData.push(new Date(baseTime + offset))
            }
            
            return testData
        }

        function updateProgress(current, total) {
            const progressContainer = document.getElementById('progress-container')
            const progressBar = document.getElementById('progress-bar')
            
            if (total > 0) {
                progressContainer.style.display = 'block'
                const percentage = (current / total) * 100
                progressBar.style.width = percentage + '%'
            } else {
                progressContainer.style.display = 'none'
            }
        }

        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('test-results')
            const resultDiv = document.createElement('div')
            resultDiv.className = `test-result performance-${type}`
            resultDiv.innerHTML = `<h3>${title}</h3>${content}`
            resultsDiv.appendChild(resultDiv)
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = ''
            document.getElementById('format-validation').innerHTML = ''
            dateTimeCache.clear()
            performanceStats.reset()
        }

        async function runPerformanceTest() {
            const testSizes = [100, 500, 1000, 2000]
            
            for (const size of testSizes) {
                updateProgress(0, size)
                
                // 测试优化版本
                dateTimeCache.clear()
                performanceStats.reset()
                
                const testData = generateTestData(size)
                const startTime = performance.now()
                
                for (let i = 0; i < testData.length; i++) {
                    formatDateTime(testData[i])
                    if (i % 50 === 0) {
                        updateProgress(i, size)
                        await new Promise(resolve => setTimeout(resolve, 1))
                    }
                }
                
                const optimizedTime = performance.now() - startTime
                const stats = performanceStats.getStats()
                
                // 测试原始版本
                const originalStartTime = performance.now()
                for (const data of testData) {
                    formatDateTimeOriginal(data)
                }
                const originalTime = performance.now() - originalStartTime
                
                const improvement = ((originalTime - optimizedTime) / originalTime * 100).toFixed(2)
                const type = improvement > 50 ? 'good' : improvement > 20 ? 'warning' : 'bad'
                
                addResult(
                    `性能测试 - ${size} 条数据`,
                    `
                    <table class="stats-table">
                        <tr><th>指标</th><th>优化版本</th><th>原始版本</th><th>改进</th></tr>
                        <tr><td>执行时间</td><td>${optimizedTime.toFixed(2)}ms</td><td>${originalTime.toFixed(2)}ms</td><td>${improvement}%</td></tr>
                        <tr><td>缓存命中率</td><td>${stats.hitRate}</td><td>N/A</td><td>-</td></tr>
                        <tr><td>缓存大小</td><td>${stats.cacheSize}</td><td>N/A</td><td>-</td></tr>
                        <tr><td>平均格式化时间</td><td>${stats.avgFormatTime}</td><td>N/A</td><td>-</td></tr>
                    </table>
                    `,
                    type
                )
            }
            
            updateProgress(0, 0)
        }

        function runCacheTest() {
            dateTimeCache.clear()
            performanceStats.reset()
            
            const testTime = '2025-07-25T21:48:28.123Z'
            const iterations = 1000
            
            const startTime = performance.now()
            for (let i = 0; i < iterations; i++) {
                formatDateTime(testTime)
            }
            const endTime = performance.now()
            
            const stats = performanceStats.getStats()
            
            addResult(
                '缓存效果测试',
                `
                <p>重复格式化同一时间戳 ${iterations} 次</p>
                <ul>
                    <li>总耗时: ${(endTime - startTime).toFixed(2)}ms</li>
                    <li>缓存命中率: ${stats.hitRate}</li>
                    <li>缓存命中: ${stats.cacheHits} 次</li>
                    <li>缓存未命中: ${stats.cacheMisses} 次</li>
                    <li>平均每次: ${((endTime - startTime) / iterations).toFixed(4)}ms</li>
                </ul>
                `,
                stats.cacheHits > 990 ? 'good' : 'warning'
            )
        }

        async function runStressTest() {
            const testSize = 10000
            updateProgress(0, testSize)
            
            dateTimeCache.clear()
            performanceStats.reset()
            
            const testData = generateTestData(testSize)
            const startTime = performance.now()
            
            for (let i = 0; i < testData.length; i++) {
                formatDateTime(testData[i])
                if (i % 100 === 0) {
                    updateProgress(i, testSize)
                    await new Promise(resolve => setTimeout(resolve, 1))
                }
            }
            
            const endTime = performance.now()
            const stats = performanceStats.getStats()
            
            const avgTime = (endTime - startTime) / testSize
            const type = avgTime < 0.1 ? 'good' : avgTime < 0.5 ? 'warning' : 'bad'
            
            addResult(
                `压力测试 - ${testSize} 条数据`,
                `
                <ul>
                    <li>总耗时: ${(endTime - startTime).toFixed(2)}ms</li>
                    <li>平均每次: ${avgTime.toFixed(4)}ms</li>
                    <li>缓存命中率: ${stats.hitRate}</li>
                    <li>缓存大小: ${stats.cacheSize}</li>
                    <li>内存使用: 约 ${(stats.cacheSize * 50 / 1024).toFixed(2)}KB</li>
                </ul>
                `,
                type
            )
            
            updateProgress(0, 0)
        }

        // 验证格式化结果
        function validateFormat() {
            const testCases = [
                new Date('2025-07-25T21:48:28.123Z'),
                new Date('2025-07-25T21:48:28.456Z'),
                new Date('2025-07-25T21:48:28.789Z'),
                new Date(),
                null,
                'invalid-date'
            ]

            const validationDiv = document.getElementById('format-validation')
            validationDiv.innerHTML = '<h3>格式化结果验证</h3>'

            testCases.forEach((testCase, index) => {
                const optimized = formatDateTime(testCase)
                const original = formatDateTimeOriginal(testCase)
                
                const div = document.createElement('div')
                div.className = 'test-result'
                div.innerHTML = `
                    <strong>测试 ${index + 1}:</strong><br>
                    输入: <code>${testCase}</code><br>
                    优化版本: <code>${optimized}</code><br>
                    原始版本: <code>${original}</code><br>
                    匹配: ${optimized === original ? '✅' : '❌'}
                `
                validationDiv.appendChild(div)
            })
        }

        // 页面加载时运行验证
        document.addEventListener('DOMContentLoaded', validateFormat)
    </script>
</body>
</html>
