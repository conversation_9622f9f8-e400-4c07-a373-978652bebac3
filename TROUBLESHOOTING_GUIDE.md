# SeatMaster 公告模块 404 错误排查指南

## 问题描述
前端显示 "获取公告列表失败: AxiosError" 和 404 错误，表明无法访问公告API端点。

## 排查步骤

### 1. 检查后端服务状态

**检查端口8081是否被占用：**
```bash
netstat -ano | findstr :8081
```

**检查Java进程：**
```bash
jps
```

### 2. 启动后端服务

**方法1：使用Maven命令**
```bash
cd backend
mvn clean compile
mvn spring-boot:run
```

**方法2：使用提供的启动脚本**
```bash
cd backend
start_server.bat
```

### 3. 验证服务启动

**检查健康端点：**
```bash
curl http://localhost:8081/api/health
```

**或者在浏览器中访问：**
```
http://localhost:8081/api/health
```

### 4. 测试公告API端点

**运行自动化测试：**
```bash
node debug_backend_startup.js
```

### 5. 常见问题和解决方案

#### 问题1：MyBatis XML解析错误
**症状：** 启动时出现 "元素内容必须由格式正确的字符数据或标记组成"
**解决方案：** 检查 AnnouncementMapper.java 中的SQL注解语法

#### 问题2：数据库连接错误
**症状：** 无法连接到MySQL数据库
**解决方案：**
1. 确保MySQL服务正在运行
2. 检查数据库配置 (application.properties)
3. 验证数据库表是否存在：
```sql
USE seat_reservation;
SHOW TABLES LIKE '%announcement%';
```

#### 问题3：端口冲突
**症状：** 端口8081已被占用
**解决方案：**
1. 找到占用端口的进程：`netstat -ano | findstr :8081`
2. 终止进程：`taskkill /PID <PID> /F`
3. 或者修改配置使用其他端口

#### 问题4：Maven编译错误
**症状：** 编译失败或依赖问题
**解决方案：**
```bash
mvn clean
mvn compile
```

### 6. 手动验证API端点

如果后端服务成功启动，可以使用以下方法测试API：

**1. 管理员登录获取Token：**
```bash
curl -X POST http://localhost:8081/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

**2. 测试公告列表API：**
```bash
curl -X GET http://localhost:8081/api/admin/announcements \
  -H "Authorization: Bearer <TOKEN>"
```

**3. 测试用户公告API：**
```bash
curl -X GET http://localhost:8081/api/user/announcements \
  -H "Authorization: Bearer <TOKEN>"
```

### 7. 前端配置检查

**检查API基础URL配置：**
- 文件：`frontend/src/utils/api.js`
- 确保baseURL指向正确的后端地址：`http://localhost:8081`

**检查公告API调用：**
- 文件：`frontend/src/api/announcement.js`
- 验证API路径是否正确

### 8. 数据库初始化

如果数据库表不存在，运行以下命令：
```bash
mysql -u root -proot seat_reservation < database/announcements_schema.sql
```

### 9. 完整的重启流程

**1. 停止所有相关服务**
```bash
# 停止后端服务
taskkill /F /IM java.exe

# 停止前端服务（如果在运行）
# Ctrl+C 在前端终端
```

**2. 重新启动后端**
```bash
cd backend
mvn clean
mvn spring-boot:run
```

**3. 等待服务完全启动（看到类似以下日志）：**
```
Started SeatReservationApplication in X.XXX seconds
Tomcat started on port(s): 8081 (http)
```

**4. 重新启动前端**
```bash
cd frontend
npm run dev
```

### 10. 联系支持

如果以上步骤都无法解决问题，请提供以下信息：

1. 后端启动日志（完整的错误信息）
2. 前端浏览器控制台错误
3. 数据库连接状态
4. 系统环境信息（Java版本、Maven版本等）

## 快速检查清单

- [ ] MySQL服务正在运行
- [ ] 数据库表已创建
- [ ] 端口8081未被占用
- [ ] 后端服务成功启动
- [ ] 健康检查端点可访问
- [ ] 管理员可以成功登录
- [ ] 公告API端点返回正确响应
- [ ] 前端API配置正确

## 自动化测试脚本

运行以下脚本进行完整的功能测试：
```bash
node tests/test_announcement_module.js
```

这个脚本会自动测试所有公告相关的API端点并报告结果。
