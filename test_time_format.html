<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间格式测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        code {
            background-color: #e9ecef;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <h1>seatMaster 时间格式测试</h1>
    
    <div class="test-container">
        <h2>前端时间格式化函数测试</h2>
        <div id="frontend-test"></div>
    </div>

    <div class="test-container">
        <h2>API 响应时间格式测试</h2>
        <div id="api-test"></div>
    </div>

    <script>
        // 复制前端的 formatDateTime 函数
        const formatDateTime = (dateTime) => {
            try {
                if (!dateTime) return '未知'
                const date = new Date(dateTime)
                if (isNaN(date.getTime())) return '无效时间'
                
                // 手动格式化以包含毫秒：YYYY/MM/DD HH:mm:ss.SSS
                const year = date.getFullYear()
                const month = String(date.getMonth() + 1).padStart(2, '0')
                const day = String(date.getDate()).padStart(2, '0')
                const hours = String(date.getHours()).padStart(2, '0')
                const minutes = String(date.getMinutes()).padStart(2, '0')
                const seconds = String(date.getSeconds()).padStart(2, '0')
                const milliseconds = String(date.getMilliseconds()).padStart(3, '0')
                
                return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}.${milliseconds}`
            } catch (error) {
                console.warn('时间格式化失败:', error)
                return '格式错误'
            }
        }

        // 测试前端格式化函数
        function testFrontendFormat() {
            const testCases = [
                new Date('2025-07-25T21:48:28.123Z'),
                new Date('2025-07-25T21:48:28.456Z'),
                new Date('2025-07-25T21:48:28.789Z'),
                new Date(),
                null,
                'invalid-date'
            ];

            const container = document.getElementById('frontend-test');
            container.innerHTML = '';

            testCases.forEach((testCase, index) => {
                const result = formatDateTime(testCase);
                const div = document.createElement('div');
                div.className = 'test-result';
                
                if (result.includes('.') && result !== '未知' && result !== '格式错误') {
                    div.classList.add('success');
                } else if (result === '未知' || result === '格式错误') {
                    div.classList.add('success'); // 这些是预期的结果
                } else {
                    div.classList.add('error');
                }
                
                div.innerHTML = `
                    <strong>测试 ${index + 1}:</strong><br>
                    输入: <code>${testCase}</code><br>
                    输出: <code>${result}</code>
                `;
                container.appendChild(div);
            });
        }

        // 测试 API 响应
        async function testApiResponse() {
            const container = document.getElementById('api-test');
            container.innerHTML = '<div class="test-result">正在测试 API 响应...</div>';

            try {
                // 这里应该调用实际的 API，但由于需要认证，我们模拟一个响应
                const mockApiResponse = {
                    apiResponseTime: "2025-07-25 21:48:28.123",
                    createdAt: "2025-07-25 21:48:28"
                };

                const div = document.createElement('div');
                div.className = 'test-result success';
                div.innerHTML = `
                    <strong>模拟 API 响应:</strong><br>
                    apiResponseTime: <code>${mockApiResponse.apiResponseTime}</code><br>
                    createdAt: <code>${mockApiResponse.createdAt}</code><br>
                    <br>
                    <strong>前端格式化结果:</strong><br>
                    apiResponseTime: <code>${formatDateTime(mockApiResponse.apiResponseTime)}</code><br>
                    createdAt: <code>${formatDateTime(mockApiResponse.createdAt)}</code>
                `;
                container.innerHTML = '';
                container.appendChild(div);

            } catch (error) {
                container.innerHTML = `<div class="test-result error">API 测试失败: ${error.message}</div>`;
            }
        }

        // 运行测试
        document.addEventListener('DOMContentLoaded', function() {
            testFrontendFormat();
            testApiResponse();
        });
    </script>
</body>
</html>
