# SeatMaster 分布式座位预约系统

<div align="center">

![SeatMaster Logo](screenshot/screenshot-2025-07-01T10-28-25-397Z.png)

**基于Spring Boot + Vue 3的现代化分布式座位预约管理系统**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Java](https://img.shields.io/badge/Java-11+-blue.svg)](https://www.oracle.com/java/)
[![Vue](https://img.shields.io/badge/Vue-3.3+-green.svg)](https://vuejs.org/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.7+-brightgreen.svg)](https://spring.io/projects/spring-boot)

</div>

## 📋 项目概述

SeatMaster是一个功能完整的分布式座位预约系统，采用主服务器+多Worker服务器架构，支持学习通自动预约、智能任务调度、实时监控等高级功能。系统设计简洁高效，易于部署和扩展。

### 🎯 核心特性

- 🏗️ **分布式架构** - 主服务器 + 多Worker服务器，支持水平扩展
- 🎓 **学习通集成** - 完整的学习通自动预约功能，支持多学校
- 🤖 **智能调度** - 多种任务分配策略，自动负载均衡
- 📊 **实时监控** - 服务器状态监控、任务执行统计、性能分析
- 🎨 **现代化UI** - Vue 3 + Element Plus，响应式设计
- ⚡ **高性能** - 基于Jetty的轻量级Worker，毫秒级任务调度
- 🔒 **安全可靠** - JWT认证、角色权限管理、数据加密

## 🏗️ 系统架构

```
                    ┌─────────────────┐
                    │   前端 Vue.js   │
                    │  (用户界面)     │
                    └─────────┬───────┘
                              │
                    ┌─────────┴─────────┐
                    │   主服务器 8081   │
                    │  Spring Boot      │
                    │  - 任务调度       │
                    │  - 用户管理       │
                    │  - 系统监控       │
                    └─────────┬─────────┘
                              │
                    ┌─────────┴─────────┐
                    │   MySQL 数据库    │
                    └─────────┬─────────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
┌───────┴───────┐    ┌───────┴───────┐    ┌───────┴───────┐
│ Worker 8085    │    │ Worker 8086    │    │ Worker 8087    │
│ Jetty Server   │    │ Jetty Server   │    │ Jetty Server   │
│ - 任务执行     │    │ - 任务执行     │    │ - 任务执行     │
│ - 学习通API    │    │ - 学习通API    │    │ - 学习通API    │
│ - 状态上报     │    │ - 状态上报     │    │ - 状态上报     │
└───────────────┘    └───────────────┘    └───────────────┘
```

## 🛠️ 技术栈

### 主服务器 (Spring Boot)
- **框架**: Spring Boot 2.7.14
- **安全**: Spring Security + JWT
- **数据库**: MySQL 8.0 + MyBatis Plus
- **调度**: Spring Scheduling
- **监控**: 自定义健康检查和统计

### Worker服务器 (Jetty)
- **服务器**: Eclipse Jetty 11.0.15
- **任务调度**: 自研SimpleTaskScheduler
- **HTTP客户端**: 内置HTTP客户端
- **配置**: 环境变量 + 命令行参数

### 前端 (Vue 3)
- **框架**: Vue 3.3.4 + Composition API
- **UI库**: Element Plus 2.3.8
- **状态管理**: Pinia 2.1.6
- **路由**: Vue Router 4.2.4
- **HTTP**: Axios 1.4.0

### 数据库设计
- **用户系统**: 用户表、角色权限
- **预约系统**: 预约表、学校房间表
- **分布式**: Worker服务器表、任务执行日志
- **监控**: 执行历史、性能统计

## ✨ 功能特性

### 🔐 用户管理
- ✅ 用户注册/登录/登出
- ✅ JWT Token认证
- ✅ 角色权限管理(用户/管理员)
- ✅ 个人资料管理
- ✅ 密码安全策略

### 📅 预约管理
- ✅ 智能座位预约
- ✅ 预约冲突检测
- ✅ 自动时间分割(超时预约)
- ✅ 预约历史查询
- ✅ 批量预约操作

### 🏫 学校管理
- ✅ 多学校支持(15+学校配置)
- ✅ 房间信息管理
- ✅ 座位容量配置
- ✅ 开放时间设置

### 🤖 学习通集成
- ✅ 自动登录认证
- ✅ 房间座位查询
- ✅ 智能预约提交
- ✅ 会话管理缓存
- ✅ 错误重试机制

### 📊 分布式任务管理
- ✅ 智能任务分配
- ✅ 多种分配策略(轮询/负载均衡/时间优化)
- ✅ 实时任务监控
- ✅ 自动故障转移
- ✅ 任务执行统计

### 🖥️ Worker服务器管理
- ✅ 自动服务器注册
- ✅ 心跳健康检查
- ✅ 负载状态监控
- ✅ 动态扩容缩容
- ✅ 服务器性能统计

### 📈 监控与统计
- ✅ 实时系统状态
- ✅ 任务执行历史
- ✅ 性能指标分析
- ✅ 错误日志追踪
- ✅ 可视化图表展示

## 🚀 快速开始

### 📋 环境要求

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| JDK | 11+ | 主服务器和Worker服务器 |
| Node.js | 16+ | 前端开发和构建 |
| MySQL | 8.0+ | 数据存储 |
| Maven | 3.6+ | Java项目构建 |

### 🗄️ 数据库配置

1. **创建数据库**
```sql
CREATE DATABASE seat_reservation CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. **执行初始化脚本**
```bash
# 基础表结构
mysql -u root -p seat_reservation < database/init.sql

# 分布式功能表
mysql -u root -p seat_reservation < database/add_distributed_fields.sql

# 任务执行日志表
mysql -u root -p seat_reservation < database/create_task_execution_logs.sql

# 学校和房间数据
mysql -u root -p seat_reservation < database/add_schools_and_rooms_simple.sql
```

### 🖥️ 主服务器启动

```bash
# 进入后端目录
cd backend

# 编译项目
mvn clean package -DskipTests

# 启动主服务器
java -jar target/seat-reservation-backend-1.0.0.jar

# 或使用Maven直接运行
mvn spring-boot:run
```

**服务地址**: http://localhost:8081

### ⚡ Worker服务器启动

```bash
# 进入Worker目录
cd worker-server-simple

# 编译项目
mvn clean package -DskipTests

# 启动Worker服务器(可启动多个)
java -jar target/worker-server-simple-1.0.0.jar --port=8085 --worker-id=worker-8085
java -jar target/worker-server-simple-1.0.0.jar --port=8086 --worker-id=worker-8086
java -jar target/worker-server-simple-1.0.0.jar --port=8087 --worker-id=worker-8087
```

**Worker地址**:
- Worker 1: http://localhost:8085
- Worker 2: http://localhost:8086
- Worker 3: http://localhost:8087

### 🎨 前端启动

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run serve

# 生产构建
npm run build
```

**前端地址**: http://localhost:3000

### 🔧 配置说明

#### 主服务器配置 (`backend/src/main/resources/application.yml`)
```yaml
server:
  port: 8081

spring:
  datasource:
    url: ********************************************
    username: root
    password: your_password

seatmaster:
  auto-assignment:
    enabled: true
    strategy: LOAD_BALANCED
```

#### Worker服务器配置
```bash
# 环境变量配置
export MASTER_URL=http://localhost:8081
export WORKER_ID=worker-8085
export WORKER_PORT=8085
export MAX_CONCURRENT_TASKS=10

# 或使用命令行参数
java -jar worker-server-simple-1.0.0.jar \
  --port=8085 \
  --worker-id=worker-8085 \
  --master-url=http://localhost:8081 \
  --max-concurrent-tasks=10
```

## 🔑 默认账号

| 角色 | 用户名 | 密码 | 权限 |
|------|--------|------|------|
| 管理员 | admin | admin123 | 全部功能 |
| 测试用户 | user | user123 | 基础功能 |

## 📡 API接口文档

### 🔐 认证接口
```http
POST /api/auth/login          # 用户登录
POST /api/auth/register       # 用户注册
POST /api/auth/logout         # 用户登出
GET  /api/auth/profile        # 获取用户信息
```

### 👥 用户管理
```http
GET    /api/admin/users       # 获取用户列表
POST   /api/admin/users       # 创建用户
PUT    /api/admin/users/{id}  # 更新用户
DELETE /api/admin/users/{id}  # 删除用户
```

### 📅 预约管理
```http
GET    /api/reservations      # 获取预约列表
POST   /api/reservations      # 创建预约
PUT    /api/reservations/{id} # 更新预约
DELETE /api/reservations/{id} # 删除预约
GET    /api/reservations/history # 预约历史
```

### 🏫 学校房间管理
```http
GET    /api/admin/schools     # 获取学校列表
GET    /api/admin/rooms       # 获取房间列表
POST   /api/admin/rooms       # 创建房间
PUT    /api/admin/rooms/{id}  # 更新房间
DELETE /api/admin/rooms/{id}  # 删除房间
```

### 🤖 分布式任务管理
```http
GET    /api/admin/distributed-tasks    # 获取任务列表
POST   /api/admin/distributed-tasks/assign # 分配任务
POST   /api/admin/distributed-tasks/execute/{id} # 立即执行
GET    /api/admin/distributed-tasks/statistics # 任务统计
```

### 🖥️ Worker服务器管理
```http
GET    /api/admin/workers     # 获取Worker列表
POST   /api/admin/workers     # 注册Worker
PUT    /api/admin/workers/{id} # 更新Worker
DELETE /api/admin/workers/{id} # 删除Worker
GET    /api/admin/workers/{id}/health # 健康检查
```

### 📊 监控统计
```http
GET    /api/admin/statistics/overview # 系统概览
GET    /api/admin/statistics/tasks    # 任务统计
GET    /api/admin/statistics/workers  # Worker统计
GET    /api/execution-logs            # 执行日志
```

### ⚡ Worker服务器API
```http
GET    /api/health            # 健康检查
POST   /api/tasks/execute/{id} # 执行任务
GET    /api/tasks/scheduled    # 获取调度任务
POST   /api/tasks/warmup       # 预热任务
```

## 📁 项目结构

```
seatMaster/
├── 📁 backend/                          # Spring Boot主服务器
│   ├── src/main/java/com/seatmaster/
│   │   ├── 📁 config/                   # 配置类
│   │   │   ├── SecurityConfig.java      # 安全配置
│   │   │   ├── ScheduleConfig.java      # 定时任务配置
│   │   │   └── AsyncConfig.java         # 异步配置
│   │   ├── 📁 controller/               # REST控制器
│   │   │   ├── AdminController.java     # 管理员接口
│   │   │   ├── UserController.java      # 用户接口
│   │   │   ├── AdminDistributedTaskController.java # 分布式任务
│   │   │   ├── AdminWorkerController.java # Worker管理
│   │   │   └── ExecutionLogController.java # 执行日志
│   │   ├── 📁 service/                  # 业务服务层
│   │   │   ├── DistributedTaskService.java # 分布式任务服务
│   │   │   ├── XuexitongApiService.java    # 学习通API服务
│   │   │   ├── WorkerServerService.java    # Worker管理服务
│   │   │   └── ReservationServiceImpl.java # 预约服务实现
│   │   ├── 📁 entity/                   # 数据实体
│   │   │   ├── User.java               # 用户实体
│   │   │   ├── Reservation.java        # 预约实体
│   │   │   ├── Room.java               # 房间实体
│   │   │   └── WorkerServer.java       # Worker服务器实体
│   │   ├── 📁 dto/                     # 数据传输对象
│   │   ├── 📁 mapper/                  # MyBatis映射器
│   │   ├── 📁 scheduler/               # 任务调度器
│   │   └── 📁 util/                    # 工具类
│   └── src/main/resources/
│       └── application.yml             # 主配置文件
├── 📁 worker-server-simple/            # Jetty Worker服务器
│   ├── src/main/java/com/seatmaster/worker/
│   │   ├── SimpleWorkerApplication.java # Worker启动类
│   │   ├── 📁 config/                  # Worker配置
│   │   ├── 📁 controller/              # Worker控制器
│   │   ├── 📁 service/                 # Worker服务
│   │   │   ├── TaskExecutionService.java # 任务执行服务
│   │   │   ├── ReservationExecutor.java  # 预约执行器
│   │   │   └── WorkerRegistrationService.java # 注册服务
│   │   ├── 📁 server/                  # Jetty服务器
│   │   ├── 📁 scheduler/               # 任务调度器
│   │   └── 📁 xuexitong/               # 学习通集成
│   │       └── api/SimpleXuexitongApiClient.java
│   └── pom.xml                         # Worker Maven配置
├── 📁 frontend/                        # Vue 3前端
│   ├── src/
│   │   ├── 📁 components/              # 可复用组件
│   │   │   ├── TimeInput.vue           # 时间输入组件
│   │   │   ├── TaskStatusCard.vue      # 任务状态卡片
│   │   │   ├── ServerStatusCard.vue    # 服务器状态卡片
│   │   │   └── TaskDetailDialog.vue    # 任务详情弹窗
│   │   ├── 📁 views/                   # 页面组件
│   │   │   ├── Dashboard.vue           # 仪表板
│   │   │   ├── Reservation.vue         # 预约管理
│   │   │   ├── DistributedTaskManagement.vue # 分布式任务管理
│   │   │   ├── WorkerManagement.vue    # Worker管理
│   │   │   ├── ExecutionHistory.vue    # 执行历史
│   │   │   ├── MemberManagement.vue    # 用户管理
│   │   │   └── RoomManagement.vue      # 房间管理
│   │   ├── 📁 router/                  # 路由配置
│   │   ├── 📁 stores/                  # Pinia状态管理
│   │   ├── 📁 utils/                   # 工具函数
│   │   └── 📁 api/                     # API接口
│   └── package.json                    # 前端依赖配置
├── 📁 database/                        # 数据库脚本
│   ├── init.sql                        # 基础表结构
│   ├── add_distributed_fields.sql      # 分布式字段
│   ├── create_task_execution_logs.sql  # 执行日志表
│   └── add_schools_and_rooms_simple.sql # 学校房间数据
├── 📁 schools_config/                  # 学校配置文件
│   ├── 九江学院.txt                    # 各学校配置
│   ├── 兰州城市学院.txt
│   └── ... (15+学校配置)
├── 📁 xuexitong_pro/                   # 学习通Python工具
│   ├── main.py                         # 主程序
│   ├── config.json                     # 配置文件
│   └── utils/                          # 工具模块
├── 📁 issues/                          # 开发文档
│   ├── 分布式任务管理功能开发.md
│   ├── 学习通预约核心代码实现.md
│   └── ... (30+开发文档)
├── 📁 screenshot/                      # 系统截图
├── 主服务器API文档.md                   # API文档
├── 副服务器API文档.md                   # Worker API文档
└── README.md                           # 项目说明
```

## 💻 开发说明

### 🔧 后端开发指南

#### 核心技术
- **数据访问**: MyBatis Plus + 自定义SQL
- **安全认证**: Spring Security + JWT Token
- **任务调度**: Spring Scheduling + 自研SimpleTaskScheduler
- **分布式通信**: HTTP REST API + 心跳机制
- **异常处理**: 全局异常处理器 + 统一响应格式

#### 开发规范
```java
// 统一响应格式
public class Result<T> {
    private int code;
    private String message;
    private T data;
}

// 服务层接口设计
@Service
public class DistributedTaskServiceImpl implements DistributedTaskService {
    // 业务逻辑实现
}

// 控制器设计
@RestController
@RequestMapping("/api/admin")
public class AdminDistributedTaskController {
    // API接口实现
}
```

#### 数据库设计原则
- 使用逻辑删除而非物理删除
- 添加创建时间和更新时间字段
- 合理使用索引优化查询性能
- 分布式字段设计支持水平扩展

### 🎨 前端开发指南

#### 技术栈
- **框架**: Vue 3 + Composition API
- **UI库**: Element Plus + 自定义组件
- **状态管理**: Pinia + 持久化存储
- **路由**: Vue Router 4 + 路由守卫
- **HTTP**: Axios + 请求/响应拦截器

#### 组件设计
```vue
<template>
  <!-- 使用Element Plus组件 -->
  <el-card class="task-card">
    <template #header>
      <span>{{ title }}</span>
    </template>
    <!-- 内容区域 -->
  </el-card>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useTaskStore } from '@/stores/task'

// Composition API风格
const taskStore = useTaskStore()
const loading = ref(false)

// 计算属性
const filteredTasks = computed(() => {
  return taskStore.tasks.filter(task => task.status === 'pending')
})
</script>
```

#### 状态管理
```javascript
// stores/task.js
import { defineStore } from 'pinia'

export const useTaskStore = defineStore('task', {
  state: () => ({
    tasks: [],
    currentTask: null
  }),
  actions: {
    async fetchTasks() {
      // 获取任务列表
    }
  }
})
```

### ⚡ Worker服务器开发

#### 架构设计
- **轻量级**: 基于Jetty，启动快，资源占用小
- **高并发**: 支持多线程任务执行
- **自注册**: 自动向主服务器注册和心跳
- **容错性**: 任务失败自动重试

#### 核心组件
```java
// 任务调度器
public class SimpleTaskScheduler {
    private final ScheduledExecutorService scheduler;
    private final Map<Long, ScheduledFuture<?>> scheduledTasks;

    public void scheduleTask(Long taskId, LocalDateTime executeTime) {
        // 精确时间调度
    }
}

// 学习通API客户端
public class SimpleXuexitongApiClient {
    public ReservationResult executeReservation(ReservationData data) {
        // 学习通预约逻辑
    }
}
```

## 🚀 部署说明

### 🏭 生产环境部署

#### 1. 环境准备
```bash
# 安装Java 11+
sudo apt update
sudo apt install openjdk-11-jdk

# 安装MySQL 8.0
sudo apt install mysql-server-8.0

# 安装Nginx (可选)
sudo apt install nginx
```

#### 2. 数据库配置
```sql
-- 创建生产数据库
CREATE DATABASE seat_reservation_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建专用用户
CREATE USER 'seatmaster'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON seat_reservation_prod.* TO 'seatmaster'@'localhost';
FLUSH PRIVILEGES;
```

#### 3. 应用配置
```yaml
# application-prod.yml
server:
  port: 8081

spring:
  profiles:
    active: prod
  datasource:
    url: *************************************************
    username: seatmaster
    password: ${DB_PASSWORD}

logging:
  level:
    com.seatmaster: INFO
  file:
    name: /var/log/seatmaster/application.log
```

#### 4. 构建和部署
```bash
# 构建主服务器
cd backend
mvn clean package -Pprod -DskipTests
sudo cp target/seat-reservation-backend-1.0.0.jar /opt/seatmaster/

# 构建Worker服务器
cd ../worker-server-simple
mvn clean package -DskipTests
sudo cp target/worker-server-simple-1.0.0.jar /opt/seatmaster/

# 构建前端
cd ../frontend
npm run build
sudo cp -r dist/* /var/www/seatmaster/
```

#### 5. 系统服务配置
```ini
# /etc/systemd/system/seatmaster-main.service
[Unit]
Description=SeatMaster Main Server
After=mysql.service

[Service]
Type=simple
User=seatmaster
WorkingDirectory=/opt/seatmaster
ExecStart=/usr/bin/java -jar seat-reservation-backend-1.0.0.jar --spring.profiles.active=prod
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```ini
# /etc/systemd/system/seatmaster-worker@.service
[Unit]
Description=SeatMaster Worker Server %i
After=seatmaster-main.service

[Service]
Type=simple
User=seatmaster
WorkingDirectory=/opt/seatmaster
ExecStart=/usr/bin/java -jar worker-server-simple-1.0.0.jar --port=%i --worker-id=worker-%i
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

#### 6. 启动服务
```bash
# 启动主服务器
sudo systemctl enable seatmaster-main
sudo systemctl start seatmaster-main

# 启动Worker服务器
sudo systemctl enable seatmaster-worker@8085
sudo systemctl enable seatmaster-worker@8086
sudo systemctl enable seatmaster-worker@8087
sudo systemctl start seatmaster-worker@8085
sudo systemctl start seatmaster-worker@8086
sudo systemctl start seatmaster-worker@8087
```

### 🐳 Docker部署

#### 1. 主服务器Dockerfile
```dockerfile
# backend/Dockerfile
FROM openjdk:11-jre-slim

WORKDIR /app
COPY target/seat-reservation-backend-1.0.0.jar app.jar

EXPOSE 8081
CMD ["java", "-jar", "app.jar"]
```

#### 2. Worker服务器Dockerfile
```dockerfile
# worker-server-simple/Dockerfile
FROM openjdk:11-jre-slim

WORKDIR /app
COPY target/worker-server-simple-1.0.0.jar app.jar

EXPOSE 8085
CMD ["java", "-jar", "app.jar"]
```

#### 3. Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: seat_reservation
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  seatmaster-main:
    build: ./backend
    ports:
      - "8081:8081"
    depends_on:
      - mysql
    environment:
      - SPRING_PROFILES_ACTIVE=docker

  seatmaster-worker-1:
    build: ./worker-server-simple
    ports:
      - "8085:8085"
    depends_on:
      - seatmaster-main
    command: ["java", "-jar", "app.jar", "--port=8085", "--worker-id=worker-8085"]

  seatmaster-worker-2:
    build: ./worker-server-simple
    ports:
      - "8086:8086"
    depends_on:
      - seatmaster-main
    command: ["java", "-jar", "app.jar", "--port=8086", "--worker-id=worker-8086"]

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./frontend/dist:/usr/share/nginx/html
      - ./nginx.conf:/etc/nginx/nginx.conf

volumes:
  mysql_data:
```

#### 4. 启动Docker环境
```bash
# 构建和启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f seatmaster-main
```

### 📊 监控和维护

#### 1. 日志管理
```bash
# 查看主服务器日志
sudo journalctl -u seatmaster-main -f

# 查看Worker日志
sudo journalctl -u seatmaster-worker@8085 -f

# 日志轮转配置
sudo logrotate -d /etc/logrotate.d/seatmaster
```

#### 2. 性能监控
- 使用JVM监控工具(JVisualVM, JProfiler)
- 数据库性能监控(MySQL Workbench)
- 系统资源监控(htop, iostat)

#### 3. 备份策略
```bash
# 数据库备份
mysqldump -u root -p seat_reservation_prod > backup_$(date +%Y%m%d).sql

# 应用配置备份
tar -czf config_backup_$(date +%Y%m%d).tar.gz /opt/seatmaster/
```

## 🔧 故障排除

### ❗ 常见问题

#### 1. 主服务器启动失败
**问题**: Spring Boot应用启动时报错
```bash
Error: Could not find or load main class com.seatmaster.SeatReservationApplication
```
**解决方案**:
```bash
# 检查JDK版本
java -version  # 需要JDK 11+

# 重新编译项目
cd backend
mvn clean compile

# 检查MySQL服务
sudo systemctl status mysql

# 验证数据库连接
mysql -u root -p -e "SHOW DATABASES;"
```

#### 2. Worker服务器注册失败
**问题**: Worker无法注册到主服务器
```
ERROR: Failed to register worker to master server
```
**解决方案**:
```bash
# 检查主服务器是否启动
curl http://localhost:8081/api/health

# 检查网络连接
telnet localhost 8081

# 查看Worker日志
tail -f worker-server-simple/logs/worker.log

# 手动指定主服务器地址
java -jar worker-server-simple-1.0.0.jar --master-url=http://localhost:8081
```

#### 3. 学习通API调用失败
**问题**: 学习通预约失败
```
ERROR: Xuexitong API call failed: 401 Unauthorized
```
**解决方案**:
```bash
# 检查学习通配置
cat schools_config/你的学校.txt

# 验证登录信息
# 在数据库中检查用户凭据是否正确

# 查看详细错误日志
grep "Xuexitong" worker-server-simple/logs/xuexitong/*.log
```

#### 4. 前端CORS错误
**问题**: 前端无法访问后端API
```
Access to XMLHttpRequest blocked by CORS policy
```
**解决方案**:
```javascript
// 检查vue.config.js代理配置
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:8081',
        changeOrigin: true
      }
    }
  }
}
```

#### 5. 数据库连接问题
**问题**: `Communications link failure`
**解决方案**:
```sql
-- 检查MySQL状态
SHOW PROCESSLIST;

-- 检查用户权限
SELECT User, Host FROM mysql.user WHERE User='root';

-- 重置密码（如果需要）
ALTER USER 'root'@'localhost' IDENTIFIED BY 'new_password';
FLUSH PRIVILEGES;
```

#### 6. 任务调度问题
**问题**: 任务不执行或执行延迟
**解决方案**:
```bash
# 检查系统时间
date

# 查看任务调度日志
grep "TaskScheduler" backend/logs/application.log

# 检查Worker负载
curl http://localhost:8085/api/health

# 手动触发任务
curl -X POST http://localhost:8081/api/admin/distributed-tasks/execute/123
```

### 📊 性能优化

#### 1. 数据库优化
```sql
-- 添加索引
CREATE INDEX idx_reservation_user_time ON reservations(user_id, start_time);
CREATE INDEX idx_worker_status ON worker_servers(status, last_heartbeat);

-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SET GLOBAL slow_query_log = 'ON';
```

#### 2. JVM优化
```bash
# 主服务器JVM参数
java -Xms512m -Xmx2g -XX:+UseG1GC \
     -XX:+PrintGCDetails -XX:+PrintGCTimeStamps \
     -jar seat-reservation-backend-1.0.0.jar

# Worker服务器JVM参数
java -Xms256m -Xmx1g -XX:+UseG1GC \
     -jar worker-server-simple-1.0.0.jar
```

#### 3. 网络优化
```bash
# 增加连接池大小
# application.yml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
```

### 🔍 调试技巧

#### 1. 启用调试日志
```yaml
# application.yml
logging:
  level:
    com.seatmaster: DEBUG
    org.springframework.web: DEBUG
```

#### 2. 使用JProfiler监控
```bash
# 启动时添加JProfiler参数
java -agentpath:/path/to/jprofiler/bin/linux-x64/libjprofilerti.so=port=8849 \
     -jar seat-reservation-backend-1.0.0.jar
```

#### 3. 数据库查询分析
```sql
-- 开启查询分析
SET profiling = 1;

-- 执行查询
SELECT * FROM reservations WHERE user_id = 1;

-- 查看分析结果
SHOW PROFILES;
SHOW PROFILE FOR QUERY 1;
```

### 📱 移动端适配

#### 1. 响应式设计检查
```css
/* 检查CSS媒体查询 */
@media (max-width: 768px) {
  .task-management-container {
    padding: 10px;
  }
}
```

#### 2. 触摸优化
```javascript
// 增加触摸事件支持
@touchstart="handleTouchStart"
@touchend="handleTouchEnd"
```

## 🤝 贡献指南

### 📋 贡献流程

1. **Fork项目**
   ```bash
   git clone https://github.com/your-username/seatMaster.git
   cd seatMaster
   ```

2. **创建功能分支**
   ```bash
   git checkout -b feature/new-feature
   ```

3. **开发和测试**
   ```bash
   # 后端测试
   cd backend
   mvn test

   # 前端测试
   cd frontend
   npm run test
   ```

4. **提交更改**
   ```bash
   git add .
   git commit -m "feat: add new feature"
   ```

5. **推送分支**
   ```bash
   git push origin feature/new-feature
   ```

6. **创建Pull Request**
   - 在GitHub上创建PR
   - 填写详细的描述
   - 等待代码审查

### 📝 代码规范

#### Java代码规范
```java
// 类命名：大驼峰
public class DistributedTaskService {

    // 方法命名：小驼峰
    public void autoAssignTasks() {
        // 方法实现
    }

    // 常量命名：全大写下划线
    private static final String DEFAULT_STRATEGY = "LOAD_BALANCED";
}
```

#### Vue代码规范
```vue
<template>
  <!-- 使用kebab-case -->
  <task-status-card :task-data="taskData" />
</template>

<script setup>
// 使用camelCase
const taskData = ref({})
const handleTaskUpdate = () => {
  // 处理逻辑
}
</script>
```

#### 提交信息规范
```bash
# 格式：type(scope): description
feat(worker): add automatic task retry mechanism
fix(frontend): resolve CORS issue in task management
docs(readme): update installation instructions
style(backend): format code according to checkstyle
refactor(scheduler): optimize task allocation algorithm
test(api): add unit tests for reservation service
```

### 🐛 Bug报告

使用GitHub Issues报告Bug，请包含：

1. **环境信息**
   - 操作系统版本
   - Java版本
   - Node.js版本
   - 浏览器版本

2. **重现步骤**
   - 详细的操作步骤
   - 预期结果
   - 实际结果

3. **日志信息**
   - 后端日志
   - 前端控制台错误
   - 数据库错误日志

4. **截图或视频**
   - 错误界面截图
   - 操作过程录屏

### 💡 功能建议

欢迎提出新功能建议，请包含：

1. **功能描述**
   - 详细的功能说明
   - 使用场景
   - 预期收益

2. **技术方案**
   - 实现思路
   - 技术难点
   - 兼容性考虑

3. **设计稿**
   - UI设计图
   - 交互流程图
   - API接口设计

## 📄 许可证

本项目采用 [MIT License](https://opensource.org/licenses/MIT) 开源协议。

```
MIT License

Copyright (c) 2025 SeatMaster Team

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

## 🙏 致谢

感谢所有为SeatMaster项目做出贡献的开发者和用户！

### 🌟 主要贡献者
- **项目架构设计**: 分布式系统架构和任务调度机制
- **学习通集成**: 完整的学习通API集成和自动预约功能
- **前端界面设计**: 现代化的Vue 3界面和用户体验
- **性能优化**: 系统性能调优和监控机制

### 📚 参考项目
- [Spring Boot](https://spring.io/projects/spring-boot) - 主服务器框架
- [Vue.js](https://vuejs.org/) - 前端框架
- [Element Plus](https://element-plus.org/) - UI组件库
- [Eclipse Jetty](https://www.eclipse.org/jetty/) - Worker服务器

### 🎯 未来规划
- [ ] 支持更多学校的座位预约系统
- [ ] 增加微信小程序端
- [ ] 实现更智能的AI预约策略
- [ ] 添加实时通知功能
- [ ] 支持集群部署和负载均衡

---

<div align="center">

**如果这个项目对您有帮助，请给我们一个 ⭐ Star！**

[🐛 报告Bug](https://github.com/csskrtao/seatMaster/issues) |
[💡 功能建议](https://github.com/csskrtao/seatMaster/issues) |
[📖 文档](https://github.com/csskrtao/seatMaster/wiki) |
[💬 讨论](https://github.com/csskrtao/seatMaster/discussions)

</div>