# 清理测试任务数据

## 问题描述
新创建的Worker会显示很多测试任务，这些任务不是真实存在的，影响了Worker的负载显示和任务分配。

## 问题分析
通过数据库查询发现，reservations表中存在以下问题任务：
1. **失败任务**：包含"用户不存在"和"学习通登录失败"的历史任务
2. **过期任务**：创建时间较早（6月22日之前）的未完成任务
3. **分配异常**：这些任务被自动分配给新创建的Worker

## 清理策略
采用精确清理策略，只删除明确的测试和失败数据：

### 清理范围
1. **失败任务清理**：
   - 删除execution_result包含"USER_NOT_FOUND"的任务
   - 删除execution_result包含"LOGIN_FAILED"的任务

2. **过期任务清理**：
   - 删除2025-06-28之前创建的未完成任务

3. **任务重置**：
   - 将剩余任务的worker_id设为NULL，重新进入待分配状态

### 保留策略
- 保留6月28日及之后创建的任务
- 保留可能有效的近期任务

## 执行过程

### 1. 清理前状态
```sql
-- 总任务数：5个
-- 任务状态分布：
-- ID 83: 用户不存在（6月9日）
-- ID 86: 登录失败（6月21日）
-- ID 91: 登录失败（6月22日）
-- ID 93: 未完成（6月22日）
-- ID 98: 未完成（6月28日）
```

### 2. 执行清理命令
```sql
-- 清理失败任务
DELETE FROM reservations 
WHERE execution_result LIKE '%USER_NOT_FOUND%' 
   OR execution_result LIKE '%LOGIN_FAILED%';

-- 清理过期未完成任务
DELETE FROM reservations 
WHERE actual_execution_time IS NULL 
  AND created_time < '2025-06-28 00:00:00';

-- 重置剩余任务分配
UPDATE reservations 
SET worker_id = NULL, assigned_time = NULL, started_time = NULL 
WHERE id = 98;
```

### 3. 清理后状态
```sql
-- 剩余任务数：1个
-- 未分配任务：1个
-- 已分配任务：0个
-- Worker负载：全部清零
```

## 清理结果

### 删除的任务
- **ID 83**: 用户不存在任务（已删除）
- **ID 86**: 登录失败任务（已删除）
- **ID 91**: 登录失败任务（已删除）
- **ID 93**: 过期未完成任务（已删除）

### 保留的任务
- **ID 98**: 6月28日创建的任务（已重置为待分配状态）

### Worker状态
- **worker-001**: 负载 0/10，状态 OFFLINE
- **worker-003**: 负载 0/10，状态 OFFLINE

## 效果验证

1. **任务分配正常**：新创建的Worker不再显示历史测试任务
2. **负载显示准确**：Worker负载从之前的错误显示恢复为0
3. **数据完整性**：保留了可能有效的近期任务
4. **系统稳定性**：清理了导致分配异常的脏数据

## 预防措施

1. **测试环境分离**：建议使用独立的测试数据库
2. **数据清理机制**：定期清理失败和过期的任务
3. **任务状态管理**：完善任务生命周期管理
4. **监控告警**：监控异常任务分配情况

## 后续建议

1. **定期维护**：建议每周清理一次失败和过期任务
2. **状态监控**：监控Worker负载的准确性
3. **测试规范**：制定测试数据管理规范
4. **自动清理**：考虑实现自动清理机制

## 执行时间
2025-06-28 22:50

## 执行结果
✅ 成功清理4个测试任务
✅ 保留1个有效任务
✅ Worker负载显示恢复正常
✅ 任务分配机制正常工作
