# 生产环境数据库脚本修复记录

## 修复概述
修复生产环境数据库初始化脚本中的索引创建错误，解决部署时出现的"Key column 'email' doesn't exist in table"等错误。

## 修复时间
2025-07-23

## 问题描述

### 错误现象
在执行部署脚本时出现以下错误：
```
ERROR 1072 (42000) at line 34: Key column 'email' doesn't exist in table
```

### 根本原因
生产环境数据库脚本 `database/production-setup.sql` 中的索引创建语句引用了不存在的列：

1. **用户表**: 尝试为不存在的 `email` 和 `status` 列创建索引
2. **预约表**: 尝试为不存在的 `reservation_date` 和 `status` 列创建索引
3. **房间表**: 尝试为不存在的 `status` 和 `capacity` 列创建索引
4. **学校表**: 尝试为不存在的 `status` 列创建索引

## 修复内容

### 1. 用户表索引修复
**修复前**:
```sql
ALTER TABLE users 
ADD INDEX idx_username (username),
ADD INDEX idx_email (email),           -- ❌ email列不存在
ADD INDEX idx_created_at (created_at), -- ❌ 字段名错误
ADD INDEX idx_status (status);         -- ❌ status列不存在
```

**修复后**:
```sql
ALTER TABLE users 
ADD INDEX IF NOT EXISTS idx_username (username),
ADD INDEX IF NOT EXISTS idx_created_time (created_time),
ADD INDEX IF NOT EXISTS idx_role (role),
ADD INDEX IF NOT EXISTS idx_remaining_days (remaining_days);
```

### 2. 预约表索引修复
**修复前**:
```sql
ALTER TABLE reservations 
ADD INDEX idx_user_id (user_id),
ADD INDEX idx_room_id (room_id),
ADD INDEX idx_reservation_date (reservation_date), -- ❌ 列不存在
ADD INDEX idx_start_time (start_time),
ADD INDEX idx_end_time (end_time),
ADD INDEX idx_status (status),                     -- ❌ 列不存在
ADD INDEX idx_created_at (created_at),             -- ❌ 字段名错误
ADD INDEX idx_user_date (user_id, reservation_date),
ADD INDEX idx_room_date (room_id, reservation_date),
ADD INDEX idx_date_time (reservation_date, start_time, end_time);
```

**修复后**:
```sql
ALTER TABLE reservations 
ADD INDEX IF NOT EXISTS idx_user_id (user_id),
ADD INDEX IF NOT EXISTS idx_room_id (room_id),
ADD INDEX IF NOT EXISTS idx_start_time (start_time),
ADD INDEX IF NOT EXISTS idx_end_time (end_time),
ADD INDEX IF NOT EXISTS idx_created_time (created_time),
ADD INDEX IF NOT EXISTS idx_worker_id (worker_id),
ADD INDEX IF NOT EXISTS idx_reservation_type (reservation_type),
ADD INDEX IF NOT EXISTS idx_user_room (user_id, room_id),
ADD INDEX IF NOT EXISTS idx_time_range (start_time, end_time);
```

### 3. 房间表索引修复
**修复前**:
```sql
ALTER TABLE rooms 
ADD INDEX idx_school_id (school_id),
ADD INDEX idx_status (status),     -- ❌ 列不存在
ADD INDEX idx_capacity (capacity); -- ❌ 列不存在
```

**修复后**:
```sql
ALTER TABLE rooms 
ADD INDEX IF NOT EXISTS idx_school_id (school_id),
ADD INDEX IF NOT EXISTS idx_name (name),
ADD INDEX IF NOT EXISTS idx_room_num (roomNum),
ADD INDEX IF NOT EXISTS idx_created_time (created_time);
```

### 4. 学校表索引修复
**修复前**:
```sql
ALTER TABLE schools 
ADD INDEX idx_name (name),
ADD INDEX idx_status (status); -- ❌ 列不存在
```

**修复后**:
```sql
ALTER TABLE schools 
ADD INDEX IF NOT EXISTS idx_name (name),
ADD INDEX IF NOT EXISTS idx_created_time (created_time),
ADD INDEX IF NOT EXISTS idx_wait_time (wait_time);
```

### 5. Worker服务器表索引修复
**修复前**:
```sql
ALTER TABLE worker_servers 
ADD INDEX idx_status (status),
ADD INDEX idx_last_heartbeat (last_heartbeat),
ADD INDEX idx_created_at (created_at); -- ❌ 字段名错误
```

**修复后**:
```sql
ALTER TABLE worker_servers 
ADD INDEX IF NOT EXISTS idx_status (status),
ADD INDEX IF NOT EXISTS idx_last_heartbeat (last_heartbeat),
ADD INDEX IF NOT EXISTS idx_created_time (created_time),
ADD INDEX IF NOT EXISTS idx_enabled (enabled),
ADD INDEX IF NOT EXISTS idx_current_load (current_load);
```

### 6. 可选表索引安全处理
为可能不存在的表（如 `task_execution_logs` 和 `distributed_tasks`）添加了存储过程来安全地创建索引：

```sql
DELIMITER $$
CREATE PROCEDURE AddIndexIfTableExists()
BEGIN
    -- 检查表是否存在再创建索引
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'task_execution_logs') THEN
        -- 创建索引
    END IF;
END$$
DELIMITER ;
```

## 修复效果

### ✅ 解决的问题
1. **部署错误**: 消除了所有"列不存在"的错误
2. **索引有效性**: 只为实际存在的列创建索引
3. **脚本健壮性**: 使用 `IF NOT EXISTS` 避免重复创建
4. **安全性**: 添加表存在性检查

### ✅ 性能优化
1. **查询优化**: 为常用查询字段创建合适的索引
2. **复合索引**: 创建多列组合索引提高查询效率
3. **分布式支持**: 为分布式任务相关字段创建索引

## 实际表结构对照

### 用户表 (users)
```sql
- id (BIGINT, 主键)
- username (VARCHAR)
- password (VARCHAR)
- name (VARCHAR)
- role (ENUM)
- remaining_days (INTEGER)
- created_time (DATETIME)
```

### 预约表 (reservations)
```sql
- id (BIGINT, 主键)
- user_id (BIGINT)
- room_id (BIGINT)
- seat_num (VARCHAR)
- start_time (TIME)
- end_time (TIME)
- created_time (DATETIME)
- reservation_open_time (VARCHAR)
- reservation_type (VARCHAR)
- worker_id (VARCHAR)
- execution_result (VARCHAR)
- last_execution_time (DATETIME)
- retry_count (INTEGER)
- started_time (DATETIME)
- actual_execution_time (DATETIME)
- error_message (VARCHAR)
```

### 房间表 (rooms)
```sql
- id (BIGINT, 主键)
- school_id (BIGINT)
- name (VARCHAR)
- roomNum (VARCHAR)
- max_reservation_hours (DECIMAL)
- created_time (DATETIME)
- description (TEXT)
- version (INTEGER)
```

### 学校表 (schools)
```sql
- id (BIGINT, 主键)
- name (VARCHAR)
- wait_time (DECIMAL)
- created_time (DATETIME)
```

## 部署验证

### 测试步骤
1. **备份数据库**: 执行修复前先备份
2. **执行脚本**: 运行修复后的生产环境脚本
3. **验证索引**: 检查索引是否正确创建
4. **功能测试**: 验证应用功能正常

### 验证命令
```sql
-- 查看表结构
DESCRIBE users;
DESCRIBE reservations;
DESCRIBE rooms;
DESCRIBE schools;

-- 查看索引
SHOW INDEX FROM users;
SHOW INDEX FROM reservations;
SHOW INDEX FROM rooms;
SHOW INDEX FROM schools;
```

## 预防措施

### 开发规范
1. **字段对照**: 创建索引前检查实际表结构
2. **命名一致**: 保持实体类和数据库字段名一致
3. **版本控制**: 数据库脚本变更需要版本管理

### 测试流程
1. **本地测试**: 在开发环境先测试数据库脚本
2. **结构验证**: 对比实体类和数据库表结构
3. **自动化检查**: 添加脚本验证索引创建的有效性

## 总结
此次修复解决了生产环境数据库初始化脚本中的所有索引创建错误，确保部署过程能够顺利完成。同时优化了索引设计，提高了数据库查询性能。
