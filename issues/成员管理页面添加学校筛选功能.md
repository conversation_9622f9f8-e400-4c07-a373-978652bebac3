# 成员管理页面添加学校筛选功能

## 任务概述
在 seatMaster 项目的成员管理页面中添加学校筛选功能，用于按学校过滤会员数据。

## 功能需求
- 在现有搜索栏区域添加学校筛选下拉选择器
- 与"预约状态"和"剩余天数"筛选器并列显示
- 使用 Element Plus 的 el-select 组件
- 保持 el-col-4 栅格布局风格
- 支持"全部学校"选项

## 技术方案
采用方案1：基于预约记录的学校筛选
- 无需修改数据库结构
- 通过用户预约记录获取学校信息
- 无预约用户显示为"未预约"

## 实现计划

### 第一步：后端API扩展
**文件：** `backend/src/main/java/com/seatmaster/controller/AdminController.java`
- 修改 `getAllUsers()` 方法添加学校筛选参数
- 利用现有 `getCurrentReservationByUserId` 查询中的学校信息

**新增API：** `/api/admin/schools` 
- 获取学校列表供前端下拉选择

### 第二步：前端组件开发
**文件：** `frontend/src/views/MemberManagement.vue`
- 添加学校下拉选择器组件
- 新增响应式数据：`schoolFilter`, `schools`
- 创建 `fetchSchools()` 方法
- 修改 `filteredUsers` 计算属性

### 第三步：前端筛选逻辑优化
- 调整栅格布局（5个筛选器使用 span=3）
- 添加筛选器重置功能
- 处理无预约用户的边界情况

### 第四步：测试与优化
- 测试各种筛选组合
- 验证数据准确性
- 检查性能表现

## 数据结构
利用现有的 `UserProfileResponse.CurrentReservation` 中的 `schoolName` 字段：
```java
public static class CurrentReservation {
    private String schoolName;  // 已存在
    // ... 其他字段
}
```

## 筛选逻辑
```javascript
// 学校筛选逻辑
if (schoolFilter.value) {
  if (schoolFilter.value === 'no-reservation') {
    filtered = filtered.filter(user => !user.currentReservation)
  } else {
    filtered = filtered.filter(user => 
      user.currentReservation && 
      user.currentReservation.schoolName === schoolFilter.value
    )
  }
}
```

## 预期结果
- 学校筛选器与现有筛选器样式一致
- 支持按学校过滤用户列表
- 无预约用户可单独筛选显示
- 筛选交互流畅，性能良好
