# 预约任务时间执行机制实现

## 🎯 实现目标

让副服务器能够根据预约开放时间和预约类型，在正确的时间执行学习通预约任务。

## 🔧 实现方案

采用**方案2：副服务器主动拉取任务**，简化架构，提高可靠性。

### 核心逻辑

- **SAME_DAY**：当天预约开放时间执行，提交预约**今天**的座位
- **ADVANCE_ONE_DAY**：当天预约开放时间执行，提交预约**明天**的座位

## 📝 主要修改

### 1. DatabaseService.java 增强

#### 新增方法：
- `getPendingTasksForWorker(String workerId)` - 获取分配给当前worker的待执行任务
- `createMockPendingTasks(String workerId)` - 创建模拟待执行任务

#### 增强ReservationInfo类：
- 添加 `xuexitongUsername` 字段
- 添加 `xuexitongPassword` 字段  
- 添加 `roomNum` 字段

### 2. TaskRequest.java 时间计算

#### 新增方法：
- `calculateExecuteTime(String reservationOpenTime, String reservationType)` - 计算执行时间
- `setExecuteTimeFromReservation(String reservationOpenTime, String reservationType)` - 设置执行时间

#### 时间计算逻辑：
```java
// 解析预约开放时间（如：08:00）
LocalTime openTime = LocalTime.parse(reservationOpenTime);

// 计算执行时间（当天的预约开放时间）
LocalDateTime executeTime = LocalDateTime.of(LocalDate.now(), openTime);

// 如果当前时间已过，推迟到明天同一时间
if (LocalDateTime.now().isAfter(executeTime)) {
    executeTime = executeTime.plusDays(1);
}
```

### 3. TaskExecutionService.java 定时拉取

#### 新增功能：
- 定时任务拉取器 `ScheduledExecutorService taskPuller`
- `pullPendingTasks()` 方法 - 每30秒拉取一次待执行任务
- 自动计算执行时间并添加到队列
- 避免重复添加相同任务

#### 拉取流程：
1. 从数据库查询分配给当前worker的待执行任务
2. 为每个任务计算执行时间
3. 检查队列中是否已存在相同任务
4. 添加新任务到执行队列

## 🕐 执行时机示例

### 场景1：SAME_DAY 当天预约
- 预约开放时间：08:00
- 预约类型：SAME_DAY
- **执行时间**：今天 08:00
- **预约日期**：今天

### 场景2：ADVANCE_ONE_DAY 提前预约
- 预约开放时间：08:00  
- 预约类型：ADVANCE_ONE_DAY
- **执行时间**：今天 08:00
- **预约日期**：明天

## 🔄 工作流程

1. **主服务器**：分配任务，只更新数据库 `worker_id` 字段
2. **副服务器**：定时拉取分配给自己的任务
3. **时间计算**：根据 `reservation_open_time` 和 `reservation_type` 计算执行时间
4. **队列管理**：任务添加到队列，等待执行时间到达
5. **任务执行**：`shouldExecuteNow()` 检查时间，到时执行预约

## ✅ 优势

- **简化架构**：无需HTTP调用，减少网络依赖
- **自主管理**：副服务器完全控制任务执行时机
- **容错性强**：数据库连接失败时使用模拟模式
- **时间精确**：精确计算预约开放时间
- **避免重复**：防止相同任务重复添加

## 🧪 测试验证

### 模拟模式测试
- 数据库未连接时自动创建模拟任务
- 包含不同预约类型和开放时间的测试数据
- 验证时间计算逻辑的正确性

### 实际运行验证
- 检查定时拉取是否正常工作
- 验证任务在正确时间执行
- 确认预约类型对应正确的预约日期

## 📊 监控指标

- 拉取任务数量和频率
- 队列中任务的执行时间分布
- 任务执行成功率
- 时间计算准确性

## 🔮 后续优化

- 支持更灵活的拉取频率配置
- 添加任务优先级机制
- 实现任务执行结果回调
- 增加详细的执行日志记录
