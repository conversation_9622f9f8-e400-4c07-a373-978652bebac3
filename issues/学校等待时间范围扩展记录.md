# 学校等待时间范围扩展记录

## 修改概述
将学校管理功能中的等待时间(wait_time)字段取值范围从0.10-1.00秒扩展到0.1-10.0秒。

## 修改时间
2025-07-23

## 修改需求
- **原范围**: 0.10 - 1.00 秒
- **新范围**: 0.10 - 10.00 秒
- **保持精度**: 支持小数点后2位
- **适用场景**: 创建和编辑学校时的等待时间设置

## 修改内容

### 1. 数据库层面修改
**文件**: `database/update_wait_time_range.sql`

**修改内容**:
1. **删除旧约束**: 移除原有的 `chk_wait_time` CHECK约束
2. **修改字段类型**: 将 `DECIMAL(3,2)` 改为 `DECIMAL(4,2)` 以支持10.00
3. **添加新约束**: 创建新的CHECK约束支持0.10-10.00范围

**SQL语句**:
```sql
-- 删除现有约束
ALTER TABLE `schools` DROP CONSTRAINT `chk_wait_time`;

-- 修改字段类型
ALTER TABLE `schools` MODIFY COLUMN `wait_time` DECIMAL(4, 2) NOT NULL DEFAULT 0.50;

-- 添加新约束
ALTER TABLE `schools` ADD CONSTRAINT `chk_wait_time` CHECK (`wait_time` >= 0.10 AND `wait_time` <= 10.00);
```

### 2. 后端验证逻辑修改
**文件**: `backend/src/main/java/com/seatmaster/service/impl/SchoolServiceImpl.java`
**位置**: 第42-48行

**修改前**:
```java
if (waitTime.compareTo(new java.math.BigDecimal("0.10")) < 0 || 
    waitTime.compareTo(new java.math.BigDecimal("1.00")) > 0) {
    throw new IllegalArgumentException("Wait time must be between 0.10 and 1.00.");
}
```

**修改后**:
```java
if (waitTime.compareTo(new java.math.BigDecimal("0.10")) < 0 || 
    waitTime.compareTo(new java.math.BigDecimal("10.00")) > 0) {
    throw new IllegalArgumentException("Wait time must be between 0.10 and 10.00 seconds.");
}
```

### 3. 前端输入组件修改
**文件**: `frontend/src/views/RoomManagement.vue`
**位置**: 第150-161行

**修改前**:
```vue
<el-input-number
  v-model="schoolForm.waitTime"
  :min="0.10"
  :max="1.00"
  :step="0.01"
  :precision="2"
  placeholder="0.10 - 1.00 秒"
  style="width: 100%"
/>
```

**修改后**:
```vue
<el-input-number
  v-model="schoolForm.waitTime"
  :min="0.10"
  :max="10.00"
  :step="0.01"
  :precision="2"
  placeholder="0.10 - 10.00 秒"
  style="width: 100%"
/>
```

### 4. 前端验证规则修改
**文件**: `frontend/src/views/RoomManagement.vue`
**位置**: 第295-304行

**修改前**:
```javascript
waitTime: [
  { required: true, message: '请输入等待时间', trigger: 'blur' },
  {
    type: 'number',
    min: 0.10,
    max: 1.00,
    message: '等待时间必须在 0.10 到 1.00 之间',
    trigger: ['blur', 'change']
  }
]
```

**修改后**:
```javascript
waitTime: [
  { required: true, message: '请输入等待时间', trigger: 'blur' },
  {
    type: 'number',
    min: 0.10,
    max: 10.00,
    message: '等待时间必须在 0.10 到 10.00 秒之间',
    trigger: ['blur', 'change']
  }
]
```

### 5. 提示文本更新
**修改前**: "学校预约操作的等待时间（秒），建议保持默认值。"
**修改后**: "学校预约操作的等待时间（秒），范围0.1-10.0秒，建议保持默认值。"

## 修改效果

### ✅ 功能增强
1. **范围扩展**: 等待时间可设置为0.1-10.0秒，满足不同场景需求
2. **精度保持**: 仍然支持小数点后2位精度
3. **验证一致**: 前后端验证规则保持一致

### ✅ 保留功能
1. **默认值**: 保持0.50秒的默认值
2. **数据类型**: 继续使用BigDecimal确保精度
3. **验证机制**: 创建和编辑时都进行范围验证

### ✅ 用户体验
1. **界面友好**: 输入组件清晰显示新的范围限制
2. **错误提示**: 提供明确的验证错误信息
3. **操作灵活**: 管理员可根据实际需求调整等待时间

## 使用场景

### 适用的等待时间设置
- **0.1-1.0秒**: 适用于高性能服务器和快速网络环境
- **1.0-3.0秒**: 适用于一般网络环境和中等负载
- **3.0-10.0秒**: 适用于网络较慢或服务器负载较高的环境

### 建议值
- **默认值**: 0.50秒（适合大多数场景）
- **高并发**: 0.10-0.30秒
- **稳定性优先**: 1.00-2.00秒
- **网络较慢**: 3.00-5.00秒

## 数据库执行步骤

1. **备份数据**: 执行修改前建议备份schools表
2. **执行脚本**: 运行 `database/update_wait_time_range.sql`
3. **验证结果**: 检查字段类型和约束是否正确修改
4. **测试功能**: 在前端测试新的范围限制

## 兼容性说明

### ✅ 向后兼容
- 现有数据不受影响（都在新范围内）
- API接口保持不变
- 数据格式保持一致

### ⚠️ 注意事项
- 需要重启后端服务以加载新的验证逻辑
- 前端页面需要刷新以应用新的输入限制
- 建议在低峰期执行数据库修改

## 测试验证

### 测试用例
1. **边界值测试**: 0.10秒和10.00秒
2. **超出范围测试**: 0.09秒和10.01秒（应该失败）
3. **精度测试**: 5.25秒等小数值
4. **默认值测试**: 新建学校的默认等待时间

### 预期结果
- ✅ 0.10-10.00秒范围内的值可以正常保存
- ❌ 超出范围的值会被前后端验证拦截
- ✅ 现有学校的等待时间设置保持不变
- ✅ 新建学校默认等待时间为0.50秒

## 总结
此次修改成功将学校等待时间的设置范围扩展了10倍，为不同网络环境和服务器性能提供了更大的灵活性，同时保持了数据的精度和验证的严格性。
