# 修复活跃任务显示不一致问题

## 问题描述
工作节点界面显示的"活跃任务"数量与实际分配的未完成任务数量不一致。

## 问题分析
- **现象**：worker-001显示活跃任务5个，但数据库中实际只有3个未完成任务
- **根本原因**：`WorkerServerService.getActualAssignedTasksCount()`方法查询条件错误
- **错误逻辑**：`.or().isNull("error_message")`包含了已完成的成功任务

## 解决方案
修正查询逻辑，只统计真正未完成的任务：
- 移除错误的`.or().isNull("error_message")`条件
- 只保留`isNull("actual_execution_time")`条件
- 增强日志输出便于监控

## 修改文件
- `backend/src/main/java/com/seatmaster/service/WorkerServerService.java`

## 预期效果
- 活跃任务数显示准确
- 负载率计算正确
- 任务分配和显示保持一致

## 优化内容
1. **性能优化**：添加任务数量缓存机制，减少频繁数据库查询
2. **逻辑优化**：优先使用数据库统计的准确数据作为最终负载
3. **配置优化**：差异阈值和缓存时间可配置化
4. **日志优化**：正常情况使用DEBUG级别，减少日志量

## 配置参数
```yaml
seatmaster:
  worker-monitoring:
    load-difference-threshold: 3     # 差异阈值
    task-count-cache-expire: 30000   # 缓存过期时间（毫秒）
    heartbeat-log-level: DEBUG       # 心跳日志级别
```

## 执行时间
- 初始修复：2025-06-28 18:10
- 优化完成：2025-06-28 18:28
