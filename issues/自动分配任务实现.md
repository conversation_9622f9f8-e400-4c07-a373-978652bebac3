# 自动分配任务实现

## 任务描述
实现当有新的任务预约信息产生时分配服务器为空，每隔一段时间检查自动根据时间段中各个服务器冗余的情况自动分配。

## 实现方案
选择了定时任务自动分配方案，具体实现：

### 1. 定时任务配置
- **文件**: `backend/src/main/java/com/seatmaster/config/ScheduleConfig.java`
- **功能**: 添加每5分钟执行的自动分配定时任务
- **Cron表达式**: `0 */5 * * * *` (每5分钟执行一次)
- **配置参数**:
  - `seatmaster.auto-assignment.enabled`: 是否启用自动分配
  - `seatmaster.auto-assignment.log-level`: 日志级别

### 2. 智能分配策略
- **文件**: `backend/src/main/java/com/seatmaster/service/impl/DistributedTaskServiceImpl.java`
- **策略类型**:
  - `ROUND_ROBIN`: 轮询分配
  - `LOAD_BALANCED`: 负载均衡分配（优先分配给负载最低的服务器）
  - `TIME_OPTIMIZED`: 时间优化分配（根据预约时间段分配）

### 3. 配置参数
- **文件**: `backend/src/main/resources/application.yml`
- **新增配置**:
```yaml
seatmaster:
  auto-assignment:
    enabled: true                    # 是否启用自动分配
    log-level: INFO                  # 日志级别
    strategy: LOAD_BALANCED          # 分配策略
    max-retry-count: 3               # 最大重试次数
    assignment-timeout: 30000        # 分配超时时间（毫秒）
```

## 核心功能

### 1. 自动检测未分配任务
- 查询条件: `worker_id` 为空且未开始执行的预约
- 按创建时间排序，优先处理早期创建的任务

### 2. 智能服务器选择
- **负载均衡策略**: 计算各服务器负载率，优先选择负载最低的服务器
- **时间优化策略**: 根据预约开放时间分组，为不同时间段分配专门服务器
- **轮询策略**: 简单的轮询分配，适合负载相对均匀的场景

### 3. 动态负载管理
- 实时更新服务器当前负载
- 自动移除负载已满的服务器
- 支持服务器容量限制检查

## 技术特点

### 1. 可配置性
- 支持启用/禁用自动分配
- 可配置分配策略
- 可调整检查频率和重试参数

### 2. 容错性
- 异常处理和日志记录
- 服务器状态检查
- 分配失败时的降级处理

### 3. 性能优化
- 批量处理待分配任务
- 智能服务器排序算法
- 避免重复查询和计算

## 使用方式

### 1. 自动运行
- 系统启动后自动开始定时检查
- 每5分钟自动分配未分配的任务
- 无需人工干预

### 2. 手动触发
- 管理员可通过API手动触发分配
- 支持批量分配和单个分配

### 3. 监控和调试
- 详细的日志记录
- 分配结果统计
- 服务器状态概览

## 预期效果
1. **自动化**: 新预约创建后自动分配服务器，无需手动操作
2. **负载均衡**: 智能分配算法确保服务器负载相对均衡
3. **时间优化**: 根据预约时间段优化分配，提高执行效率
4. **可靠性**: 完善的错误处理和重试机制
5. **可监控**: 详细的日志和统计信息便于运维监控

## 后续优化建议
1. 添加分配优先级支持
2. 实现更复杂的时间段分析算法
3. 支持服务器性能权重配置
4. 添加分配历史记录和分析
