# MySQL 语法兼容性修复记录

## 问题描述

在执行生产环境部署脚本 `sudo ./scripts/deploy.sh prod traditional` 时遇到 MySQL 语法错误：

```
ERROR 1064 (42000): You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'IF NOT EXISTS idx_username (username), ADD INDEX IF NOT EXISTS idx_created_time'
```

## 问题根源

多个SQL文件中使用了 `CREATE INDEX IF NOT EXISTS` 语法，该语法在某些MySQL版本中不被支持，导致部署失败。

## 受影响的文件

1. `database/add_distributed_fields.sql`
   - 第18行：`CREATE INDEX IF NOT EXISTS idx_worker_id ON reservations(worker_id);`
   - 第19行：`CREATE INDEX IF NOT EXISTS idx_execution_status ON reservations(execution_status);`
   - 第20行：`CREATE INDEX IF NOT EXISTS idx_last_execution_time ON reservations(last_execution_time);`
   - 第43行：`CREATE INDEX IF NOT EXISTS idx_worker_id ON worker_servers(worker_id);`
   - 第44行：`CREATE INDEX IF NOT EXISTS idx_status ON worker_servers(status);`
   - 第45行：`CREATE INDEX IF NOT EXISTS idx_last_heartbeat ON worker_servers(last_heartbeat);`

2. `database/add_reservation_time_fields.sql`
   - 第56行：`CREATE INDEX IF NOT EXISTS idx_reservations_open_time ON reservations(reservation_open_time);`
   - 第57行：`CREATE INDEX IF NOT EXISTS idx_reservations_type ON reservations(reservation_type);`

3. `database/update_schema_simple.sql`
   - 第15行：`CREATE INDEX IF NOT EXISTS idx_reservations_room_seat_date ON reservations(room_id, seat_id, start_time, status);`
   - 第16行：`CREATE INDEX IF NOT EXISTS idx_users_remaining_days ON users(remaining_days);`

## 修复方案

采用存储过程方式安全地创建索引，参考 `production-setup-safe.sql` 中已验证的方法：

### 修复模式

```sql
-- 创建安全索引创建存储过程
DELIMITER $$
CREATE PROCEDURE SafeCreateIndex(
    IN table_name VARCHAR(64),
    IN index_name VARCHAR(64),
    IN column_list VARCHAR(255)
)
BEGIN
    DECLARE index_exists INT DEFAULT 0;

    -- 检查索引是否已存在
    SELECT COUNT(*) INTO index_exists
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = table_name
    AND index_name = index_name;

    -- 如果索引不存在则创建
    IF index_exists = 0 THEN
        SET @sql = CONCAT('CREATE INDEX ', index_name, ' ON ', table_name, ' (', column_list, ')');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END$$
DELIMITER ;

-- 使用存储过程创建索引
CALL SafeCreateIndex('table_name', 'index_name', 'column_list');

-- 清理存储过程
DROP PROCEDURE SafeCreateIndex;
```

## 修复详情

### 1. add_distributed_fields.sql
- 替换所有 `CREATE INDEX IF NOT EXISTS` 为 `CALL SafeCreateIndex()` 调用
- 为避免索引名冲突，worker_servers表的索引名添加了 `_ws` 后缀
- 在文件末尾添加存储过程清理

### 2. add_reservation_time_fields.sql
- 替换所有 `CREATE INDEX IF NOT EXISTS` 为 `CALL SafeCreateIndex()` 调用
- 在文件末尾添加存储过程清理

### 3. update_schema_simple.sql
- 替换所有 `CREATE INDEX IF NOT EXISTS` 为 `CALL SafeCreateIndex()` 调用
- 在文件末尾添加存储过程清理

## 验证结果

修复完成后，使用以下命令验证没有残留的问题语法：

```bash
grep -r "CREATE INDEX IF NOT EXISTS" /root/seatMaster/database/
```

结果：无输出，确认所有问题语法已修复。

## 兼容性说明

- **修复前**：仅在支持 `CREATE INDEX IF NOT EXISTS` 语法的MySQL版本中工作
- **修复后**：兼容所有MySQL 5.7+ 版本，包括MySQL 8.0

## 最佳实践

1. **避免使用** `CREATE INDEX IF NOT EXISTS` 语法
2. **推荐使用** 存储过程方式进行条件索引创建
3. **参考** `production-setup-safe.sql` 中的安全实现模式
4. **测试** 在目标MySQL版本中验证SQL脚本兼容性

## 修复时间

- 修复日期：2025-07-25
- 修复人员：AI Assistant
- 验证状态：已完成

## 相关文档

- [MySQL兼容性指南](../docs/mysql_compatibility_guide.md)
- [生产环境部署指南](../docs/DEPLOYMENT.md)
