# Worker端口配置增强功能

## 需求描述
改进Worker服务器的命令行参数解析，支持更多格式和验证，让用户能够更方便地指定端口和其他配置。

## 实现内容

### 1. 增强的命令行参数解析器
**文件**：`worker-server-simple/src/main/java/com/seatmaster/worker/config/CommandLineParser.java`

**支持的参数格式**：
- **端口配置**：
  - `-p 8082` (简写)
  - `--port=8082` (标准格式)
  - `--server.port=8082` (Spring Boot格式)
  - `-port 8082` (兼容格式)

- **Worker ID配置**：
  - `-id worker-001`
  - `--worker-id=worker-001`
  - `--worker.id=worker-001`

- **Worker名称配置**：
  - `-name "Worker1"`
  - `--worker-name="Worker1"`
  - `--worker.name="Worker1"`

- **其他配置**：
  - `--master-url=http://localhost:8081`
  - `--max-tasks=10`
  - `--db-url=********************************************`
  - `--db-user=root`
  - `--db-password=root`

**验证功能**：
- 端口范围验证（1024-65535）
- 端口可用性检查
- Worker ID格式验证（字母、数字、下划线、横线）
- 参数冲突检测
- 详细的错误提示

### 2. 增强的配置验证
**文件**：`worker-server-simple/src/main/java/com/seatmaster/worker/config/WorkerConfig.java`

**改进内容**：
- 更详细的配置验证逻辑
- 端口可用性检查
- 数据库URL格式验证
- 主服务器URL格式验证
- 线程池配置验证
- 友好的错误信息

### 3. 配置向导工具
**文件**：`worker-server-simple/src/main/java/com/seatmaster/worker/util/ConfigWizard.java`

**功能**：
- 交互式配置生成
- 端口可用性实时检测
- 配置验证和提示
- 自动生成启动脚本
- 高级配置选项

### 4. 启动脚本示例
**文件**：`start_worker_examples.bat`

**功能**：
- 多种启动方式选择
- 端口可用性检查
- 批量启动多个Worker
- 配置向导集成

## 使用示例

# 进入worker-server-simple目录
cd worker-server-simple

# 打包项目
cd worker-server-simple && mvn clean package -DskipTests"

# 测试帮助信息
java -jar target/worker-server-simple-1.0.0.jar --help

# 测试不同的端口配置格式
java -jar target/worker-server-simple-1.0.0.jar -p 8084 --worker-id=worker-8084

# 测试完整配置
java -jar target/worker-server-simple-1.0.0.jar --port=8084 --worker-id=worker-003 --worker-name="Test Worker"


### 帮助信息
```bash
java -jar worker-server.jar --help
java -jar worker-server.jar -h
java -jar worker-server.jar -?
```

### 配置向导
```bash
java -cp target/classes com.seatmaster.worker.util.ConfigWizard
```

## 技术特点

1. **多格式支持**：兼容多种参数格式，提高易用性
2. **智能验证**：实时检查配置有效性，避免启动失败
3. **友好提示**：详细的错误信息和使用建议
4. **向导模式**：交互式配置生成，降低使用门槛
5. **脚本生成**：自动生成启动脚本，便于部署

## 错误处理

- **参数格式错误**：提供正确格式示例
- **端口冲突**：自动检测并提示可用端口
- **配置无效**：详细说明错误原因和修复建议
- **依赖缺失**：检查必要的配置项

## 兼容性

- 保持与现有启动方式的兼容性
- 支持环境变量和系统属性
- 向后兼容原有的配置格式

## 执行时间
2025-06-28 19:05
