# execution-history 页面重构字段映射分析

## 概述
将 execution-history 页面从 `task_execution_logs` 表重构为使用 `reservation_logs` 表，需要建立详细的字段映射关系。

## 表结构对比

### task_execution_logs 表字段（当前使用）
```sql
-- 基础字段
id                      BIGINT AUTO_INCREMENT PRIMARY KEY
task_id                 BIGINT NOT NULL COMMENT '任务ID（对应reservations.id）'
worker_id               VARCHAR(100) COMMENT '执行的副服务器ID'
execution_time          DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间'
response_message        TEXT COMMENT '学习通返回的消息'
response_json           TEXT COMMENT '完整的响应JSON'
execution_duration_ms   INT COMMENT '执行耗时（毫秒）'
is_success              BOOLEAN COMMENT '是否成功'
created_time            DATETIME DEFAULT CURRENT_TIMESTAMP

-- 增强字段
step1_login_duration_ms     INT COMMENT '步骤1登录耗时(毫秒)'
step2_token_duration_ms     INT COMMENT '步骤2获取Token耗时(毫秒)'
step3_submit_duration_ms    INT COMMENT '步骤3提交预约耗时(毫秒)'
total_duration_ms           INT COMMENT '总执行时间(毫秒)'
execution_start_time        DATETIME COMMENT '执行开始时间'
execution_end_time          DATETIME COMMENT '执行结束时间'
error_details               TEXT COMMENT '详细错误信息'
api_response_json           TEXT COMMENT '学习通API原始响应'
execution_status            VARCHAR(20) COMMENT '执行状态(SUCCESS/FAILED/ERROR)'
user_info                   VARCHAR(200) COMMENT '用户信息(用户名/手机号等)'
seat_info                   VARCHAR(100) COMMENT '座位信息'
room_info                   VARCHAR(100) COMMENT '房间信息'

-- 拆分预约字段（将被移除）
is_split_reservation        BOOLEAN DEFAULT FALSE
split_group_id              VARCHAR(50)
split_sequence              INT
split_total_count           INT
split_time_range            VARCHAR(50)
original_duration_hours     DECIMAL(4,1)
```

### reservation_logs 表字段（目标表）
```sql
id                      BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID'
reservation_id          BIGINT NOT NULL COMMENT '预约记录ID（关联reservations表）'
username                VARCHAR(50) NOT NULL COMMENT '用户名'
roomid                  VARCHAR(20) NOT NULL COMMENT '房间ID'
seatid                  VARCHAR(10) NOT NULL COMMENT '座位ID'
reserve_date            DATE NOT NULL COMMENT '预约日期'
start_time              TIME NOT NULL COMMENT '开始时间'
end_time                TIME NOT NULL COMMENT '结束时间'
status                  VARCHAR(20) NOT NULL COMMENT '执行状态（success/error/failed）'
error_message           TEXT COMMENT '错误消息'
api_response            JSON COMMENT '完整的API响应JSON'
api_response_time       TIMESTAMP(3) COMMENT 'API响应时间（毫秒精度）'
attempt_count           INT DEFAULT 1 COMMENT '尝试次数'
execution_time          DECIMAL(10,6) COMMENT '执行时间（秒）'
created_at              TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
```

## 详细字段映射关系

### 1. 直接映射字段
| task_execution_logs | reservation_logs | 映射说明 |
|-------------------|------------------|----------|
| `id` | `id` | 主键，直接映射 |
| `task_id` | `reservation_id` | 任务ID对应预约ID |
| `execution_status` | `status` | 执行状态，需要值转换 |
| `api_response_json` | `api_response` | API响应，类型从TEXT转为JSON |
| `user_info` | `username` | 用户信息映射为用户名 |
| `seat_info` | `seatid` | 座位信息 |
| `room_info` | `roomid` | 房间信息 |
| `error_details` | `error_message` | 错误信息 |
| `created_time` | `created_at` | 创建时间 |

### 2. 需要转换的字段
| task_execution_logs | reservation_logs | 转换逻辑 |
|-------------------|------------------|----------|
| `total_duration_ms` | `execution_time` | 毫秒转秒：ms/1000 |
| `execution_end_time` | `api_response_time` | 使用执行结束时间作为响应时间 |
| `execution_start_time` | `created_at` | 如果没有created_time，使用开始时间 |

### 3. 状态值映射
| task_execution_logs.execution_status | reservation_logs.status |
|-------------------------------------|-------------------------|
| `SUCCESS` | `success` |
| `FAILED` | `failed` |
| `ERROR` | `error` |
| `null` | `failed` (默认) |

### 4. 新增字段（reservation_logs独有）
| 字段 | 数据来源 | 说明 |
|-----|---------|------|
| `reserve_date` | 需要从reservations表关联获取 | 预约日期 |
| `start_time` | 需要从reservations表关联获取 | 预约开始时间 |
| `end_time` | 需要从reservations表关联获取 | 预约结束时间 |
| `attempt_count` | 默认值1或从重试逻辑推导 | 尝试次数 |

### 5. 移除字段（task_execution_logs独有）
以下字段在新表中不存在，将被移除：
- `worker_id` - 副服务器ID
- `step1_login_duration_ms` - 登录耗时
- `step2_token_duration_ms` - Token耗时  
- `step3_submit_duration_ms` - 提交耗时
- `response_message` - 简化响应消息
- `response_json` - 旧版响应JSON
- `execution_time` - 旧版执行时间
- `execution_duration_ms` - 旧版执行耗时
- `is_success` - 布尔成功标志
- 所有拆分预约相关字段

## 数据转换策略

### 1. ExecutionLogDTO 类调整
```java
// 移除字段
- Long taskId → Long reservationId
- String workerId (移除)
- LocalDateTime executionStartTime (移除)
- Integer step1LoginDurationMs (移除)
- Integer step2TokenDurationMs (移除)  
- Integer step3SubmitDurationMs (移除)
- String responseMessage → String errorMessage

// 新增字段
+ LocalDate reserveDate
+ LocalTime startTime
+ LocalTime endTime
+ Integer attemptCount
+ LocalDateTime apiResponseTime

// 类型调整
- Integer totalDurationMs → BigDecimal executionTime (秒)
- String apiResponseJson → Object apiResponse (JSON)
```

### 2. 查询逻辑调整
```sql
-- 旧查询（复杂关联）
SELECT tel.*, r.user_id, u.username, u.name as user_name,
       r.start_time, r.end_time, r.seat_num, 
       rm.name as room_name, s.name as school_name
FROM task_execution_logs tel 
LEFT JOIN reservations r ON tel.task_id = r.id 
LEFT JOIN users u ON r.user_id = u.id 
LEFT JOIN rooms rm ON r.room_id = rm.id 
LEFT JOIN schools s ON rm.school_id = s.id

-- 新查询（简化）
SELECT rl.*, u.id as user_id, u.name as user_name,
       rm.name as room_name, s.name as school_name
FROM reservation_logs rl
LEFT JOIN reservations r ON rl.reservation_id = r.id
LEFT JOIN users u ON u.username = rl.username
LEFT JOIN rooms rm ON rm.roomNum = rl.roomid
LEFT JOIN schools s ON rm.school_id = s.id
```

### 3. 前端显示调整
```javascript
// 时间显示
- log.executionEndTime → log.apiResponseTime
- log.durationText (基于totalDurationMs) → 基于executionTime

// 状态显示
- log.executionStatus → log.status
- 状态图标映射需要调整

// 详情显示
- log.stepDurationText (移除)
- log.userInfo → log.username
- log.seatInfo → log.seatid
- log.roomInfo → log.roomid
```

## 实施优先级

1. **高优先级**：基础字段映射和查询逻辑
2. **中优先级**：前端显示调整和状态映射
3. **低优先级**：统计功能和导出功能调整

## 风险评估

### 数据丢失风险
- **高风险**：详细执行步骤信息（登录、Token、提交耗时）
- **中风险**：Worker服务器信息
- **低风险**：拆分预约相关信息（已废弃）

### 功能影响
- **无影响**：基础的执行历史查看
- **轻微影响**：执行性能分析（失去步骤级别的耗时分析）
- **显著影响**：技术调试信息（失去Worker和详细错误信息）

## 建议
1. 保留 `task_execution_logs` 表作为技术调试备份
2. 在 `reservation_logs` 表中增加更多调试信息字段（如果需要）
3. 考虑在管理员界面保留对 `task_execution_logs` 的访问
