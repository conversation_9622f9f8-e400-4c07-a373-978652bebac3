# Java
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*
replay_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Build outputs
dist/
build/
out/

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
log/

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Database
*.db
*.sqlite
*.sqlite3

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Large files that shouldn't be in git
*.jar
*.zip
*.tar.gz
*.rar
*.7z

# Screenshots (optional - comment out if you want to keep them)
# screenshot/

# OpenCV debug files
opencv_debug/
python_debug/
python_real_debug/

# Configuration files with sensitive data
xuexitong_pro/config*.json
backend/src/main/resources/application-prod.yml
backend/src/main/resources/application-prod.properties

# MySQL configuration files with passwords
.mysql_config
.my.cnf

# Sensitive test files
**/test_*.java
test_*.sql
