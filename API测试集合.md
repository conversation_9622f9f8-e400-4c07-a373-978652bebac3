# SeatMaster API 测试集合

## 概述

本文档提供了 SeatMaster 系统所有 API 的测试用例和示例，包括主服务器和副服务器的接口。

---

## 🚀 快速测试

### 1. 服务器状态检查

#### 检查主服务器状态
```bash
# 检查主服务器是否运行 (端口 8081)
curl -X GET http://localhost:8081/api/admin/worker-management/servers
```

#### 检查副服务器状态
```bash
# 检查副服务器是否运行 (端口 8082)
curl -X GET http://localhost:8082/api/health
```

### 2. 负载率验证测试

#### 获取副服务器负载数据
```bash
# 获取所有副服务器的负载信息
curl -X GET http://localhost:8081/api/admin/worker-management/servers | jq '.data[] | {name: .name, currentLoad: .currentLoad, maxConcurrentTasks: .maxConcurrentTasks, loadRate: ((.currentLoad / .maxConcurrentTasks) * 100)}'
```

#### 验证负载率计算
```javascript
// JavaScript 验证脚本
fetch('http://localhost:8081/api/admin/worker-management/servers')
  .then(response => response.json())
  .then(data => {
    data.data.forEach(server => {
      const loadRate = Math.round((server.currentLoad / server.maxConcurrentTasks) * 100);
      console.log(`${server.name}: ${server.currentLoad}/${server.maxConcurrentTasks} = ${loadRate}%`);
    });
  });
```

---

## 📊 主服务器 API 测试

### 副服务器管理

#### 1. 获取服务器列表
```bash
# 基础请求
curl -X GET http://localhost:8081/api/admin/worker-management/servers

# 格式化输出
curl -X GET http://localhost:8081/api/admin/worker-management/servers | jq '.'

# 只显示关键信息
curl -X GET http://localhost:8081/api/admin/worker-management/servers | jq '.data[] | {name: .name, status: .status, load: .currentLoad, max: .maxConcurrentTasks}'
```

#### 2. 获取在线服务器
```bash
curl -X GET http://localhost:8081/api/admin/worker-management/servers/online
```

#### 3. 获取统计信息
```bash
curl -X GET http://localhost:8081/api/admin/worker-management/statistics
```

#### 4. 健康检查
```bash
# 检查特定服务器
curl -X POST http://localhost:8081/api/admin/worker-management/servers/worker-001/health-check

# 批量健康检查
curl -X POST http://localhost:8081/api/admin/worker-management/servers/batch-health-check \
  -H "Content-Type: application/json" \
  -d '["worker-001", "worker-002"]'
```

#### 5. 创建新服务器
```bash
curl -X POST http://localhost:8081/api/admin/worker-management/servers \
  -H "Content-Type: application/json" \
  -d '{
    "workerId": "worker-test",
    "name": "测试副服务器",
    "host": "localhost",
    "port": 8085,
    "maxConcurrentTasks": 5
  }'
```

#### 6. 更新服务器配置
```bash
curl -X PUT http://localhost:8081/api/admin/worker-management/servers/worker-test \
  -H "Content-Type: application/json" \
  -d '{
    "workerId": "worker-test",
    "name": "更新后的测试服务器",
    "host": "localhost",
    "port": 8085,
    "maxConcurrentTasks": 8
  }'
```

#### 7. 删除服务器
```bash
curl -X DELETE http://localhost:8081/api/admin/worker-management/servers/worker-test
```

### 分布式任务管理

#### 1. 获取任务统计
```bash
curl -X GET http://localhost:8081/api/admin/distributed-tasks/statistics
```

#### 2. 获取副服务器状态概览
```bash
curl -X GET http://localhost:8081/api/admin/distributed-tasks/worker-status
```

#### 3. 获取任务列表
```bash
# 获取所有任务
curl -X GET http://localhost:8081/api/admin/distributed-tasks/tasks

# 分页查询
curl -X GET "http://localhost:8081/api/admin/distributed-tasks/tasks?page=1&size=10"

# 按状态筛选
curl -X GET "http://localhost:8081/api/admin/distributed-tasks/tasks?status=PENDING"

# 按关键词搜索
curl -X GET "http://localhost:8081/api/admin/distributed-tasks/tasks?keyword=user123"
```

#### 4. 任务操作
```bash
# 手动分配任务
curl -X POST "http://localhost:8081/api/admin/distributed-tasks/tasks/123/assign?workerId=worker-001"

# 重试失败任务
curl -X POST http://localhost:8081/api/admin/distributed-tasks/tasks/123/retry

# 取消任务
curl -X POST http://localhost:8081/api/admin/distributed-tasks/tasks/123/cancel

# 自动分配任务
curl -X POST http://localhost:8081/api/admin/distributed-tasks/auto-assign
```

---

## 🔧 副服务器 API 测试

### 基础状态检查

#### 1. 健康检查
```bash
curl -X GET http://localhost:8082/api/health
```

#### 2. 详细状态
```bash
curl -X GET http://localhost:8082/api/status
```

### 任务管理

#### 1. 获取任务概览
```bash
curl -X GET http://localhost:8082/api/tasks/
```

#### 2. 获取队列详情
```bash
curl -X GET http://localhost:8082/api/tasks/queue
```

#### 3. 获取正在执行的任务
```bash
curl -X GET http://localhost:8082/api/tasks/running
```

#### 4. 获取已完成任务
```bash
curl -X GET http://localhost:8082/api/tasks/completed
```

#### 5. 添加测试任务
```bash
# 创建测试任务
curl -X GET http://localhost:8082/api/tasks/test

# 手动添加任务
curl -X POST http://localhost:8082/api/tasks/add \
  -H "Content-Type: application/json" \
  -d '{
    "reservationId": 123,
    "priority": 5,
    "executeTime": "2025-06-28T10:00:00"
  }'
```

#### 6. 立即执行任务
```bash
curl -X POST http://localhost:8082/api/tasks/execute/task_123
```

#### 7. 清理队列
```bash
curl -X GET http://localhost:8082/api/tasks/clear
```

---

## 🧪 负载率测试场景

### 场景1: 验证负载率修复

#### 步骤1: 检查初始状态
```bash
echo "=== 检查初始负载率 ==="
curl -s http://localhost:8081/api/admin/worker-management/servers | jq '.data[] | {name: .name, currentLoad: .currentLoad, maxTasks: .maxConcurrentTasks, loadRate: ((.currentLoad / .maxConcurrentTasks) * 100)}'
```

#### 步骤2: 添加测试任务
```bash
echo "=== 添加测试任务 ==="
curl -X GET http://localhost:8082/api/tasks/test
curl -X GET http://localhost:8082/api/tasks/test
curl -X GET http://localhost:8082/api/tasks/test
```

#### 步骤3: 检查负载变化
```bash
echo "=== 检查负载变化 ==="
sleep 5
curl -s http://localhost:8081/api/admin/worker-management/servers | jq '.data[] | {name: .name, currentLoad: .currentLoad, maxTasks: .maxConcurrentTasks, loadRate: ((.currentLoad / .maxConcurrentTasks) * 100)}'
```

### 场景2: 压力测试

#### 创建多个任务
```bash
echo "=== 创建压力测试任务 ==="
for i in {1..5}; do
  curl -X POST http://localhost:8082/api/tasks/add \
    -H "Content-Type: application/json" \
    -d "{\"reservationId\": $i, \"priority\": 5}"
  echo "任务 $i 已添加"
done
```

#### 监控负载变化
```bash
echo "=== 监控负载变化 ==="
for i in {1..10}; do
  echo "--- 第 $i 次检查 ---"
  curl -s http://localhost:8081/api/admin/worker-management/servers | jq '.data[] | select(.status == "ONLINE") | {name: .name, load: .currentLoad, max: .maxConcurrentTasks, rate: ((.currentLoad / .maxConcurrentTasks) * 100)}'
  sleep 10
done
```

---

## 📝 PowerShell 测试脚本

### 完整测试脚本
```powershell
# SeatMaster API 测试脚本

Write-Host "=== SeatMaster API 测试开始 ===" -ForegroundColor Green

# 1. 检查服务器状态
Write-Host "1. 检查主服务器状态..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8081/api/admin/worker-management/servers" -Method GET
    $data = $response.Content | ConvertFrom-Json
    Write-Host "✅ 主服务器正常运行，发现 $($data.data.Count) 个副服务器" -ForegroundColor Green
} catch {
    Write-Host "❌ 主服务器连接失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 检查副服务器状态
Write-Host "2. 检查副服务器状态..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8082/api/health" -Method GET
    $data = $response.Content | ConvertFrom-Json
    Write-Host "✅ 副服务器正常运行: $($data.status)" -ForegroundColor Green
} catch {
    Write-Host "❌ 副服务器连接失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 验证负载率数据
Write-Host "3. 验证负载率数据..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8081/api/admin/worker-management/servers" -Method GET
    $data = $response.Content | ConvertFrom-Json
    
    foreach ($server in $data.data) {
        $loadRate = [math]::Round(($server.currentLoad / $server.maxConcurrentTasks) * 100, 1)
        Write-Host "  $($server.name): $($server.currentLoad)/$($server.maxConcurrentTasks) = $loadRate%" -ForegroundColor Cyan
        
        if ($server.maxConcurrentTasks -gt 0) {
            Write-Host "    ✅ maxConcurrentTasks 字段正常" -ForegroundColor Green
        } else {
            Write-Host "    ❌ maxConcurrentTasks 字段异常" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "❌ 负载率验证失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "=== 测试完成 ===" -ForegroundColor Green
```

---

## 🔍 故障排查

### 常见问题及解决方案

#### 1. 连接失败
```bash
# 检查端口是否被占用
netstat -ano | findstr :8081
netstat -ano | findstr :8082

# 检查服务是否启动
curl -I http://localhost:8081/api/admin/worker-management/servers
curl -I http://localhost:8082/api/health
```

#### 2. 负载率显示0%
```bash
# 检查 maxConcurrentTasks 字段
curl -s http://localhost:8081/api/admin/worker-management/servers | jq '.data[] | {name: .name, maxConcurrentTasks: .maxConcurrentTasks}'

# 检查 currentLoad 字段
curl -s http://localhost:8081/api/admin/worker-management/servers | jq '.data[] | {name: .name, currentLoad: .currentLoad}'
```

#### 3. 心跳超时
```bash
# 检查最后心跳时间
curl -s http://localhost:8081/api/admin/worker-management/servers | jq '.data[] | {name: .name, lastHeartbeat: .lastHeartbeat, status: .status}'

# 手动更新超时状态
curl -X POST "http://localhost:8081/api/admin/worker-management/servers/update-timeout-status?timeoutMinutes=5"
```

---

## 📈 性能监控

### 实时监控脚本
```bash
#!/bin/bash
# 实时监控副服务器负载

while true; do
    clear
    echo "=== SeatMaster 实时监控 ==="
    echo "时间: $(date)"
    echo ""
    
    # 获取服务器状态
    curl -s http://localhost:8081/api/admin/worker-management/servers | jq -r '
        .data[] | 
        "服务器: \(.name) | 状态: \(.status) | 负载: \(.currentLoad)/\(.maxConcurrentTasks) (\(((.currentLoad / .maxConcurrentTasks) * 100) | round)%) | 心跳: \(.lastHeartbeat)"
    '
    
    echo ""
    echo "按 Ctrl+C 退出监控"
    sleep 5
done
```

---

## 📋 测试检查清单

### 修复验证清单

- [ ] 主服务器启动正常 (端口 8081)
- [ ] 副服务器启动正常 (端口 8082)
- [ ] 副服务器成功注册到主服务器
- [ ] 心跳数据正常发送
- [ ] `maxConcurrentTasks` 字段正确返回
- [ ] `currentLoad` 字段显示真实负载
- [ ] 负载率计算公式正确
- [ ] 前端负载率显示正常
- [ ] 任务执行时负载率实时更新
- [ ] 任务完成后负载率正确下降

### API 功能清单

- [ ] 获取服务器列表
- [ ] 获取在线服务器
- [ ] 获取统计信息
- [ ] 健康检查功能
- [ ] 创建/更新/删除服务器
- [ ] 批量操作功能
- [ ] 任务管理功能
- [ ] 错误处理正常

---

**测试完成后，请确保所有功能正常工作，特别是负载率的实时计算和显示！** 🎉
