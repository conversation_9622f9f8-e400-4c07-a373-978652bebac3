# SeatMaster 生产环境部署包

## 🎯 项目概述

SeatMaster 是一个智能座位预约管理系统，提供完整的生产环境部署解决方案。本项目包含了从开发到生产的完整部署配置，支持传统部署和容器化部署两种方式。

## ✨ 特性

### 核心功能
- 🪑 智能座位预约管理
- 👥 用户权限管理
- 🏫 多校区支持
- 📊 数据统计分析
- 🔔 消息通知系统
- 📱 响应式前端界面

### 生产环境特性
- 🚀 一键部署脚本
- 🐳 Docker容器化支持
- 🔒 完整安全配置
- 📈 性能监控和调优
- 💾 自动备份和恢复
- 🔄 滚动更新支持
- 📋 完整运维文档

## 🏗️ 技术架构

### 后端技术栈
- **框架**: Spring Boot 2.7.x
- **数据库**: MySQL 8.0
- **缓存**: Redis 6.0 (可选)
- **安全**: Spring Security + JWT
- **监控**: Spring Boot Actuator + Micrometer

### 前端技术栈
- **框架**: Vue 3 + TypeScript
- **UI库**: Element Plus
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router 4

### 基础设施
- **Web服务器**: Nginx 1.18+
- **反向代理**: Nginx + SSL/TLS
- **进程管理**: systemd + PM2
- **容器化**: Docker + Docker Compose
- **监控**: Prometheus + Grafana (可选)

## 📦 部署包结构

```
seatMaster/
├── backend/                    # 后端应用
│   ├── src/                   # 源代码
│   ├── Dockerfile             # Docker构建文件
│   ├── docker-entrypoint.sh   # Docker启动脚本
│   └── pom.xml               # Maven配置
├── frontend/                   # 前端应用
│   ├── src/                   # 源代码
│   ├── Dockerfile             # Docker构建文件
│   ├── nginx.conf             # Nginx配置
│   └── package.json          # npm配置
├── scripts/                    # 部署脚本
│   ├── deploy.sh              # 一键部署脚本
│   ├── build.sh               # 构建脚本
│   ├── update.sh              # 更新脚本
│   ├── backup.sh              # 备份脚本
│   ├── restore.sh             # 恢复脚本
│   ├── setup-ssl.sh           # SSL配置脚本
│   ├── security-hardening.sh  # 安全加固脚本
│   ├── performance-tuning.sh  # 性能调优脚本
│   ├── health-check.sh        # 健康检查脚本
│   ├── log-manager.sh         # 日志管理脚本
│   ├── install-services.sh    # 服务安装脚本
│   └── setup-backup.sh        # 备份配置脚本
├── config/                     # 配置文件
│   ├── jvm-performance.conf   # JVM性能配置
│   └── database-performance.cnf # 数据库性能配置
├── systemd/                    # 系统服务配置
│   ├── seatmaster-backend.service
│   ├── seatmaster-frontend.service
│   └── seatmaster-nginx.service
├── nginx/                      # Nginx配置
│   ├── nginx.conf             # 主配置
│   └── conf.d/                # 站点配置
├── mysql/                      # MySQL配置
│   └── conf.d/mysql.cnf       # 性能配置
├── redis/                      # Redis配置
│   └── redis.conf             # 配置文件
├── monitoring/                 # 监控配置
│   ├── prometheus.yml         # Prometheus配置
│   ├── alert_rules.yml        # 告警规则
│   └── grafana-dashboard.json # Grafana仪表板
├── database/                   # 数据库脚本
│   ├── seat_reservation.sql   # 数据库结构
│   ├── init.sql              # 初始化数据
│   └── production-setup.sql   # 生产环境配置
├── docs/                       # 文档
│   ├── DEPLOYMENT_GUIDE.md    # 部署指南
│   ├── OPERATIONS_MANUAL.md   # 运维手册
│   └── QUICK_REFERENCE.md     # 快速参考
├── docker-compose.yml          # Docker Compose配置
├── .env.docker                # Docker环境变量
└── README_PRODUCTION.md       # 本文件
```

## 🚀 快速开始

### 方式一：一键部署 (推荐)

```bash
# 1. 克隆项目
git clone https://github.com/yourusername/seatmaster.git
cd seatmaster

# 2. 执行一键部署
sudo ./scripts/deploy.sh prod traditional

# 3. 配置SSL证书 (可选)
sudo ./scripts/setup-ssl.sh yourdomain.com <EMAIL> letsencrypt

# 4. 配置自动备份
sudo ./scripts/setup-backup.sh --schedule daily --time 02:00
```

### 方式二：Docker部署

```bash
# 1. 克隆项目
git clone https://github.com/yourusername/seatmaster.git
cd seatmaster

# 2. 配置环境变量
cp .env.docker .env
# 编辑 .env 文件配置数据库密码等

# 3. 启动服务
docker-compose up -d

# 4. 检查服务状态
docker-compose ps
```

### 方式三：手动部署

详细步骤请参考 [部署指南](docs/DEPLOYMENT_GUIDE.md)

## 🔧 配置说明

### 环境变量配置

主要环境变量说明：

```bash
# 应用配置
SPRING_PROFILES_ACTIVE=prod    # 运行环境
SERVER_PORT=8081               # 后端端口

# 数据库配置
DB_HOST=localhost              # 数据库主机
DB_PORT=3306                   # 数据库端口
DB_NAME=seat_reservation       # 数据库名
DB_USERNAME=root               # 数据库用户名
DB_PASSWORD=your_password      # 数据库密码

# 安全配置
JWT_SECRET=your_jwt_secret     # JWT密钥
CORS_ALLOWED_ORIGINS=https://yourdomain.com

# 性能配置
JVM_OPTS="-Xms1g -Xmx2g -XX:+UseG1GC"
```

### 端口配置

| 服务 | 端口 | 说明 |
|------|------|------|
| HTTP | 80 | HTTP访问 (重定向到HTTPS) |
| HTTPS | 443 | HTTPS访问 |
| 前端开发 | 3000 | 前端开发服务器 |
| 后端API | 8081 | 后端API服务 |
| MySQL | 3306 | 数据库服务 |
| Redis | 6379 | 缓存服务 (可选) |

## 📊 监控和运维

### 健康检查

```bash
# 系统健康检查
sudo /usr/local/bin/seatmaster-performance.sh

# 应用健康检查
curl http://localhost:8081/actuator/health

# 服务状态检查
sudo seatmaster status
```

### 日志管理

```bash
# 查看实时日志
sudo seatmaster logs backend

# 分析日志
sudo ./scripts/log-manager.sh analyze backend

# 清理日志
sudo ./scripts/log-manager.sh clean --days 30
```

### 备份管理

```bash
# 手动备份
sudo ./scripts/backup.sh full

# 恢复数据
sudo ./scripts/restore.sh /var/backups/seatmaster/20241122_143000 full

# 查看备份状态
sudo /usr/local/bin/seatmaster-backup-monitor.sh
```

## 🔒 安全特性

### 已实现的安全措施

- ✅ SSL/TLS加密传输
- ✅ JWT身份认证
- ✅ CORS跨域保护
- ✅ SQL注入防护
- ✅ XSS攻击防护
- ✅ CSRF攻击防护
- ✅ 防火墙配置
- ✅ Fail2Ban入侵防护
- ✅ SSH安全加固
- ✅ 系统安全加固
- ✅ 审计日志记录

### 安全配置

```bash
# 执行安全加固
sudo ./scripts/security-hardening.sh

# 配置SSL证书
sudo ./scripts/setup-ssl.sh yourdomain.com <EMAIL> letsencrypt

# 安全检查
sudo /usr/local/bin/security-check.sh
```

## 📈 性能优化

### 已实现的性能优化

- ✅ JVM参数调优
- ✅ 数据库连接池优化
- ✅ MySQL性能配置
- ✅ Nginx性能配置
- ✅ 前端代码分割和压缩
- ✅ 静态资源缓存
- ✅ 系统内核参数优化
- ✅ 文件描述符限制优化

### 性能调优

```bash
# 执行性能调优
sudo ./scripts/performance-tuning.sh all

# 监控性能指标
sudo /usr/local/bin/jvm-monitor.sh
sudo /usr/local/bin/mysql-monitor.sh
```

## 🔄 更新和维护

### 应用更新

```bash
# 滚动更新 (推荐)
sudo ./scripts/update.sh rolling

# 蓝绿部署
sudo ./scripts/update.sh blue-green

# 立即更新
sudo ./scripts/update.sh immediate
```

### 系统维护

```bash
# 系统更新
sudo apt update && sudo apt upgrade -y

# 数据库维护
mysql -u root -p -e "OPTIMIZE TABLE seat_reservation.*"

# 清理系统
sudo ./scripts/log-manager.sh clean --days 30
```

## 📚 文档

- [部署指南](docs/DEPLOYMENT_GUIDE.md) - 详细的部署步骤和配置说明
- [运维手册](docs/OPERATIONS_MANUAL.md) - 日常运维和故障处理指南
- [快速参考](docs/QUICK_REFERENCE.md) - 常用命令和配置快速参考

## 🆘 技术支持

### 获取帮助

1. **查看文档**: 首先查看相关文档
2. **检查日志**: 查看应用和系统日志
3. **运行诊断**: 执行健康检查脚本
4. **联系支持**: 发送邮件到 <EMAIL>

### 故障报告

请在报告故障时提供以下信息：

```bash
# 收集系统信息
sudo ./scripts/log-manager.sh collect

# 生成诊断报告
sudo /usr/local/bin/seatmaster-performance.sh > diagnostic-report.txt
```

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 📞 联系方式

- **项目主页**: https://github.com/yourusername/seatmaster
- **技术支持**: <EMAIL>
- **文档站点**: https://docs.seatmaster.com

---

## 🎉 致谢

感谢所有为 SeatMaster 项目做出贡献的开发者和用户！

---

*SeatMaster 生产环境部署包 v1.0.0*  
*最后更新: 2024年11月22日*
