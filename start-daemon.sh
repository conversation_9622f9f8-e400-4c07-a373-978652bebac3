#!/bin/bash

# 座位预订系统后台服务管理脚本
# 作者：Augment Agent
# 版本：2.0.0 - 支持后台持久运行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 项目路径
PROJECT_ROOT=$(pwd)
BACKEND_DIR="$PROJECT_ROOT/backend"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
LOG_DIR="$PROJECT_ROOT/logs"
PID_DIR="$PROJECT_ROOT/pids"

# 服务名称
BACKEND_SERVICE="seatmaster-backend"
FRONTEND_APP="seatmaster-frontend"

# 创建必要目录
mkdir -p "$LOG_DIR" "$PID_DIR"

# 打印函数
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] ${message}${NC}"
}

print_success() { print_message "$GREEN" "✅ $1"; }
print_error() { print_message "$RED" "❌ $1"; }
print_warning() { print_message "$YELLOW" "⚠️  $1"; }
print_info() { print_message "$BLUE" "ℹ️  $1"; }
print_step() { print_message "$PURPLE" "🔄 $1"; }

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_error "$1 未安装或不在PATH中"
        return 1
    fi
    return 0
}

# 检查服务状态
check_backend_status() {
    systemctl is-active --quiet $BACKEND_SERVICE 2>/dev/null
}

check_frontend_status() {
    pm2 describe $FRONTEND_APP &>/dev/null && [ "$(pm2 jlist | jq -r '.[] | select(.name=="'$FRONTEND_APP'") | .pm2_env.status')" = "online" ]
}

# 安装systemd服务
install_systemd_service() {
    print_step "安装后端systemd服务..."
    
    if [ ! -f "$PROJECT_ROOT/seatmaster-backend.service" ]; then
        print_error "systemd服务文件不存在: $PROJECT_ROOT/seatmaster-backend.service"
        return 1
    fi
    
    # 复制服务文件
    sudo cp "$PROJECT_ROOT/seatmaster-backend.service" /etc/systemd/system/
    
    # 重新加载systemd
    sudo systemctl daemon-reload
    
    # 启用服务（开机自启）
    sudo systemctl enable $BACKEND_SERVICE
    
    print_success "后端systemd服务安装完成"
}

# 启动后端服务
start_backend() {
    print_step "启动后端服务..."
    
    if check_backend_status; then
        print_warning "后端服务已在运行"
        return 0
    fi
    
    # 确保服务已安装
    if ! systemctl list-unit-files | grep -q $BACKEND_SERVICE; then
        install_systemd_service
    fi
    
    # 启动服务
    sudo systemctl start $BACKEND_SERVICE
    
    # 等待启动
    local count=0
    while [ $count -lt 30 ]; do
        if check_backend_status; then
            print_success "后端服务启动成功"
            return 0
        fi
        sleep 2
        count=$((count + 1))
        echo -n "."
    done
    
    echo ""
    print_error "后端服务启动超时"
    print_info "查看日志: sudo journalctl -u $BACKEND_SERVICE -f"
    return 1
}

# 启动前端服务
start_frontend() {
    print_step "启动前端服务..."
    
    if check_frontend_status; then
        print_warning "前端服务已在运行"
        return 0
    fi
    
    # 检查PM2是否安装
    if ! check_command pm2; then
        print_step "安装PM2..."
        npm install -g pm2
    fi
    
    # 检查前端依赖
    if [ ! -d "$FRONTEND_DIR/node_modules" ]; then
        print_step "安装前端依赖..."
        cd "$FRONTEND_DIR"
        npm install
        cd "$PROJECT_ROOT"
    fi
    
    # 启动前端应用
    pm2 start ecosystem.config.js
    
    # 等待启动
    local count=0
    while [ $count -lt 20 ]; do
        if check_frontend_status; then
            print_success "前端服务启动成功"
            return 0
        fi
        sleep 2
        count=$((count + 1))
        echo -n "."
    done
    
    echo ""
    print_error "前端服务启动超时"
    print_info "查看日志: pm2 logs $FRONTEND_APP"
    return 1
}

# 停止后端服务
stop_backend() {
    print_step "停止后端服务..."
    
    if check_backend_status; then
        sudo systemctl stop $BACKEND_SERVICE
        print_success "后端服务已停止"
    else
        print_info "后端服务未运行"
    fi
}

# 停止前端服务
stop_frontend() {
    print_step "停止前端服务..."
    
    if check_frontend_status; then
        pm2 stop $FRONTEND_APP
        print_success "前端服务已停止"
    else
        print_info "前端服务未运行"
    fi
}

# 重启服务
restart_backend() {
    print_step "重启后端服务..."
    sudo systemctl restart $BACKEND_SERVICE
    
    # 等待重启完成
    sleep 5
    if check_backend_status; then
        print_success "后端服务重启成功"
    else
        print_error "后端服务重启失败"
        return 1
    fi
}

restart_frontend() {
    print_step "重启前端服务..."
    pm2 restart $FRONTEND_APP
    
    # 等待重启完成
    sleep 3
    if check_frontend_status; then
        print_success "前端服务重启成功"
    else
        print_error "前端服务重启失败"
        return 1
    fi
}

# 查看状态
show_status() {
    echo ""
    echo -e "${CYAN}================================================${NC}"
    echo -e "${CYAN}    📊 座位预订系统服务状态${NC}"
    echo -e "${CYAN}================================================${NC}"
    echo ""
    
    # 后端状态
    print_info "后端服务状态:"
    if check_backend_status; then
        print_success "  运行中 (systemd)"
        echo "  服务详情: $(sudo systemctl status $BACKEND_SERVICE --no-pager -l | head -3 | tail -1)"
    else
        print_error "  未运行"
    fi
    
    # 前端状态
    print_info "前端服务状态:"
    if check_frontend_status; then
        print_success "  运行中 (PM2)"
        pm2 describe $FRONTEND_APP | grep -E "(status|uptime|restarts|memory|cpu)"
    else
        print_error "  未运行"
    fi
    
    # 端口状态
    echo ""
    print_info "端口状态:"
    if netstat -tlnp 2>/dev/null | grep -q ":8081 "; then
        print_success "  后端端口 8081: 监听中"
    else
        print_error "  后端端口 8081: 未监听"
    fi
    
    if netstat -tlnp 2>/dev/null | grep -q ":3000 "; then
        print_success "  前端端口 3000: 监听中"
    else
        print_error "  前端端口 3000: 未监听"
    fi
    
    # 数据库状态
    echo ""
    print_info "数据库状态:"
    if mysql -u root -proot5869087 -e "SELECT 1" &>/dev/null; then
        print_success "  MySQL: 连接正常"
    else
        print_error "  MySQL: 连接失败"
    fi
    
    echo ""
}

# 查看日志
show_logs() {
    local service=$1
    case $service in
        "backend"|"b")
            print_info "查看后端日志 (Ctrl+C 退出):"
            sudo journalctl -u $BACKEND_SERVICE -f
            ;;
        "frontend"|"f")
            print_info "查看前端日志 (Ctrl+C 退出):"
            pm2 logs $FRONTEND_APP --lines 50
            ;;
        "all"|"")
            print_info "选择要查看的日志:"
            echo "1) 后端日志"
            echo "2) 前端日志"
            echo "3) 同时查看"
            read -p "请选择 (1-3): " choice
            case $choice in
                1) show_logs backend ;;
                2) show_logs frontend ;;
                3) 
                    print_info "在新终端中运行以下命令查看日志:"
                    echo "后端: sudo journalctl -u $BACKEND_SERVICE -f"
                    echo "前端: pm2 logs $FRONTEND_APP -f"
                    ;;
                *) print_error "无效选择" ;;
            esac
            ;;
        *)
            print_error "未知的日志类型: $service"
            ;;
    esac
}

# 设置开机自启
setup_autostart() {
    print_step "配置开机自启..."
    
    # 后端开机自启（systemd自动处理）
    sudo systemctl enable $BACKEND_SERVICE
    print_success "后端服务开机自启已启用"
    
    # 前端开机自启（通过PM2）
    pm2 startup
    pm2 save
    print_success "前端服务开机自启已配置"
    
    print_info "重启系统后服务将自动启动"
}

# 主函数
main() {
    local action=$1
    local target=$2
    
    case $action in
        "start")
            echo ""
            echo -e "${CYAN}================================================${NC}"
            echo -e "${CYAN}    🚀 启动座位预订系统服务${NC}"
            echo -e "${CYAN}================================================${NC}"
            echo ""
            
            case $target in
                "backend"|"b")
                    start_backend
                    ;;
                "frontend"|"f")
                    start_frontend
                    ;;
                ""|"all")
                    start_backend && start_frontend
                    if [ $? -eq 0 ]; then
                        echo ""
                        print_success "🎉 所有服务启动成功！"
                        print_info "前端: http://localhost:3000"
                        print_info "后端: http://localhost:8081"
                        echo ""
                    fi
                    ;;
                *)
                    print_error "未知的服务: $target"
                    exit 1
                    ;;
            esac
            ;;
            
        "stop")
            echo ""
            echo -e "${YELLOW}================================================${NC}"
            echo -e "${YELLOW}    🛑 停止座位预订系统服务${NC}"
            echo -e "${YELLOW}================================================${NC}"
            echo ""
            
            case $target in
                "backend"|"b")
                    stop_backend
                    ;;
                "frontend"|"f")
                    stop_frontend
                    ;;
                ""|"all")
                    stop_frontend
                    stop_backend
                    print_success "所有服务已停止"
                    ;;
                *)
                    print_error "未知的服务: $target"
                    exit 1
                    ;;
            esac
            ;;
            
        "restart")
            case $target in
                "backend"|"b")
                    restart_backend
                    ;;
                "frontend"|"f")
                    restart_frontend
                    ;;
                ""|"all")
                    restart_backend && restart_frontend
                    print_success "所有服务已重启"
                    ;;
                *)
                    print_error "未知的服务: $target"
                    exit 1
                    ;;
            esac
            ;;
            
        "status"|"st")
            show_status
            ;;
            
        "logs"|"log")
            show_logs $target
            ;;
            
        "autostart"|"auto")
            setup_autostart
            ;;
            
        "install")
            print_step "安装服务..."
            install_systemd_service
            print_info "安装完成，使用 './start-daemon.sh start' 启动服务"
            ;;
            
        "help"|"-h"|"--help"|"")
            echo "座位预订系统后台服务管理脚本 v2.0.0"
            echo ""
            echo "用法:"
            echo "  $0 start [backend|frontend|all]     启动服务"
            echo "  $0 stop [backend|frontend|all]      停止服务"
            echo "  $0 restart [backend|frontend|all]   重启服务"
            echo "  $0 status                            查看状态"
            echo "  $0 logs [backend|frontend]           查看日志"
            echo "  $0 autostart                         配置开机自启"
            echo "  $0 install                           安装systemd服务"
            echo "  $0 help                              显示帮助"
            echo ""
            echo "示例:"
            echo "  $0 start                             启动所有服务"
            echo "  $0 start backend                     只启动后端"
            echo "  $0 logs frontend                     查看前端日志"
            echo "  $0 restart all                       重启所有服务"
            echo ""
            ;;
            
        *)
            print_error "未知命令: $action"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 检查是否为root用户（systemd需要）
if [ "$EUID" -ne 0 ] && [[ "$1" =~ ^(start|stop|restart|install|autostart)$ ]]; then
    print_warning "某些操作需要root权限，正在使用sudo..."
fi

# 运行主函数
main "$@"
