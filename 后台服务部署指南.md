# 座位预订系统后台服务部署指南

## 📋 概述

本指南介绍如何将座位预订系统部署为后台持久运行的服务，支持自动重启、开机自启、日志管理和健康监控。

## 🏗️ 架构设计

### 技术栈
- **后端**: Spring Boot + systemd服务管理
- **前端**: Vue.js + PM2进程管理
- **数据库**: MySQL
- **日志**: systemd journal + PM2日志 + logrotate轮转
- **监控**: 自定义健康检查脚本 + cron定时任务

### 服务架构
```
┌─────────────────┐    ┌─────────────────┐
│   前端服务      │    │   后端服务      │
│   (PM2管理)     │    │  (systemd管理)  │
│   端口: 3000    │    │   端口: 8081    │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
         ┌─────────────────┐
         │   MySQL数据库   │
         │   端口: 3306    │
         └─────────────────┘
```

## 🚀 快速部署

### 1. 安装后台服务

```bash
# 以root权限运行安装脚本
sudo ./install-daemon.sh
```

安装脚本会自动：
- ✅ 检查系统环境和依赖
- ✅ 安装PM2进程管理器
- ✅ 配置systemd服务
- ✅ 设置日志轮转
- ✅ 配置健康检查
- ✅ 设置文件权限
- ✅ 配置防火墙规则

### 2. 启动所有服务

```bash
# 启动所有服务
./start-daemon.sh start

# 或分别启动
./start-daemon.sh start backend   # 只启动后端
./start-daemon.sh start frontend  # 只启动前端
```

### 3. 配置开机自启

```bash
./start-daemon.sh autostart
```

## 🛠️ 服务管理

### 基本命令

```bash
# 启动服务
./start-daemon.sh start [backend|frontend|all]

# 停止服务
./start-daemon.sh stop [backend|frontend|all]

# 重启服务
./start-daemon.sh restart [backend|frontend|all]

# 查看状态
./start-daemon.sh status

# 查看日志
./start-daemon.sh logs [backend|frontend]

# 配置自启
./start-daemon.sh autostart

# 安装服务
./start-daemon.sh install
```

### 高级管理

#### 后端服务管理（systemd）

```bash
# 查看服务状态
sudo systemctl status seatmaster-backend

# 启动/停止/重启
sudo systemctl start seatmaster-backend
sudo systemctl stop seatmaster-backend
sudo systemctl restart seatmaster-backend

# 查看日志
sudo journalctl -u seatmaster-backend -f

# 启用/禁用开机自启
sudo systemctl enable seatmaster-backend
sudo systemctl disable seatmaster-backend
```

#### 前端服务管理（PM2）

```bash
# 查看PM2状态
pm2 status

# 查看详细信息
pm2 describe seatmaster-frontend

# 查看日志
pm2 logs seatmaster-frontend

# 重启应用
pm2 restart seatmaster-frontend

# 停止应用
pm2 stop seatmaster-frontend

# 删除应用
pm2 delete seatmaster-frontend

# 监控面板
pm2 monit
```

## 📊 监控和日志

### 健康检查

系统每5分钟自动执行健康检查：

```bash
# 手动执行健康检查
./health-check.sh

# 查看健康检查日志
tail -f logs/health-check.log
```

健康检查内容：
- ✅ 后端服务状态和API可访问性
- ✅ 前端服务状态和页面可访问性
- ✅ 数据库连接状态
- ✅ 系统资源使用情况（CPU、内存、磁盘）

### 日志管理

#### 日志文件位置

```
logs/
├── backend.log              # 后端应用日志
├── frontend-combined.log    # 前端合并日志
├── frontend-out.log         # 前端标准输出
├── frontend-error.log       # 前端错误日志
└── health-check.log         # 健康检查日志

/var/log/
└── syslog                   # 系统日志（包含systemd服务日志）

~/.pm2/logs/
├── seatmaster-frontend-out.log   # PM2前端输出日志
└── seatmaster-frontend-error.log # PM2前端错误日志
```

#### 查看日志

```bash
# 实时查看后端日志
sudo journalctl -u seatmaster-backend -f

# 实时查看前端日志
pm2 logs seatmaster-frontend --lines 100

# 查看健康检查日志
tail -f logs/health-check.log

# 查看系统日志
tail -f /var/log/syslog | grep seatmaster
```

#### 日志轮转

系统自动配置了日志轮转：
- 📅 每日轮转
- 🗂️ 保留30天
- 🗜️ 自动压缩
- 📧 异常时邮件通知

## 🔧 配置文件

### systemd服务配置

文件位置: `/etc/systemd/system/seatmaster-backend.service`

```ini
[Unit]
Description=SeatMaster Backend Service
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=simple
User=root
WorkingDirectory=/root/seatMaster/backend
ExecStart=/usr/share/maven/bin/mvn spring-boot:run
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### PM2配置

文件位置: `ecosystem.config.js`

```javascript
module.exports = {
  apps: [{
    name: 'seatmaster-frontend',
    script: 'npm',
    args: 'run serve',
    cwd: './frontend',
    instances: 1,
    autorestart: true,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'development',
      PORT: 3000
    }
  }]
};
```

## 🚨 故障排除

### 常见问题

#### 1. 后端服务启动失败

```bash
# 查看详细错误
sudo journalctl -u seatmaster-backend -n 50

# 检查Java和Maven环境
java -version
mvn -version

# 检查端口占用
netstat -tlnp | grep 8081

# 手动启动测试
cd backend && mvn spring-boot:run
```

#### 2. 前端服务启动失败

```bash
# 查看PM2日志
pm2 logs seatmaster-frontend

# 检查Node.js环境
node --version
npm --version

# 重新安装依赖
cd frontend && rm -rf node_modules && npm install

# 手动启动测试
cd frontend && npm run serve
```

#### 3. 数据库连接失败

```bash
# 检查MySQL服务
sudo systemctl status mysql

# 测试数据库连接
mysql -u root -proot5869087 -e "SELECT 1"

# 检查配置文件
cat backend/src/main/resources/application.yml
```

#### 4. 端口冲突

```bash
# 查看端口占用
netstat -tlnp | grep -E "(8081|3000)"

# 杀死占用进程
sudo kill -9 <PID>

# 修改端口配置
# 后端: backend/src/main/resources/application.yml
# 前端: frontend/vue.config.js
```

### 服务恢复

#### 完全重启

```bash
# 停止所有服务
./start-daemon.sh stop

# 清理进程
sudo pkill -f "spring-boot:run"
pm2 kill

# 重新启动
./start-daemon.sh start
```

#### 重置PM2

```bash
# 删除所有PM2应用
pm2 delete all

# 重新启动前端
./start-daemon.sh start frontend
```

#### 重新安装systemd服务

```bash
# 停止并禁用服务
sudo systemctl stop seatmaster-backend
sudo systemctl disable seatmaster-backend

# 删除服务文件
sudo rm /etc/systemd/system/seatmaster-backend.service

# 重新安装
./start-daemon.sh install
```

## 📈 性能优化

### 系统优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化网络参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
sysctl -p
```

### 应用优化

#### 后端优化

在 `backend/src/main/resources/application.yml` 中：

```yaml
server:
  tomcat:
    max-threads: 200
    min-spare-threads: 10
    max-connections: 8192

spring:
  datasource:
    hikari:
      maximum-pool-size: 30
      minimum-idle: 15
      connection-timeout: 20000
```

#### 前端优化

在 `ecosystem.config.js` 中：

```javascript
{
  instances: 'max',  // 使用所有CPU核心
  exec_mode: 'cluster',  // 集群模式
  max_memory_restart: '2G'  // 增加内存限制
}
```

## 🔒 安全配置

### 防火墙设置

```bash
# 只允许必要端口
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 8081/tcp  # 后端
sudo ufw allow 3000/tcp  # 前端
sudo ufw enable
```

### 服务安全

在systemd服务中已配置：
- ✅ 私有临时目录
- ✅ 保护系统目录
- ✅ 限制新权限
- ✅ 资源限制

## 📧 告警配置

### 邮件告警

编辑 `health-check.sh` 中的邮件配置：

```bash
ALERT_EMAIL="<EMAIL>"
ALERT_THRESHOLD=3
```

安装邮件服务：

```bash
sudo apt install mailutils postfix
```

### 自定义告警

可以集成其他告警系统：
- Slack Webhook
- 钉钉机器人
- 企业微信
- Prometheus + Grafana

## 🔄 备份和恢复

### 数据备份

```bash
# 数据库备份
mysqldump -u root -proot5869087 seat_reservation > backup_$(date +%Y%m%d).sql

# 应用备份
tar -czf seatmaster_backup_$(date +%Y%m%d).tar.gz /root/seatMaster
```

### 服务迁移

1. 备份数据和配置
2. 在新服务器上运行 `install-daemon.sh`
3. 恢复数据和配置
4. 启动服务

## 📞 技术支持

如遇问题，请按以下顺序排查：

1. 🔍 查看服务状态: `./start-daemon.sh status`
2. 📋 查看日志: `./start-daemon.sh logs`
3. 🏥 运行健康检查: `./health-check.sh`
4. 📖 参考故障排除章节
5. 🔄 尝试重启服务: `./start-daemon.sh restart`

---

**注意**: 本指南基于Ubuntu/Debian系统，其他Linux发行版可能需要适当调整。
