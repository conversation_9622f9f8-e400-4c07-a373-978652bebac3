#!/bin/bash

# 座位预订系统一键启动脚本
# 作者：Augment Agent
# 版本：1.0.0

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_ROOT=$(pwd)
BACKEND_DIR="$PROJECT_ROOT/backend"
FRONTEND_DIR="$PROJECT_ROOT/frontend"

# 日志文件
LOG_DIR="$PROJECT_ROOT/logs"
BACKEND_LOG="$LOG_DIR/backend.log"
FRONTEND_LOG="$LOG_DIR/frontend.log"

# PID文件
PID_DIR="$PROJECT_ROOT/pids"
BACKEND_PID="$PID_DIR/backend.pid"
FRONTEND_PID="$PID_DIR/frontend.pid"

# 创建必要的目录
mkdir -p "$LOG_DIR" "$PID_DIR"

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] ${message}${NC}"
}

print_success() {
    print_message "$GREEN" "✅ $1"
}

print_error() {
    print_message "$RED" "❌ $1"
}

print_warning() {
    print_message "$YELLOW" "⚠️  $1"
}

print_info() {
    print_message "$BLUE" "ℹ️  $1"
}

print_step() {
    print_message "$PURPLE" "🔄 $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_error "$1 未安装或不在PATH中"
        return 1
    fi
    return 0
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
        return 0  # 端口被占用
    else
        return 1  # 端口空闲
    fi
}

# 等待端口启动
wait_for_port() {
    local port=$1
    local service_name=$2
    local max_wait=60
    local count=0
    
    print_step "等待 $service_name 在端口 $port 启动..."
    
    while [ $count -lt $max_wait ]; do
        if check_port $port; then
            print_success "$service_name 已在端口 $port 启动"
            return 0
        fi
        sleep 2
        count=$((count + 2))
        echo -n "."
    done
    
    echo ""
    print_error "$service_name 启动超时（等待了 ${max_wait}s）"
    return 1
}

# 停止服务
stop_services() {
    print_step "停止所有服务..."
    
    # 停止后端
    if [ -f "$BACKEND_PID" ]; then
        local backend_pid=$(cat "$BACKEND_PID")
        if kill -0 "$backend_pid" 2>/dev/null; then
            print_step "停止后端服务 (PID: $backend_pid)"
            kill "$backend_pid" 2>/dev/null || true
            sleep 3
            # 强制杀死如果还在运行
            if kill -0 "$backend_pid" 2>/dev/null; then
                kill -9 "$backend_pid" 2>/dev/null || true
            fi
        fi
        rm -f "$BACKEND_PID"
    fi
    
    # 停止前端
    if [ -f "$FRONTEND_PID" ]; then
        local frontend_pid=$(cat "$FRONTEND_PID")
        if kill -0 "$frontend_pid" 2>/dev/null; then
            print_step "停止前端服务 (PID: $frontend_pid)"
            kill "$frontend_pid" 2>/dev/null || true
            sleep 3
            # 强制杀死如果还在运行
            if kill -0 "$frontend_pid" 2>/dev/null; then
                kill -9 "$frontend_pid" 2>/dev/null || true
            fi
        fi
        rm -f "$FRONTEND_PID"
    fi
    
    # 额外清理：杀死可能残留的进程
    pkill -f "SeatReservationApplication" 2>/dev/null || true
    pkill -f "vue-cli-service serve" 2>/dev/null || true
    
    print_success "所有服务已停止"
}

# 信号处理
cleanup() {
    echo ""
    print_warning "收到中断信号，正在清理..."
    stop_services
    exit 0
}

trap cleanup SIGINT SIGTERM

# 主函数
main() {
    echo ""
    echo -e "${CYAN}================================================${NC}"
    echo -e "${CYAN}    🚀 座位预订系统一键启动脚本 v1.0.0${NC}"
    echo -e "${CYAN}================================================${NC}"
    echo ""
    
    # 1. 环境检查
    print_step "检查运行环境..."
    
    local env_ok=true
    
    if ! check_command java; then
        env_ok=false
    else
        local java_version=$(java -version 2>&1 | head -n 1)
        print_success "Java: $java_version"
    fi
    
    if ! check_command mvn; then
        env_ok=false
    else
        local mvn_version=$(mvn -version 2>/dev/null | head -n 1)
        print_success "Maven: $mvn_version"
    fi
    
    if ! check_command node; then
        env_ok=false
    else
        local node_version=$(node --version)
        print_success "Node.js: $node_version"
    fi
    
    if ! check_command npm; then
        env_ok=false
    else
        local npm_version=$(npm --version)
        print_success "npm: v$npm_version"
    fi
    
    if [ "$env_ok" = false ]; then
        print_error "环境检查失败，请安装缺失的依赖"
        exit 1
    fi
    
    # 2. 检查项目目录
    print_step "检查项目结构..."
    
    if [ ! -d "$BACKEND_DIR" ]; then
        print_error "后端目录不存在: $BACKEND_DIR"
        exit 1
    fi
    
    if [ ! -d "$FRONTEND_DIR" ]; then
        print_error "前端目录不存在: $FRONTEND_DIR"
        exit 1
    fi
    
    if [ ! -f "$BACKEND_DIR/pom.xml" ]; then
        print_error "后端pom.xml文件不存在"
        exit 1
    fi
    
    if [ ! -f "$FRONTEND_DIR/package.json" ]; then
        print_error "前端package.json文件不存在"
        exit 1
    fi
    
    print_success "项目结构检查通过"
    
    # 3. 停止现有服务
    print_step "清理现有服务..."
    stop_services
    
    # 4. 检查端口
    print_step "检查端口占用情况..."
    
    if check_port 8081; then
        print_warning "端口8081仍被占用，尝试清理..."
        pkill -f ":8081" 2>/dev/null || true
        sleep 2
        if check_port 8081; then
            print_error "无法释放端口8081，请手动检查"
            exit 1
        fi
    fi
    
    if check_port 3000; then
        print_warning "端口3000仍被占用，尝试清理..."
        pkill -f ":3000" 2>/dev/null || true
        sleep 2
        if check_port 3000; then
            print_error "无法释放端口3000，请手动检查"
            exit 1
        fi
    fi
    
    print_success "端口检查通过"
    
    # 5. 安装前端依赖（如果需要）
    print_step "检查前端依赖..."
    
    if [ ! -d "$FRONTEND_DIR/node_modules" ]; then
        print_step "安装前端依赖..."
        cd "$FRONTEND_DIR"
        npm install
        if [ $? -ne 0 ]; then
            print_error "前端依赖安装失败"
            exit 1
        fi
        print_success "前端依赖安装完成"
        cd "$PROJECT_ROOT"
    else
        print_success "前端依赖已存在"
    fi
    
    # 6. 启动后端服务
    print_step "启动后端服务..."
    
    cd "$BACKEND_DIR"
    nohup mvn spring-boot:run > "$BACKEND_LOG" 2>&1 &
    local backend_pid=$!
    echo $backend_pid > "$BACKEND_PID"
    
    print_info "后端服务启动中... (PID: $backend_pid)"
    print_info "后端日志: $BACKEND_LOG"
    
    cd "$PROJECT_ROOT"
    
    # 7. 等待后端启动
    if ! wait_for_port 8081 "后端服务"; then
        print_error "后端服务启动失败，查看日志: $BACKEND_LOG"
        stop_services
        exit 1
    fi
    
    # 8. 启动前端服务
    print_step "启动前端服务..."
    
    cd "$FRONTEND_DIR"
    nohup npm run serve > "$FRONTEND_LOG" 2>&1 &
    local frontend_pid=$!
    echo $frontend_pid > "$FRONTEND_PID"
    
    print_info "前端服务启动中... (PID: $frontend_pid)"
    print_info "前端日志: $FRONTEND_LOG"
    
    cd "$PROJECT_ROOT"
    
    # 9. 等待前端启动
    if ! wait_for_port 3000 "前端服务"; then
        print_error "前端服务启动失败，查看日志: $FRONTEND_LOG"
        stop_services
        exit 1
    fi
    
    # 10. 启动完成
    echo ""
    echo -e "${GREEN}================================================${NC}"
    echo -e "${GREEN}    🎉 所有服务启动成功！${NC}"
    echo -e "${GREEN}================================================${NC}"
    echo ""
    print_success "后端服务: http://localhost:8081"
    print_success "前端服务: http://localhost:3000"
    print_success "API文档: http://localhost:8081/swagger-ui.html (如果配置了)"
    echo ""
    print_info "日志文件:"
    print_info "  后端: $BACKEND_LOG"
    print_info "  前端: $FRONTEND_LOG"
    echo ""
    print_info "PID文件:"
    print_info "  后端: $BACKEND_PID"
    print_info "  前端: $FRONTEND_PID"
    echo ""
    print_warning "按 Ctrl+C 停止所有服务"
    echo ""
    
    # 11. 保持脚本运行，监控服务状态
    while true; do
        sleep 10
        
        # 检查后端是否还在运行
        if [ -f "$BACKEND_PID" ]; then
            local backend_pid=$(cat "$BACKEND_PID")
            if ! kill -0 "$backend_pid" 2>/dev/null; then
                print_error "后端服务意外停止"
                stop_services
                exit 1
            fi
        fi
        
        # 检查前端是否还在运行
        if [ -f "$FRONTEND_PID" ]; then
            local frontend_pid=$(cat "$FRONTEND_PID")
            if ! kill -0 "$frontend_pid" 2>/dev/null; then
                print_error "前端服务意外停止"
                stop_services
                exit 1
            fi
        fi
    done
}

# 检查参数
if [ "$1" = "stop" ]; then
    stop_services
    exit 0
elif [ "$1" = "status" ]; then
    echo "服务状态检查:"
    if check_port 8081; then
        print_success "后端服务运行中 (端口8081)"
    else
        print_error "后端服务未运行"
    fi
    
    if check_port 3000; then
        print_success "前端服务运行中 (端口3000)"
    else
        print_error "前端服务未运行"
    fi
    exit 0
elif [ "$1" = "help" ] || [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    echo "座位预订系统一键启动脚本"
    echo ""
    echo "用法:"
    echo "  $0          启动所有服务"
    echo "  $0 stop     停止所有服务"
    echo "  $0 status   检查服务状态"
    echo "  $0 help     显示帮助信息"
    echo ""
    exit 0
fi

# 运行主函数
main
