const { spawn } = require('child_process');
const axios = require('axios');

async function debugBackendStartup() {
    console.log('=== SeatMaster 后端启动调试 ===\n');
    
    // 1. 检查8081端口是否被占用
    console.log('1. 检查端口8081状态...');
    try {
        const response = await axios.get('http://localhost:8081/api/health', { timeout: 3000 });
        console.log('✅ 后端服务已在运行');
        console.log('   响应状态:', response.status);
        return true;
    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            console.log('❌ 端口8081未被占用，需要启动后端服务');
        } else {
            console.log('❌ 连接错误:', error.message);
        }
    }
    
    // 2. 尝试启动后端服务
    console.log('\n2. 尝试启动后端服务...');
    console.log('请手动执行以下命令启动后端服务:');
    console.log('cd backend');
    console.log('mvn spring-boot:run');
    console.log('\n等待后端服务启动...');
    
    // 3. 等待服务启动并检查
    let attempts = 0;
    const maxAttempts = 30; // 等待3分钟
    
    while (attempts < maxAttempts) {
        try {
            console.log(`尝试连接 (${attempts + 1}/${maxAttempts})...`);
            const response = await axios.get('http://localhost:8081/api/health', { timeout: 3000 });
            console.log('✅ 后端服务启动成功!');
            console.log('   响应状态:', response.status);
            return true;
        } catch (error) {
            attempts++;
            if (attempts < maxAttempts) {
                await new Promise(resolve => setTimeout(resolve, 6000)); // 等待6秒
            }
        }
    }
    
    console.log('❌ 后端服务启动超时');
    return false;
}

async function testAnnouncementEndpoints() {
    console.log('\n=== 测试公告API端点 ===\n');
    
    const baseURL = 'http://localhost:8081';
    
    try {
        // 1. 测试管理员登录
        console.log('1. 测试管理员登录...');
        const loginResponse = await axios.post(`${baseURL}/api/auth/login`, {
            username: 'admin',
            password: 'admin123'
        });
        
        if (loginResponse.data.code !== 200) {
            console.log('❌ 管理员登录失败:', loginResponse.data.message);
            return false;
        }
        
        const token = loginResponse.data.data.token;
        console.log('✅ 管理员登录成功');
        
        // 2. 测试公告API端点
        console.log('\n2. 测试公告API端点...');
        
        const endpoints = [
            { method: 'GET', url: '/api/admin/announcements', desc: '获取公告列表' },
            { method: 'GET', url: '/api/user/announcements', desc: '获取用户公告' },
            { method: 'GET', url: '/api/user/announcements/unread-popup', desc: '获取未读弹窗公告' }
        ];
        
        for (const endpoint of endpoints) {
            try {
                console.log(`测试: ${endpoint.desc} (${endpoint.method} ${endpoint.url})`);
                const response = await axios({
                    method: endpoint.method,
                    url: `${baseURL}${endpoint.url}`,
                    headers: { 'Authorization': `Bearer ${token}` },
                    timeout: 5000
                });
                console.log(`✅ ${endpoint.desc} - 状态: ${response.status}`);
            } catch (error) {
                if (error.response) {
                    console.log(`❌ ${endpoint.desc} - 错误: ${error.response.status} ${error.response.statusText}`);
                    if (error.response.data) {
                        console.log(`   详细信息: ${JSON.stringify(error.response.data)}`);
                    }
                } else {
                    console.log(`❌ ${endpoint.desc} - 网络错误: ${error.message}`);
                }
            }
        }
        
        return true;
        
    } catch (error) {
        console.log('❌ 测试过程中发生错误:', error.message);
        return false;
    }
}

async function main() {
    const backendRunning = await debugBackendStartup();
    
    if (backendRunning) {
        await testAnnouncementEndpoints();
    } else {
        console.log('\n请按照以下步骤手动启动后端服务:');
        console.log('1. 打开新的命令行窗口');
        console.log('2. cd C:\\Users\\<USER>\\Desktop\\fsdownload\\seatMaster\\backend');
        console.log('3. mvn spring-boot:run');
        console.log('4. 等待服务启动完成后重新运行此脚本');
    }
}

main().catch(console.error);
